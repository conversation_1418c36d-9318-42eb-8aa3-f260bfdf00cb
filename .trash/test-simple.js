/**
 * 简单的测试视图文件
 * 用于验证 dv.view() 是否能正常工作
 */

console.log('test-simple.js 被加载了！');
console.log('输入参数:', input);

// 创建一个简单的测试输出
dv.container.innerHTML = `
    <div style="padding: 20px; border: 2px solid green; border-radius: 8px; background-color: #f0f8f0;">
        <h3 style="color: green;">✅ dv.view() 测试成功！</h3>
        <p><strong>文件:</strong> web3/farm/lib/test-simple.js</p>
        <p><strong>加载时间:</strong> ${new Date().toLocaleString()}</p>
        <p><strong>输入参数:</strong> ${JSON.stringify(input, null, 2)}</p>
        <hr>
        <p>这证明 dv.view() 方法可以正常工作，现在可以加载更复杂的 Dashboard 代码了。</p>
    </div>
`;

console.log('test-simple.js 执行完成');


```dataviewjs
// ===== Web3 Farm Dashboard 仪表板 =====
//
// 🚨 重要提醒：在修改此代码或进行开发之前，请务必先阅读 README_dashboard.md 文档！
//
// 该文档包含：
// - 完整的代码架构说明和模块化设计
// - 详细的函数说明和计算逻辑
// - 开发者修改指引和影响分析
// - 常见问题排查和调试建议
// - 高风险修改警告和最佳实践
//
// 文档路径：web3/farm/README_dashboard.md
//
// ===== 重构说明 =====
// 本文件已重构为使用独立的 JS 库模块
// 核心逻辑已迁移到 web3/farm/lib/ 目录下的独立文件中
//
// ===== 初始化全局变量 ===== (静默执行)
// 清空当前输出
dv.container.innerHTML = '';

// ===== 加载核心库文件 =====
// 在 Obsidian 环境中，我们需要使用 script 标签来加载 JS 文件
const loadScript = (src) => {
    return new Promise((resolve, reject) => {
        // 检查是否已经加载过
        if (document.querySelector(`script[src="${src}"]`)) {
            resolve();
            return;
        }

        const script = document.createElement('script');
        script.src = src;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
    });
};

// 获取当前文件的基础路径（在 try 块外定义，以便在 catch 块中使用）
const currentFile = dv.current().file;
const basePath = currentFile.folder ? `${currentFile.folder}/` : '';
const libPath = `${basePath}lib/`;

try {
    console.log(`开始加载库文件，路径: ${libPath}`);

    // 按依赖顺序加载模块
    await loadScript(`${libPath}dashboard-config.js`);
    console.log('dashboard-config.js 加载完成');

    await loadScript(`${libPath}dashboard-utils.js`);
    console.log('dashboard-utils.js 加载完成');

    await loadScript(`${libPath}dashboard-data-loader.js`);
    console.log('dashboard-data-loader.js 加载完成');

    await loadScript(`${libPath}dashboard-calculator.js`);
    console.log('dashboard-calculator.js 加载完成');

    await loadScript(`${libPath}dashboard-table-builder.js`);
    console.log('dashboard-table-builder.js 加载完成');

    await loadScript(`${libPath}dashboard-renderer.js`);
    console.log('dashboard-renderer.js 加载完成');

    await loadScript(`${libPath}dashboard-ui.js`);
    console.log('dashboard-ui.js 加载完成');

    await loadScript(`${libPath}dashboard-core.js`);
    console.log('dashboard-core.js 加载完成');

    console.log('所有库模块加载成功');

    // 等待一小段时间确保所有模块都已初始化
    await new Promise(resolve => setTimeout(resolve, 200));

    // 验证关键对象是否已创建
    console.log('DashboardCore 存在:', typeof window.DashboardCore !== 'undefined');
    console.log('DashboardCore.DefaultConfig 存在:', typeof window.DashboardCore?.DefaultConfig !== 'undefined');
    console.log('DashboardCore.Utils 存在:', typeof window.DashboardCore?.Utils !== 'undefined');
    console.log('DashboardCore.DataLoader 存在:', typeof window.DashboardCore?.DataLoader !== 'undefined');
    console.log('DashboardCore.BaseCalculator 存在:', typeof window.DashboardCore?.BaseCalculator !== 'undefined');
    console.log('DashboardCore.USDTCalculator 存在:', typeof window.DashboardCore?.USDTCalculator !== 'undefined');
    console.log('DashboardCore.DerivedCalculator 存在:', typeof window.DashboardCore?.DerivedCalculator !== 'undefined');
    console.log('DashboardCore.TableBuilder 存在:', typeof window.DashboardCore?.TableBuilder !== 'undefined');
    console.log('DashboardCore.Renderer 存在:', typeof window.DashboardCore?.Renderer !== 'undefined');
    console.log('DashboardCore.UI 存在:', typeof window.DashboardCore?.UI !== 'undefined');
    console.log('DashboardCore.API 存在:', typeof window.DashboardCore?.API !== 'undefined');
    console.log('DashboardCore.validateDependencies 存在:', typeof window.DashboardCore?.validateDependencies !== 'undefined');

} catch (error) {
    console.error('库模块加载失败:', error);
    dv.container.innerHTML = `
        <div style="color: red; padding: 20px; border: 1px solid red; border-radius: 6px;">
            <h3>❌ 库加载失败</h3>
            <p>无法加载 Dashboard 库文件: ${error.message}</p>
            <p>请检查 web3/farm/lib/ 目录下的文件是否存在。</p>
            <details>
                <summary>技术详情</summary>
                <p>尝试加载路径: ${libPath}</p>
                <p>当前文件: ${currentFile.path}</p>
                <p>错误堆栈: ${error.stack}</p>
            </details>
        </div>
    `;
    throw error;
}

// ===== 配置初始化 =====
const dashboardConfig = {
    HISTORY_PATH: '"web3/farm/history"',
    RISK_LIMIT_PATH: 'web3/farm/data/risk_percentage',
    PRICES_FILE_PATH: 'web3/farm/data/token_prices',
    LEVEL_LIMIT_PATH: 'web3/farm/data/invest_level_limit'
};

// ===== 初始化 Dashboard 核心库 =====
// 检查 DashboardCore 是否已加载
if (typeof window.DashboardCore === 'undefined') {
    throw new Error('DashboardCore 库未加载。请检查 lib/dashboard-core.js 文件。');
}

if (!window.DashboardCore.API) {
    throw new Error('DashboardCore.API 未定义。请检查库文件是否正确加载。');
}

// 运行依赖验证
if (window.DashboardCore.validateDependencies && !window.DashboardCore.validateDependencies()) {
    throw new Error('Dashboard 依赖验证失败。请检查所有库文件是否正确加载。');
}

await window.DashboardCore.API.initialize(dv, dashboardConfig);

console.log('Dashboard 初始化完成');
```

```dataviewjs
// ===== 主执行逻辑 =====
// 清空当前输出
dv.container.innerHTML = '';

try {
    // 检查核心库是否已正确加载
    if (typeof window.DashboardCore === 'undefined') {
        throw new Error('DashboardCore 库未加载');
    }

    if (!window.DashboardCore.API) {
        throw new Error('DashboardCore.API 未定义');
    }

    if (!window.DashboardCore.UI) {
        throw new Error('DashboardCore.UI 未定义');
    }

    // 渲染完整的仪表板
    const dashboardHTML = await window.DashboardCore.API.renderDashboard('dashboard-container');

    // 将生成的HTML添加到容器中
    const container = document.createElement('div');
    container.innerHTML = dashboardHTML;
    dv.container.appendChild(container);

    // 绑定事件处理器
    window.DashboardCore.UI.bindEvents();

    console.log('Dashboard 渲染完成');

} catch (error) {
    console.error('Dashboard 渲染失败:', error);

    // 获取当前文件信息用于调试
    const currentFile = dv.current().file;
    const basePath = currentFile.folder ? `${currentFile.folder}/` : '';

    dv.container.innerHTML = `
        <div style="color: red; padding: 20px; border: 1px solid red; border-radius: 6px; margin: 20px;">
            <h3>❌ 仪表板渲染失败</h3>
            <p><strong>错误信息:</strong> ${error.message}</p>
            <p><strong>错误详情:</strong> ${error.stack || '无详细信息'}</p>
            <hr>
            <p><strong>调试信息:</strong></p>
            <ul>
                <li>当前文件路径: ${currentFile.path}</li>
                <li>库文件基础路径: ${basePath}lib/</li>
                <li>DashboardCore 是否存在: ${typeof window.DashboardCore !== 'undefined'}</li>
                <li>DashboardCore.API 是否存在: ${typeof window.DashboardCore?.API !== 'undefined'}</li>
                <li>DashboardCore.UI 是否存在: ${typeof window.DashboardCore?.UI !== 'undefined'}</li>
            </ul>
            <hr>
            <p><strong>可能的解决方案:</strong></p>
            <ul>
                <li>检查 ${basePath}lib/ 目录下的所有 JS 文件是否存在</li>
                <li>确认数据文件路径配置是否正确</li>
                <li>查看浏览器开发者工具的控制台获取更多错误信息</li>
                <li>尝试刷新页面重新加载</li>
                <li>检查文件权限和网络连接</li>
            </ul>
        </div>
    `;
}
```

# Dashboard 库加载测试

这是一个简单的测试文件，用于验证 Dashboard 库文件是否能正确加载。

```dataviewjs
// ===== 简单的库加载测试 =====

// 在 Obsidian 环境中，我们需要使用 script 标签来加载 JS 文件
const loadScript = (src) => {
    return new Promise((resolve, reject) => {
        // 检查是否已经加载过
        if (document.querySelector(`script[src="${src}"]`)) {
            resolve();
            return;
        }
        
        const script = document.createElement('script');
        script.src = src;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
    });
};

// 获取当前文件的基础路径
const currentFile = dv.current().file;
const basePath = currentFile.folder ? `${currentFile.folder}/` : '';
const libPath = `${basePath}lib/`;

console.log(`开始测试库文件加载，路径: ${libPath}`);

try {
    // 先测试加载单个文件
    console.log('测试加载 dashboard-config.js...');
    await loadScript(`${libPath}dashboard-config.js`);
    console.log('✅ dashboard-config.js 加载完成');

    // 检查是否创建了预期的对象
    console.log('DashboardCore 存在:', typeof window.DashboardCore !== 'undefined');
    console.log('DashboardCore.DefaultConfig 存在:', typeof window.DashboardCore?.DefaultConfig !== 'undefined');

    if (typeof window.DashboardCore !== 'undefined' && window.DashboardCore.DefaultConfig) {
        console.log('✅ 第一个模块加载成功，继续加载其他模块...');

        console.log('加载 dashboard-utils.js...');
        await loadScript(`${libPath}dashboard-utils.js`);
        console.log('✅ dashboard-utils.js 加载完成');

        console.log('加载 dashboard-data-loader.js...');
        await loadScript(`${libPath}dashboard-data-loader.js`);
        console.log('✅ dashboard-data-loader.js 加载完成');

        console.log('加载 dashboard-calculator.js...');
        await loadScript(`${libPath}dashboard-calculator.js`);
        console.log('✅ dashboard-calculator.js 加载完成');

        console.log('加载 dashboard-table-builder.js...');
        await loadScript(`${libPath}dashboard-table-builder.js`);
        console.log('✅ dashboard-table-builder.js 加载完成');

        console.log('加载 dashboard-renderer.js...');
        await loadScript(`${libPath}dashboard-renderer.js`);
        console.log('✅ dashboard-renderer.js 加载完成');

        console.log('加载 dashboard-ui.js...');
        await loadScript(`${libPath}dashboard-ui.js`);
        console.log('✅ dashboard-ui.js 加载完成');

        console.log('加载 dashboard-core.js...');
        await loadScript(`${libPath}dashboard-core.js`);
        console.log('✅ dashboard-core.js 加载完成');
    } else {
        throw new Error('第一个模块加载失败，DashboardCore 或 DefaultConfig 未创建');
    }

    // 等待一小段时间确保所有模块都已初始化
    await new Promise(resolve => setTimeout(resolve, 200));
    
    // 验证关键对象是否已创建
    const results = {
        'DashboardCore': typeof window.DashboardCore !== 'undefined',
        'DashboardCore.DefaultConfig': typeof window.DashboardCore?.DefaultConfig !== 'undefined',
        'DashboardCore.Utils': typeof window.DashboardCore?.Utils !== 'undefined',
        'DashboardCore.DataLoader': typeof window.DashboardCore?.DataLoader !== 'undefined',
        'DashboardCore.BaseCalculator': typeof window.DashboardCore?.BaseCalculator !== 'undefined',
        'DashboardCore.USDTCalculator': typeof window.DashboardCore?.USDTCalculator !== 'undefined',
        'DashboardCore.DerivedCalculator': typeof window.DashboardCore?.DerivedCalculator !== 'undefined',
        'DashboardCore.TableBuilder': typeof window.DashboardCore?.TableBuilder !== 'undefined',
        'DashboardCore.Renderer': typeof window.DashboardCore?.Renderer !== 'undefined',
        'DashboardCore.UI': typeof window.DashboardCore?.UI !== 'undefined',
        'DashboardCore.API': typeof window.DashboardCore?.API !== 'undefined',
        'DashboardCore.validateDependencies': typeof window.DashboardCore?.validateDependencies !== 'undefined'
    };
    
    console.log('模块验证结果:', results);
    
    // 运行依赖验证
    if (window.DashboardCore.validateDependencies) {
        const dependenciesValid = window.DashboardCore.validateDependencies();
        console.log('依赖验证结果:', dependenciesValid);
    }
    
    // 显示结果
    let resultHTML = '<div style="padding: 20px; border: 1px solid green; border-radius: 6px; margin: 20px;">';
    resultHTML += '<h3>✅ 库加载测试结果</h3>';
    resultHTML += '<p><strong>所有库文件加载成功！</strong></p>';
    resultHTML += '<h4>模块验证结果：</h4>';
    resultHTML += '<ul>';
    
    for (const [module, exists] of Object.entries(results)) {
        const status = exists ? '✅' : '❌';
        const color = exists ? 'green' : 'red';
        resultHTML += `<li style="color: ${color};">${status} ${module}: ${exists}</li>`;
    }
    
    resultHTML += '</ul>';
    
    if (window.DashboardCore.validateDependencies) {
        const dependenciesValid = window.DashboardCore.validateDependencies();
        const depStatus = dependenciesValid ? '✅' : '❌';
        const depColor = dependenciesValid ? 'green' : 'red';
        resultHTML += `<p style="color: ${depColor};"><strong>${depStatus} 依赖验证: ${dependenciesValid}</strong></p>`;
    }
    
    resultHTML += '</div>';
    
    dv.container.innerHTML = resultHTML;
    
} catch (error) {
    console.error('库文件加载失败:', error);
    
    dv.container.innerHTML = `
        <div style="color: red; padding: 20px; border: 1px solid red; border-radius: 6px; margin: 20px;">
            <h3>❌ 库加载测试失败</h3>
            <p><strong>错误信息:</strong> ${error.message}</p>
            <p><strong>错误详情:</strong> ${error.stack || '无详细信息'}</p>
            <hr>
            <p><strong>调试信息:</strong></p>
            <ul>
                <li>当前文件路径: ${currentFile.path}</li>
                <li>库文件基础路径: ${libPath}</li>
            </ul>
        </div>
    `;
}
```

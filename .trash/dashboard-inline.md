# Web3 Farm Dashboard (内联版本)

这是一个内联版本的 Dashboard，所有代码都直接包含在这个文件中，避免外部文件加载的问题。

```dataviewjs
// ===== 内联版本的 Dashboard =====
console.log('开始加载内联版本的 Dashboard...');

// 清空容器
dv.container.innerHTML = '';

// 创建全局对象
window.DashboardCore = window.DashboardCore || {};

// 基础配置
window.DashboardCore.DefaultConfig = {
    // 文件路径配置
    PATHS: {
        HISTORY: '"web3/farm/history"',
        RISK_LIMIT: 'web3/farm/data/risk_percentage',
        PRICES: 'web3/farm/data/token_prices',
        LEVEL_LIMIT: 'web3/farm/data/invest_level_limit'
    },
    
    // 颜色配置
    COLORS: {
        RISK: {
            LOW: '#28a745',      // 绿色：无风险、低风险 (0-1)
            MEDIUM: '#ffc107',   // 黄色：中风险 (2)
            HIGH: '#dc3545'      // 红色：高风险、极高风险 (3+)
        },
        VALUE: {
            POSITIVE: '#28a745',   // 绿色：正数
            NEGATIVE: '#dc3545',   // 红色：负数
            NEUTRAL: '#000000'     // 黑色：零值
        }
    }
};

// 基础工具函数
window.DashboardCore.Utils = {
    formatNumber: (num) => {
        if (num === null || num === undefined || isNaN(num)) return "0";
        return parseFloat(num).toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    },
    
    formatPercentage: (num, decimals = 2) => {
        if (num === null || num === undefined || isNaN(num)) return "0%";
        return `${parseFloat(num).toFixed(decimals)}%`;
    },
    
    formatColoredNumber: (num) => {
        const formatted = window.DashboardCore.Utils.formatNumber(num);
        if (num > 0) {
            return `<span style="color: #28a745;">${formatted}</span>`;
        } else if (num < 0) {
            return `<span style="color: #dc3545;">${formatted}</span>`;
        } else {
            return formatted;
        }
    }
};

// 数据加载器
window.DashboardCore.DataLoader = {
    loadAllData: () => {
        try {
            // 加载项目数据
            const doingProjects = dv.pages(window.DashboardCore.DefaultConfig.PATHS.HISTORY)
                .where(p => p.Status === "Doing")
                .array();
            
            const allProjects = dv.pages(window.DashboardCore.DefaultConfig.PATHS.HISTORY).array();
            
            // 加载风险配置
            const riskPages = dv.pages(`"${window.DashboardCore.DefaultConfig.PATHS.RISK_LIMIT}"`).array();
            const riskInfo = {};
            if (riskPages.length > 0) {
                const riskPage = riskPages[0];
                if (riskPage.RiskLevels) {
                    for (const [level, config] of Object.entries(riskPage.RiskLevels)) {
                        riskInfo[level] = {
                            text: config.text || `风险 ${level}`,
                            expectApr: config.expectApr || 0
                        };
                    }
                }
            }
            
            // 加载等级配置
            const levelPages = dv.pages(`"${window.DashboardCore.DefaultConfig.PATHS.LEVEL_LIMIT}"`).array();
            const levelInfo = {};
            if (levelPages.length > 0) {
                const levelPage = levelPages[0];
                if (levelPage.InvestLevels) {
                    for (const [level, config] of Object.entries(levelPage.InvestLevels)) {
                        levelInfo[level] = {
                            text: config.text || `仓位 ${level}`,
                            limit: config.limit || 0
                        };
                    }
                }
            }
            
            // 创建空的价格映射（简化版本）
            const priceMap = new Map();
            
            return {
                doingProjects,
                allProjects,
                riskInfo,
                levelInfo,
                priceMap
            };
        } catch (error) {
            console.error('数据加载失败:', error);
            throw error;
        }
    }
};

// 简化的表格渲染器
window.DashboardCore.Renderer = {
    renderSimpleTable: (data) => {
        const { doingProjects, riskInfo } = data;
        
        if (!doingProjects || doingProjects.length === 0) {
            return `
                <div style="text-align: center; padding: 40px; color: #666;">
                    <h3>📊 Dashboard</h3>
                    <p>当前没有正在进行中的项目</p>
                </div>
            `;
        }
        
        let html = `
            <div style="padding: 20px;">
                <h2>🚀 Web3 Farm Dashboard</h2>
                <p>找到 ${doingProjects.length} 个正在进行的项目</p>
                
                <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
                    <thead>
                        <tr style="background-color: #f8f9fa;">
                            <th style="border: 1px solid #ddd; padding: 8px;">项目</th>
                            <th style="border: 1px solid #ddd; padding: 8px;">协议</th>
                            <th style="border: 1px solid #ddd; padding: 8px;">风险等级</th>
                            <th style="border: 1px solid #ddd; padding: 8px;">状态</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        for (const project of doingProjects) {
            const riskLevel = project.Risk || 0;
            const riskText = riskInfo[riskLevel]?.text || `风险 ${riskLevel}`;
            const protocol = project.Protocol || '-';
            const status = project.Status || 'Doing';
            
            html += `
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px;">${project.file.link}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">${protocol}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">${riskText}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">
                        <span style="color: #28a745;">🟢 ${status}</span>
                    </td>
                </tr>
            `;
        }
        
        html += `
                    </tbody>
                </table>
            </div>
        `;
        
        return html;
    }
};

// 核心 API
window.DashboardCore.API = {
    initialize: async (dv, config = {}) => {
        window.DashboardCore.dv = dv;
        window.DashboardCore.Config = {
            ...window.DashboardCore.DefaultConfig,
            ...config
        };
        console.log('Dashboard Core 初始化完成');
    },
    
    renderDashboard: async () => {
        try {
            const data = window.DashboardCore.DataLoader.loadAllData();
            return window.DashboardCore.Renderer.renderSimpleTable(data);
        } catch (error) {
            console.error('Dashboard 渲染失败:', error);
            return `
                <div style="color: red; padding: 20px; border: 1px solid red; border-radius: 6px;">
                    <h3>❌ Dashboard 渲染失败</h3>
                    <p>错误信息: ${error.message}</p>
                </div>
            `;
        }
    }
};

console.log('内联 Dashboard 模块加载完成');

// 初始化和渲染
try {
    await window.DashboardCore.API.initialize(dv);
    const dashboardHTML = await window.DashboardCore.API.renderDashboard();
    dv.container.innerHTML = dashboardHTML;
    console.log('Dashboard 渲染成功');
} catch (error) {
    console.error('Dashboard 初始化或渲染失败:', error);
    dv.container.innerHTML = `
        <div style="color: red; padding: 20px; border: 1px solid red; border-radius: 6px;">
            <h3>❌ Dashboard 失败</h3>
            <p>错误信息: ${error.message}</p>
            <p>错误详情: ${error.stack}</p>
        </div>
    `;
}
```

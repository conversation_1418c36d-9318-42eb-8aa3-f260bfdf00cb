# Web3 Farm Dashboard (工作版本)

这是一个完全内联的 Dashboard 版本，避免了外部文件加载的复杂性。

```dataviewjs
// ===== Web3 Farm Dashboard 内联版本 =====
console.log('开始加载 Dashboard...');

// 清空容器
dv.container.innerHTML = '';

try {
    // ===== 配置 =====
    const config = {
        HISTORY_PATH: '"web3/farm/history"',
        RISK_LIMIT_PATH: 'web3/farm/data/risk_percentage',
        PRICES_FILE_PATH: 'web3/farm/data/token_prices',
        LEVEL_LIMIT_PATH: 'web3/farm/data/invest_level_limit'
    };

    // ===== 工具函数 =====
    const utils = {
        formatNumber: (num) => {
            if (num === null || num === undefined || isNaN(num)) return "0";
            return parseFloat(num).toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        },
        
        formatPercentage: (num, decimals = 2) => {
            if (num === null || num === undefined || isNaN(num)) return "0%";
            return `${parseFloat(num).toFixed(decimals)}%`;
        },
        
        formatColoredNumber: (num) => {
            const formatted = utils.formatNumber(num);
            if (num > 0) {
                return `<span style="color: #28a745;">${formatted}</span>`;
            } else if (num < 0) {
                return `<span style="color: #dc3545;">${formatted}</span>`;
            } else {
                return formatted;
            }
        }
    };

    // ===== 数据加载 =====
    console.log('开始加载数据...');
    
    // 加载项目数据
    const doingProjects = dv.pages(config.HISTORY_PATH)
        .where(p => p.Status === "Doing")
        .array();
    
    const allProjects = dv.pages(config.HISTORY_PATH).array();
    
    console.log(`加载了 ${doingProjects.length} 个进行中的项目，${allProjects.length} 个总项目`);
    
    // 加载风险配置
    const riskPages = dv.pages(`"${config.RISK_LIMIT_PATH}"`).array();
    const riskInfo = {};
    if (riskPages.length > 0) {
        const riskPage = riskPages[0];
        if (riskPage.RiskLevels) {
            for (const [level, riskConfig] of Object.entries(riskPage.RiskLevels)) {
                riskInfo[level] = {
                    text: riskConfig.text || `风险 ${level}`,
                    expectApr: riskConfig.expectApr || 0
                };
            }
        }
    }
    
    // 加载等级配置
    const levelPages = dv.pages(`"${config.LEVEL_LIMIT_PATH}"`).array();
    const levelInfo = {};
    if (levelPages.length > 0) {
        const levelPage = levelPages[0];
        if (levelPage.InvestLevels) {
            for (const [level, levelConfig] of Object.entries(levelPage.InvestLevels)) {
                levelInfo[level] = {
                    text: levelConfig.text || `仓位 ${level}`,
                    limit: levelConfig.limit || 0
                };
            }
        }
    }

    console.log('风险配置:', Object.keys(riskInfo));
    console.log('等级配置:', Object.keys(levelInfo));

    // ===== 渲染 Dashboard =====
    if (!doingProjects || doingProjects.length === 0) {
        dv.container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #666; border: 1px solid #ddd; border-radius: 8px; background-color: #f8f9fa;">
                <h3 style="margin-bottom: 16px;">📊 Web3 Farm Dashboard</h3>
                <p style="margin-bottom: 8px;">当前没有正在进行中的项目</p>
                <p style="font-size: 14px; color: #999;">请添加状态为 "Doing" 的项目到 ${config.HISTORY_PATH} 路径下</p>
                <hr style="margin: 20px 0;">
                <p style="font-size: 12px; color: #999;">
                    总项目数: ${allProjects.length}<br>
                    风险配置: ${Object.keys(riskInfo).length} 个等级<br>
                    仓位配置: ${Object.keys(levelInfo).length} 个等级
                </p>
            </div>
        `;
        return;
    }

    // 按风险等级分组
    const projectsByRisk = {};
    for (const project of doingProjects) {
        const riskLevel = project.Risk || 0;
        if (!projectsByRisk[riskLevel]) {
            projectsByRisk[riskLevel] = [];
        }
        projectsByRisk[riskLevel].push(project);
    }

    // 构建 HTML
    let html = `
        <div style="padding: 20px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding-bottom: 10px; border-bottom: 2px solid #007acc;">
                <h2 style="margin: 0; color: #007acc;">🚀 Web3 Farm Dashboard</h2>
                <div style="font-size: 14px; color: #666;">
                    活跃项目: ${doingProjects.length} / ${allProjects.length}
                </div>
            </div>
    `;

    // 按风险等级排序
    const sortedRiskLevels = Object.keys(projectsByRisk).sort((a, b) => parseInt(a) - parseInt(b));

    for (const riskLevel of sortedRiskLevels) {
        const projects = projectsByRisk[riskLevel];
        const riskText = riskInfo[riskLevel]?.text || `风险等级 ${riskLevel}`;
        const expectApr = riskInfo[riskLevel]?.expectApr || 0;

        // 风险等级颜色
        let riskColor = '#28a745'; // 默认绿色
        if (parseInt(riskLevel) >= 3) {
            riskColor = '#dc3545'; // 红色：高风险
        } else if (parseInt(riskLevel) === 2) {
            riskColor = '#ffc107'; // 黄色：中风险
        }

        html += `
            <div style="margin-bottom: 30px; border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
                <div style="background-color: ${riskColor}; color: white; padding: 12px 16px; font-weight: bold;">
                    ${riskText} (${projects.length} 个项目)
                    ${expectApr > 0 ? ` - 期望APR: ${utils.formatPercentage(expectApr * 100, 1)}` : ''}
                </div>
                
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background-color: #f8f9fa;">
                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">项目</th>
                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">协议</th>
                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">类型</th>
                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">等级</th>
                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">单位</th>
                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">状态</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        // 按项目名称排序
        projects.sort((a, b) => {
            const nameA = a.file.name || '';
            const nameB = b.file.name || '';
            return nameA.localeCompare(nameB);
        });

        for (const project of projects) {
            const protocol = project.Protocol || '-';
            const type = project.Type || '-';
            const level = project.Level || 0;
            const levelText = levelInfo[level]?.text || `等级 ${level}`;
            const unit = project.Unit || '-';
            const status = project.Status || 'Doing';

            html += `
                <tr style="border-bottom: 1px solid #eee;">
                    <td style="border: 1px solid #ddd; padding: 8px;">${project.file.link}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">${protocol}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">${type}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">${levelText}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">${unit}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">
                        <span style="color: #28a745;">🟢 ${status}</span>
                    </td>
                </tr>
            `;
        }

        html += `
                    </tbody>
                </table>
            </div>
        `;
    }

    html += `
            <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 6px; border-left: 4px solid #007acc;">
                <h4 style="margin: 0 0 10px 0; color: #007acc;">📈 统计信息</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; font-size: 14px;">
                    <div><strong>总项目数:</strong> ${allProjects.length}</div>
                    <div><strong>活跃项目:</strong> ${doingProjects.length}</div>
                    <div><strong>风险等级:</strong> ${sortedRiskLevels.length} 种</div>
                    <div><strong>最后更新:</strong> ${new Date().toLocaleString()}</div>
                </div>
            </div>
            
            <div style="margin-top: 15px; padding: 10px; background-color: #e9ecef; border-radius: 6px; font-size: 12px; color: #666;">
                <strong>说明：</strong> 这是一个简化版本的 Dashboard，显示基本的项目信息和分组。
                完整功能包括 APR 计算、收益分析、日期维度视图等。
            </div>
        </div>
    `;

    dv.container.innerHTML = html;
    console.log('Dashboard 渲染完成');

} catch (error) {
    console.error('Dashboard 渲染失败:', error);
    
    dv.container.innerHTML = `
        <div style="color: red; padding: 20px; border: 1px solid red; border-radius: 6px; margin: 20px;">
            <h3>❌ Dashboard 渲染失败</h3>
            <p><strong>错误信息:</strong> ${error.message}</p>
            <p><strong>错误详情:</strong> ${error.stack || '无详细信息'}</p>
            <hr>
            <p><strong>调试信息:</strong></p>
            <ul>
                <li>当前文件: ${dv.current().file.path}</li>
                <li>DataviewJS 版本: ${dv.version || '未知'}</li>
                <li>错误类型: ${error.constructor.name}</li>
            </ul>
            <hr>
            <p><strong>可能的解决方案:</strong></p>
            <ul>
                <li>检查数据文件路径是否正确</li>
                <li>确认 Dataview 插件已启用</li>
                <li>查看浏览器开发者工具的控制台</li>
                <li>尝试刷新页面</li>
            </ul>
        </div>
    `;
}
```

# 简单的 dv.view() 测试

```dataviewjs
console.log('开始简单的 dv.view() 测试...');

try {
    await dv.view("web3/farm/lib/test-simple", { message: "Hello from test!" });
} catch (error) {
    console.error('dv.view() 测试失败:', error);
    dv.container.innerHTML = `
        <div style="color: red; padding: 20px; border: 1px solid red; border-radius: 6px;">
            <h3>❌ dv.view() 测试失败</h3>
            <p><strong>错误信息:</strong> ${error.message}</p>
            <p><strong>尝试的路径:</strong> web3/farm/lib/test-simple</p>
            <p><strong>完整错误:</strong> ${error.stack}</p>
        </div>
    `;
}
```

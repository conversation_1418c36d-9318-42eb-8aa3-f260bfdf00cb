
"smart_sources:web3/farm/功能对比报告.md": {"path":"web3/farm/功能对比报告.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04848995,-0.05266986,0.05462081,0.00884978,0.04610517,0.00482263,-0.10526808,-0.02829394,0.05733017,-0.0067012,0.03296858,-0.11151855,0.0303443,0.03605025,0.02662158,0.03236873,-0.02711816,0.02498662,-0.00694156,-0.05777469,0.07360522,-0.00212673,-0.01418974,-0.04174942,0.03315922,0.05195241,-0.02355228,-0.01050503,-0.00960769,-0.13322023,0.01745894,-0.05758052,0.03302458,0.01888932,0.01920673,-0.08770315,0.01528222,0.07797318,-0.015462,0.02091177,-0.00335051,0.01642158,-0.0272175,-0.03139425,-0.02318368,-0.03155431,-0.02287767,0.01655973,0.02081022,-0.03979804,-0.02924425,-0.06514982,0.02292495,-0.02388872,0.03295597,0.04502363,0.01878567,0.09236333,-0.01875027,-0.00598866,0.08133231,0.04368541,-0.17611854,0.071228,0.06734259,-0.02876593,-0.0202748,-0.03245658,0.06235679,0.06141386,-0.07692705,-0.00334082,-0.01266721,0.04156196,0.01090293,-0.02845051,0.00752865,0.00556205,-0.02122293,-0.06066535,-0.04761587,0.06221882,-0.052471,0.04249658,-0.0080171,0.02254217,-0.0504976,-0.00819387,-0.0055453,0.02882235,-0.02501416,-0.02467872,-0.02251397,0.04726263,-0.08020464,-0.0008413,0.00933118,-0.02109838,-0.04199833,0.10910337,-0.06377987,0.01832039,0.00910632,-0.04017969,0.01211639,-0.00729227,-0.01685861,-0.03780969,-0.06208726,0.02289838,-0.05454612,0.02735028,-0.01069633,-0.06322639,-0.00197062,-0.01430862,-0.01031721,-0.02985385,-0.01537476,0.00700242,-0.00446066,0.06716685,0.07511341,-0.03287442,0.05543023,0.00246411,0.06474613,0.06385375,0.02618991,0.05030191,0.03190973,0.01116895,-0.0750231,0.00248709,-0.01089794,-0.01312836,0.00875212,0.00929539,-0.02437853,0.02226313,-0.02568298,-0.05470203,-0.01343041,-0.10102668,-0.03882594,0.08078226,-0.0488583,0.01758758,-0.04574629,-0.05220347,-0.0236268,0.09303481,0.01719497,-0.00609775,-0.00854342,0.04019179,-0.0120919,0.11408616,-0.02448461,-0.03702667,-0.03944726,-0.04970014,-0.0461063,0.07101521,0.03781481,-0.12814867,-0.01173872,0.04028203,0.01416218,-0.0009632,-0.00712019,0.04864622,-0.05472956,0.01918699,0.09750719,-0.04107407,-0.01202332,0.03198687,0.02547561,0.01987205,0.04011032,0.00739778,-0.0483684,0.05173425,-0.0423145,-0.0090224,0.00601934,-0.01075612,0.03743123,-0.02934668,-0.08542123,0.01401825,-0.05480909,0.01093283,0.00785671,0.0357076,0.03278147,-0.03409459,0.09077587,0.00918845,0.15987386,0.04668039,0.00502749,0.00423257,-0.01491178,-0.01195695,-0.00958375,-0.02091708,0.01612739,0.01163591,-0.03101308,0.03643872,0.00382763,0.04918765,-0.02249969,-0.00696107,0.02205733,0.01377088,-0.05284813,0.04703162,0.01018252,0.03005214,-0.03882708,-0.23148859,0.01173067,0.00231318,-0.02893266,-0.08155521,-0.00613695,-0.01303941,0.03015211,0.04840314,0.05781789,0.1255164,0.07757543,-0.02510274,0.00213391,-0.06987184,0.0308526,-0.0013737,-0.00079556,-0.03750417,-0.01261937,-0.00033767,0.03473947,0.06771165,-0.0372489,0.06195999,-0.05269044,0.13732086,0.00157856,0.03734299,-0.01121938,0.10267322,-0.00318539,-0.03611756,-0.11470991,-0.00658425,0.05251596,-0.04501305,-0.01516582,-0.09086634,-0.04665801,0.00113591,0.01378561,-0.00466525,-0.08710409,0.02974769,-0.03278669,-0.02493206,-0.031234,0.00683314,0.01781227,0.02361384,0.04515833,0.02407174,0.10166515,-0.00286746,-0.00260204,0.01617259,0.0673277,-0.03596982,-0.00662177,0.01997893,-0.00369934,-0.01427501,0.00014323,-0.02182965,-0.02246674,0.00706487,-0.04073174,-0.02640579,0.02095192,-0.04003728,0.11365214,-0.02530278,-0.00171255,0.05292134,-0.02266188,-0.04853794,-0.07833186,0.01433323,-0.03418634,-0.04292907,-0.00736383,0.09505247,0.05725541,0.04482132,0.07213481,0.00304698,-0.0497236,0.0074982,0.01280553,-0.00832828,-0.05870252,-0.08621959,-0.06340536,0.10421436,-0.01592325,-0.28523514,0.00652889,-0.00485479,-0.0101256,0.04287572,-0.00538545,0.00442703,-0.01031769,-0.05918378,-0.00109402,-0.00161851,0.08027159,0.00110561,-0.04703758,0.05655168,0.03491204,0.05436151,0.05008587,0.02362213,0.02766922,-0.01265263,0.03671275,0.18891837,0.02848502,0.0481241,0.02602776,0.02838209,0.00896438,0.07378918,0.05098779,-0.03548073,-0.02608595,0.11111257,0.0069888,0.05880119,0.04293253,0.00622212,-0.04034649,-0.02508191,-0.02623404,-0.06144794,0.03152903,-0.05631427,0.06559111,0.04165137,-0.02599304,-0.02634719,-0.11740321,-0.02417838,0.03180347,-0.02563839,-0.10467825,-0.03788688,-0.00243589,0.00408081,0.0668067,0.03717603,-0.05704308,-0.03775832,0.0240606,0.02906794,0.0106826,0.0011617,0.07415756,0.01815778],"last_embed":{"hash":"nzo6qa","tokens":468}}},"last_read":{"hash":"nzo6qa","at":1752191930261},"class_name":"SmartSource","last_import":{"mtime":1752191872000,"size":3990,"at":1752191930097,"hash":"nzo6qa"},"blocks":{"#Dashboard 功能对比报告":[1,108],"#Dashboard 功能对比报告#📋 执行总结":[3,6],"#Dashboard 功能对比报告#📋 执行总结#{1}":[5,6],"#Dashboard 功能对比报告#❌ 缺失的核心功能":[7,33],"#Dashboard 功能对比报告#❌ 缺失的核心功能#1. **日期维度视图系统**":[9,13],"#Dashboard 功能对比报告#❌ 缺失的核心功能#1. **日期维度视图系统**#{1}":[10,10],"#Dashboard 功能对比报告#❌ 缺失的核心功能#1. **日期维度视图系统**#{2}":[11,11],"#Dashboard 功能对比报告#❌ 缺失的核心功能#1. **日期维度视图系统**#{3}":[12,13],"#Dashboard 功能对比报告#❌ 缺失的核心功能#2. **完整的 API 架构集成**":[14,18],"#Dashboard 功能对比报告#❌ 缺失的核心功能#2. **完整的 API 架构集成**#{1}":[15,15],"#Dashboard 功能对比报告#❌ 缺失的核心功能#2. **完整的 API 架构集成**#{2}":[16,16],"#Dashboard 功能对比报告#❌ 缺失的核心功能#2. **完整的 API 架构集成**#{3}":[17,18],"#Dashboard 功能对比报告#❌ 缺失的核心功能#3. **交互式视图切换**":[19,23],"#Dashboard 功能对比报告#❌ 缺失的核心功能#3. **交互式视图切换**#{1}":[20,20],"#Dashboard 功能对比报告#❌ 缺失的核心功能#3. **交互式视图切换**#{2}":[21,21],"#Dashboard 功能对比报告#❌ 缺失的核心功能#3. **交互式视图切换**#{3}":[22,23],"#Dashboard 功能对比报告#❌ 缺失的核心功能#4. **事件绑定和交互**":[24,28],"#Dashboard 功能对比报告#❌ 缺失的核心功能#4. **事件绑定和交互**#{1}":[25,25],"#Dashboard 功能对比报告#❌ 缺失的核心功能#4. **事件绑定和交互**#{2}":[26,26],"#Dashboard 功能对比报告#❌ 缺失的核心功能#4. **事件绑定和交互**#{3}":[27,28],"#Dashboard 功能对比报告#❌ 缺失的核心功能#5. **完整的表格功能**":[29,33],"#Dashboard 功能对比报告#❌ 缺失的核心功能#5. **完整的表格功能**#{1}":[30,30],"#Dashboard 功能对比报告#❌ 缺失的核心功能#5. **完整的表格功能**#{2}":[31,31],"#Dashboard 功能对比报告#❌ 缺失的核心功能#5. **完整的表格功能**#{3}":[32,33],"#Dashboard 功能对比报告#✅ 已实现的功能":[34,50],"#Dashboard 功能对比报告#✅ 已实现的功能#1. **模块化架构**":[36,40],"#Dashboard 功能对比报告#✅ 已实现的功能#1. **模块化架构**#{1}":[37,37],"#Dashboard 功能对比报告#✅ 已实现的功能#1. **模块化架构**#{2}":[38,38],"#Dashboard 功能对比报告#✅ 已实现的功能#1. **模块化架构**#{3}":[39,40],"#Dashboard 功能对比报告#✅ 已实现的功能#2. **基础数据处理**":[41,45],"#Dashboard 功能对比报告#✅ 已实现的功能#2. **基础数据处理**#{1}":[42,42],"#Dashboard 功能对比报告#✅ 已实现的功能#2. **基础数据处理**#{2}":[43,43],"#Dashboard 功能对比报告#✅ 已实现的功能#2. **基础数据处理**#{3}":[44,45],"#Dashboard 功能对比报告#✅ 已实现的功能#3. **风险维度视图**":[46,50],"#Dashboard 功能对比报告#✅ 已实现的功能#3. **风险维度视图**#{1}":[47,47],"#Dashboard 功能对比报告#✅ 已实现的功能#3. **风险维度视图**#{2}":[48,48],"#Dashboard 功能对比报告#✅ 已实现的功能#3. **风险维度视图**#{3}":[49,50],"#Dashboard 功能对比报告#🔧 需要修复的问题":[51,71],"#Dashboard 功能对比报告#🔧 需要修复的问题#1. **集成 API 系统**":[53,59],"#Dashboard 功能对比报告#🔧 需要修复的问题#1. **集成 API 系统**#{1}":[54,59],"#Dashboard 功能对比报告#🔧 需要修复的问题#2. **启用日期维度视图**":[60,63],"#Dashboard 功能对比报告#🔧 需要修复的问题#2. **启用日期维度视图**#{1}":[61,61],"#Dashboard 功能对比报告#🔧 需要修复的问题#2. **启用日期维度视图**#{2}":[62,63],"#Dashboard 功能对比报告#🔧 需要修复的问题#3. **完善视图切换器**":[64,67],"#Dashboard 功能对比报告#🔧 需要修复的问题#3. **完善视图切换器**#{1}":[65,65],"#Dashboard 功能对比报告#🔧 需要修复的问题#3. **完善视图切换器**#{2}":[66,67],"#Dashboard 功能对比报告#🔧 需要修复的问题#4. **统一渲染流程**":[68,71],"#Dashboard 功能对比报告#🔧 需要修复的问题#4. **统一渲染流程**#{1}":[69,69],"#Dashboard 功能对比报告#🔧 需要修复的问题#4. **统一渲染流程**#{2}":[70,71],"#Dashboard 功能对比报告#📊 功能完整度评估":[72,86],"#Dashboard 功能对比报告#📊 功能完整度评估#{1}":[74,86],"#Dashboard 功能对比报告#🚀 修复建议":[87,102],"#Dashboard 功能对比报告#🚀 修复建议#优先级 1 (高)":[89,93],"#Dashboard 功能对比报告#🚀 修复建议#优先级 1 (高)#{1}":[90,90],"#Dashboard 功能对比报告#🚀 修复建议#优先级 1 (高)#{2}":[91,91],"#Dashboard 功能对比报告#🚀 修复建议#优先级 1 (高)#{3}":[92,93],"#Dashboard 功能对比报告#🚀 修复建议#优先级 2 (中)":[94,98],"#Dashboard 功能对比报告#🚀 修复建议#优先级 2 (中)#{1}":[95,95],"#Dashboard 功能对比报告#🚀 修复建议#优先级 2 (中)#{2}":[96,96],"#Dashboard 功能对比报告#🚀 修复建议#优先级 2 (中)#{3}":[97,98],"#Dashboard 功能对比报告#🚀 修复建议#优先级 3 (低)":[99,102],"#Dashboard 功能对比报告#🚀 修复建议#优先级 3 (低)#{1}":[100,100],"#Dashboard 功能对比报告#🚀 修复建议#优先级 3 (低)#{2}":[101,102],"#Dashboard 功能对比报告#📝 结论":[103,108],"#Dashboard 功能对比报告#📝 结论#{1}":[105,108]},"outlinks":[],"last_embed":{"hash":"nzo6qa","at":1752191930101}},
"smart_sources:web3/farm/功能对比报告.md": {"path":"web3/farm/功能对比报告.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04910068,-0.05247097,0.05523479,0.00770858,0.04586309,0.00575984,-0.10487555,-0.02787258,0.05701688,-0.00686746,0.03303566,-0.11119978,0.03072327,0.03617165,0.02633194,0.03198998,-0.02695643,0.02423098,-0.00618181,-0.05703535,0.07279614,-0.00283464,-0.01390295,-0.0412072,0.03368032,0.05214585,-0.02419741,-0.01141931,-0.00991975,-0.13381216,0.01775809,-0.05681217,0.03263021,0.0195291,0.01959559,-0.0878182,0.01498295,0.07743115,-0.01520193,0.02055961,-0.00306351,0.01598816,-0.02690625,-0.0312558,-0.02281703,-0.03120677,-0.02307649,0.01691042,0.02028885,-0.0395252,-0.02912116,-0.06485613,0.02278324,-0.02339951,0.03306697,0.04483237,0.01840115,0.09223843,-0.01867714,-0.00602361,0.08096112,0.0440506,-0.17675455,0.07190517,0.06697521,-0.02909807,-0.02026454,-0.03201907,0.06289501,0.06140582,-0.07719166,-0.00371855,-0.01272434,0.0417666,0.01083996,-0.02830547,0.00730816,0.00535913,-0.02159538,-0.06093825,-0.0476031,0.06261062,-0.0521302,0.04257757,-0.0077654,0.02311342,-0.04992334,-0.00873122,-0.00562415,0.02932935,-0.02496365,-0.02472857,-0.02251022,0.04696771,-0.08061826,-0.00089553,0.00948193,-0.02096406,-0.04232712,0.10902024,-0.06363718,0.01867565,0.00885603,-0.04007379,0.01241807,-0.00768419,-0.01643505,-0.03771656,-0.06275911,0.02324915,-0.05434298,0.02697637,-0.0109287,-0.0634853,-0.00142285,-0.01455004,-0.01003505,-0.02959523,-0.01545951,0.00668907,-0.00440823,0.06705444,0.07541692,-0.03292707,0.05489251,0.00247013,0.06492572,0.06330717,0.0268614,0.05031449,0.03260037,0.0111353,-0.07448611,0.00319007,-0.010322,-0.01348939,0.0088664,0.00950424,-0.02401765,0.02178322,-0.02599069,-0.05510334,-0.01383951,-0.10078884,-0.03941303,0.0817108,-0.04881545,0.017237,-0.04660914,-0.05216751,-0.02328769,0.09261557,0.01715569,-0.00614227,-0.00888701,0.04051781,-0.01203845,0.11476031,-0.02509662,-0.03712544,-0.03887639,-0.05021343,-0.0466672,0.0714747,0.03758454,-0.1282239,-0.01232048,0.04023729,0.01422456,-0.0009935,-0.00682764,0.04753366,-0.05386334,0.01927176,0.09821188,-0.04040794,-0.01128841,0.03183271,0.0245949,0.01924271,0.03986959,0.00700335,-0.04876991,0.05176646,-0.04270115,-0.00921162,0.00595334,-0.0113507,0.03803673,-0.02980739,-0.08596957,0.01323137,-0.05414088,0.01063398,0.00778494,0.03577511,0.03259762,-0.03419958,0.09018079,0.0091124,0.15972355,0.04677441,0.00440325,0.00317575,-0.01413354,-0.01260973,-0.01044737,-0.02050628,0.01656732,0.01230196,-0.03058077,0.03727446,0.00385514,0.04833842,-0.02211817,-0.00677715,0.02197793,0.01438016,-0.05244152,0.04699612,0.01021701,0.03058687,-0.03865754,-0.23180442,0.01132869,0.00153782,-0.0286965,-0.08147741,-0.0063646,-0.01319776,0.03018565,0.04816522,0.05726016,0.12565516,0.07811537,-0.02626022,0.00267175,-0.06959797,0.03106419,-0.00106234,-0.00091965,-0.03742298,-0.01310272,-0.00025528,0.03503281,0.06719371,-0.03726716,0.06142468,-0.05270888,0.13725959,0.00158745,0.03715112,-0.01076088,0.10282794,-0.00219449,-0.0356919,-0.11426369,-0.00724004,0.05299977,-0.04514803,-0.01455554,-0.09090392,-0.04690928,0.0018462,0.01388921,-0.00437664,-0.08740905,0.02960309,-0.03221743,-0.02478018,-0.03057255,0.00670847,0.01796079,0.02369859,0.04545621,0.02391388,0.10177851,-0.00317865,-0.00253706,0.01638268,0.06683555,-0.03544557,-0.00663748,0.0190172,-0.00330078,-0.01357813,0.00058944,-0.02182098,-0.02206076,0.00718642,-0.04055147,-0.02645768,0.0209857,-0.04050459,0.11408221,-0.02590932,-0.00153825,0.05224798,-0.02221999,-0.04841796,-0.07788086,0.01399423,-0.03446367,-0.04310784,-0.00663891,0.09501971,0.05708557,0.04407247,0.07175119,0.00375426,-0.04967384,0.00770853,0.01323289,-0.00741801,-0.05890438,-0.08565841,-0.06381235,0.10399202,-0.01531155,-0.28632599,0.00709776,-0.004452,-0.01037451,0.04273498,-0.00516072,0.0045919,-0.01010688,-0.05962811,-0.00175405,-0.0020404,0.08025213,0.00111073,-0.04704433,0.05594768,0.03492873,0.05511044,0.04997191,0.02327849,0.02721152,-0.01352345,0.03678755,0.18859883,0.02857787,0.04801169,0.02569672,0.02872345,0.0078711,0.07482124,0.05097384,-0.0361957,-0.02653548,0.11062502,0.00735307,0.0582131,0.04287206,0.00578174,-0.03991484,-0.02433649,-0.02643143,-0.06144074,0.03172527,-0.05582283,0.06598818,0.04177505,-0.02565354,-0.02658351,-0.11743892,-0.02410769,0.03185865,-0.02492642,-0.10467657,-0.03735922,-0.00155346,0.00392286,0.06641112,0.03684684,-0.05681356,-0.03761889,0.02379388,0.02912028,0.01105284,0.00127645,0.07359845,0.01918244],"last_embed":{"hash":"omfl3x","tokens":475}}},"last_read":{"hash":"omfl3x","at":1752191990231},"class_name":"SmartSource","last_import":{"mtime":1752191956000,"size":4985,"at":1752191990092,"hash":"omfl3x"},"blocks":{"#Dashboard 功能对比报告":[1,124],"#Dashboard 功能对比报告#📋 执行总结":[3,6],"#Dashboard 功能对比报告#📋 执行总结#{1}":[5,6],"#Dashboard 功能对比报告#❌ 缺失的核心功能":[7,33],"#Dashboard 功能对比报告#❌ 缺失的核心功能#1. **日期维度视图系统**":[9,13],"#Dashboard 功能对比报告#❌ 缺失的核心功能#1. **日期维度视图系统**#{1}":[10,10],"#Dashboard 功能对比报告#❌ 缺失的核心功能#1. **日期维度视图系统**#{2}":[11,11],"#Dashboard 功能对比报告#❌ 缺失的核心功能#1. **日期维度视图系统**#{3}":[12,13],"#Dashboard 功能对比报告#❌ 缺失的核心功能#2. **完整的 API 架构集成**":[14,18],"#Dashboard 功能对比报告#❌ 缺失的核心功能#2. **完整的 API 架构集成**#{1}":[15,15],"#Dashboard 功能对比报告#❌ 缺失的核心功能#2. **完整的 API 架构集成**#{2}":[16,16],"#Dashboard 功能对比报告#❌ 缺失的核心功能#2. **完整的 API 架构集成**#{3}":[17,18],"#Dashboard 功能对比报告#❌ 缺失的核心功能#3. **交互式视图切换**":[19,23],"#Dashboard 功能对比报告#❌ 缺失的核心功能#3. **交互式视图切换**#{1}":[20,20],"#Dashboard 功能对比报告#❌ 缺失的核心功能#3. **交互式视图切换**#{2}":[21,21],"#Dashboard 功能对比报告#❌ 缺失的核心功能#3. **交互式视图切换**#{3}":[22,23],"#Dashboard 功能对比报告#❌ 缺失的核心功能#4. **事件绑定和交互**":[24,28],"#Dashboard 功能对比报告#❌ 缺失的核心功能#4. **事件绑定和交互**#{1}":[25,25],"#Dashboard 功能对比报告#❌ 缺失的核心功能#4. **事件绑定和交互**#{2}":[26,26],"#Dashboard 功能对比报告#❌ 缺失的核心功能#4. **事件绑定和交互**#{3}":[27,28],"#Dashboard 功能对比报告#❌ 缺失的核心功能#5. **完整的表格功能**":[29,33],"#Dashboard 功能对比报告#❌ 缺失的核心功能#5. **完整的表格功能**#{1}":[30,30],"#Dashboard 功能对比报告#❌ 缺失的核心功能#5. **完整的表格功能**#{2}":[31,31],"#Dashboard 功能对比报告#❌ 缺失的核心功能#5. **完整的表格功能**#{3}":[32,33],"#Dashboard 功能对比报告#✅ 已实现的功能":[34,50],"#Dashboard 功能对比报告#✅ 已实现的功能#1. **模块化架构**":[36,40],"#Dashboard 功能对比报告#✅ 已实现的功能#1. **模块化架构**#{1}":[37,37],"#Dashboard 功能对比报告#✅ 已实现的功能#1. **模块化架构**#{2}":[38,38],"#Dashboard 功能对比报告#✅ 已实现的功能#1. **模块化架构**#{3}":[39,40],"#Dashboard 功能对比报告#✅ 已实现的功能#2. **基础数据处理**":[41,45],"#Dashboard 功能对比报告#✅ 已实现的功能#2. **基础数据处理**#{1}":[42,42],"#Dashboard 功能对比报告#✅ 已实现的功能#2. **基础数据处理**#{2}":[43,43],"#Dashboard 功能对比报告#✅ 已实现的功能#2. **基础数据处理**#{3}":[44,45],"#Dashboard 功能对比报告#✅ 已实现的功能#3. **风险维度视图**":[46,50],"#Dashboard 功能对比报告#✅ 已实现的功能#3. **风险维度视图**#{1}":[47,47],"#Dashboard 功能对比报告#✅ 已实现的功能#3. **风险维度视图**#{2}":[48,48],"#Dashboard 功能对比报告#✅ 已实现的功能#3. **风险维度视图**#{3}":[49,50],"#Dashboard 功能对比报告#🔧 需要修复的问题":[51,71],"#Dashboard 功能对比报告#🔧 需要修复的问题#1. **集成 API 系统**":[53,59],"#Dashboard 功能对比报告#🔧 需要修复的问题#1. **集成 API 系统**#{1}":[54,59],"#Dashboard 功能对比报告#🔧 需要修复的问题#2. **启用日期维度视图**":[60,63],"#Dashboard 功能对比报告#🔧 需要修复的问题#2. **启用日期维度视图**#{1}":[61,61],"#Dashboard 功能对比报告#🔧 需要修复的问题#2. **启用日期维度视图**#{2}":[62,63],"#Dashboard 功能对比报告#🔧 需要修复的问题#3. **完善视图切换器**":[64,67],"#Dashboard 功能对比报告#🔧 需要修复的问题#3. **完善视图切换器**#{1}":[65,65],"#Dashboard 功能对比报告#🔧 需要修复的问题#3. **完善视图切换器**#{2}":[66,67],"#Dashboard 功能对比报告#🔧 需要修复的问题#4. **统一渲染流程**":[68,71],"#Dashboard 功能对比报告#🔧 需要修复的问题#4. **统一渲染流程**#{1}":[69,69],"#Dashboard 功能对比报告#🔧 需要修复的问题#4. **统一渲染流程**#{2}":[70,71],"#Dashboard 功能对比报告#📊 功能完整度评估 (更新后)":[72,86],"#Dashboard 功能对比报告#📊 功能完整度评估 (更新后)#{1}":[74,86],"#Dashboard 功能对比报告#🚀 修复建议":[87,102],"#Dashboard 功能对比报告#🚀 修复建议#优先级 1 (高)":[89,93],"#Dashboard 功能对比报告#🚀 修复建议#优先级 1 (高)#{1}":[90,90],"#Dashboard 功能对比报告#🚀 修复建议#优先级 1 (高)#{2}":[91,91],"#Dashboard 功能对比报告#🚀 修复建议#优先级 1 (高)#{3}":[92,93],"#Dashboard 功能对比报告#🚀 修复建议#优先级 2 (中)":[94,98],"#Dashboard 功能对比报告#🚀 修复建议#优先级 2 (中)#{1}":[95,95],"#Dashboard 功能对比报告#🚀 修复建议#优先级 2 (中)#{2}":[96,96],"#Dashboard 功能对比报告#🚀 修复建议#优先级 2 (中)#{3}":[97,98],"#Dashboard 功能对比报告#🚀 修复建议#优先级 3 (低)":[99,102],"#Dashboard 功能对比报告#🚀 修复建议#优先级 3 (低)#{1}":[100,100],"#Dashboard 功能对比报告#🚀 修复建议#优先级 3 (低)#{2}":[101,102],"#Dashboard 功能对比报告#📝 结论 (更新)":[103,124],"#Dashboard 功能对比报告#📝 结论 (更新)#{1}":[105,106],"#Dashboard 功能对比报告#📝 结论 (更新)#✅ **已完成的修复**":[107,112],"#Dashboard 功能对比报告#📝 结论 (更新)#✅ **已完成的修复**#{1}":[108,108],"#Dashboard 功能对比报告#📝 结论 (更新)#✅ **已完成的修复**#{2}":[109,109],"#Dashboard 功能对比报告#📝 结论 (更新)#✅ **已完成的修复**#{3}":[110,110],"#Dashboard 功能对比报告#📝 结论 (更新)#✅ **已完成的修复**#{4}":[111,112],"#Dashboard 功能对比报告#📝 结论 (更新)#⚠️ **仍需完成的工作**":[113,117],"#Dashboard 功能对比报告#📝 结论 (更新)#⚠️ **仍需完成的工作**#{1}":[114,114],"#Dashboard 功能对比报告#📝 结论 (更新)#⚠️ **仍需完成的工作**#{2}":[115,115],"#Dashboard 功能对比报告#📝 结论 (更新)#⚠️ **仍需完成的工作**#{3}":[116,117],"#Dashboard 功能对比报告#📝 结论 (更新)#🎯 **当前状态**":[118,124],"#Dashboard 功能对比报告#📝 结论 (更新)#🎯 **当前状态**#{1}":[119,119],"#Dashboard 功能对比报告#📝 结论 (更新)#🎯 **当前状态**#{2}":[120,120],"#Dashboard 功能对比报告#📝 结论 (更新)#🎯 **当前状态**#{3}":[121,122],"#Dashboard 功能对比报告#📝 结论 (更新)#🎯 **当前状态**#{4}":[123,124]},"outlinks":[],"last_embed":{"hash":"omfl3x","at":1752191990093}},
"smart_sources:web3/farm/功能对比报告.md": {"path":"web3/farm/功能对比报告.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05936937,-0.05619617,0.05172905,0.00699012,0.04938425,-0.00571143,-0.10054689,-0.04010342,0.04952295,0.00431218,0.03534487,-0.11480529,0.03121775,0.03571681,0.02414968,0.02347404,-0.02746124,0.02024478,-0.00336872,-0.0560155,0.06611621,-0.00012798,-0.02281938,-0.03879661,0.02635491,0.06230348,-0.02133398,-0.01265348,-0.01329727,-0.13033572,0.01758501,-0.05708577,0.03621245,0.0174252,0.02445217,-0.08689941,0.02257481,0.08442115,-0.0215383,0.02512106,0.00759316,0.01079736,-0.01808447,-0.03337806,-0.02983272,-0.02942572,-0.02442573,0.02101575,0.00890406,-0.03200127,-0.02262226,-0.06706761,0.02286748,-0.01777362,0.02887749,0.04574642,0.01575819,0.08463937,-0.01653357,-0.00564959,0.07001602,0.03636654,-0.17260027,0.06665152,0.06260438,-0.03647397,-0.01481366,-0.03419332,0.05595035,0.06039952,-0.07271862,-0.00278536,-0.01880632,0.04296152,0.00781181,-0.03215621,0.01522409,0.00804915,-0.02166492,-0.05707275,-0.04209823,0.0732192,-0.05269918,0.04153064,-0.00823349,0.01861381,-0.0467361,-0.0089535,-0.01105489,0.028646,-0.02185776,-0.02563737,-0.01926867,0.05184042,-0.06988443,0.0045783,0.01898687,-0.00760915,-0.03665283,0.10973024,-0.06520598,0.02380088,0.00146248,-0.04873932,0.02138804,-0.01239751,-0.01864447,-0.04212593,-0.06196609,0.02405063,-0.05773015,0.03815672,-0.00152784,-0.07113656,0.00600468,-0.02132344,-0.01355802,-0.02635336,-0.00794852,0.01655719,0.00036909,0.06506746,0.07977453,-0.02807456,0.04706939,0.00902982,0.06327308,0.05726942,0.0124151,0.05039839,0.04688333,0.00614652,-0.07911455,0.00538418,-0.01204655,-0.012924,0.0027266,0.00160467,-0.01866444,0.01659259,-0.03480506,-0.06139744,-0.01732982,-0.09601724,-0.04433648,0.08796075,-0.04852707,0.01329743,-0.03482017,-0.05539859,-0.03237123,0.09678684,0.01433493,-0.00975194,-0.00066794,0.04470412,-0.00632442,0.11704896,-0.02041972,-0.02486159,-0.03953988,-0.05205621,-0.04637376,0.07214486,0.03829276,-0.13856533,0.00715348,0.04905398,0.0211795,-0.00136941,-0.01221087,0.04475987,-0.04087124,0.01158196,0.10363471,-0.03918885,-0.00546929,0.033993,0.01346939,0.02241164,0.02347243,0.00422108,-0.05373112,0.05796942,-0.04647047,-0.01567949,0.01510731,-0.01506187,0.03085081,-0.03223078,-0.08020235,0.00148382,-0.05312487,0.01285888,0.0093311,0.03505713,0.02784951,-0.02174313,0.0826304,0.01342437,0.15767071,0.0407924,0.01322747,0.00155482,-0.00679469,-0.01439617,-0.00314432,-0.02878336,0.0042593,0.01244517,-0.02529465,0.0316222,0.00936867,0.04860627,-0.01774651,-0.00160301,0.02039001,0.00760933,-0.05848255,0.0470575,0.00238568,0.04129119,-0.03656844,-0.22716577,0.01749771,0.00053804,-0.02390176,-0.08500034,-0.01070088,-0.00773842,0.03164916,0.0599623,0.06365459,0.12877217,0.0831391,-0.02028876,-0.00194115,-0.06078401,0.03722715,0.00139912,0.00186338,-0.03882234,-0.01238071,0.00001964,0.03027835,0.05852756,-0.04421354,0.05654181,-0.0455324,0.13457648,0.00472952,0.0255892,-0.01000985,0.10796914,0.00037147,-0.03684029,-0.11885115,0.00408263,0.0422569,-0.04229571,-0.0225352,-0.09369925,-0.05043655,-0.00012033,0.01260289,0.00792691,-0.07366534,0.03051987,-0.02945396,-0.01490425,-0.02768082,-0.00299165,0.00116502,0.02045099,0.03880023,0.02420806,0.10709137,0.00241925,0.00073537,0.0168062,0.06870195,-0.03389066,-0.0108233,0.01533564,0.00157645,-0.00945029,0.00615339,-0.02800286,-0.0205191,0.0028299,-0.04655455,-0.03628341,0.03017713,-0.04177378,0.12657419,-0.03346516,-0.0071551,0.05223826,-0.03602517,-0.04919844,-0.06792373,0.02178046,-0.03474135,-0.0372791,-0.01048777,0.09600172,0.0589571,0.04551994,0.0636894,0.00239736,-0.05016727,0.00736196,0.0052594,-0.00421989,-0.06264959,-0.08449362,-0.07022759,0.10276219,-0.00778575,-0.28923547,0.0095869,-0.00782152,-0.01495911,0.04563962,-0.00832455,0.00002799,-0.01206715,-0.06417163,0.00271207,-0.01345357,0.09289807,0.00159362,-0.05384653,0.04782832,0.03755815,0.04321679,0.04299229,0.01555119,0.02752845,-0.00643156,0.03433776,0.18901169,0.02838178,0.04704737,0.03350694,0.02932963,0.00864195,0.07642353,0.04600554,-0.03215575,-0.03629903,0.11354779,0.00964548,0.05812311,0.04535159,0.00346831,-0.03633039,-0.02591899,-0.01613797,-0.06663869,0.04427056,-0.04989243,0.0542492,0.03974199,-0.02825607,-0.02569643,-0.11544253,-0.02620167,0.03545884,-0.01933524,-0.10035916,-0.03992175,0.00439321,-0.00298407,0.07169092,0.03865291,-0.0553687,-0.04422658,0.0204544,0.03103138,0.00013611,0.00285595,0.06381214,0.01593904],"last_embed":{"hash":"1hc524v","tokens":475}}},"last_read":{"hash":"1hc524v","at":1752192462230},"class_name":"SmartSource","last_import":{"mtime":1752192443000,"size":5014,"at":1752192462094,"hash":"1hc524v"},"blocks":{"#Dashboard 功能对比报告":[1,124],"#Dashboard 功能对比报告#📋 执行总结":[3,6],"#Dashboard 功能对比报告#📋 执行总结#{1}":[5,6],"#Dashboard 功能对比报告#✅ 已完成的核心功能":[7,33],"#Dashboard 功能对比报告#✅ 已完成的核心功能#1. **日期维度视图系统**":[9,13],"#Dashboard 功能对比报告#✅ 已完成的核心功能#1. **日期维度视图系统**#{1}":[10,10],"#Dashboard 功能对比报告#✅ 已完成的核心功能#1. **日期维度视图系统**#{2}":[11,11],"#Dashboard 功能对比报告#✅ 已完成的核心功能#1. **日期维度视图系统**#{3}":[12,13],"#Dashboard 功能对比报告#✅ 已完成的核心功能#2. **完整的 API 架构集成**":[14,18],"#Dashboard 功能对比报告#✅ 已完成的核心功能#2. **完整的 API 架构集成**#{1}":[15,15],"#Dashboard 功能对比报告#✅ 已完成的核心功能#2. **完整的 API 架构集成**#{2}":[16,16],"#Dashboard 功能对比报告#✅ 已完成的核心功能#2. **完整的 API 架构集成**#{3}":[17,18],"#Dashboard 功能对比报告#✅ 已完成的核心功能#3. **交互式视图切换**":[19,23],"#Dashboard 功能对比报告#✅ 已完成的核心功能#3. **交互式视图切换**#{1}":[20,20],"#Dashboard 功能对比报告#✅ 已完成的核心功能#3. **交互式视图切换**#{2}":[21,21],"#Dashboard 功能对比报告#✅ 已完成的核心功能#3. **交互式视图切换**#{3}":[22,23],"#Dashboard 功能对比报告#✅ 已完成的核心功能#4. **事件绑定和交互**":[24,28],"#Dashboard 功能对比报告#✅ 已完成的核心功能#4. **事件绑定和交互**#{1}":[25,25],"#Dashboard 功能对比报告#✅ 已完成的核心功能#4. **事件绑定和交互**#{2}":[26,26],"#Dashboard 功能对比报告#✅ 已完成的核心功能#4. **事件绑定和交互**#{3}":[27,28],"#Dashboard 功能对比报告#✅ 已完成的核心功能#5. **完整的表格功能**":[29,33],"#Dashboard 功能对比报告#✅ 已完成的核心功能#5. **完整的表格功能**#{1}":[30,30],"#Dashboard 功能对比报告#✅ 已完成的核心功能#5. **完整的表格功能**#{2}":[31,31],"#Dashboard 功能对比报告#✅ 已完成的核心功能#5. **完整的表格功能**#{3}":[32,33],"#Dashboard 功能对比报告#✅ 已实现的功能":[34,50],"#Dashboard 功能对比报告#✅ 已实现的功能#1. **模块化架构**":[36,40],"#Dashboard 功能对比报告#✅ 已实现的功能#1. **模块化架构**#{1}":[37,37],"#Dashboard 功能对比报告#✅ 已实现的功能#1. **模块化架构**#{2}":[38,38],"#Dashboard 功能对比报告#✅ 已实现的功能#1. **模块化架构**#{3}":[39,40],"#Dashboard 功能对比报告#✅ 已实现的功能#2. **基础数据处理**":[41,45],"#Dashboard 功能对比报告#✅ 已实现的功能#2. **基础数据处理**#{1}":[42,42],"#Dashboard 功能对比报告#✅ 已实现的功能#2. **基础数据处理**#{2}":[43,43],"#Dashboard 功能对比报告#✅ 已实现的功能#2. **基础数据处理**#{3}":[44,45],"#Dashboard 功能对比报告#✅ 已实现的功能#3. **风险维度视图**":[46,50],"#Dashboard 功能对比报告#✅ 已实现的功能#3. **风险维度视图**#{1}":[47,47],"#Dashboard 功能对比报告#✅ 已实现的功能#3. **风险维度视图**#{2}":[48,48],"#Dashboard 功能对比报告#✅ 已实现的功能#3. **风险维度视图**#{3}":[49,50],"#Dashboard 功能对比报告#🔧 需要修复的问题":[51,71],"#Dashboard 功能对比报告#🔧 需要修复的问题#1. **集成 API 系统**":[53,59],"#Dashboard 功能对比报告#🔧 需要修复的问题#1. **集成 API 系统**#{1}":[54,59],"#Dashboard 功能对比报告#🔧 需要修复的问题#2. **启用日期维度视图**":[60,63],"#Dashboard 功能对比报告#🔧 需要修复的问题#2. **启用日期维度视图**#{1}":[61,61],"#Dashboard 功能对比报告#🔧 需要修复的问题#2. **启用日期维度视图**#{2}":[62,63],"#Dashboard 功能对比报告#🔧 需要修复的问题#3. **完善视图切换器**":[64,67],"#Dashboard 功能对比报告#🔧 需要修复的问题#3. **完善视图切换器**#{1}":[65,65],"#Dashboard 功能对比报告#🔧 需要修复的问题#3. **完善视图切换器**#{2}":[66,67],"#Dashboard 功能对比报告#🔧 需要修复的问题#4. **统一渲染流程**":[68,71],"#Dashboard 功能对比报告#🔧 需要修复的问题#4. **统一渲染流程**#{1}":[69,69],"#Dashboard 功能对比报告#🔧 需要修复的问题#4. **统一渲染流程**#{2}":[70,71],"#Dashboard 功能对比报告#📊 功能完整度评估 (最终更新)":[72,86],"#Dashboard 功能对比报告#📊 功能完整度评估 (最终更新)#{1}":[74,86],"#Dashboard 功能对比报告#🚀 修复建议":[87,102],"#Dashboard 功能对比报告#🚀 修复建议#优先级 1 (高)":[89,93],"#Dashboard 功能对比报告#🚀 修复建议#优先级 1 (高)#{1}":[90,90],"#Dashboard 功能对比报告#🚀 修复建议#优先级 1 (高)#{2}":[91,91],"#Dashboard 功能对比报告#🚀 修复建议#优先级 1 (高)#{3}":[92,93],"#Dashboard 功能对比报告#🚀 修复建议#优先级 2 (中)":[94,98],"#Dashboard 功能对比报告#🚀 修复建议#优先级 2 (中)#{1}":[95,95],"#Dashboard 功能对比报告#🚀 修复建议#优先级 2 (中)#{2}":[96,96],"#Dashboard 功能对比报告#🚀 修复建议#优先级 2 (中)#{3}":[97,98],"#Dashboard 功能对比报告#🚀 修复建议#优先级 3 (低)":[99,102],"#Dashboard 功能对比报告#🚀 修复建议#优先级 3 (低)#{1}":[100,100],"#Dashboard 功能对比报告#🚀 修复建议#优先级 3 (低)#{2}":[101,102],"#Dashboard 功能对比报告#📝 结论 (更新)":[103,124],"#Dashboard 功能对比报告#📝 结论 (更新)#{1}":[105,106],"#Dashboard 功能对比报告#📝 结论 (更新)#✅ **已完成的修复**":[107,112],"#Dashboard 功能对比报告#📝 结论 (更新)#✅ **已完成的修复**#{1}":[108,108],"#Dashboard 功能对比报告#📝 结论 (更新)#✅ **已完成的修复**#{2}":[109,109],"#Dashboard 功能对比报告#📝 结论 (更新)#✅ **已完成的修复**#{3}":[110,110],"#Dashboard 功能对比报告#📝 结论 (更新)#✅ **已完成的修复**#{4}":[111,112],"#Dashboard 功能对比报告#📝 结论 (更新)#⚠️ **仍需完成的工作**":[113,117],"#Dashboard 功能对比报告#📝 结论 (更新)#⚠️ **仍需完成的工作**#{1}":[114,114],"#Dashboard 功能对比报告#📝 结论 (更新)#⚠️ **仍需完成的工作**#{2}":[115,115],"#Dashboard 功能对比报告#📝 结论 (更新)#⚠️ **仍需完成的工作**#{3}":[116,117],"#Dashboard 功能对比报告#📝 结论 (更新)#🎯 **当前状态**":[118,124],"#Dashboard 功能对比报告#📝 结论 (更新)#🎯 **当前状态**#{1}":[119,119],"#Dashboard 功能对比报告#📝 结论 (更新)#🎯 **当前状态**#{2}":[120,120],"#Dashboard 功能对比报告#📝 结论 (更新)#🎯 **当前状态**#{3}":[121,122],"#Dashboard 功能对比报告#📝 结论 (更新)#🎯 **当前状态**#{4}":[123,124]},"outlinks":[],"last_embed":{"hash":"1hc524v","at":1752192462096}},
"smart_sources:web3/farm/功能对比报告.md": {"path":"web3/farm/功能对比报告.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05936937,-0.05619617,0.05172905,0.00699012,0.04938425,-0.00571143,-0.10054689,-0.04010342,0.04952295,0.00431218,0.03534487,-0.11480529,0.03121775,0.03571681,0.02414968,0.02347404,-0.02746124,0.02024478,-0.00336872,-0.0560155,0.06611621,-0.00012798,-0.02281938,-0.03879661,0.02635491,0.06230348,-0.02133398,-0.01265348,-0.01329727,-0.13033572,0.01758501,-0.05708577,0.03621245,0.0174252,0.02445217,-0.08689941,0.02257481,0.08442115,-0.0215383,0.02512106,0.00759316,0.01079736,-0.01808447,-0.03337806,-0.02983272,-0.02942572,-0.02442573,0.02101575,0.00890406,-0.03200127,-0.02262226,-0.06706761,0.02286748,-0.01777362,0.02887749,0.04574642,0.01575819,0.08463937,-0.01653357,-0.00564959,0.07001602,0.03636654,-0.17260027,0.06665152,0.06260438,-0.03647397,-0.01481366,-0.03419332,0.05595035,0.06039952,-0.07271862,-0.00278536,-0.01880632,0.04296152,0.00781181,-0.03215621,0.01522409,0.00804915,-0.02166492,-0.05707275,-0.04209823,0.0732192,-0.05269918,0.04153064,-0.00823349,0.01861381,-0.0467361,-0.0089535,-0.01105489,0.028646,-0.02185776,-0.02563737,-0.01926867,0.05184042,-0.06988443,0.0045783,0.01898687,-0.00760915,-0.03665283,0.10973024,-0.06520598,0.02380088,0.00146248,-0.04873932,0.02138804,-0.01239751,-0.01864447,-0.04212593,-0.06196609,0.02405063,-0.05773015,0.03815672,-0.00152784,-0.07113656,0.00600468,-0.02132344,-0.01355802,-0.02635336,-0.00794852,0.01655719,0.00036909,0.06506746,0.07977453,-0.02807456,0.04706939,0.00902982,0.06327308,0.05726942,0.0124151,0.05039839,0.04688333,0.00614652,-0.07911455,0.00538418,-0.01204655,-0.012924,0.0027266,0.00160467,-0.01866444,0.01659259,-0.03480506,-0.06139744,-0.01732982,-0.09601724,-0.04433648,0.08796075,-0.04852707,0.01329743,-0.03482017,-0.05539859,-0.03237123,0.09678684,0.01433493,-0.00975194,-0.00066794,0.04470412,-0.00632442,0.11704896,-0.02041972,-0.02486159,-0.03953988,-0.05205621,-0.04637376,0.07214486,0.03829276,-0.13856533,0.00715348,0.04905398,0.0211795,-0.00136941,-0.01221087,0.04475987,-0.04087124,0.01158196,0.10363471,-0.03918885,-0.00546929,0.033993,0.01346939,0.02241164,0.02347243,0.00422108,-0.05373112,0.05796942,-0.04647047,-0.01567949,0.01510731,-0.01506187,0.03085081,-0.03223078,-0.08020235,0.00148382,-0.05312487,0.01285888,0.0093311,0.03505713,0.02784951,-0.02174313,0.0826304,0.01342437,0.15767071,0.0407924,0.01322747,0.00155482,-0.00679469,-0.01439617,-0.00314432,-0.02878336,0.0042593,0.01244517,-0.02529465,0.0316222,0.00936867,0.04860627,-0.01774651,-0.00160301,0.02039001,0.00760933,-0.05848255,0.0470575,0.00238568,0.04129119,-0.03656844,-0.22716577,0.01749771,0.00053804,-0.02390176,-0.08500034,-0.01070088,-0.00773842,0.03164916,0.0599623,0.06365459,0.12877217,0.0831391,-0.02028876,-0.00194115,-0.06078401,0.03722715,0.00139912,0.00186338,-0.03882234,-0.01238071,0.00001964,0.03027835,0.05852756,-0.04421354,0.05654181,-0.0455324,0.13457648,0.00472952,0.0255892,-0.01000985,0.10796914,0.00037147,-0.03684029,-0.11885115,0.00408263,0.0422569,-0.04229571,-0.0225352,-0.09369925,-0.05043655,-0.00012033,0.01260289,0.00792691,-0.07366534,0.03051987,-0.02945396,-0.01490425,-0.02768082,-0.00299165,0.00116502,0.02045099,0.03880023,0.02420806,0.10709137,0.00241925,0.00073537,0.0168062,0.06870195,-0.03389066,-0.0108233,0.01533564,0.00157645,-0.00945029,0.00615339,-0.02800286,-0.0205191,0.0028299,-0.04655455,-0.03628341,0.03017713,-0.04177378,0.12657419,-0.03346516,-0.0071551,0.05223826,-0.03602517,-0.04919844,-0.06792373,0.02178046,-0.03474135,-0.0372791,-0.01048777,0.09600172,0.0589571,0.04551994,0.0636894,0.00239736,-0.05016727,0.00736196,0.0052594,-0.00421989,-0.06264959,-0.08449362,-0.07022759,0.10276219,-0.00778575,-0.28923547,0.0095869,-0.00782152,-0.01495911,0.04563962,-0.00832455,0.00002799,-0.01206715,-0.06417163,0.00271207,-0.01345357,0.09289807,0.00159362,-0.05384653,0.04782832,0.03755815,0.04321679,0.04299229,0.01555119,0.02752845,-0.00643156,0.03433776,0.18901169,0.02838178,0.04704737,0.03350694,0.02932963,0.00864195,0.07642353,0.04600554,-0.03215575,-0.03629903,0.11354779,0.00964548,0.05812311,0.04535159,0.00346831,-0.03633039,-0.02591899,-0.01613797,-0.06663869,0.04427056,-0.04989243,0.0542492,0.03974199,-0.02825607,-0.02569643,-0.11544253,-0.02620167,0.03545884,-0.01933524,-0.10035916,-0.03992175,0.00439321,-0.00298407,0.07169092,0.03865291,-0.0553687,-0.04422658,0.0204544,0.03103138,0.00013611,0.00285595,0.06381214,0.01593904],"last_embed":{"hash":"17wqkdi","tokens":475}}},"last_read":{"hash":"17wqkdi","at":1752192522226},"class_name":"SmartSource","last_import":{"mtime":1752192469000,"size":5556,"at":1752192522088,"hash":"17wqkdi"},"blocks":{"#Dashboard 功能对比报告":[1,133],"#Dashboard 功能对比报告#📋 执行总结":[3,6],"#Dashboard 功能对比报告#📋 执行总结#{1}":[5,6],"#Dashboard 功能对比报告#✅ 已完成的核心功能":[7,33],"#Dashboard 功能对比报告#✅ 已完成的核心功能#1. **日期维度视图系统**":[9,13],"#Dashboard 功能对比报告#✅ 已完成的核心功能#1. **日期维度视图系统**#{1}":[10,10],"#Dashboard 功能对比报告#✅ 已完成的核心功能#1. **日期维度视图系统**#{2}":[11,11],"#Dashboard 功能对比报告#✅ 已完成的核心功能#1. **日期维度视图系统**#{3}":[12,13],"#Dashboard 功能对比报告#✅ 已完成的核心功能#2. **完整的 API 架构集成**":[14,18],"#Dashboard 功能对比报告#✅ 已完成的核心功能#2. **完整的 API 架构集成**#{1}":[15,15],"#Dashboard 功能对比报告#✅ 已完成的核心功能#2. **完整的 API 架构集成**#{2}":[16,16],"#Dashboard 功能对比报告#✅ 已完成的核心功能#2. **完整的 API 架构集成**#{3}":[17,18],"#Dashboard 功能对比报告#✅ 已完成的核心功能#3. **交互式视图切换**":[19,23],"#Dashboard 功能对比报告#✅ 已完成的核心功能#3. **交互式视图切换**#{1}":[20,20],"#Dashboard 功能对比报告#✅ 已完成的核心功能#3. **交互式视图切换**#{2}":[21,21],"#Dashboard 功能对比报告#✅ 已完成的核心功能#3. **交互式视图切换**#{3}":[22,23],"#Dashboard 功能对比报告#✅ 已完成的核心功能#4. **事件绑定和交互**":[24,28],"#Dashboard 功能对比报告#✅ 已完成的核心功能#4. **事件绑定和交互**#{1}":[25,25],"#Dashboard 功能对比报告#✅ 已完成的核心功能#4. **事件绑定和交互**#{2}":[26,26],"#Dashboard 功能对比报告#✅ 已完成的核心功能#4. **事件绑定和交互**#{3}":[27,28],"#Dashboard 功能对比报告#✅ 已完成的核心功能#5. **完整的表格功能**":[29,33],"#Dashboard 功能对比报告#✅ 已完成的核心功能#5. **完整的表格功能**#{1}":[30,30],"#Dashboard 功能对比报告#✅ 已完成的核心功能#5. **完整的表格功能**#{2}":[31,31],"#Dashboard 功能对比报告#✅ 已完成的核心功能#5. **完整的表格功能**#{3}":[32,33],"#Dashboard 功能对比报告#✅ 已实现的功能":[34,50],"#Dashboard 功能对比报告#✅ 已实现的功能#1. **模块化架构**":[36,40],"#Dashboard 功能对比报告#✅ 已实现的功能#1. **模块化架构**#{1}":[37,37],"#Dashboard 功能对比报告#✅ 已实现的功能#1. **模块化架构**#{2}":[38,38],"#Dashboard 功能对比报告#✅ 已实现的功能#1. **模块化架构**#{3}":[39,40],"#Dashboard 功能对比报告#✅ 已实现的功能#2. **基础数据处理**":[41,45],"#Dashboard 功能对比报告#✅ 已实现的功能#2. **基础数据处理**#{1}":[42,42],"#Dashboard 功能对比报告#✅ 已实现的功能#2. **基础数据处理**#{2}":[43,43],"#Dashboard 功能对比报告#✅ 已实现的功能#2. **基础数据处理**#{3}":[44,45],"#Dashboard 功能对比报告#✅ 已实现的功能#3. **风险维度视图**":[46,50],"#Dashboard 功能对比报告#✅ 已实现的功能#3. **风险维度视图**#{1}":[47,47],"#Dashboard 功能对比报告#✅ 已实现的功能#3. **风险维度视图**#{2}":[48,48],"#Dashboard 功能对比报告#✅ 已实现的功能#3. **风险维度视图**#{3}":[49,50],"#Dashboard 功能对比报告#🔧 需要修复的问题":[51,71],"#Dashboard 功能对比报告#🔧 需要修复的问题#1. **集成 API 系统**":[53,59],"#Dashboard 功能对比报告#🔧 需要修复的问题#1. **集成 API 系统**#{1}":[54,59],"#Dashboard 功能对比报告#🔧 需要修复的问题#2. **启用日期维度视图**":[60,63],"#Dashboard 功能对比报告#🔧 需要修复的问题#2. **启用日期维度视图**#{1}":[61,61],"#Dashboard 功能对比报告#🔧 需要修复的问题#2. **启用日期维度视图**#{2}":[62,63],"#Dashboard 功能对比报告#🔧 需要修复的问题#3. **完善视图切换器**":[64,67],"#Dashboard 功能对比报告#🔧 需要修复的问题#3. **完善视图切换器**#{1}":[65,65],"#Dashboard 功能对比报告#🔧 需要修复的问题#3. **完善视图切换器**#{2}":[66,67],"#Dashboard 功能对比报告#🔧 需要修复的问题#4. **统一渲染流程**":[68,71],"#Dashboard 功能对比报告#🔧 需要修复的问题#4. **统一渲染流程**#{1}":[69,69],"#Dashboard 功能对比报告#🔧 需要修复的问题#4. **统一渲染流程**#{2}":[70,71],"#Dashboard 功能对比报告#📊 功能完整度评估 (最终更新)":[72,86],"#Dashboard 功能对比报告#📊 功能完整度评估 (最终更新)#{1}":[74,86],"#Dashboard 功能对比报告#🚀 修复建议":[87,102],"#Dashboard 功能对比报告#🚀 修复建议#优先级 1 (高)":[89,93],"#Dashboard 功能对比报告#🚀 修复建议#优先级 1 (高)#{1}":[90,90],"#Dashboard 功能对比报告#🚀 修复建议#优先级 1 (高)#{2}":[91,91],"#Dashboard 功能对比报告#🚀 修复建议#优先级 1 (高)#{3}":[92,93],"#Dashboard 功能对比报告#🚀 修复建议#优先级 2 (中)":[94,98],"#Dashboard 功能对比报告#🚀 修复建议#优先级 2 (中)#{1}":[95,95],"#Dashboard 功能对比报告#🚀 修复建议#优先级 2 (中)#{2}":[96,96],"#Dashboard 功能对比报告#🚀 修复建议#优先级 2 (中)#{3}":[97,98],"#Dashboard 功能对比报告#🚀 修复建议#优先级 3 (低)":[99,102],"#Dashboard 功能对比报告#🚀 修复建议#优先级 3 (低)#{1}":[100,100],"#Dashboard 功能对比报告#🚀 修复建议#优先级 3 (低)#{2}":[101,102],"#Dashboard 功能对比报告#📝 结论 (最终完成)":[103,133],"#Dashboard 功能对比报告#📝 结论 (最终完成)#{1}":[105,106],"#Dashboard 功能对比报告#📝 结论 (最终完成)#✅ **完全实现的功能**":[107,115],"#Dashboard 功能对比报告#📝 结论 (最终完成)#✅ **完全实现的功能**#{1}":[108,108],"#Dashboard 功能对比报告#📝 结论 (最终完成)#✅ **完全实现的功能**#{2}":[109,109],"#Dashboard 功能对比报告#📝 结论 (最终完成)#✅ **完全实现的功能**#{3}":[110,110],"#Dashboard 功能对比报告#📝 结论 (最终完成)#✅ **完全实现的功能**#{4}":[111,111],"#Dashboard 功能对比报告#📝 结论 (最终完成)#✅ **完全实现的功能**#{5}":[112,112],"#Dashboard 功能对比报告#📝 结论 (最终完成)#✅ **完全实现的功能**#{6}":[113,113],"#Dashboard 功能对比报告#📝 结论 (最终完成)#✅ **完全实现的功能**#{7}":[114,115],"#Dashboard 功能对比报告#📝 结论 (最终完成)#🚀 **新增优势**":[116,121],"#Dashboard 功能对比报告#📝 结论 (最终完成)#🚀 **新增优势**#{1}":[117,117],"#Dashboard 功能对比报告#📝 结论 (最终完成)#🚀 **新增优势**#{2}":[118,118],"#Dashboard 功能对比报告#📝 结论 (最终完成)#🚀 **新增优势**#{3}":[119,119],"#Dashboard 功能对比报告#📝 结论 (最终完成)#🚀 **新增优势**#{4}":[120,121],"#Dashboard 功能对比报告#📝 结论 (最终完成)#🎯 **当前状态**":[122,126],"#Dashboard 功能对比报告#📝 结论 (最终完成)#🎯 **当前状态**#{1}":[123,123],"#Dashboard 功能对比报告#📝 结论 (最终完成)#🎯 **当前状态**#{2}":[124,124],"#Dashboard 功能对比报告#📝 结论 (最终完成)#🎯 **当前状态**#{3}":[125,126],"#Dashboard 功能对比报告#📝 结论 (最终完成)#📋 **验证建议**":[127,133],"#Dashboard 功能对比报告#📝 结论 (最终完成)#📋 **验证建议**#{1}":[128,128],"#Dashboard 功能对比报告#📝 结论 (最终完成)#📋 **验证建议**#{2}":[129,129],"#Dashboard 功能对比报告#📝 结论 (最终完成)#📋 **验证建议**#{3}":[130,131],"#Dashboard 功能对比报告#📝 结论 (最终完成)#📋 **验证建议**#{4}":[132,133]},"outlinks":[],"last_embed":{"hash":"17wqkdi","at":1752192522091}},
"smart_sources:web3/farm/功能对比报告.md": {"path":"web3/farm/功能对比报告.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05936937,-0.05619617,0.05172905,0.00699012,0.04938425,-0.00571143,-0.10054689,-0.04010342,0.04952295,0.00431218,0.03534487,-0.11480529,0.03121775,0.03571681,0.02414968,0.02347404,-0.02746124,0.02024478,-0.00336872,-0.0560155,0.06611621,-0.00012798,-0.02281938,-0.03879661,0.02635491,0.06230348,-0.02133398,-0.01265348,-0.01329727,-0.13033572,0.01758501,-0.05708577,0.03621245,0.0174252,0.02445217,-0.08689941,0.02257481,0.08442115,-0.0215383,0.02512106,0.00759316,0.01079736,-0.01808447,-0.03337806,-0.02983272,-0.02942572,-0.02442573,0.02101575,0.00890406,-0.03200127,-0.02262226,-0.06706761,0.02286748,-0.01777362,0.02887749,0.04574642,0.01575819,0.08463937,-0.01653357,-0.00564959,0.07001602,0.03636654,-0.17260027,0.06665152,0.06260438,-0.03647397,-0.01481366,-0.03419332,0.05595035,0.06039952,-0.07271862,-0.00278536,-0.01880632,0.04296152,0.00781181,-0.03215621,0.01522409,0.00804915,-0.02166492,-0.05707275,-0.04209823,0.0732192,-0.05269918,0.04153064,-0.00823349,0.01861381,-0.0467361,-0.0089535,-0.01105489,0.028646,-0.02185776,-0.02563737,-0.01926867,0.05184042,-0.06988443,0.0045783,0.01898687,-0.00760915,-0.03665283,0.10973024,-0.06520598,0.02380088,0.00146248,-0.04873932,0.02138804,-0.01239751,-0.01864447,-0.04212593,-0.06196609,0.02405063,-0.05773015,0.03815672,-0.00152784,-0.07113656,0.00600468,-0.02132344,-0.01355802,-0.02635336,-0.00794852,0.01655719,0.00036909,0.06506746,0.07977453,-0.02807456,0.04706939,0.00902982,0.06327308,0.05726942,0.0124151,0.05039839,0.04688333,0.00614652,-0.07911455,0.00538418,-0.01204655,-0.012924,0.0027266,0.00160467,-0.01866444,0.01659259,-0.03480506,-0.06139744,-0.01732982,-0.09601724,-0.04433648,0.08796075,-0.04852707,0.01329743,-0.03482017,-0.05539859,-0.03237123,0.09678684,0.01433493,-0.00975194,-0.00066794,0.04470412,-0.00632442,0.11704896,-0.02041972,-0.02486159,-0.03953988,-0.05205621,-0.04637376,0.07214486,0.03829276,-0.13856533,0.00715348,0.04905398,0.0211795,-0.00136941,-0.01221087,0.04475987,-0.04087124,0.01158196,0.10363471,-0.03918885,-0.00546929,0.033993,0.01346939,0.02241164,0.02347243,0.00422108,-0.05373112,0.05796942,-0.04647047,-0.01567949,0.01510731,-0.01506187,0.03085081,-0.03223078,-0.08020235,0.00148382,-0.05312487,0.01285888,0.0093311,0.03505713,0.02784951,-0.02174313,0.0826304,0.01342437,0.15767071,0.0407924,0.01322747,0.00155482,-0.00679469,-0.01439617,-0.00314432,-0.02878336,0.0042593,0.01244517,-0.02529465,0.0316222,0.00936867,0.04860627,-0.01774651,-0.00160301,0.02039001,0.00760933,-0.05848255,0.0470575,0.00238568,0.04129119,-0.03656844,-0.22716577,0.01749771,0.00053804,-0.02390176,-0.08500034,-0.01070088,-0.00773842,0.03164916,0.0599623,0.06365459,0.12877217,0.0831391,-0.02028876,-0.00194115,-0.06078401,0.03722715,0.00139912,0.00186338,-0.03882234,-0.01238071,0.00001964,0.03027835,0.05852756,-0.04421354,0.05654181,-0.0455324,0.13457648,0.00472952,0.0255892,-0.01000985,0.10796914,0.00037147,-0.03684029,-0.11885115,0.00408263,0.0422569,-0.04229571,-0.0225352,-0.09369925,-0.05043655,-0.00012033,0.01260289,0.00792691,-0.07366534,0.03051987,-0.02945396,-0.01490425,-0.02768082,-0.00299165,0.00116502,0.02045099,0.03880023,0.02420806,0.10709137,0.00241925,0.00073537,0.0168062,0.06870195,-0.03389066,-0.0108233,0.01533564,0.00157645,-0.00945029,0.00615339,-0.02800286,-0.0205191,0.0028299,-0.04655455,-0.03628341,0.03017713,-0.04177378,0.12657419,-0.03346516,-0.0071551,0.05223826,-0.03602517,-0.04919844,-0.06792373,0.02178046,-0.03474135,-0.0372791,-0.01048777,0.09600172,0.0589571,0.04551994,0.0636894,0.00239736,-0.05016727,0.00736196,0.0052594,-0.00421989,-0.06264959,-0.08449362,-0.07022759,0.10276219,-0.00778575,-0.28923547,0.0095869,-0.00782152,-0.01495911,0.04563962,-0.00832455,0.00002799,-0.01206715,-0.06417163,0.00271207,-0.01345357,0.09289807,0.00159362,-0.05384653,0.04782832,0.03755815,0.04321679,0.04299229,0.01555119,0.02752845,-0.00643156,0.03433776,0.18901169,0.02838178,0.04704737,0.03350694,0.02932963,0.00864195,0.07642353,0.04600554,-0.03215575,-0.03629903,0.11354779,0.00964548,0.05812311,0.04535159,0.00346831,-0.03633039,-0.02591899,-0.01613797,-0.06663869,0.04427056,-0.04989243,0.0542492,0.03974199,-0.02825607,-0.02569643,-0.11544253,-0.02620167,0.03545884,-0.01933524,-0.10035916,-0.03992175,0.00439321,-0.00298407,0.07169092,0.03865291,-0.0553687,-0.04422658,0.0204544,0.03103138,0.00013611,0.00285595,0.06381214,0.01593904],"last_embed":{"hash":"p2lrdv","tokens":475}}},"last_read":{"hash":"p2lrdv","at":1752193036243},"class_name":"SmartSource","last_import":{"mtime":1752192972000,"size":6498,"at":1752193036090,"hash":"p2lrdv"},"blocks":{"#Dashboard 功能对比报告":[1,152],"#Dashboard 功能对比报告#📋 执行总结":[3,6],"#Dashboard 功能对比报告#📋 执行总结#{1}":[5,6],"#Dashboard 功能对比报告#✅ 已完成的核心功能":[7,33],"#Dashboard 功能对比报告#✅ 已完成的核心功能#1. **日期维度视图系统**":[9,13],"#Dashboard 功能对比报告#✅ 已完成的核心功能#1. **日期维度视图系统**#{1}":[10,10],"#Dashboard 功能对比报告#✅ 已完成的核心功能#1. **日期维度视图系统**#{2}":[11,11],"#Dashboard 功能对比报告#✅ 已完成的核心功能#1. **日期维度视图系统**#{3}":[12,13],"#Dashboard 功能对比报告#✅ 已完成的核心功能#2. **完整的 API 架构集成**":[14,18],"#Dashboard 功能对比报告#✅ 已完成的核心功能#2. **完整的 API 架构集成**#{1}":[15,15],"#Dashboard 功能对比报告#✅ 已完成的核心功能#2. **完整的 API 架构集成**#{2}":[16,16],"#Dashboard 功能对比报告#✅ 已完成的核心功能#2. **完整的 API 架构集成**#{3}":[17,18],"#Dashboard 功能对比报告#✅ 已完成的核心功能#3. **交互式视图切换**":[19,23],"#Dashboard 功能对比报告#✅ 已完成的核心功能#3. **交互式视图切换**#{1}":[20,20],"#Dashboard 功能对比报告#✅ 已完成的核心功能#3. **交互式视图切换**#{2}":[21,21],"#Dashboard 功能对比报告#✅ 已完成的核心功能#3. **交互式视图切换**#{3}":[22,23],"#Dashboard 功能对比报告#✅ 已完成的核心功能#4. **事件绑定和交互**":[24,28],"#Dashboard 功能对比报告#✅ 已完成的核心功能#4. **事件绑定和交互**#{1}":[25,25],"#Dashboard 功能对比报告#✅ 已完成的核心功能#4. **事件绑定和交互**#{2}":[26,26],"#Dashboard 功能对比报告#✅ 已完成的核心功能#4. **事件绑定和交互**#{3}":[27,28],"#Dashboard 功能对比报告#✅ 已完成的核心功能#5. **完整的表格功能**":[29,33],"#Dashboard 功能对比报告#✅ 已完成的核心功能#5. **完整的表格功能**#{1}":[30,30],"#Dashboard 功能对比报告#✅ 已完成的核心功能#5. **完整的表格功能**#{2}":[31,31],"#Dashboard 功能对比报告#✅ 已完成的核心功能#5. **完整的表格功能**#{3}":[32,33],"#Dashboard 功能对比报告#✅ 已实现的功能":[34,50],"#Dashboard 功能对比报告#✅ 已实现的功能#1. **模块化架构**":[36,40],"#Dashboard 功能对比报告#✅ 已实现的功能#1. **模块化架构**#{1}":[37,37],"#Dashboard 功能对比报告#✅ 已实现的功能#1. **模块化架构**#{2}":[38,38],"#Dashboard 功能对比报告#✅ 已实现的功能#1. **模块化架构**#{3}":[39,40],"#Dashboard 功能对比报告#✅ 已实现的功能#2. **基础数据处理**":[41,45],"#Dashboard 功能对比报告#✅ 已实现的功能#2. **基础数据处理**#{1}":[42,42],"#Dashboard 功能对比报告#✅ 已实现的功能#2. **基础数据处理**#{2}":[43,43],"#Dashboard 功能对比报告#✅ 已实现的功能#2. **基础数据处理**#{3}":[44,45],"#Dashboard 功能对比报告#✅ 已实现的功能#3. **风险维度视图**":[46,50],"#Dashboard 功能对比报告#✅ 已实现的功能#3. **风险维度视图**#{1}":[47,47],"#Dashboard 功能对比报告#✅ 已实现的功能#3. **风险维度视图**#{2}":[48,48],"#Dashboard 功能对比报告#✅ 已实现的功能#3. **风险维度视图**#{3}":[49,50],"#Dashboard 功能对比报告#🔧 需要修复的问题":[51,71],"#Dashboard 功能对比报告#🔧 需要修复的问题#1. **集成 API 系统**":[53,59],"#Dashboard 功能对比报告#🔧 需要修复的问题#1. **集成 API 系统**#{1}":[54,59],"#Dashboard 功能对比报告#🔧 需要修复的问题#2. **启用日期维度视图**":[60,63],"#Dashboard 功能对比报告#🔧 需要修复的问题#2. **启用日期维度视图**#{1}":[61,61],"#Dashboard 功能对比报告#🔧 需要修复的问题#2. **启用日期维度视图**#{2}":[62,63],"#Dashboard 功能对比报告#🔧 需要修复的问题#3. **完善视图切换器**":[64,67],"#Dashboard 功能对比报告#🔧 需要修复的问题#3. **完善视图切换器**#{1}":[65,65],"#Dashboard 功能对比报告#🔧 需要修复的问题#3. **完善视图切换器**#{2}":[66,67],"#Dashboard 功能对比报告#🔧 需要修复的问题#4. **统一渲染流程**":[68,71],"#Dashboard 功能对比报告#🔧 需要修复的问题#4. **统一渲染流程**#{1}":[69,69],"#Dashboard 功能对比报告#🔧 需要修复的问题#4. **统一渲染流程**#{2}":[70,71],"#Dashboard 功能对比报告#📊 功能完整度评估 (最终更新)":[72,86],"#Dashboard 功能对比报告#📊 功能完整度评估 (最终更新)#{1}":[74,86],"#Dashboard 功能对比报告#🚀 修复建议":[87,102],"#Dashboard 功能对比报告#🚀 修复建议#优先级 1 (高)":[89,93],"#Dashboard 功能对比报告#🚀 修复建议#优先级 1 (高)#{1}":[90,90],"#Dashboard 功能对比报告#🚀 修复建议#优先级 1 (高)#{2}":[91,91],"#Dashboard 功能对比报告#🚀 修复建议#优先级 1 (高)#{3}":[92,93],"#Dashboard 功能对比报告#🚀 修复建议#优先级 2 (中)":[94,98],"#Dashboard 功能对比报告#🚀 修复建议#优先级 2 (中)#{1}":[95,95],"#Dashboard 功能对比报告#🚀 修复建议#优先级 2 (中)#{2}":[96,96],"#Dashboard 功能对比报告#🚀 修复建议#优先级 2 (中)#{3}":[97,98],"#Dashboard 功能对比报告#🚀 修复建议#优先级 3 (低)":[99,102],"#Dashboard 功能对比报告#🚀 修复建议#优先级 3 (低)#{1}":[100,100],"#Dashboard 功能对比报告#🚀 修复建议#优先级 3 (低)#{2}":[101,102],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)":[103,152],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#{1}":[105,106],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#✅ **完全实现的功能**":[107,129],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#✅ **完全实现的功能**#**1. 核心架构功能**":[109,113],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#✅ **完全实现的功能**#**1. 核心架构功能**#{1}":[110,110],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#✅ **完全实现的功能**#**1. 核心架构功能**#{2}":[111,111],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#✅ **完全实现的功能**#**1. 核心架构功能**#{3}":[112,113],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#✅ **完全实现的功能**#**2. 数据处理功能**":[114,119],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#✅ **完全实现的功能**#**2. 数据处理功能**#{1}":[115,115],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#✅ **完全实现的功能**#**2. 数据处理功能**#{2}":[116,116],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#✅ **完全实现的功能**#**2. 数据处理功能**#{3}":[117,117],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#✅ **完全实现的功能**#**2. 数据处理功能**#{4}":[118,119],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#✅ **完全实现的功能**#**3. 用户界面功能**":[120,124],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#✅ **完全实现的功能**#**3. 用户界面功能**#{1}":[121,121],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#✅ **完全实现的功能**#**3. 用户界面功能**#{2}":[122,122],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#✅ **完全实现的功能**#**3. 用户界面功能**#{3}":[123,124],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#✅ **完全实现的功能**#**4. API 和兼容性**":[125,129],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#✅ **完全实现的功能**#**4. API 和兼容性**#{1}":[126,126],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#✅ **完全实现的功能**#**4. API 和兼容性**#{2}":[127,127],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#✅ **完全实现的功能**#**4. API 和兼容性**#{3}":[128,129],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#🚀 **超越原版的优势**":[130,136],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#🚀 **超越原版的优势**#{1}":[131,131],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#🚀 **超越原版的优势**#{2}":[132,132],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#🚀 **超越原版的优势**#{3}":[133,133],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#🚀 **超越原版的优势**#{4}":[134,134],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#🚀 **超越原版的优势**#{5}":[135,136],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#🎯 **当前状态**":[137,142],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#🎯 **当前状态**#{1}":[138,138],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#🎯 **当前状态**#{2}":[139,139],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#🎯 **当前状态**#{3}":[140,140],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#🎯 **当前状态**#{4}":[141,142],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#📋 **最终验证**":[143,152],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#📋 **最终验证**#{1}":[144,144],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#📋 **最终验证**#{2}":[145,145],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#📋 **最终验证**#{3}":[146,146],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#📋 **最终验证**#{4}":[147,147],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#📋 **最终验证**#{5}":[148,148],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#📋 **最终验证**#{6}":[149,150],"#Dashboard 功能对比报告#📝 结论 (最终完成 - 第二次验证)#📋 **最终验证**#{7}":[151,152]},"outlinks":[],"last_embed":{"hash":"p2lrdv","at":1752193036092}},

"smart_sources:web3/report/Nockchain.md": {"path":"web3/report/Nockchain.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05195498,-0.01446124,-0.06109621,-0.04579177,0.04688855,0.02225339,0.00832907,0.01378968,0.01060787,-0.00684795,0.0590109,-0.04969358,0.0456413,0.03660356,0.02548405,-0.02882005,0.01494812,-0.03346371,-0.01783686,0.0214234,0.1107764,-0.07048488,0.04008924,0.00210298,0.06967803,0.04340161,0.01826668,-0.030929,-0.03929631,-0.17579518,0.00152296,-0.06559011,0.00086485,0.02351546,0.01504543,-0.05164666,-0.01774608,0.05773254,-0.05015976,0.00169597,0.00253093,0.00254172,0.00744095,-0.01318784,0.04710629,-0.06810659,0.02739119,-0.06714708,0.01989886,-0.05563262,-0.0331252,-0.0750199,-0.00251415,0.02966199,0.00269542,0.02034063,0.00261639,0.03343452,0.02064628,0.04061593,0.08068016,0.06727975,-0.19185363,0.01421426,0.07956833,0.0014064,-0.03953527,0.02893102,0.03579668,0.0737084,0.01901533,-0.00254222,-0.01502904,0.06101694,0.0248456,-0.00384722,0.03487268,0.03724562,-0.02294173,-0.0421981,-0.07320254,0.09918199,-0.00092186,-0.01581875,-0.01700567,-0.03547287,-0.01173743,-0.04895858,-0.04606934,0.02180337,-0.0092501,0.02643808,0.03633797,-0.00257631,-0.0141873,-0.02270241,-0.01461893,0.01422412,-0.10745063,0.12403364,-0.0465156,0.03375838,0.00606655,-0.04895762,0.0525708,0.01018821,0.00825961,-0.06309895,0.02492165,0.04815147,-0.0008864,0.0075677,0.06703023,-0.04941516,0.02846929,0.02601652,-0.00697621,0.00082157,-0.0162882,0.01395842,-0.01683472,0.04792462,0.03747706,-0.04387576,-0.0283661,-0.0229042,0.01404449,0.04886817,0.04631749,0.02914311,0.01414454,0.03801455,-0.08503909,-0.00101562,-0.07765917,-0.03726463,-0.00601223,-0.00083673,-0.02365477,-0.03163762,-0.02866752,-0.08395886,-0.02539836,-0.12091986,-0.06903148,0.0701495,-0.00930037,-0.00373366,0.0060634,-0.04195913,0.01479281,0.08627689,-0.04613953,-0.08987653,-0.03197532,0.00004106,0.03121387,0.10914218,-0.03809918,-0.00366707,-0.00934982,-0.05095798,-0.03105153,0.11242457,0.0305601,-0.11454917,0.04269043,0.02591673,-0.01745971,-0.06799498,0.00511727,0.02542659,-0.06303461,0.00783242,0.07625585,-0.04854154,-0.01898572,-0.02898761,0.01354718,0.05621912,0.00088318,-0.01466089,-0.0830033,-0.02006672,-0.01096227,-0.01986158,-0.02562084,0.04521293,0.04403574,0.04684327,-0.07078555,0.05459581,-0.0142699,0.02903694,-0.03229749,-0.00182603,-0.01726997,-0.01678564,0.02477814,-0.05369546,0.13851215,0.02263716,0.02843344,0.01246045,-0.06248407,0.02137162,-0.03733984,-0.05292118,0.00578202,0.02323987,-0.02122547,0.01604508,-0.03140255,0.04524982,-0.03086734,-0.00251588,0.01148648,0.04499851,-0.0320273,0.048807,0.02046886,0.04103604,-0.09487557,-0.18151988,-0.03684453,0.06449336,-0.00358549,0.02237329,0.00892136,0.03680393,0.026904,0.04445286,0.10412638,0.10004731,0.0669135,-0.04945436,0.00198756,-0.01920485,0.05176216,0.06318666,-0.00682202,-0.00189652,0.03969237,-0.03699649,0.02588034,-0.00187628,-0.03018309,0.04765382,-0.06034065,0.10923855,-0.01985756,-0.00409211,-0.00032408,0.07420436,0.0123499,-0.03717725,-0.16256644,0.0353973,0.09023848,-0.01544044,-0.01378034,-0.04734441,-0.02334089,-0.02706858,0.01537377,0.04974671,-0.09980727,0.00871581,-0.07285459,-0.03612448,-0.04786695,-0.00557548,0.03111422,0.01944192,0.01826249,0.03149845,0.11539303,-0.01591657,-0.01351262,0.01967237,-0.06713489,-0.00490818,0.03680017,-0.00972993,-0.01378406,-0.0145537,-0.002664,0.02032805,-0.01705717,-0.02253446,0.02683188,0.02763129,-0.01617015,-0.03204817,0.14223662,0.02712325,0.00684528,0.05422463,0.01123212,-0.0558155,-0.08644795,0.05133552,-0.0077666,0.02322218,0.01627271,0.02747702,0.04100472,-0.01428691,0.04849465,0.01970292,-0.00822595,0.03522184,0.00638113,-0.00582475,-0.05115408,-0.04370716,-0.03451356,0.06764409,0.02010754,-0.28445542,-0.04644148,-0.01990054,-0.02482265,0.07792674,0.02932137,0.05712524,0.03134765,-0.05309769,0.02958193,0.01648065,0.03139177,-0.0126639,-0.06926608,0.01056818,-0.01864229,0.03993959,-0.00508019,0.0227328,0.05650032,0.00240121,0.06206356,0.22986811,-0.01991509,0.0513425,0.02875859,-0.05233293,0.04251732,0.01724167,0.00601894,-0.04782273,-0.00748465,0.0416519,-0.0133611,0.01732074,0.01436637,-0.00942953,-0.00910546,-0.00689966,-0.00388377,-0.04048477,-0.00455611,-0.03035939,0.02932597,0.11340589,0.00605917,-0.05493456,-0.06659999,0.01067412,0.04873757,-0.0540785,-0.02372607,0.01793418,0.03660401,-0.01183645,0.07289313,0.03326507,-0.05462627,-0.07119333,-0.01634114,-0.05651393,-0.0066256,-0.04734616,0.07160582,0.01307317],"last_embed":{"hash":"14fdg2n","tokens":471}}},"last_read":{"hash":"14fdg2n","at":1751426530276},"class_name":"SmartSource","last_import":{"mtime":1751382188549,"size":44627,"at":1751426521956,"hash":"14fdg2n"},"blocks":{"#**Nockchain 综合研究报告：可验证计算的新范式**":[3,353],"#**Nockchain 综合研究报告：可验证计算的新范式**##**执行摘要**":[5,67],"#**Nockchain 综合研究报告：可验证计算的新范式**##**执行摘要**#{1}":[7,67],"#---frontmatter---":[15,66],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**":[68,99],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.1. 共识引擎：zkPoW \\- 有用工作量证明**":[70,75],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.1. 共识引擎：zkPoW \\- 有用工作量证明**#{1}":[72,75],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.2. 虚拟机：基于 Nock ISA 的新型 ZKVM**":[76,79],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.2. 虚拟机：基于 Nock ISA 的新型 ZKVM**#{1}":[78,79],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.3. 应用层：NockApp 框架与意图模型**":[80,89],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.3. 应用层：NockApp 框架与意图模型**#{1}":[82,89],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.4. 开发者体验：Jock 编程语言**":[90,99],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.4. 开发者体验：Jock 编程语言**#{1}":[92,99],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**":[100,148],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.1. “公平启动”原则**":[102,109],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.1. “公平启动”原则**#{1}":[104,109],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.2. 代币供应、发行与效用**":[110,116],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.2. 代币供应、发行与效用**#{1}":[112,112],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.2. 代币供应、发行与效用**#{2}":[113,113],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.2. 代币供应、发行与效用**#{3}":[114,114],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.2. 代币供应、发行与效用**#{4}":[115,116],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.3. 市场分析与普遍存在的代币混淆**":[117,124],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.3. 市场分析与普遍存在的代币混淆**#{1}":[119,124],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.4. 挖矿经济学**":[125,132],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.4. 挖矿经济学**#{1}":[127,127],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.4. 挖矿经济学**#{2}":[128,128],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.4. 挖矿经济学**#{3}":[129,129],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.4. 挖矿经济学**#{4}":[130,130],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.4. 挖矿经济学**#{5}":[131,132],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**表 2: $NOCK 代币经济学与分配模型**":[133,148],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**表 2: $NOCK 代币经济学与分配模型**#{1}":[135,148],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**":[149,172],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.1. 创始人简介：Logan Allen**":[151,154],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.1. 创始人简介：Logan Allen**#{1}":[153,154],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.2. 投资者分析与“公平启动”的困境**":[155,166],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.2. 投资者分析与“公平启动”的困境**#{1}":[157,166],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.3. 治理模型**":[167,172],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.3. 治理模型**#{1}":[169,172],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**":[173,195],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.1. “实验性与未经审计”的现实**":[175,184],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.1. “实验性与未经审计”的现实**#{1}":[177,184],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.2. 潜在攻击向量**":[185,195],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.2. 潜在攻击向量**#{1}":[187,188],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.2. 潜在攻击向量**#{2}":[189,189],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.2. 潜在攻击向量**#{3}":[190,190],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.2. 潜在攻击向量**#{4}":[191,191],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.2. 潜在攻击向量**#{5}":[192,193],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.2. 潜在攻击向量**#{6}":[194,195],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**":[196,216],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.1. 官方渠道与开发者资源**":[198,204],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.1. 官方渠道与开发者资源**#{1}":[200,200],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.1. 官方渠道与开发者资源**#{2}":[201,201],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.1. 官方渠道与开发者资源**#{3}":[202,202],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.1. 官方渠道与开发者资源**#{4}":[203,204],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.2. 开发者采纳的障碍**":[205,208],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.2. 开发者采纳的障碍**#{1}":[207,208],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.3. 社区情绪与目标受众**":[209,216],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.3. 社区情绪与目标受众**#{1}":[211,216],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**":[217,246],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#{1}":[219,220],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**表 3: 竞争分析：Nockchain vs. Aleo vs. RISC Zero**":[221,234],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**表 3: 竞争分析：Nockchain vs. Aleo vs. RISC Zero**#{1}":[223,234],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**7.1. Nockchain vs. Aleo**":[235,238],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**7.1. Nockchain vs. Aleo**#{1}":[237,238],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**7.2. Nockchain vs. RISC Zero**":[239,246],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**7.2. Nockchain vs. RISC Zero**#{1}":[241,246],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**":[247,353],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.1. 综合分析 (SWOT)**":[249,270],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.1. 综合分析 (SWOT)**#{1}":[251,255],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.1. 综合分析 (SWOT)**#{2}":[256,260],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.1. 综合分析 (SWOT)**#{3}":[261,264],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.1. 综合分析 (SWOT)**#{4}":[265,270],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.2. 未来路线图与关键里程碑**":[271,281],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.2. 未来路线图与关键里程碑**#{1}":[273,276],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.2. 未来路线图与关键里程碑**#{2}":[277,277],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.2. 未来路线图与关键里程碑**#{3}":[278,278],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.2. 未来路线图与关键里程碑**#{4}":[279,279],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.2. 未来路线图与关键里程碑**#{5}":[280,281],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.3. 针对用户的建议**":[282,290],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.3. 针对用户的建议**#{1}":[284,284],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.3. 针对用户的建议**#{2}":[285,285],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.3. 针对用户的建议**#{3}":[286,286],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.3. 针对用户的建议**#{4}":[287,287],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.3. 针对用户的建议**#{5}":[288,288],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.3. 针对用户的建议**#{6}":[289,290],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**":[291,353],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{1}":[293,293],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{2}":[294,294],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{3}":[295,295],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{4}":[296,296],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{5}":[297,297],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{6}":[298,298],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{7}":[299,299],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{8}":[300,300],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{9}":[301,301],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{10}":[302,302],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{11}":[303,303],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{12}":[304,304],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{13}":[305,305],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{14}":[306,306],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{15}":[307,307],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{16}":[308,308],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{17}":[309,309],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{18}":[310,310],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{19}":[311,311],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{20}":[312,312],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{21}":[313,313],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{22}":[314,314],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{23}":[315,315],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{24}":[316,316],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{25}":[317,317],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{26}":[318,318],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{27}":[319,319],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{28}":[320,320],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{29}":[321,321],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{30}":[322,322],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{31}":[323,323],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{32}":[324,324],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{33}":[325,325],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{34}":[326,326],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{35}":[327,327],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{36}":[328,328],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{37}":[329,329],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{38}":[330,330],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{39}":[331,331],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{40}":[332,332],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{41}":[333,333],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{42}":[334,334],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{43}":[335,335],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{44}":[336,336],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{45}":[337,337],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{46}":[338,338],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{47}":[339,339],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{48}":[340,340],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{49}":[341,341],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{50}":[342,342],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{51}":[343,343],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{52}":[344,344],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{53}":[345,345],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{54}":[346,346],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{55}":[347,347],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{56}":[348,348],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{57}":[349,349],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{58}":[350,350],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{59}":[351,351],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{60}":[352,352],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{61}":[353,353]},"outlinks":[{"title":"https://github.com/zorp-corp/nockchain","target":"https://github.com/zorp-corp/nockchain","line":293},{"title":"https://github.com/0xmoei/nockchain","target":"https://github.com/0xmoei/nockchain","line":294},{"title":"https://www.nockchain.org/","target":"https://www.nockchain.org/","line":295},{"title":"https://icoanalytics.org/projects/zorp-nockchain/","target":"https://icoanalytics.org/projects/zorp-nockchain/","line":296},{"title":"https://urbit.org/overview/history","target":"https://urbit.org/overview/history","line":297},{"title":"https://github.com/zorp-corp/jock-lang","target":"https://github.com/zorp-corp/jock-lang","line":298},{"title":"https://www.globenewswire.com/news-release/2024/12/30/3002639/0/en/Alpha-Sigma-Capital-Research-Unveils-2025-Crypto-Market-Predictions-AI-Agents-Staking-ETFs-and-Decentralized-Infrastructure-Lead-the-Way.html","target":"https://www.globenewswire.com/news-release/2024/12/30/3002639/0/en/Alpha-Sigma-Capital-Research-Unveils-2025-Crypto-Market-Predictions-AI-Agents-Staking-ETFs-and-Decentralized-Infrastructure-Lead-the-Way.html","line":299},{"title":"https://crypto-fundraising.info/projects/zorp-nockchain/","target":"https://crypto-fundraising.info/projects/zorp-nockchain/","line":300},{"title":"https://members.delphidigital.io/feed/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","target":"https://members.delphidigital.io/feed/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","line":301},{"title":"https://podwise.ai/dashboard/episodes/4062914","target":"https://podwise.ai/dashboard/episodes/4062914","line":302},{"title":"https://player.fm/series/the-delphi-podcast/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","target":"https://player.fm/series/the-delphi-podcast/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","line":303},{"title":"https://tracxn.com/d/companies/zorp/\\_\\_VTLVrft6NMSWw6R9NmmNgOwSmC1kn-XXhp2K\\_MTFXxM","target":"https://tracxn.com/d/companies/zorp/__VTLVrft6NMSWw6R9NmmNgOwSmC1kn-XXhp2K_MTFXxM","line":304},{"title":"https://www.zorp.one/","target":"https://www.zorp.one/","line":305},{"title":"https://www.zorp.one/about","target":"https://www.zorp.one/about","line":306},{"title":"https://en.wikipedia.org/wiki/End\\_of\\_the\\_World\\_(Parks\\_and\\_Recreation)","target":"https://en.wikipedia.org/wiki/End_of_the_World_\\(Parks_and_Recreation\\","line":307},{"title":"https://podcasts.apple.com/gr/podcast/the-delphi-podcast/id1438148082?l=el","target":"https://podcasts.apple.com/gr/podcast/the-delphi-podcast/id1438148082?l=el","line":308},{"title":"https://www.chaincatcher.com/en/article/2181453","target":"https://www.chaincatcher.com/en/article/2181453","line":309},{"title":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","target":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","line":310},{"title":"https://cryptorank.io/ico/nockchain","target":"https://cryptorank.io/ico/nockchain","line":311},{"title":"https://cryptorank.io/price/nockchain","target":"https://cryptorank.io/price/nockchain","line":312},{"title":"https://github.com/zorp-corp/nockapp","target":"https://github.com/zorp-corp/nockapp","line":313},{"title":"https://www.everand.com/podcast/864012011/Logan-Allen-Nockchain-s-Global-Competition-for-Useful-ZK-Proof-of-Work-Miners-Kicks-Off","target":"https://www.everand.com/podcast/864012011/Logan-Allen-Nockchain-s-Global-Competition-for-Useful-ZK-Proof-of-Work-Miners-Kicks-Off","line":314},{"title":"https://www.nervos.org/knowledge-base/what\\_are\\_blockchain\\_intents\\_(explainCKBot)","target":"https://www.nervos.org/knowledge-base/what_are_blockchain_intents_\\(explainCKBot\\","line":315},{"title":"https://volet.com/blog/post/understanding-intents-in-blockchain-a-comprehensive-guide-to-intents-01jdpzq69t52gfwxy6z0bx1nmv","target":"https://volet.com/blog/post/understanding-intents-in-blockchain-a-comprehensive-guide-to-intents-01jdpzq69t52gfwxy6z0bx1nmv","line":316},{"title":"https://crypto.com/en/university/intent-centric-design-for-blockchain","target":"https://crypto.com/en/university/intent-centric-design-for-blockchain","line":317},{"title":"https://www.bitget.com/zh-TC/news/detail/12560604756213","target":"https://www.bitget.com/zh-TC/news/detail/12560604756213","line":318},{"title":"https://www.youtube.com/watch?v=G5tE0LFFiTY","target":"https://www.youtube.com/watch?v=G5tE0LFFiTY","line":319},{"title":"https://ethplorer.io/address/******************************************","target":"https://ethplorer.io/address/******************************************","line":320},{"title":"https://phantom.com/tokens/solana/6tCEUogw2uk4VQz5JKb295ixFrj6vAPEQbZqL9VfFXSz","target":"https://phantom.com/tokens/solana/6tCEUogw2uk4VQz5JKb295ixFrj6vAPEQbZqL9VfFXSz","line":321},{"title":"https://www.dextools.io/app/en/solana/pair-explorer/ENDyunqABSwMvfUoRxsNA1N2QLhXeZQTYrnPZUQdnygP","target":"https://www.dextools.io/app/en/solana/pair-explorer/ENDyunqABSwMvfUoRxsNA1N2QLhXeZQTYrnPZUQdnygP","line":322},{"title":"https://web3.bitget.com/en/wiki/nock-wallet","target":"https://web3.bitget.com/en/wiki/nock-wallet","line":323},{"title":"https://m.sosovalue.com/blog/comprehensive-guide-to-bitcoin-mining","target":"https://m.sosovalue.com/blog/comprehensive-guide-to-bitcoin-mining","line":324},{"title":"https://www.cryptominerbros.com/blog/calculate-crypto-mining-profits/","target":"https://www.cryptominerbros.com/blog/calculate-crypto-mining-profits/","line":325},{"title":"https://asicmarketplace.com/blog/how-to-calculate-profit-in-crypto-mining/","target":"https://asicmarketplace.com/blog/how-to-calculate-profit-in-crypto-mining/","line":326},{"title":"https://www.rootdata.com/Projects/detail/Nockpool?k=MTc2Mzg%3D","target":"https://www.rootdata.com/Projects/detail/Nockpool?k=MTc2Mzg%3D","line":327},{"title":"https://en.wikipedia.org/wiki/Mining\\_pool","target":"https://en.wikipedia.org/wiki/Mining_pool","line":328},{"title":"https://podcasts.apple.com/us/podcast/the-delphi-podcast/id1438148082","target":"https://podcasts.apple.com/us/podcast/the-delphi-podcast/id1438148082","line":329},{"title":"https://icodrops.com/nockchain/","target":"https://icodrops.com/nockchain/","line":330},{"title":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA=","target":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","line":331},{"title":"https://github.com/0xmoei/nockchain/security","target":"https://github.com/0xmoei/nockchain/security","line":332},{"title":"https://dysnix.com/blockchain-security-audit","target":"https://dysnix.com/blockchain-security-audit","line":333},{"title":"https://www.lcx.com/how-to-conduct-blockchain-security-audit/","target":"https://www.lcx.com/how-to-conduct-blockchain-security-audit/","line":334},{"title":"https://veridise.com/audits/nft-security/","target":"https://veridise.com/audits/nft-security/","line":335},{"title":"https://immunebytes.com/blog/blockchain-security-audit-the-ultimate-guide/","target":"https://immunebytes.com/blog/blockchain-security-audit-the-ultimate-guide/","line":336},{"title":"https://hacken.io/services/blockchain-security/blockchain-protocol-security/","target":"https://hacken.io/services/blockchain-security/blockchain-protocol-security/","line":337},{"title":"https://quantstamp.com/audits","target":"https://quantstamp.com/audits","line":338},{"title":"https://www.reddit.com/r/CryptoCurrency/comments/1lhoxfc/outside\\_of\\_the\\_crypto\\_community\\_crypto\\_is\\_still/","target":"https://www.reddit.com/r/CryptoCurrency/comments/1lhoxfc/outside_of_the_crypto_community_crypto_is_still/","line":339},{"title":"https://www.reddit.com/r/CryptoMarkets/comments/1l5jqcu/which\\_crypto\\_community\\_still\\_feels\\_genuinely/","target":"https://www.reddit.com/r/CryptoMarkets/comments/1l5jqcu/which_crypto_community_still_feels_genuinely/","line":340},{"title":"https://www.reddit.com/r/CryptoMarkets/comments/1jakyzw/why\\_community\\_sentiment\\_of\\_every\\_crypto\\_on/","target":"https://www.reddit.com/r/CryptoMarkets/comments/1jakyzw/why_community_sentiment_of_every_crypto_on/","line":341},{"title":"https://www.everand.com/podcast/593966517/Launching-the-Mars-Review-of-Books-with-Noah-Kumin-Noah-Kumin-is-the-founder-and-editor-of-the-Mars-Review-of-Books","target":"https://www.everand.com/podcast/593966517/Launching-the-Mars-Review-of-Books-with-Noah-Kumin-Noah-Kumin-is-the-founder-and-editor-of-the-Mars-Review-of-Books","line":342},{"title":"https://podcasts.apple.com/bt/podcast/urbit-nockchain-and-the-current-state/id1195362330?i=1000709342627","target":"https://podcasts.apple.com/bt/podcast/urbit-nockchain-and-the-current-state/id1195362330?i=1000709342627","line":343},{"title":"https://www.nansen.ai/post/best-telegram-channels-for-crypto-chats","target":"https://www.nansen.ai/post/best-telegram-channels-for-crypto-chats","line":344},{"title":"https://ryanschultz.com/2018/07/17/the-telegram-discussion-groups-for-the-various-blockchain-based-vr-projects-have-been-endlessly-entertaining/","target":"https://ryanschultz.com/2018/07/17/the-telegram-discussion-groups-for-the-various-blockchain-based-vr-projects-have-been-endlessly-entertaining/","line":345},{"title":"https://www.reddit.com/r/CryptoCurrency/comments/lz1f54/beware\\_of\\_fake\\_pump\\_coins\\_groups\\_on\\_telegram/","target":"https://www.reddit.com/r/CryptoCurrency/comments/lz1f54/beware_of_fake_pump_coins_groups_on_telegram/","line":346},{"title":"https://asicmarketplace.com/blog/what-is-aleo/","target":"https://asicmarketplace.com/blog/what-is-aleo/","line":347},{"title":"https://risczero.com/blog/zkvm","target":"https://risczero.com/blog/zkvm","line":348},{"title":"https://www.rootdata.com/Projects/detail/RISC%20Zero?k=MjgwMw%3D%3D","target":"https://www.rootdata.com/Projects/detail/RISC%20Zero?k=MjgwMw%3D%3D","line":349},{"title":"https://en.foresightnews.pro/an-overview-of-privacy-solutions-based-on-zero-knowlege-cryptography/","target":"https://en.foresightnews.pro/an-overview-of-privacy-solutions-based-on-zero-knowlege-cryptography/","line":350},{"title":"https://equilibrium.co/writing/privacy-blockchains-and-aleo-deep-dive","target":"https://equilibrium.co/writing/privacy-blockchains-and-aleo-deep-dive","line":351},{"title":"https://www.blockchaincoinvestors.com/blog/risczero-eliminating-tradeoffs-and-unleashing-the-power-of-blockchain-technology-254ec","target":"https://www.blockchaincoinvestors.com/blog/risczero-eliminating-tradeoffs-and-unleashing-the-power-of-blockchain-technology-254ec","line":352},{"title":"https://podcasts.musixmatch.com/podcast/the-delphi-podcast-01hpa65hx48s2p8649mhbr8ke5/episode/logan-allen-nockchains-global-competition-for-useful-01jvmp9agdsppxbp1n6gwtz1ck","target":"https://podcasts.musixmatch.com/podcast/the-delphi-podcast-01hpa65hx48s2p8649mhbr8ke5/episode/logan-allen-nockchains-global-competition-for-useful-01jvmp9agdsppxbp1n6gwtz1ck","line":353}],"last_embed":{"hash":"14fdg2n","at":1751426529296}},"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05209928,-0.01983031,-0.05900241,-0.04159747,0.0542668,0.02537937,0.01147205,0.0091958,0.01691806,-0.0036796,0.05357911,-0.05243861,0.05157819,0.03208044,0.02471961,-0.02598504,0.01314872,-0.03575703,-0.01970401,0.01620092,0.11230433,-0.06174985,0.03796258,0.00605848,0.06630708,0.04209745,0.01742994,-0.0326609,-0.03968378,-0.170331,-0.00040688,-0.06614769,0.00232687,0.0182792,0.02129152,-0.05145583,-0.01978709,0.05563856,-0.05261078,0.00477108,0.00134977,0.00184539,0.01506509,-0.01578734,0.04301767,-0.06282068,0.02931855,-0.06735713,0.02432508,-0.05762042,-0.03415793,-0.07868545,-0.00023235,0.03083586,0.01242269,0.01965474,-0.00201544,0.03176493,0.02276132,0.03796979,0.08870055,0.07132571,-0.19222915,0.01702851,0.07929009,0.00672775,-0.03269253,0.02678291,0.04034782,0.07367276,0.01903347,0.00231748,-0.01496728,0.06248271,0.03165302,0.00247121,0.02978039,0.03883202,-0.02145001,-0.0403249,-0.07883824,0.09792259,-0.00129101,-0.01476314,-0.02362572,-0.03828849,-0.01531092,-0.04699591,-0.04841498,0.02142801,-0.00544888,0.02717923,0.02988829,-0.00248891,-0.01298005,-0.02837553,-0.01303225,0.0094646,-0.10452478,0.12786444,-0.04893331,0.03569862,0.0066149,-0.04763799,0.05317735,0.01170812,0.0056991,-0.06016538,0.02260733,0.04451206,0.00502087,0.00851275,0.06922477,-0.05501514,0.02318854,0.02850214,-0.00876064,-0.00296025,-0.01327678,0.01258535,-0.01516272,0.04620744,0.03414837,-0.04600495,-0.0326466,-0.02059445,0.01481383,0.04627026,0.04764315,0.0347785,0.01497994,0.04110204,-0.08756118,-0.0075053,-0.07703914,-0.03992318,-0.0084397,-0.00381878,-0.0197104,-0.03061193,-0.02774962,-0.08035539,-0.02754267,-0.11855289,-0.06868242,0.06737906,-0.00624141,-0.00191205,0.0054603,-0.03951986,0.01322417,0.0915382,-0.04541579,-0.09009651,-0.03402803,0.00005531,0.02943666,0.11391266,-0.04203429,-0.00667725,-0.00751316,-0.0501538,-0.02882935,0.10784499,0.03110237,-0.10968367,0.04592357,0.02553595,-0.0142467,-0.0619293,0.00782033,0.02547527,-0.05992771,0.00785585,0.07620744,-0.04412114,-0.01738833,-0.02912756,0.01256998,0.05342744,0.00078996,-0.02611816,-0.07990952,-0.01809268,-0.01007282,-0.02435277,-0.02661485,0.0499491,0.04493904,0.04986662,-0.07359043,0.05414648,-0.01194891,0.0329801,-0.0304383,0.00100442,-0.01983649,-0.01905642,0.02177684,-0.04586976,0.14182395,0.02287699,0.02872835,0.00977461,-0.0662231,0.02877189,-0.03720791,-0.05571181,0.00995066,0.02244362,-0.01981836,0.01825197,-0.03134109,0.0456764,-0.03572939,-0.00453993,0.01297127,0.05056598,-0.03281311,0.04573547,0.02139148,0.03764267,-0.09241469,-0.1862186,-0.04046455,0.05946748,-0.00013772,0.01918314,0.00476441,0.03918238,0.02751756,0.04281644,0.10172357,0.09632947,0.07084697,-0.05125256,0.0015261,-0.01743826,0.0507883,0.059435,-0.00792152,0.00102146,0.04306898,-0.03492612,0.0232849,-0.00572995,-0.03472744,0.04696833,-0.05285175,0.11188217,-0.02414302,-0.00355837,-0.004643,0.06922333,0.01452325,-0.03770623,-0.16581462,0.03385546,0.08688564,-0.01967085,-0.00981128,-0.05165711,-0.02114383,-0.0273235,0.00995336,0.04829409,-0.09398001,0.00694735,-0.07542975,-0.03358885,-0.05354861,-0.00067914,0.02507165,0.01741842,0.01753484,0.03529619,0.11357328,-0.01361131,-0.01250574,0.01920301,-0.06524036,-0.00196939,0.03688161,-0.01103707,-0.01727571,-0.01724677,0.00227064,0.01798035,-0.01652179,-0.02677454,0.02592888,0.02839041,-0.01038816,-0.03133463,0.14351033,0.02860915,0.00670449,0.05021575,0.00765615,-0.05523091,-0.08333949,0.04596205,-0.00748734,0.02544738,0.01606098,0.03056505,0.04165783,-0.02187788,0.04880083,0.01796297,-0.00399597,0.03202753,0.0083521,-0.00653256,-0.05157022,-0.04858462,-0.04385248,0.06335615,0.02849349,-0.28452373,-0.04923433,-0.01704164,-0.02618274,0.08203965,0.02413568,0.05202143,0.03297686,-0.0560484,0.02510827,0.01592444,0.03741925,-0.01277379,-0.0732461,0.00572968,-0.02119747,0.04074597,-0.00659483,0.02223096,0.05675416,0.00345143,0.05701254,0.22800887,-0.01923178,0.05012021,0.02918076,-0.05676023,0.04434203,0.01909241,0.00901302,-0.04811215,-0.00351057,0.04255924,-0.0176275,0.0253517,0.01536225,-0.01099369,-0.00957345,-0.00557787,-0.00345828,-0.03955767,-0.00067065,-0.02822443,0.02431242,0.11187688,0.00861431,-0.0551085,-0.07115889,0.01362794,0.04348961,-0.05370359,-0.02260884,0.01898797,0.03548534,-0.00694724,0.07436224,0.03084585,-0.05318319,-0.07231314,-0.01398398,-0.05776372,-0.00608372,-0.04630417,0.06832717,0.0140678],"last_embed":{"hash":"rr8a7o","tokens":459}}},"text":null,"length":0,"last_read":{"hash":"rr8a7o","at":1751426529318},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**","lines":[3,353],"size":27863,"outlinks":[{"title":"https://github.com/zorp-corp/nockchain","target":"https://github.com/zorp-corp/nockchain","line":291},{"title":"https://github.com/0xmoei/nockchain","target":"https://github.com/0xmoei/nockchain","line":292},{"title":"https://www.nockchain.org/","target":"https://www.nockchain.org/","line":293},{"title":"https://icoanalytics.org/projects/zorp-nockchain/","target":"https://icoanalytics.org/projects/zorp-nockchain/","line":294},{"title":"https://urbit.org/overview/history","target":"https://urbit.org/overview/history","line":295},{"title":"https://github.com/zorp-corp/jock-lang","target":"https://github.com/zorp-corp/jock-lang","line":296},{"title":"https://www.globenewswire.com/news-release/2024/12/30/3002639/0/en/Alpha-Sigma-Capital-Research-Unveils-2025-Crypto-Market-Predictions-AI-Agents-Staking-ETFs-and-Decentralized-Infrastructure-Lead-the-Way.html","target":"https://www.globenewswire.com/news-release/2024/12/30/3002639/0/en/Alpha-Sigma-Capital-Research-Unveils-2025-Crypto-Market-Predictions-AI-Agents-Staking-ETFs-and-Decentralized-Infrastructure-Lead-the-Way.html","line":297},{"title":"https://crypto-fundraising.info/projects/zorp-nockchain/","target":"https://crypto-fundraising.info/projects/zorp-nockchain/","line":298},{"title":"https://members.delphidigital.io/feed/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","target":"https://members.delphidigital.io/feed/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","line":299},{"title":"https://podwise.ai/dashboard/episodes/4062914","target":"https://podwise.ai/dashboard/episodes/4062914","line":300},{"title":"https://player.fm/series/the-delphi-podcast/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","target":"https://player.fm/series/the-delphi-podcast/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","line":301},{"title":"https://tracxn.com/d/companies/zorp/\\_\\_VTLVrft6NMSWw6R9NmmNgOwSmC1kn-XXhp2K\\_MTFXxM","target":"https://tracxn.com/d/companies/zorp/__VTLVrft6NMSWw6R9NmmNgOwSmC1kn-XXhp2K_MTFXxM","line":302},{"title":"https://www.zorp.one/","target":"https://www.zorp.one/","line":303},{"title":"https://www.zorp.one/about","target":"https://www.zorp.one/about","line":304},{"title":"https://en.wikipedia.org/wiki/End\\_of\\_the\\_World\\_(Parks\\_and\\_Recreation)","target":"https://en.wikipedia.org/wiki/End_of_the_World_\\(Parks_and_Recreation\\","line":305},{"title":"https://podcasts.apple.com/gr/podcast/the-delphi-podcast/id1438148082?l=el","target":"https://podcasts.apple.com/gr/podcast/the-delphi-podcast/id1438148082?l=el","line":306},{"title":"https://www.chaincatcher.com/en/article/2181453","target":"https://www.chaincatcher.com/en/article/2181453","line":307},{"title":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","target":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","line":308},{"title":"https://cryptorank.io/ico/nockchain","target":"https://cryptorank.io/ico/nockchain","line":309},{"title":"https://cryptorank.io/price/nockchain","target":"https://cryptorank.io/price/nockchain","line":310},{"title":"https://github.com/zorp-corp/nockapp","target":"https://github.com/zorp-corp/nockapp","line":311},{"title":"https://www.everand.com/podcast/864012011/Logan-Allen-Nockchain-s-Global-Competition-for-Useful-ZK-Proof-of-Work-Miners-Kicks-Off","target":"https://www.everand.com/podcast/864012011/Logan-Allen-Nockchain-s-Global-Competition-for-Useful-ZK-Proof-of-Work-Miners-Kicks-Off","line":312},{"title":"https://www.nervos.org/knowledge-base/what\\_are\\_blockchain\\_intents\\_(explainCKBot)","target":"https://www.nervos.org/knowledge-base/what_are_blockchain_intents_\\(explainCKBot\\","line":313},{"title":"https://volet.com/blog/post/understanding-intents-in-blockchain-a-comprehensive-guide-to-intents-01jdpzq69t52gfwxy6z0bx1nmv","target":"https://volet.com/blog/post/understanding-intents-in-blockchain-a-comprehensive-guide-to-intents-01jdpzq69t52gfwxy6z0bx1nmv","line":314},{"title":"https://crypto.com/en/university/intent-centric-design-for-blockchain","target":"https://crypto.com/en/university/intent-centric-design-for-blockchain","line":315},{"title":"https://www.bitget.com/zh-TC/news/detail/12560604756213","target":"https://www.bitget.com/zh-TC/news/detail/12560604756213","line":316},{"title":"https://www.youtube.com/watch?v=G5tE0LFFiTY","target":"https://www.youtube.com/watch?v=G5tE0LFFiTY","line":317},{"title":"https://ethplorer.io/address/******************************************","target":"https://ethplorer.io/address/******************************************","line":318},{"title":"https://phantom.com/tokens/solana/6tCEUogw2uk4VQz5JKb295ixFrj6vAPEQbZqL9VfFXSz","target":"https://phantom.com/tokens/solana/6tCEUogw2uk4VQz5JKb295ixFrj6vAPEQbZqL9VfFXSz","line":319},{"title":"https://www.dextools.io/app/en/solana/pair-explorer/ENDyunqABSwMvfUoRxsNA1N2QLhXeZQTYrnPZUQdnygP","target":"https://www.dextools.io/app/en/solana/pair-explorer/ENDyunqABSwMvfUoRxsNA1N2QLhXeZQTYrnPZUQdnygP","line":320},{"title":"https://web3.bitget.com/en/wiki/nock-wallet","target":"https://web3.bitget.com/en/wiki/nock-wallet","line":321},{"title":"https://m.sosovalue.com/blog/comprehensive-guide-to-bitcoin-mining","target":"https://m.sosovalue.com/blog/comprehensive-guide-to-bitcoin-mining","line":322},{"title":"https://www.cryptominerbros.com/blog/calculate-crypto-mining-profits/","target":"https://www.cryptominerbros.com/blog/calculate-crypto-mining-profits/","line":323},{"title":"https://asicmarketplace.com/blog/how-to-calculate-profit-in-crypto-mining/","target":"https://asicmarketplace.com/blog/how-to-calculate-profit-in-crypto-mining/","line":324},{"title":"https://www.rootdata.com/Projects/detail/Nockpool?k=MTc2Mzg%3D","target":"https://www.rootdata.com/Projects/detail/Nockpool?k=MTc2Mzg%3D","line":325},{"title":"https://en.wikipedia.org/wiki/Mining\\_pool","target":"https://en.wikipedia.org/wiki/Mining_pool","line":326},{"title":"https://podcasts.apple.com/us/podcast/the-delphi-podcast/id1438148082","target":"https://podcasts.apple.com/us/podcast/the-delphi-podcast/id1438148082","line":327},{"title":"https://icodrops.com/nockchain/","target":"https://icodrops.com/nockchain/","line":328},{"title":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA=","target":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","line":329},{"title":"https://github.com/0xmoei/nockchain/security","target":"https://github.com/0xmoei/nockchain/security","line":330},{"title":"https://dysnix.com/blockchain-security-audit","target":"https://dysnix.com/blockchain-security-audit","line":331},{"title":"https://www.lcx.com/how-to-conduct-blockchain-security-audit/","target":"https://www.lcx.com/how-to-conduct-blockchain-security-audit/","line":332},{"title":"https://veridise.com/audits/nft-security/","target":"https://veridise.com/audits/nft-security/","line":333},{"title":"https://immunebytes.com/blog/blockchain-security-audit-the-ultimate-guide/","target":"https://immunebytes.com/blog/blockchain-security-audit-the-ultimate-guide/","line":334},{"title":"https://hacken.io/services/blockchain-security/blockchain-protocol-security/","target":"https://hacken.io/services/blockchain-security/blockchain-protocol-security/","line":335},{"title":"https://quantstamp.com/audits","target":"https://quantstamp.com/audits","line":336},{"title":"https://www.reddit.com/r/CryptoCurrency/comments/1lhoxfc/outside\\_of\\_the\\_crypto\\_community\\_crypto\\_is\\_still/","target":"https://www.reddit.com/r/CryptoCurrency/comments/1lhoxfc/outside_of_the_crypto_community_crypto_is_still/","line":337},{"title":"https://www.reddit.com/r/CryptoMarkets/comments/1l5jqcu/which\\_crypto\\_community\\_still\\_feels\\_genuinely/","target":"https://www.reddit.com/r/CryptoMarkets/comments/1l5jqcu/which_crypto_community_still_feels_genuinely/","line":338},{"title":"https://www.reddit.com/r/CryptoMarkets/comments/1jakyzw/why\\_community\\_sentiment\\_of\\_every\\_crypto\\_on/","target":"https://www.reddit.com/r/CryptoMarkets/comments/1jakyzw/why_community_sentiment_of_every_crypto_on/","line":339},{"title":"https://www.everand.com/podcast/593966517/Launching-the-Mars-Review-of-Books-with-Noah-Kumin-Noah-Kumin-is-the-founder-and-editor-of-the-Mars-Review-of-Books","target":"https://www.everand.com/podcast/593966517/Launching-the-Mars-Review-of-Books-with-Noah-Kumin-Noah-Kumin-is-the-founder-and-editor-of-the-Mars-Review-of-Books","line":340},{"title":"https://podcasts.apple.com/bt/podcast/urbit-nockchain-and-the-current-state/id1195362330?i=1000709342627","target":"https://podcasts.apple.com/bt/podcast/urbit-nockchain-and-the-current-state/id1195362330?i=1000709342627","line":341},{"title":"https://www.nansen.ai/post/best-telegram-channels-for-crypto-chats","target":"https://www.nansen.ai/post/best-telegram-channels-for-crypto-chats","line":342},{"title":"https://ryanschultz.com/2018/07/17/the-telegram-discussion-groups-for-the-various-blockchain-based-vr-projects-have-been-endlessly-entertaining/","target":"https://ryanschultz.com/2018/07/17/the-telegram-discussion-groups-for-the-various-blockchain-based-vr-projects-have-been-endlessly-entertaining/","line":343},{"title":"https://www.reddit.com/r/CryptoCurrency/comments/lz1f54/beware\\_of\\_fake\\_pump\\_coins\\_groups\\_on\\_telegram/","target":"https://www.reddit.com/r/CryptoCurrency/comments/lz1f54/beware_of_fake_pump_coins_groups_on_telegram/","line":344},{"title":"https://asicmarketplace.com/blog/what-is-aleo/","target":"https://asicmarketplace.com/blog/what-is-aleo/","line":345},{"title":"https://risczero.com/blog/zkvm","target":"https://risczero.com/blog/zkvm","line":346},{"title":"https://www.rootdata.com/Projects/detail/RISC%20Zero?k=MjgwMw%3D%3D","target":"https://www.rootdata.com/Projects/detail/RISC%20Zero?k=MjgwMw%3D%3D","line":347},{"title":"https://en.foresightnews.pro/an-overview-of-privacy-solutions-based-on-zero-knowlege-cryptography/","target":"https://en.foresightnews.pro/an-overview-of-privacy-solutions-based-on-zero-knowlege-cryptography/","line":348},{"title":"https://equilibrium.co/writing/privacy-blockchains-and-aleo-deep-dive","target":"https://equilibrium.co/writing/privacy-blockchains-and-aleo-deep-dive","line":349},{"title":"https://www.blockchaincoinvestors.com/blog/risczero-eliminating-tradeoffs-and-unleashing-the-power-of-blockchain-technology-254ec","target":"https://www.blockchaincoinvestors.com/blog/risczero-eliminating-tradeoffs-and-unleashing-the-power-of-blockchain-technology-254ec","line":350},{"title":"https://podcasts.musixmatch.com/podcast/the-delphi-podcast-01hpa65hx48s2p8649mhbr8ke5/episode/logan-allen-nockchains-global-competition-for-useful-01jvmp9agdsppxbp1n6gwtz1ck","target":"https://podcasts.musixmatch.com/podcast/the-delphi-podcast-01hpa65hx48s2p8649mhbr8ke5/episode/logan-allen-nockchains-global-competition-for-useful-01jvmp9agdsppxbp1n6gwtz1ck","line":351}],"class_name":"SmartBlock","last_embed":{"hash":"rr8a7o","at":1751426529318}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**执行摘要**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05103851,-0.01593774,-0.05969016,-0.04263342,0.04558216,0.02210035,0.00661824,0.01095177,0.01054083,-0.0052582,0.06148769,-0.04786317,0.04475167,0.03358451,0.02807141,-0.02553138,0.01193281,-0.03597547,-0.01497909,0.02223625,0.11025219,-0.07032827,0.04087824,0.00124147,0.06946309,0.04358771,0.01738071,-0.02782949,-0.03741835,-0.17349955,0.00004618,-0.06662007,-0.0010052,0.02268387,0.01669155,-0.05458441,-0.02027869,0.06031328,-0.05002377,0.00161127,0.0020485,0.00310079,0.00803438,-0.0125692,0.04237954,-0.07057193,0.02894364,-0.06572566,0.01742407,-0.05561272,-0.03015753,-0.07525587,-0.00104427,0.03078854,0.0029406,0.01801863,0.00206197,0.03454408,0.01846276,0.03881482,0.07623358,0.06452919,-0.19188854,0.01060081,0.08134145,0.00030309,-0.03959388,0.02666912,0.03784032,0.07188202,0.01555968,-0.00203371,-0.01734996,0.05812159,0.02506622,-0.00578072,0.03378985,0.03754262,-0.02443477,-0.04309748,-0.07227871,0.09800579,-0.0029719,-0.01454152,-0.01729747,-0.03554074,-0.00814685,-0.04913942,-0.0437128,0.02314597,-0.01097276,0.0255826,0.03619416,-0.00125366,-0.01601626,-0.01986544,-0.01395443,0.01581045,-0.10788944,0.12370485,-0.04334053,0.03711214,0.00626782,-0.05322975,0.05103118,0.0098529,0.00671583,-0.05940892,0.02816425,0.04548225,-0.00246053,0.00711303,0.06633189,-0.04738567,0.02507556,0.02570666,-0.00778404,0.0002343,-0.01641231,0.0138757,-0.01369062,0.04292433,0.0371257,-0.0429486,-0.02841489,-0.02139482,0.01478817,0.04694651,0.04775038,0.02784581,0.01617988,0.0377647,-0.08675949,-0.00083199,-0.07586562,-0.03903194,-0.00949869,0.00010435,-0.02137255,-0.03317138,-0.03041559,-0.08409876,-0.02817873,-0.12419809,-0.06932599,0.07105099,-0.01198919,-0.00477603,0.00682623,-0.04568152,0.01544955,0.08594526,-0.04532892,-0.08989608,-0.03279094,0.00311861,0.03098834,0.11010443,-0.03406159,-0.00284467,-0.01123318,-0.04826853,-0.03437478,0.11455796,0.03219306,-0.1153545,0.04247262,0.02781592,-0.01698847,-0.06427877,0.00404986,0.0253208,-0.06011981,0.00523604,0.07496274,-0.05285106,-0.01481835,-0.02761334,0.0152023,0.05512918,0.00234523,-0.01416679,-0.08382194,-0.01682884,-0.01074193,-0.01893206,-0.02496666,0.0467485,0.04011308,0.04222484,-0.07474987,0.05545791,-0.01131434,0.0287276,-0.03064774,-0.00193297,-0.01548777,-0.01542689,0.02427097,-0.0555322,0.14449905,0.02684273,0.02649475,0.00973377,-0.06516864,0.01903881,-0.03823033,-0.04833773,0.00370813,0.02265075,-0.02154274,0.01712934,-0.02882122,0.04608248,-0.03147945,0.00082392,0.00855617,0.04561046,-0.03245882,0.05225648,0.01934141,0.04247877,-0.09461188,-0.18176018,-0.0417594,0.06308883,-0.00276286,0.02109016,0.01022808,0.03677707,0.02624469,0.04596107,0.10385648,0.09699121,0.06708985,-0.05022508,0.0034301,-0.01619009,0.04753113,0.06634192,-0.00592548,-0.00458064,0.03934751,-0.03780448,0.02357021,0.00009567,-0.03082856,0.0500653,-0.0593063,0.10989155,-0.0261319,-0.00456604,0.00172425,0.07333779,0.01055,-0.03517534,-0.16343723,0.03463237,0.09265988,-0.01599724,-0.01458777,-0.05077061,-0.02223472,-0.02567497,0.01577032,0.04986313,-0.09842447,0.01027232,-0.07165235,-0.03283264,-0.04598643,-0.00622747,0.03351712,0.01873122,0.01780242,0.03165711,0.11403838,-0.01271648,-0.01230577,0.02155867,-0.06602009,-0.00616885,0.03204625,-0.00691402,-0.01342318,-0.01347888,-0.000591,0.0193472,-0.01681372,-0.02363055,0.02938389,0.02821019,-0.01382152,-0.03377045,0.14282644,0.02449303,0.00465365,0.05543743,0.01317828,-0.05844856,-0.08794595,0.05330122,-0.00789185,0.02166892,0.01655455,0.02774885,0.04376506,-0.013417,0.04876856,0.02003258,-0.00866853,0.03740965,0.00767166,-0.00779147,-0.05229088,-0.0459027,-0.03187717,0.06770678,0.0212309,-0.28723532,-0.04744205,-0.02536581,-0.02673171,0.07686824,0.03142155,0.05706089,0.03194352,-0.05239984,0.03004179,0.01382926,0.03032065,-0.01280403,-0.06971238,0.01169884,-0.01725371,0.0398792,-0.00478082,0.02049614,0.05827531,-0.00007017,0.05991426,0.23055048,-0.01663918,0.05107854,0.02507403,-0.0469396,0.04242917,0.01840062,0.00358424,-0.04415086,-0.00809141,0.04372784,-0.01253866,0.01837441,0.01886639,-0.00854879,-0.00901919,-0.00858271,-0.00183546,-0.04056806,-0.00267905,-0.03197319,0.03029543,0.11434399,0.00840041,-0.05559152,-0.06774588,0.00936621,0.04820387,-0.05421098,-0.02418332,0.01596539,0.03616777,-0.01078267,0.07308018,0.03418251,-0.05336887,-0.07200357,-0.01523764,-0.05484756,-0.00598292,-0.04671997,0.06908589,0.01433272],"last_embed":{"hash":"as5obu","tokens":508}}},"text":null,"length":0,"last_read":{"hash":"as5obu","at":1751426529400},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**执行摘要**","lines":[5,67],"size":3095,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"as5obu","at":1751426529400}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**执行摘要**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05075523,-0.01537927,-0.0587567,-0.04083843,0.04749567,0.02299095,0.00620504,0.00986748,0.01358193,-0.00466917,0.06008487,-0.04734343,0.04656285,0.03251187,0.02733407,-0.02424925,0.01221938,-0.03547793,-0.01513101,0.01982235,0.1100627,-0.07232144,0.04119069,0.0030811,0.06906331,0.04201839,0.01464279,-0.02571516,-0.03700416,-0.17233722,-0.00188991,-0.0670623,0.00137665,0.02139596,0.0201601,-0.05396393,-0.02048061,0.06074897,-0.0508731,-0.00000166,0.00273957,0.00494951,0.01020008,-0.01306045,0.03861521,-0.06793846,0.03104243,-0.06642423,0.01590556,-0.05706741,-0.03043441,-0.07715656,-0.00175779,0.03246213,0.00300466,0.01875969,-0.00074627,0.0351695,0.01658723,0.03762065,0.07726926,0.06228289,-0.19287643,0.00870141,0.08274736,0.00144293,-0.04012877,0.02340903,0.03693251,0.07365355,0.01527979,-0.00265166,-0.01886807,0.05620144,0.0260604,-0.00654306,0.03305986,0.03574448,-0.0253045,-0.04546981,-0.07278957,0.09814706,-0.00084245,-0.0134574,-0.01733319,-0.03329949,-0.00848915,-0.04976126,-0.04159634,0.02428557,-0.00980292,0.02393468,0.03547867,0.0011339,-0.01672617,-0.02124293,-0.01301793,0.01480185,-0.10748105,0.1250096,-0.04328409,0.03560188,0.0064001,-0.05437315,0.04821168,0.01197398,0.0075915,-0.05861734,0.02874754,0.04594761,-0.00189597,0.00623509,0.06627952,-0.05059006,0.02353744,0.02705478,-0.00555412,0.00089929,-0.01568697,0.01197461,-0.01237934,0.04153952,0.03595447,-0.04257294,-0.02842526,-0.0210428,0.01408973,0.04695371,0.04864937,0.0287202,0.01723103,0.0380186,-0.08915942,-0.00111951,-0.07397016,-0.03931794,-0.00931034,0.002599,-0.02073935,-0.03507725,-0.03046831,-0.08126377,-0.02859893,-0.12542751,-0.07098058,0.07086653,-0.01193531,-0.00360238,0.00734107,-0.04542435,0.01495949,0.08531529,-0.04442437,-0.08995853,-0.034844,0.00323563,0.03195646,0.11103679,-0.03510117,-0.00235316,-0.01156961,-0.0464256,-0.03508966,0.11557092,0.03320988,-0.1131099,0.04566301,0.02808467,-0.0167945,-0.0614347,0.00579887,0.02643413,-0.06072634,0.00504262,0.0760026,-0.05283294,-0.01553998,-0.02384769,0.01297274,0.05684449,0.00088953,-0.01622279,-0.08203085,-0.01473766,-0.00911425,-0.01847453,-0.02508371,0.0470411,0.03998109,0.04259204,-0.07825124,0.0552894,-0.01099073,0.02897545,-0.03294082,-0.00185705,-0.0142374,-0.01417006,0.02274046,-0.0552832,0.14724964,0.02604852,0.02546901,0.01151779,-0.06602465,0.01978361,-0.03690217,-0.05059772,0.00441268,0.02487501,-0.02180292,0.01807809,-0.03061757,0.04734407,-0.02946833,0.00354764,0.00997187,0.04582939,-0.03443359,0.05188688,0.01897422,0.0417054,-0.09538616,-0.1821723,-0.04315717,0.06238461,-0.0002925,0.01823414,0.00971564,0.03789641,0.02639194,0.04422249,0.09940753,0.09457643,0.0682041,-0.05059182,0.00331699,-0.01761857,0.04890867,0.06523138,-0.00543969,-0.0039906,0.03894041,-0.03646966,0.02488459,-0.00010569,-0.02965349,0.05303271,-0.05839747,0.10898308,-0.03067985,-0.00221964,0.00326239,0.07509108,0.00949757,-0.03509763,-0.16161251,0.0335478,0.08989684,-0.0201635,-0.01647575,-0.05273622,-0.02129241,-0.02506654,0.01487766,0.04825366,-0.09571861,0.01133591,-0.07031962,-0.03150034,-0.04534537,-0.00664144,0.03257016,0.01743841,0.01721205,0.03393506,0.11545491,-0.01198235,-0.01301907,0.02053072,-0.06526136,-0.00864116,0.03136129,-0.00697729,-0.01427916,-0.01325454,0.00118841,0.01577702,-0.01596183,-0.02266787,0.02939215,0.02830721,-0.01251282,-0.03456996,0.14465296,0.02593217,0.00418842,0.05526317,0.01277559,-0.05800316,-0.08958049,0.05284701,-0.00881565,0.02293257,0.01828739,0.02652714,0.04504561,-0.01408062,0.04801737,0.01921572,-0.00866376,0.03656828,0.00889693,-0.01030574,-0.05095856,-0.04773489,-0.03425494,0.06772551,0.0217373,-0.28634951,-0.0482117,-0.02556701,-0.02456056,0.07647317,0.02753926,0.05550459,0.03206612,-0.05279605,0.02812167,0.01350032,0.03066713,-0.01278811,-0.0704646,0.01163543,-0.01899903,0.04192327,-0.00471283,0.02280935,0.06140562,0.00034369,0.05824208,0.22854668,-0.0157761,0.05026848,0.02223377,-0.04501222,0.04070267,0.01785368,0.00286685,-0.04538501,-0.00700773,0.04807563,-0.0110175,0.01881476,0.01986191,-0.00969858,-0.00992646,-0.00997959,-0.00153646,-0.04118207,-0.00266442,-0.03199075,0.03025291,0.11722734,0.00924217,-0.05313126,-0.0686134,0.00927858,0.04810644,-0.05564337,-0.02640307,0.01584646,0.0356749,-0.01061967,0.07317968,0.03528487,-0.05526665,-0.07427882,-0.01612412,-0.05377058,-0.00507639,-0.04729467,0.06774098,0.01433703],"last_embed":{"hash":"kzn3cv","tokens":509}}},"text":null,"length":0,"last_read":{"hash":"kzn3cv","at":1751426529421},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**执行摘要**#{1}","lines":[7,67],"size":3081,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"kzn3cv","at":1751426529421}},
"smart_blocks:web3/report/Nockchain.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04663917,-0.01818995,-0.07635425,-0.04624635,0.00154637,0.02231302,0.01375802,-0.01023918,-0.00964483,-0.02242311,0.03647067,-0.03807211,0.03402977,0.02917323,0.01337817,-0.00808741,0.01354686,-0.02624848,-0.02200089,0.01149512,0.11317796,-0.06438822,0.02439696,-0.00127321,0.08207938,0.06548238,0.00994845,-0.0220892,-0.03211604,-0.16747308,-0.00548556,-0.07762658,0.0097457,0.023516,0.02225978,-0.04562917,-0.0341473,0.05954249,-0.07486965,-0.00983241,0.00828137,0.02289246,0.02602634,-0.0024759,0.03760241,-0.08240211,0.02780289,-0.05497737,-0.01013976,-0.07537292,-0.04220994,-0.06563813,-0.015792,0.03678241,-0.00581283,0.01824121,0.00421447,0.06921904,0.0280054,0.03560845,0.06796051,0.05794322,-0.16192673,0.0048015,0.08294278,0.01522365,-0.02845609,0.0122436,0.07009929,0.06673363,0.02371651,-0.01511541,-0.01792531,0.04891355,0.02262081,0.00772411,0.028803,0.01145769,-0.03644881,-0.05429723,-0.05738411,0.05913054,0.03670897,-0.0000287,-0.03509957,-0.00348547,-0.02376805,-0.03503898,-0.02854177,0.01606922,0.00757476,0.02095176,0.04641681,-0.00788165,-0.03223914,-0.02313645,-0.02944965,0.03507214,-0.09369345,0.12446835,-0.03707335,0.03311427,-0.0229885,-0.0429265,0.03769693,-0.00474366,-0.01122835,-0.06739657,0.01918503,0.02534417,0.00733566,0.02256051,0.0801092,-0.04931711,0.02217663,0.02043587,0.02422988,0.00511098,-0.02056921,0.01675409,0.0055084,0.05649772,0.03564177,-0.04395584,-0.035342,-0.0091043,0.01835989,0.05183687,0.04754467,0.05469362,0.00176527,0.03263339,-0.06058317,0.00065467,-0.04276823,-0.04119146,-0.02519244,0.03515167,-0.02775746,-0.02950842,-0.02616995,-0.06471925,-0.02545958,-0.11598608,-0.0601324,0.06048158,-0.01045395,0.01935245,-0.00321582,-0.05578943,0.01596211,0.08081999,-0.04815622,-0.06899709,-0.01236415,-0.00886923,0.01805587,0.08841499,-0.05299637,0.00496142,-0.02841116,-0.02975135,-0.03059363,0.12909588,0.01487863,-0.1181894,0.05372427,0.03970481,-0.01015558,-0.06206077,0.00843692,0.03026082,-0.06609762,-0.02161079,0.04803365,-0.04978986,-0.03620869,-0.01904563,0.0142196,0.0551098,-0.00195994,-0.02667533,-0.09603892,-0.00755475,-0.0083761,-0.02216143,-0.01342933,0.02959129,0.04789457,0.0314318,-0.10009243,0.02677657,0.00875109,0.02789522,-0.06535464,-0.01776629,-0.01382482,-0.02159395,0.0115811,-0.07234942,0.15747218,0.03366971,0.01615598,0.01646879,-0.05228613,0.01608058,-0.03308546,-0.06819391,0.00504665,0.0484742,-0.03048347,0.03605758,-0.0305329,0.04995769,-0.02171654,0.0179469,0.00609536,0.03461219,-0.01961612,0.04362652,0.03094543,-0.00360742,-0.08879048,-0.19173123,-0.05399739,0.04770403,0.00332356,0.01890934,-0.00637515,0.02974476,0.00649824,0.03905163,0.09193464,0.09170777,0.05887306,-0.07229762,0.01106374,-0.01165874,0.05107447,0.04852179,-0.01464372,0.00766019,0.04035658,-0.04179796,0.02078669,0.0115632,-0.04514029,0.04519729,-0.05379551,0.11387827,-0.03087229,0.00908297,0.02095712,0.07399475,0.00548192,-0.0306471,-0.14044388,0.03015517,0.08697656,-0.03407312,-0.01046758,-0.0269306,-0.03578194,0.00110883,0.01595941,0.02858958,-0.10557636,0.023062,-0.05450183,-0.03310795,-0.03318073,-0.00045034,0.05962652,0.00383193,0.05092299,0.03391232,0.11616003,-0.01056603,-0.00694271,0.03536901,-0.06271482,-0.01191207,0.03801016,-0.01646395,-0.01854355,-0.02552982,-0.00123601,0.02038399,0.00096279,-0.01725973,0.01699144,0.04652809,-0.02767246,-0.04558647,0.16752669,0.01485782,-0.03456865,0.04759178,0.03328561,-0.06520226,-0.10221759,0.06750149,0.00061714,0.02944586,0.02557199,0.03570758,0.04820223,-0.01183403,0.06944165,0.00736116,-0.00580531,0.02009184,0.00658231,-0.00131287,-0.04142987,-0.03451703,-0.05124423,0.08196076,0.00846534,-0.2912775,-0.04282486,-0.02259769,-0.00566534,0.05675438,0.01543856,0.04948971,0.02918743,-0.08282294,0.01398316,-0.02117895,0.03126701,0.01412183,-0.06620061,0.02581188,-0.00964447,0.04617879,-0.01828857,0.02760421,0.04869386,0.01114297,0.06430921,0.23977818,-0.0075875,0.04669139,0.02086685,-0.04595301,0.05711392,0.045039,0.0298427,-0.03015285,-0.01368612,0.03300303,-0.01990101,0.02772656,0.01237018,-0.01457978,-0.00991031,-0.00386784,-0.01525593,-0.02721344,-0.00960697,-0.01304269,0.01676004,0.12266508,-0.0086993,-0.03157074,-0.06064346,0.0139088,0.03899913,-0.05836148,-0.03174339,0.02100506,0.02833872,-0.01782178,0.06008317,0.0330267,-0.04088277,-0.08056445,-0.02106436,-0.03561789,-0.01805397,-0.02204121,0.08125975,0.02191967],"last_embed":{"hash":"126582n","tokens":456}}},"text":null,"length":0,"last_read":{"hash":"126582n","at":1751426529445},"key":"web3/report/Nockchain.md#---frontmatter---","lines":[15,66],"size":2410,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"126582n","at":1751426529445}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07900704,-0.02144109,-0.07586298,-0.03965375,-0.01784174,0.01817488,0.00054301,0.00287366,-0.0182609,-0.02562558,0.0567826,-0.03761205,0.02127791,0.02849599,0.0159402,-0.01346937,0.00712744,-0.03053653,-0.03765666,0.01410457,0.11049599,-0.05845593,0.03597287,-0.01271637,0.08886115,0.07119797,0.02723611,0.00430852,-0.00027747,-0.18184318,-0.00823821,-0.07957835,-0.00195253,0.03725177,0.03101865,-0.06776368,-0.03173145,0.06507075,-0.06860513,0.00573127,0.00204413,0.02314996,0.0015609,-0.01260695,0.0382675,-0.08105922,0.02814493,-0.07740863,-0.01418432,-0.06162691,-0.02970406,-0.07012315,-0.01335845,0.05253872,-0.00461962,-0.00333555,0.02942102,0.06114177,0.01656835,0.02266323,0.045419,0.06146365,-0.17760301,0.01112885,0.09272677,0.01550659,-0.02577097,0.00824467,0.04499882,0.06338271,0.02240879,-0.01331992,-0.01550274,0.04868668,0.02607704,0.01285341,0.02987211,-0.00006755,-0.05592198,-0.05558418,-0.06701839,0.06322995,0.03577363,0.02098562,-0.02514105,0.00162211,-0.00528772,-0.03395756,0.00098105,0.024446,0.00128974,0.01604997,0.03422087,-0.00344101,-0.04023124,0.01487938,-0.02083316,0.03989538,-0.09148171,0.11937097,-0.04587515,0.047553,-0.01949566,-0.03613333,0.02728715,-0.02292347,-0.01570949,-0.04754319,0.00664811,0.01428739,0.00078569,0.01210964,0.0651154,-0.03100647,0.01690447,0.00632515,0.02567037,0.00987152,-0.01522371,0.01673086,0.02272901,0.05189181,0.02351923,-0.02801074,-0.04387608,-0.01550103,0.01687085,0.05339174,0.05989668,0.04565873,0.02300803,0.03460583,-0.03301056,0.01560109,-0.04753924,-0.0368236,-0.00617417,0.04569561,-0.02013215,-0.05315886,0.00632236,-0.07592809,-0.02950508,-0.10506475,-0.07323551,0.06270826,-0.01846704,0.02594645,0.01877915,-0.05909053,0.01613632,0.07897824,-0.03640011,-0.06485054,-0.00516894,-0.00088378,0.0398265,0.08956107,-0.06655236,0.01748435,-0.02569009,-0.01734193,-0.05267188,0.11968658,0.02581166,-0.10649832,0.05544099,0.03533385,-0.01008944,-0.08748806,0.00724422,0.01161059,-0.05585227,-0.01584673,0.07125886,-0.05192908,-0.04819266,-0.00002473,0.01974693,0.06109535,0.01281385,-0.0242453,-0.10595682,0.0050731,0.00064433,-0.02660411,-0.01417779,0.04169074,0.03577326,0.03214332,-0.05771318,0.03972954,-0.01306016,0.01989069,-0.04273378,-0.03105704,-0.01894595,-0.01334994,0.02011901,-0.08182669,0.13476823,0.03601952,0.01469211,0.00515259,-0.0569272,-0.01876416,-0.02590936,-0.0396201,0.00693697,0.04990422,-0.02344331,0.03164082,-0.04735802,0.04330837,0.00134082,0.03438095,-0.00984983,0.03771767,-0.01689548,0.034608,0.02080457,0.00468399,-0.07830103,-0.17616665,-0.04974226,0.03735358,0.01222499,0.0277514,0.00533501,0.0293302,0.00188338,0.02789005,0.06592771,0.0906923,0.04715304,-0.06435091,0.01679162,-0.02099687,0.03717417,0.04856718,-0.01505206,0.00588853,0.04686541,-0.03555302,0.01833311,0.01044106,-0.03687543,0.07560027,-0.08099024,0.09268049,-0.03530869,-0.00017847,0.01860706,0.08539411,0.00678278,-0.02433074,-0.1619821,0.04296981,0.09833645,-0.0489954,-0.01206965,-0.03355282,-0.02050991,0.02018784,0.0273101,0.01919941,-0.11946595,0.03886802,-0.04951482,-0.05120613,-0.01028202,-0.01644085,0.04627135,-0.00768729,0.06234528,0.03107274,0.11189813,-0.01332789,-0.02168532,0.02432942,-0.02504056,-0.02335111,0.03299757,-0.00701673,-0.01435225,-0.0406538,0.02408988,0.0334857,0.01177067,-0.01423783,0.02604171,0.04797757,-0.02860738,-0.04008084,0.15172131,0.0233242,-0.03717233,0.05394123,0.03686168,-0.07258002,-0.09677548,0.06861152,-0.00166046,0.02627374,0.01098316,0.03796896,0.0563691,0.00984634,0.06123973,0.02265041,-0.00353771,0.02682241,-0.01794443,-0.01271284,-0.06224658,-0.02375264,-0.03309324,0.08998687,0.02005843,-0.30156747,-0.03082222,-0.03792296,-0.01139256,0.01715953,0.01530978,0.03275493,0.03414068,-0.09524494,0.02242305,-0.03433174,0.03465602,0.00002021,-0.04505705,0.02170237,0.00498852,0.04176662,-0.03034625,0.02898446,0.03671968,-0.00817889,0.07798837,0.23109902,0.00517241,0.05795889,0.00291799,-0.02511162,0.05116917,0.0394327,0.02187894,-0.0333925,-0.01165442,0.02655518,-0.02189495,0.00333058,0.03798281,-0.02622774,-0.00653661,-0.01925032,-0.00860167,-0.03430994,-0.017722,-0.03350686,0.0290962,0.11355724,-0.01235386,-0.04186347,-0.07290154,0.0050177,0.02993295,-0.0502515,-0.03536316,0.03099691,0.0165307,-0.01400933,0.05605513,0.04103538,-0.04193043,-0.08960812,-0.0175612,-0.03278277,0.00389353,-0.03170735,0.0800957,-0.00188364],"last_embed":{"hash":"3ld2vn","tokens":463}}},"text":null,"length":0,"last_read":{"hash":"3ld2vn","at":1751426529462},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**","lines":[68,99],"size":1927,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"3ld2vn","at":1751426529462}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.1. 共识引擎：zkPoW \\- 有用工作量证明**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07810305,-0.02153785,-0.07476965,-0.04007076,-0.01787205,0.01761979,-0.00207611,0.00389592,-0.01777773,-0.02440899,0.05617672,-0.03665389,0.01944231,0.02695729,0.01529518,-0.01458765,0.00555148,-0.03110345,-0.03764704,0.01515392,0.11206354,-0.05928548,0.03501488,-0.01220398,0.0895795,0.07169294,0.02381323,0.00584085,-0.00019753,-0.18143666,-0.0080529,-0.07942215,-0.00269731,0.03672599,0.03350425,-0.06602325,-0.03269919,0.06460477,-0.06893022,0.00469351,0.00090877,0.02311667,0.00230567,-0.01262829,0.03800384,-0.08231248,0.02701076,-0.07848335,-0.0142685,-0.06118618,-0.02787954,-0.07062853,-0.01226769,0.05374284,-0.00319775,-0.0075161,0.0297258,0.05736168,0.01845367,0.02126533,0.04633675,0.06032776,-0.178698,0.01086581,0.09533618,0.01682026,-0.02611529,0.00963489,0.04436046,0.06424593,0.02206119,-0.01370997,-0.01624352,0.04817139,0.02732893,0.01164028,0.02993599,-0.00057891,-0.05702307,-0.05647877,-0.06776201,0.06162346,0.03720872,0.02272242,-0.02604158,0.00064888,-0.00599923,-0.03382256,0.00209471,0.02392011,0.0008266,0.01584613,0.03415006,-0.00325443,-0.03982944,0.01542497,-0.02196593,0.03914238,-0.08970233,0.11902466,-0.04477432,0.04897331,-0.01777529,-0.03628506,0.02579428,-0.02159714,-0.0175951,-0.04762186,0.00475463,0.01454965,0.00181453,0.01412112,0.0662763,-0.03107907,0.01820732,0.00444089,0.0247775,0.01069705,-0.01693197,0.01951286,0.02469176,0.05134335,0.0213946,-0.02868564,-0.04440543,-0.01574975,0.01823459,0.05393369,0.05951127,0.04495604,0.02375397,0.03474323,-0.0328984,0.01710422,-0.04908573,-0.03655935,-0.00314633,0.04501099,-0.01996044,-0.05542552,0.00896145,-0.07542788,-0.03047962,-0.10567112,-0.07105868,0.06309818,-0.01964542,0.02481738,0.01940741,-0.05685972,0.015037,0.07818678,-0.03846396,-0.06230513,-0.00562847,-0.00215261,0.04037185,0.08969228,-0.06799384,0.01686299,-0.0256083,-0.01699393,-0.05157817,0.11848417,0.02819858,-0.10696837,0.05478543,0.0337088,-0.01068727,-0.08768802,0.00464153,0.01217186,-0.05619521,-0.01617891,0.07128917,-0.04959133,-0.04648747,0.00031894,0.01877109,0.06198926,0.01224232,-0.02516142,-0.10464294,0.00603383,0.00136371,-0.02412709,-0.01191521,0.04159256,0.03462869,0.03468628,-0.05908113,0.03933548,-0.01513796,0.01886655,-0.04402366,-0.03214348,-0.01962276,-0.0144474,0.0197287,-0.08234829,0.1343298,0.03784902,0.01579014,0.00616753,-0.05730402,-0.01792547,-0.02780074,-0.04030414,0.00630104,0.04816774,-0.02460872,0.02941876,-0.04699966,0.04460214,0.00108737,0.03388682,-0.00993375,0.03711897,-0.01741765,0.03361743,0.01905205,0.00178245,-0.07628795,-0.1764079,-0.05324682,0.037109,0.0115741,0.02743056,0.0044821,0.02893047,0.00111601,0.02869436,0.06574062,0.09429043,0.04600899,-0.06415744,0.01558324,-0.01875928,0.03786936,0.04709514,-0.014488,0.00531147,0.04727719,-0.0348091,0.01607367,0.01188259,-0.03758322,0.07598391,-0.08178113,0.09375603,-0.03327572,-0.00039161,0.0186445,0.08401398,0.005542,-0.02490544,-0.16196923,0.04355063,0.09656943,-0.04755537,-0.01318427,-0.03269251,-0.02070462,0.01684187,0.02673304,0.02007473,-0.11906403,0.03815166,-0.04941867,-0.04970972,-0.00969294,-0.01747717,0.04927849,-0.00838283,0.06314603,0.0306525,0.11199912,-0.01232524,-0.02135497,0.02471839,-0.02426322,-0.02230215,0.03384911,-0.00565085,-0.01452811,-0.04179078,0.02472223,0.0325694,0.0117534,-0.01373167,0.02640663,0.04864898,-0.02678297,-0.03880006,0.15076037,0.02296196,-0.03824643,0.05328339,0.03890321,-0.07220187,-0.095846,0.06874591,-0.00120178,0.02580887,0.01020488,0.03744595,0.05687503,0.01010962,0.06236138,0.02291628,-0.0033032,0.02737786,-0.01816914,-0.01374183,-0.06271555,-0.0230232,-0.03110634,0.08689755,0.02097683,-0.30146569,-0.03253834,-0.03656705,-0.01118865,0.01613483,0.01384012,0.03478766,0.03565598,-0.09537186,0.02365005,-0.03608248,0.03477094,-0.00080055,-0.0419363,0.02090786,0.00308404,0.04108295,-0.02846525,0.02867276,0.03582905,-0.00840867,0.07827206,0.23188092,0.00604682,0.05933495,0.0005599,-0.02342148,0.0525493,0.03945041,0.02171972,-0.03379972,-0.01123242,0.02616108,-0.02205946,0.0038259,0.03769887,-0.0252324,-0.00670368,-0.0184768,-0.00742866,-0.03483775,-0.01775554,-0.03140417,0.02725279,0.11493441,-0.01150762,-0.0421298,-0.07473411,0.00663388,0.02998128,-0.05235441,-0.03542041,0.03020051,0.01602311,-0.01206396,0.05730164,0.04110633,-0.041132,-0.0898641,-0.01637198,-0.03341737,0.00507279,-0.031292,0.08142587,-0.00209518],"last_embed":{"hash":"16aj6q6","tokens":460}}},"text":null,"length":0,"last_read":{"hash":"16aj6q6","at":1751426529487},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.1. 共识引擎：zkPoW \\- 有用工作量证明**","lines":[70,75],"size":668,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"16aj6q6","at":1751426529487}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.1. 共识引擎：zkPoW \\- 有用工作量证明**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07786267,-0.02081405,-0.07387987,-0.03780781,-0.01777966,0.01783356,-0.00131408,0.00456914,-0.01956535,-0.02524074,0.05742804,-0.03525279,0.02073065,0.0277532,0.01591767,-0.01533706,0.00462222,-0.0308859,-0.0371563,0.0129317,0.11083857,-0.05959045,0.03610738,-0.01278529,0.0887959,0.0722599,0.02590862,0.00370244,0.0002102,-0.18182087,-0.00829357,-0.08025964,-0.00195809,0.03878661,0.03319542,-0.06760065,-0.03154563,0.06745723,-0.06871907,0.00546867,0.00031387,0.02135924,0.0009246,-0.01126882,0.03868194,-0.08012944,0.0286065,-0.07698619,-0.01439838,-0.05919754,-0.02830781,-0.07083811,-0.01475687,0.05297495,-0.00275698,-0.00650573,0.02654137,0.05850593,0.01448997,0.02164733,0.04509312,0.06091414,-0.1803837,0.01286667,0.09260809,0.01627385,-0.02628855,0.00766892,0.04600613,0.06361564,0.0230277,-0.01492669,-0.0174564,0.0473401,0.0269715,0.01357591,0.0268396,0.0002588,-0.05448848,-0.05550573,-0.06927379,0.06400214,0.03834067,0.02119923,-0.02642724,0.00275343,-0.00561324,-0.0354839,0.00501656,0.02522288,0.00067272,0.01343154,0.03337239,-0.00406898,-0.04111296,0.01589264,-0.02136571,0.0424579,-0.08871178,0.11962636,-0.04696104,0.04793602,-0.02022579,-0.03515319,0.02661718,-0.0232689,-0.01566347,-0.04606261,0.00710932,0.01354102,0.00052608,0.0143865,0.0637328,-0.02969705,0.01733196,0.00434474,0.02496118,0.01243472,-0.01527788,0.01897205,0.02459714,0.05088441,0.02049478,-0.02799919,-0.04481498,-0.01384716,0.01945553,0.05383899,0.06178815,0.04294077,0.02334395,0.03481276,-0.03230974,0.01771435,-0.0478288,-0.03742211,-0.00287234,0.04688885,-0.01983604,-0.05483482,0.00835287,-0.07590162,-0.03046321,-0.10206522,-0.07206821,0.06409105,-0.02020952,0.02881156,0.01623826,-0.06018066,0.01562251,0.07761341,-0.03856923,-0.06175828,-0.0020867,-0.0028275,0.04091252,0.08979196,-0.06765233,0.01658492,-0.02656271,-0.01678562,-0.05062883,0.11762962,0.027353,-0.10471483,0.05508757,0.03611436,-0.01032604,-0.08842664,0.00657886,0.01025946,-0.05676562,-0.01672668,0.07244736,-0.04979753,-0.04742209,0.00068867,0.01868766,0.06137273,0.01259743,-0.02549963,-0.10574779,0.00327213,0.00227766,-0.02684752,-0.01130346,0.04219779,0.0341071,0.03495576,-0.05699084,0.04106471,-0.01426218,0.02006607,-0.04358917,-0.03240501,-0.01887232,-0.01505866,0.02036167,-0.08240989,0.13282111,0.03686317,0.01598678,0.00423812,-0.05689991,-0.01808266,-0.0249465,-0.03936188,0.00909284,0.04824044,-0.02332545,0.03045652,-0.04868281,0.04177759,0.00162037,0.03575653,-0.01074244,0.0361034,-0.01718449,0.03645156,0.01822037,0.00350347,-0.0750365,-0.17756724,-0.0503375,0.0373606,0.01262811,0.02524688,0.00421753,0.02914106,0.0019025,0.02863155,0.06619113,0.09161834,0.04400623,-0.0641479,0.01556789,-0.01749439,0.03745826,0.04706066,-0.01585406,0.00436781,0.0450136,-0.03431569,0.017011,0.00964557,-0.03866574,0.07631709,-0.08166623,0.09219278,-0.03552386,-0.00141886,0.01866872,0.0838632,0.00649097,-0.02433333,-0.16510506,0.04343539,0.09622131,-0.05116095,-0.01180356,-0.03612864,-0.02021297,0.01849519,0.02513398,0.01736579,-0.11793278,0.03811728,-0.05007178,-0.04922545,-0.01087068,-0.01663087,0.04965471,-0.00840859,0.06267439,0.03204356,0.110879,-0.01365036,-0.02362863,0.02452766,-0.0251551,-0.02536901,0.03398475,-0.00708086,-0.01249238,-0.04236013,0.02266045,0.03184176,0.01263087,-0.01640864,0.02541496,0.04720066,-0.02620123,-0.03981689,0.15258515,0.02362316,-0.03792447,0.05243174,0.03771123,-0.07406864,-0.09630447,0.06931941,-0.00164629,0.02554072,0.01121999,0.0384738,0.05593381,0.01148268,0.06118826,0.0234864,-0.00230591,0.02842184,-0.0163561,-0.01528071,-0.06102586,-0.02384087,-0.02981912,0.08679275,0.02153493,-0.30177298,-0.03069414,-0.03550721,-0.01140331,0.01355833,0.01371312,0.03119786,0.03508574,-0.09248284,0.02225341,-0.03338806,0.0344058,-0.00112726,-0.04492225,0.02228518,0.0053392,0.04195468,-0.03034634,0.03112711,0.03792694,-0.01020829,0.0791758,0.2327629,0.00645899,0.05978499,0.00037378,-0.02531756,0.05082527,0.03924488,0.02396395,-0.03257225,-0.01210513,0.02616651,-0.02254775,0.00037623,0.04011262,-0.02722388,-0.0071679,-0.02022616,-0.00736468,-0.03472825,-0.0186771,-0.03421685,0.02791951,0.11769346,-0.01056368,-0.04157696,-0.07290709,0.00544429,0.03162272,-0.0510045,-0.03429792,0.02961935,0.01739456,-0.0149854,0.05631702,0.03989172,-0.04031927,-0.08885012,-0.01720346,-0.03292368,0.00672538,-0.03059518,0.07920758,0.00086986],"last_embed":{"hash":"13grky3","tokens":461}}},"text":null,"length":0,"last_read":{"hash":"13grky3","at":1751426529502},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.1. 共识引擎：zkPoW \\- 有用工作量证明**#{1}","lines":[72,75],"size":631,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"13grky3","at":1751426529502}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.2. 虚拟机：基于 Nock ISA 的新型 ZKVM**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03491699,-0.0150146,-0.06890669,-0.0324033,0.02486336,0.0239527,-0.0172529,0.00618618,-0.00462423,-0.00190208,0.07122811,-0.05408666,0.01841665,0.02536999,0.03014261,-0.01262685,0.0330269,-0.05203096,-0.01697484,0.02438687,0.06790978,-0.06517383,0.03555743,-0.01313383,0.06387485,0.06310243,0.02525784,0.00377068,-0.04264465,-0.1841604,-0.01988478,-0.08109567,0.00970679,0.02700558,0.00849676,-0.06420124,-0.04877551,0.05382795,-0.05410511,0.00228803,0.00853997,0.00277607,0.01973727,0.00370025,0.02357322,-0.08976381,0.04746107,-0.06709962,-0.00566018,-0.07286349,-0.04850406,-0.0825929,-0.02028433,0.03619791,-0.00670892,0.00525391,0.01057488,0.04180691,0.02754395,0.0167397,0.0823011,0.04958361,-0.18849355,0.00648949,0.07803977,0.02017431,-0.0525092,0.01317771,0.0550903,0.08314862,0.00229227,0.00513484,-0.02861682,0.04438799,0.02473786,-0.01156174,0.01844868,0.02110461,-0.02826988,-0.06287926,-0.06881844,0.08240975,0.01296917,-0.02765672,-0.00255944,-0.01861269,-0.01701358,-0.04490051,-0.01493671,0.02546543,0.00256242,0.0204441,0.02708528,0.00203717,-0.02480588,-0.00931284,-0.01569057,0.02216391,-0.08862574,0.12756759,-0.04030837,0.03683144,-0.01171278,-0.06715949,0.04093992,0.00628565,-0.02530631,-0.06729989,0.0369078,0.02595767,0.03214325,0.01222889,0.08837526,-0.04808834,0.02499421,0.02170232,0.00485849,-0.00101516,-0.02405749,0.01943111,0.00118431,0.0340015,0.05270663,-0.02900106,-0.01855016,-0.03427847,0.01561255,0.04160707,0.05682135,0.02884132,0.02254367,0.04477219,-0.07279831,-0.00830918,-0.03635857,-0.03665378,-0.00824486,0.00896421,-0.03158319,-0.04831812,-0.0247854,-0.08920904,-0.02542004,-0.10857768,-0.06734479,0.09539167,0.00595144,-0.00300765,0.01652505,-0.06012674,0.02801478,0.08386023,-0.03052907,-0.09179807,-0.01268242,-0.00059897,0.01729089,0.10611331,-0.07210516,0.02243922,-0.0161764,-0.03733417,-0.02089926,0.13879585,0.0224508,-0.09446547,0.04608467,0.03903564,0.00298944,-0.06702962,0.00761329,0.03320225,-0.04369201,-0.01665621,0.08307854,-0.04988505,-0.01438157,-0.02798956,0.00990311,0.04704858,0.01194593,-0.03358598,-0.08804485,0.00206232,-0.00726153,-0.01517127,-0.03910501,0.02015244,0.02113742,0.02458704,-0.06903562,0.05677249,0.00283994,0.01988078,-0.01659086,0.01300248,-0.00233756,-0.00970393,0.02684928,-0.07253263,0.14754051,0.02764073,0.00287237,0.01743895,-0.08095464,-0.00608638,-0.03679756,-0.06215274,0.01779455,0.06296114,-0.0281839,0.02826667,-0.03794671,0.02813235,-0.03513676,0.0109801,0.00347751,0.04928175,-0.03360679,0.03187212,0.03821184,0.02047306,-0.07872339,-0.17951418,-0.03262534,0.05993575,-0.00964073,0.00898956,0.02492249,0.05217115,0.03157136,0.04611307,0.07490085,0.08901815,0.06675755,-0.05961342,-0.0062487,-0.00192127,0.05948417,0.04853195,-0.00998906,0.00751633,0.04328693,-0.06687921,0.02459157,0.02488097,-0.04711447,0.07251471,-0.06398905,0.08447877,-0.0275975,0.00576862,0.00379136,0.0929207,0.0273823,-0.02324094,-0.15132067,0.0375823,0.0654726,-0.02379656,0.0029368,-0.06044915,-0.02544151,-0.00118638,-0.00132821,0.05518798,-0.09341702,0.02815474,-0.06638365,-0.01903882,-0.03298618,-0.00800468,0.03992192,-0.00902783,0.03147338,0.04299607,0.11112794,-0.00031892,-0.01100665,0.024971,-0.0601188,0.00782606,0.03364896,-0.00944218,0.00218452,-0.02719452,-0.0038602,0.03492457,-0.02508241,-0.00445669,0.03470374,0.03766513,-0.03799853,-0.03761252,0.14442293,0.02255965,-0.00954876,0.04819046,0.03551916,-0.06077264,-0.07698405,0.05033227,-0.00309676,0.02517178,0.01103258,0.02506618,0.0491243,-0.00681776,0.05138272,-0.0173418,0.01423583,0.02215885,0.00840968,0.0003142,-0.05325288,-0.04258266,-0.03891647,0.07414865,0.01621699,-0.27747577,-0.02603683,-0.02903519,-0.00627396,0.06087645,0.02294443,0.03487439,0.0248829,-0.07812487,0.02014208,-0.01193804,0.03688458,-0.00748196,-0.05479321,-0.00422292,-0.01034504,0.05097194,-0.0151881,0.02406554,0.03315289,0.00687984,0.07308969,0.23075697,0.00131765,0.03635837,0.03725275,-0.05293435,0.06041031,0.03351825,0.00796872,-0.03410715,-0.02479599,0.02976298,-0.01301503,0.04325331,0.03126546,-0.00457419,-0.0170556,-0.00462505,0.0099265,-0.04467288,0.00383853,-0.0251185,0.02481359,0.11805448,-0.0124173,-0.05763756,-0.07866126,0.01028121,0.04162487,-0.06605828,-0.04367863,0.01714412,0.03365764,0.00604639,0.06403578,0.04179018,-0.04486862,-0.0646578,-0.01860334,-0.03705323,-0.03237065,-0.05170646,0.07252233,-0.00327084],"last_embed":{"hash":"11na867","tokens":298}}},"text":null,"length":0,"last_read":{"hash":"11na867","at":1751426529523},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.2. 虚拟机：基于 Nock ISA 的新型 ZKVM**","lines":[76,79],"size":284,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"11na867","at":1751426529523}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.2. 虚拟机：基于 Nock ISA 的新型 ZKVM**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03561961,-0.01470949,-0.06866468,-0.03173953,0.02284712,0.02258665,-0.01496003,0.00577591,-0.0052626,-0.00137609,0.07243772,-0.05435098,0.01619017,0.02340622,0.03180887,-0.01500045,0.03473922,-0.05343831,-0.01723401,0.02654341,0.06682242,-0.06487894,0.03590448,-0.01349787,0.06447223,0.06347645,0.02373648,0.00352674,-0.04196041,-0.18534635,-0.01993131,-0.08266015,0.00637026,0.02902097,0.00598624,-0.06433848,-0.0490898,0.05295517,-0.05137467,0.00212147,0.00946585,0.00230506,0.01748599,0.00275441,0.02317886,-0.09097634,0.04608737,-0.06584905,-0.00576444,-0.07386809,-0.04842588,-0.08316526,-0.02193081,0.03642531,-0.0070601,0.00412576,0.00945222,0.04237378,0.02682739,0.01950934,0.08241812,0.04529603,-0.18900032,0.00577463,0.07670937,0.01759549,-0.05244758,0.01192742,0.05503923,0.08407317,0.00507051,0.00494269,-0.03177374,0.04328195,0.02527449,-0.01317557,0.01814953,0.02047022,-0.02801635,-0.063287,-0.06898104,0.07964811,0.01413387,-0.02892,-0.00176698,-0.01795878,-0.01833966,-0.04540608,-0.01432571,0.02541499,0.00283303,0.02194589,0.02855366,0.00154368,-0.02476837,-0.00797545,-0.01609481,0.02610114,-0.08748665,0.12784423,-0.03821644,0.0374172,-0.01399515,-0.06535367,0.04292075,0.00661441,-0.02635218,-0.06507994,0.03829399,0.02533521,0.03370681,0.01158228,0.09047127,-0.04870489,0.02267401,0.02148278,0.00418856,0.00068209,-0.02247311,0.01921161,0.00360225,0.03239863,0.05273356,-0.02882069,-0.0193511,-0.03286711,0.01868227,0.04160289,0.0550094,0.02867364,0.022444,0.04428727,-0.07161652,-0.00766253,-0.03323419,-0.03405524,-0.00871628,0.01015865,-0.03412214,-0.04944528,-0.02434355,-0.09006475,-0.02629425,-0.10837408,-0.06626246,0.09668467,0.00390425,-0.00411603,0.01835573,-0.06281721,0.02770638,0.08549984,-0.02962274,-0.09305589,-0.01220684,-0.00104161,0.01892825,0.10419642,-0.07394983,0.02418189,-0.01744605,-0.03633313,-0.01826228,0.13733402,0.01880283,-0.0924359,0.04651151,0.03825606,0.00422976,-0.06584272,0.00785523,0.03070485,-0.04221699,-0.01535394,0.08593863,-0.05151206,-0.01410959,-0.02834648,0.00957591,0.04640443,0.00948297,-0.03418026,-0.08827769,0.0015287,-0.00711113,-0.01345101,-0.03761988,0.01918448,0.02025783,0.02351506,-0.06869452,0.05873039,0.00230081,0.0208381,-0.01552144,0.015022,-0.00312414,-0.00951862,0.02329335,-0.07349498,0.14606695,0.02663712,0.0034234,0.01657045,-0.07996404,-0.0070149,-0.03684574,-0.06348725,0.01890667,0.06229024,-0.02720653,0.02814172,-0.03965063,0.02517898,-0.03400454,0.01224275,0.00326702,0.04688322,-0.0343082,0.0325322,0.03821243,0.02099962,-0.07938563,-0.18102771,-0.03063634,0.05792382,-0.00924129,0.00948458,0.02518679,0.05252213,0.03243946,0.04604876,0.07442594,0.08945233,0.06411233,-0.06036736,-0.00779525,0.00247556,0.06026348,0.04860543,-0.0100229,0.00812303,0.04357167,-0.06572951,0.02477817,0.02535245,-0.0488988,0.07307784,-0.06211794,0.08550756,-0.02696363,0.00681479,0.00378801,0.09094074,0.02870472,-0.0229684,-0.15138447,0.03664375,0.06388709,-0.02204975,0.00512786,-0.06099886,-0.02595375,-0.00276425,-0.00110338,0.05643442,-0.09268267,0.02990562,-0.06537667,-0.01929797,-0.03094151,-0.00857042,0.03931489,-0.00756419,0.03188267,0.04040518,0.11095256,0.00202528,-0.01275159,0.02540787,-0.06302255,0.00751108,0.03335368,-0.0114604,0.00215111,-0.02798672,-0.00883882,0.03892617,-0.02729577,-0.00468412,0.0352663,0.03759801,-0.03800084,-0.0388697,0.14411892,0.02145416,-0.01128889,0.04780414,0.03522135,-0.06076822,-0.0757957,0.04877814,-0.00312825,0.02271621,0.01170807,0.02496443,0.05133407,-0.00620173,0.05005655,-0.01700215,0.01696067,0.01985195,0.00913965,0.00115266,-0.05409505,-0.0405682,-0.03689708,0.07472806,0.0155811,-0.27637702,-0.02545393,-0.03011385,-0.00826988,0.05789484,0.02350732,0.03460788,0.02570623,-0.07904209,0.0206396,-0.01261316,0.03772062,-0.00637774,-0.05232178,-0.00688049,-0.01069302,0.05096957,-0.01294704,0.0261205,0.03009986,0.00929448,0.07328409,0.2345773,0.00138179,0.03726505,0.03717012,-0.05453906,0.06284487,0.03367377,0.0071633,-0.03087632,-0.02429268,0.02914385,-0.01221341,0.04257764,0.03195767,-0.00367977,-0.0154447,-0.0035345,0.01051135,-0.04631774,0.00500959,-0.02492509,0.02356267,0.12094498,-0.0117564,-0.05808044,-0.07852686,0.01129326,0.04128981,-0.06640081,-0.04226538,0.01980789,0.03249695,0.00644593,0.06151394,0.03927596,-0.04688828,-0.06406405,-0.01704265,-0.03742746,-0.03137447,-0.05116675,0.07189696,-0.00207416],"last_embed":{"hash":"1byzbip","tokens":295}}},"text":null,"length":0,"last_read":{"hash":"1byzbip","at":1751426529533},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.2. 虚拟机：基于 Nock ISA 的新型 ZKVM**#{1}","lines":[78,79],"size":244,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1byzbip","at":1751426529533}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.3. 应用层：NockApp 框架与意图模型**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06846246,-0.02302044,-0.05472516,-0.04911875,0.02643116,0.00808266,0.00480113,0.00106679,-0.00509477,-0.02526702,0.05500791,-0.0573551,0.0409727,0.03317687,0.02310088,-0.01127355,0.01173477,-0.03074662,-0.03119153,0.01535726,0.10151264,-0.05079725,0.03757564,-0.01891548,0.07418207,0.05528256,-0.0013394,-0.02042745,-0.01378637,-0.18136215,-0.01794799,-0.08124828,0.02670569,0.01735818,0.00759311,-0.02543121,-0.03260247,0.05243215,-0.06045106,0.00392851,0.01264085,0.02256488,0.0187195,0.02690422,0.03741875,-0.08840248,0.03703564,-0.08290441,0.0094153,-0.05656721,-0.03613279,-0.08744719,-0.02128144,0.01862836,0.02038705,0.04624033,0.01489849,0.05982503,0.06578854,0.04738567,0.08769808,0.06447335,-0.18465237,0.01354712,0.10483359,0.0263358,-0.0385277,0.01800345,0.03644269,0.06983472,0.05400985,0.00767361,0.002656,0.06045628,0.02455286,0.00213768,0.02998432,-0.00202048,-0.03716344,-0.05875789,-0.05435659,0.08914942,0.02880267,0.00735473,-0.03157052,-0.01535462,-0.01829116,-0.04015931,-0.02834171,0.00132302,0.01427235,0.03687873,0.07469512,0.00968935,-0.04853455,-0.02452284,-0.02588454,0.01253455,-0.11562002,0.11194541,-0.01482338,0.0246228,-0.03092553,-0.06099821,0.05824602,0.00563028,-0.03054704,-0.07805245,0.00657361,0.01377254,0.0377547,0.02415547,0.04504985,-0.06934889,0.02917379,0.01393359,-0.00061215,-0.01639831,-0.03681112,0.0206146,0.00935828,0.04366749,0.0371005,-0.01419884,-0.0300752,-0.0074874,0.03184819,0.03296182,0.04017778,0.04657112,0.02338653,0.04316444,-0.09021419,-0.01314538,-0.045941,-0.02281578,-0.02929194,0.00418768,-0.02351891,-0.04813804,-0.036141,-0.09508694,-0.0326613,-0.10099724,-0.04706237,0.06348472,-0.00721788,0.00073905,-0.02175039,-0.02384949,-0.00066079,0.07161893,-0.04599017,-0.049049,-0.01284577,-0.02365923,0.03526283,0.06893602,-0.0475191,-0.00761223,-0.00993261,-0.0402517,-0.02165838,0.13336036,0.03474701,-0.12601806,0.06267644,0.06024907,-0.02358724,-0.06605122,0.00447712,0.04028605,-0.056736,-0.00120068,0.0798153,-0.01798064,-0.02748201,-0.0198357,0.0077395,0.05226268,0.01677107,-0.01185581,-0.071776,-0.01389683,-0.01711649,-0.0269126,-0.00895944,0.01560512,0.04994773,0.02365922,-0.10082083,0.04630004,0.00368831,0.03730966,-0.03072375,-0.01233601,-0.0082713,-0.02856414,0.01973806,-0.04432056,0.13166551,0.04866749,0.0020528,0.01422421,-0.04020346,0.02815147,-0.08675369,-0.06780344,-0.01447823,0.03470393,-0.03844019,0.03526122,-0.02632435,0.04789589,-0.02366363,0.01438876,0.00619763,0.0495195,-0.04123843,0.06232508,0.04169341,-0.00522921,-0.06749634,-0.20243335,-0.01004931,0.05046853,-0.03277102,0.0194793,-0.02286818,0.05769418,0.00672436,0.05505532,0.08231188,0.07918702,0.07213119,-0.04211562,-0.01427576,0.00063701,0.05015539,0.03940677,-0.01261155,0.00355581,0.05583265,-0.000781,0.01773452,0.00940135,-0.06004926,0.02043455,-0.05341729,0.1146864,-0.02583371,0.02180849,0.0051971,0.06520747,0.01987563,-0.01428759,-0.12620543,0.02342182,0.05016803,-0.01057201,-0.00810734,-0.04731083,-0.02741731,-0.00575532,0.03970224,0.05101204,-0.10984632,0.0387307,-0.08335143,-0.03534811,-0.0335785,0.02034078,0.03752163,-0.00186561,0.05306635,0.01990251,0.12424121,-0.00317144,-0.0107479,0.01788229,-0.06146689,0.0052892,0.04715818,-0.00225494,-0.00724603,-0.02527419,-0.01035088,0.00685925,0.00409594,-0.01382097,0.01641756,0.03508411,-0.03482298,-0.02712991,0.14399694,0.03340668,-0.00563698,0.01095056,0.02695886,-0.05266646,-0.09701422,0.05549732,-0.00074013,0.00248013,0.01462286,0.03839334,0.0139256,-0.01321222,0.07296824,-0.00252422,-0.00533842,0.01633438,0.00525718,-0.00431872,-0.05104932,-0.05055781,-0.05068247,0.05233648,0.01928369,-0.29177424,-0.04808838,0.02107316,0.00011692,0.07492516,-0.00335634,0.05359028,0.03437851,-0.06003047,0.03305465,-0.02754305,0.04132383,-0.02159913,-0.08523656,0.0093586,-0.00605807,0.0543641,0.00610283,0.00489262,0.00959628,0.00648053,0.07344167,0.19904326,-0.03166862,0.06069604,0.02789316,-0.05246348,0.02289594,0.06178784,0.02431325,-0.0522867,-0.01082135,0.0351274,-0.0283607,0.01794863,0.03743352,-0.01390215,-0.02143259,-0.013658,0.00833823,-0.05144028,-0.00541534,-0.03378311,0.02545986,0.12128632,-0.0017762,-0.04772288,-0.06546152,0.01533753,0.04708611,-0.0516996,-0.00711404,0.01049509,0.02209242,0.00715914,0.03303685,0.03413266,-0.0175796,-0.07500354,-0.01428052,-0.02825623,-0.01601576,-0.05072952,0.06203517,0.03011555],"last_embed":{"hash":"6q17ve","tokens":442}}},"text":null,"length":0,"last_read":{"hash":"6q17ve","at":1751426529544},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.3. 应用层：NockApp 框架与意图模型**","lines":[80,89],"size":636,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"6q17ve","at":1751426529544}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.3. 应用层：NockApp 框架与意图模型**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06603977,-0.02449748,-0.05549442,-0.04967347,0.02646339,0.00490918,0.00271696,0.00132414,-0.00612822,-0.0291283,0.05334516,-0.05599213,0.03890695,0.03357721,0.02329172,-0.00938159,0.01198754,-0.03126459,-0.03002498,0.01243989,0.10518175,-0.05164317,0.03664239,-0.01981231,0.07412361,0.05516124,-0.00139799,-0.02027215,-0.0134001,-0.18356301,-0.0213164,-0.08274835,0.0277848,0.01711282,0.0077242,-0.02686729,-0.03373338,0.05011801,-0.05790016,0.00102179,0.01205129,0.02384418,0.01588598,0.02635507,0.03869276,-0.08843683,0.03822818,-0.08175056,0.00988744,-0.05545932,-0.03900333,-0.08835283,-0.01994837,0.01678865,0.02101225,0.04718286,0.01428294,0.06038192,0.06223907,0.04758459,0.08729491,0.06761952,-0.18579255,0.01671128,0.10359439,0.02441846,-0.03869374,0.01530845,0.03887607,0.06928328,0.05033953,0.00752664,0.00179409,0.06367856,0.02387565,0.00488516,0.02864378,-0.00297776,-0.03563514,-0.05895441,-0.05696839,0.09024833,0.02786693,0.00952433,-0.03285966,-0.01515818,-0.01334326,-0.04158211,-0.02590079,0.00217789,0.01608117,0.03490065,0.07532276,0.00962836,-0.05403636,-0.02336297,-0.02394193,0.01199143,-0.11480989,0.10866464,-0.01578845,0.02437075,-0.02864113,-0.05924117,0.05877042,0.00768897,-0.02974221,-0.07746304,0.00808737,0.01594937,0.03849891,0.0253899,0.04198765,-0.0666003,0.02850606,0.01447488,0.00013797,-0.01333073,-0.03854948,0.01942535,0.0108004,0.03941447,0.03494074,-0.01375882,-0.02544498,-0.00628498,0.0337402,0.03326918,0.03960701,0.0451761,0.0242628,0.04468362,-0.09445205,-0.01253893,-0.0470972,-0.02298263,-0.02918058,0.0039502,-0.02202388,-0.05086302,-0.03497741,-0.09338791,-0.03605869,-0.09959466,-0.04979969,0.06200794,-0.00885904,0.00017972,-0.02503736,-0.02500102,-0.001667,0.07556248,-0.0469493,-0.05166082,-0.01129252,-0.02254182,0.03875709,0.07038101,-0.04425259,-0.00748428,-0.00889779,-0.03927673,-0.02286482,0.13129617,0.03778958,-0.12445752,0.06457435,0.06095479,-0.02489071,-0.06337742,0.00638351,0.04156634,-0.05822503,-0.00170661,0.08007978,-0.01857803,-0.02877119,-0.01929383,0.00867359,0.05380563,0.01496437,-0.01279196,-0.07400083,-0.00882104,-0.01707545,-0.02651696,-0.00651455,0.01490439,0.04968342,0.02058137,-0.09642974,0.04617719,0.00413347,0.03587212,-0.02955435,-0.01142756,-0.00883824,-0.02851249,0.0212478,-0.04391658,0.13232009,0.05101125,0.00125983,0.01414611,-0.0400896,0.02655482,-0.08886997,-0.06614763,-0.01274016,0.0352029,-0.03988308,0.03153112,-0.02648005,0.04752159,-0.02492537,0.01276188,0.00558538,0.05003987,-0.04030477,0.06315728,0.04148976,-0.00446761,-0.06526103,-0.20378797,-0.00958936,0.04790723,-0.03254318,0.02100877,-0.02432457,0.05780178,0.00749523,0.05446533,0.08245011,0.0781853,0.07120659,-0.0413685,-0.01540086,-0.00067754,0.04930134,0.03704765,-0.01425709,0.0025191,0.05462242,-0.00053046,0.01868202,0.00865437,-0.05886206,0.01826726,-0.05332023,0.11455584,-0.02564964,0.02147188,0.0036513,0.064509,0.02012231,-0.01296371,-0.12571812,0.0218538,0.05219164,-0.01038449,-0.00996774,-0.04518262,-0.02630145,-0.00497584,0.04186819,0.05123282,-0.11003487,0.03758716,-0.08356552,-0.0345423,-0.03597565,0.02306046,0.03878745,-0.00403804,0.05432931,0.02022726,0.12533768,-0.00622235,-0.01105222,0.01812855,-0.05813092,0.00576141,0.04603485,-0.00430035,-0.00869399,-0.02626305,-0.00845413,0.00844949,0.00428015,-0.01371787,0.01508675,0.03707582,-0.03522371,-0.02625143,0.14297356,0.03240621,-0.00115454,0.01033542,0.02533272,-0.05156353,-0.0951995,0.05434422,-0.00245083,0.00154745,0.01656997,0.03888037,0.01379454,-0.01254631,0.07126313,-0.00071165,-0.00785503,0.01903032,0.00712798,-0.00716844,-0.05169833,-0.05428238,-0.0488918,0.05217539,0.01880145,-0.29097006,-0.04997071,0.02155362,0.00111022,0.07321413,-0.00437847,0.05531938,0.03564815,-0.05618929,0.03286126,-0.02817187,0.04245681,-0.0235285,-0.0847175,0.0109071,-0.00468545,0.05621621,0.00635108,0.00514115,0.00827275,0.00765631,0.0729072,0.20001481,-0.03322783,0.06375233,0.02859957,-0.0498544,0.02086915,0.06114049,0.02452452,-0.05131764,-0.01213306,0.03927405,-0.03034013,0.01786632,0.03564223,-0.01354681,-0.0208553,-0.01378507,0.00775934,-0.05112785,-0.00415089,-0.03457561,0.02693529,0.12128958,-0.00174371,-0.04656727,-0.06615685,0.01220252,0.04515972,-0.05259328,-0.0085409,0.01152469,0.0231306,0.00935466,0.03218266,0.03590279,-0.01865522,-0.07499082,-0.01272858,-0.03030021,-0.01699478,-0.05316247,0.05977824,0.03409786],"last_embed":{"hash":"11oxra3","tokens":443}}},"text":null,"length":0,"last_read":{"hash":"11oxra3","at":1751426529558},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.3. 应用层：NockApp 框架与意图模型**#{1}","lines":[82,89],"size":601,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"11oxra3","at":1751426529558}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.4. 开发者体验：Jock 编程语言**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04587325,0.01822255,-0.06238,-0.07108558,0.07104203,0.02099795,-0.00250526,0.00164396,-0.02107657,-0.01004952,0.05063745,-0.09300754,0.03807444,0.0069078,0.02002809,-0.02866091,0.01486317,-0.01680911,-0.01665769,0.0412765,0.07897439,-0.06156654,0.01560545,-0.02079016,0.09839345,0.02013693,-0.00123815,-0.01142216,-0.04340287,-0.16912971,-0.00100418,-0.0710748,0.0396963,0.03868972,0.00364167,-0.05892183,-0.01674694,0.05218669,-0.05308633,0.03531588,-0.00852849,-0.01788778,0.03224954,-0.01308223,0.02491243,-0.05112072,0.0433083,-0.08205084,0.04734912,-0.02809319,-0.04866255,-0.04345163,0.00012906,0.00723345,0.04735298,0.02747099,0.00694827,0.04021954,0.03268558,0.02927731,0.06932874,0.07428167,-0.19284293,0.01842027,0.08151037,0.0177794,-0.04376669,0.02337818,0.06223729,0.08240534,0.00947008,-0.00680431,-0.0071124,0.04391226,0.03082173,0.01654522,-0.00286651,0.03437763,-0.01781128,-0.05005354,-0.10148057,0.08358767,-0.01658968,-0.0317525,-0.05418462,-0.0094907,-0.0309297,-0.04377709,-0.04135934,0.03862353,-0.01045956,0.02500804,0.01619592,0.02067934,-0.05374496,-0.01515969,-0.04576486,0.00508601,-0.1211095,0.1282296,-0.04452838,0.01950577,-0.00766197,-0.06472303,0.03320102,-0.00376566,0.00168607,-0.05483144,-0.00067162,0.01548954,0.02585376,0.02681118,0.05579374,-0.05205934,0.03585847,0.02860201,-0.01978161,0.00341217,-0.0388622,0.02399096,0.00785018,0.06661168,0.02657982,-0.02777855,-0.02724882,-0.00860909,0.01541369,0.05126939,0.05811122,0.02563957,-0.0094814,0.07074673,-0.07177699,-0.01579606,-0.04375418,-0.02369116,-0.00840155,0.0110909,-0.0104698,-0.00784023,-0.01263385,-0.07748237,-0.03618284,-0.1098547,-0.05404161,0.0174858,0.0019211,0.00003309,0.02406488,-0.03595385,-0.00728593,0.07325163,-0.03938968,-0.07271651,-0.01994137,-0.01999221,0.02389602,0.09439512,-0.05294447,0.00839453,-0.04845805,-0.04573445,-0.03241931,0.12676404,0.01102019,-0.10385532,0.0187573,0.03075214,-0.02015554,-0.0777392,0.00062844,0.04303971,-0.0311199,0.00359952,0.08972531,-0.03202454,-0.00240358,-0.04221377,0.01314598,0.05320308,0.03031127,-0.01836526,-0.06573023,-0.01305812,-0.00538048,-0.02201219,-0.01663614,0.04760369,0.06469555,0.04668403,-0.07026704,0.07142185,-0.00603904,0.00485707,-0.03629746,0.00593555,0.00937734,-0.00371572,0.06064344,-0.05242839,0.09995121,0.01861052,0.01707592,0.02278933,-0.10734332,0.02782742,-0.06552897,-0.06212622,0.03464138,0.00625791,-0.03708227,0.01545685,-0.02507904,0.04180606,-0.02421689,0.01585828,0.04100477,0.06322873,-0.04058248,0.04549633,0.00487863,0.0242809,-0.08791387,-0.19283083,-0.03065382,0.08971208,-0.01429043,0.01694609,-0.010932,0.03154781,0.02469153,0.05021922,0.08421694,0.11126076,0.05752564,-0.05946575,-0.02563769,-0.03480085,0.06536826,0.03642246,-0.03490022,-0.0020644,0.04854018,-0.0270713,0.0482457,-0.02514806,-0.02789413,0.05964183,-0.08285276,0.10559728,0.00192881,0.04763199,-0.03534114,0.08729093,0.03565554,-0.00834279,-0.12480638,0.03624537,0.07489506,-0.01345978,-0.02629947,-0.03830299,-0.01171061,-0.01943946,0.0222769,0.0289014,-0.09743607,0.01405452,-0.07018095,-0.03649507,-0.06159002,-0.01109665,0.0365597,0.01695575,0.02965565,0.06347147,0.11408122,-0.00974109,-0.03280126,0.0019586,-0.08051766,0.01337046,0.05920566,0.01536129,0.00678207,-0.01086287,-0.02911326,0.03269993,-0.01410976,-0.02951144,0.01604789,0.02123676,-0.02935396,-0.02142379,0.0972406,0.02691522,0.00203761,0.03504948,0.01573019,-0.07381959,-0.07897405,0.05918971,-0.00553099,0.04078145,0.0244634,0.03082252,0.03608247,-0.00860979,0.03115865,-0.00293344,0.02055455,0.05762505,0.01491232,-0.0138775,-0.03173635,-0.03957123,-0.03751094,0.06322683,0.0210775,-0.2874392,-0.02805973,0.0256653,-0.00161648,0.06432827,0.02276331,0.05414196,0.02834317,-0.0693289,0.05133677,0.01746423,0.04996864,-0.01447738,-0.06118581,0.00124616,-0.01545109,0.02528116,-0.01260288,0.02325332,0.01202294,-0.00145677,0.0535363,0.2088536,-0.03103897,0.07370418,0.04161118,-0.05128767,0.03190503,-0.01404754,0.00964235,-0.03088239,0.00317164,0.03681837,-0.0111507,-0.00960976,0.00441767,-0.0022902,0.00475559,-0.01306354,-0.00325135,-0.06289908,-0.0060002,-0.03647371,0.0261319,0.11117188,0.01754043,-0.06200687,-0.0615328,-0.0028816,0.04625069,-0.06506117,-0.0290129,0.03661051,0.03064144,-0.03025322,0.06170277,0.03392259,-0.04659086,-0.05718932,-0.00287837,-0.04932392,-0.00525297,-0.045816,0.07876446,0.01884708],"last_embed":{"hash":"1ads4ky","tokens":281}}},"text":null,"length":0,"last_read":{"hash":"1ads4ky","at":1751426529572},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.4. 开发者体验：Jock 编程语言**","lines":[90,99],"size":298,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1ads4ky","at":1751426529572}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.4. 开发者体验：Jock 编程语言**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04378413,0.01960311,-0.06126134,-0.06862732,0.07413866,0.02411317,-0.00415113,0.00524351,-0.02173364,-0.00852241,0.04955673,-0.09518459,0.03834154,0.00999056,0.02076239,-0.03218834,0.01300191,-0.01718335,-0.02094036,0.04077699,0.08084654,-0.06165001,0.01519679,-0.01842447,0.09709048,0.01756974,-0.00059308,-0.01136663,-0.04532741,-0.17090797,0.0020834,-0.0704933,0.04000695,0.0394677,0.00303045,-0.06090222,-0.01754044,0.05432011,-0.05120064,0.03768611,-0.00900011,-0.0208861,0.02896981,-0.01372965,0.02702344,-0.05128669,0.04415176,-0.0815042,0.04856776,-0.0262477,-0.04599496,-0.04345687,0.00058113,0.00692944,0.04767311,0.02550316,0.00814157,0.03880644,0.03326138,0.03206638,0.06972719,0.07581616,-0.19548889,0.01700508,0.08090032,0.01953125,-0.04601751,0.02472564,0.06342736,0.08372768,0.01061108,-0.0071601,-0.00706293,0.04053642,0.03187231,0.01654517,-0.00301083,0.03582375,-0.01832128,-0.04971963,-0.10083244,0.08368248,-0.01813097,-0.03359151,-0.05545856,-0.01281364,-0.03097424,-0.04251657,-0.04438364,0.03897418,-0.00949173,0.02585974,0.01699888,0.02401755,-0.05290017,-0.01546385,-0.0476454,0.00327964,-0.1208841,0.13057265,-0.0448794,0.02043256,-0.00586731,-0.06060993,0.03399255,-0.00549849,0.00314485,-0.05433512,0.00176558,0.01602206,0.02330259,0.02800964,0.05111748,-0.04652839,0.03489704,0.02843393,-0.02155844,0.00223849,-0.03889503,0.02767034,0.00921161,0.06426637,0.02803998,-0.03061251,-0.0302729,-0.00825403,0.01708171,0.05153328,0.05521456,0.02044232,-0.01087124,0.07070524,-0.07432881,-0.01363903,-0.04214969,-0.02288339,-0.00768514,0.00658735,-0.01023428,-0.00519995,-0.00934976,-0.07922282,-0.03083766,-0.11103398,-0.0520331,0.02036161,0.00202268,-0.00135143,0.02659864,-0.03173403,-0.00481931,0.07149315,-0.03723721,-0.07540537,-0.01904992,-0.02024908,0.02651737,0.09167012,-0.05368377,0.01117538,-0.0492137,-0.04305611,-0.03014554,0.12706724,0.0136983,-0.10211135,0.01960368,0.03021423,-0.02067432,-0.07667422,0.00115268,0.04224758,-0.03209761,0.00399158,0.09040806,-0.03265877,-0.00142101,-0.04629271,0.01725278,0.05400234,0.03083012,-0.02120384,-0.06535438,-0.01273949,-0.0054738,-0.02114986,-0.01914402,0.04415745,0.06478941,0.04858726,-0.06696757,0.07293232,-0.00812938,0.00540774,-0.03640718,0.00411699,0.0082671,-0.0053718,0.05963266,-0.05124137,0.09475645,0.01809104,0.02031223,0.02112312,-0.10518184,0.02922019,-0.06552279,-0.06460043,0.03716181,0.00128877,-0.03849838,0.01123989,-0.02430169,0.04133991,-0.02647086,0.01837711,0.04234145,0.05883782,-0.04411178,0.04401217,0.00136192,0.0278276,-0.08861816,-0.19483453,-0.03322734,0.09205619,-0.0162207,0.01629064,-0.00707864,0.03204485,0.02462845,0.05363064,0.08789916,0.10874333,0.05546877,-0.05832425,-0.03010973,-0.03380917,0.06857449,0.03660316,-0.03367948,-0.00148311,0.04742365,-0.02707288,0.0502147,-0.02826848,-0.02848981,0.05953015,-0.08127603,0.10667445,0.00495386,0.04531502,-0.03426864,0.0862433,0.03439722,-0.00643279,-0.1251473,0.03926643,0.07332266,-0.01293598,-0.03206924,-0.03878814,-0.01501929,-0.02353883,0.02281925,0.02841456,-0.09700792,0.01351837,-0.06934398,-0.03598393,-0.06236485,-0.01274603,0.03922744,0.01893063,0.02706338,0.06435581,0.11391386,-0.00828718,-0.03474008,0.00291616,-0.07803383,0.01236011,0.057332,0.01679719,0.00681365,-0.01187956,-0.02926962,0.03076501,-0.01423623,-0.02839006,0.01592465,0.01885572,-0.02961078,-0.01875135,0.09227763,0.02878357,0.0039633,0.03838604,0.01232826,-0.07189102,-0.0790332,0.05844112,-0.00556714,0.04058515,0.02375693,0.03154296,0.03918016,-0.01019352,0.02773141,-0.00511297,0.01963774,0.05523283,0.01526974,-0.01488032,-0.02703488,-0.03847414,-0.03406641,0.05763419,0.0200352,-0.28477389,-0.02913891,0.02440533,-0.00001276,0.06358372,0.02085585,0.05361038,0.0319094,-0.06881595,0.05050281,0.0216692,0.05179652,-0.01542555,-0.06097134,-0.00148711,-0.01677543,0.02612236,-0.01440915,0.0253232,0.01609635,0.00104793,0.05264596,0.20793144,-0.03391077,0.07435311,0.04239029,-0.0512577,0.03381212,-0.01624884,0.01051501,-0.02893082,0.00255278,0.03513638,-0.00813723,-0.00723539,0.00428006,-0.00025,0.00290652,-0.01628167,-0.00354986,-0.06381097,-0.00659735,-0.04193324,0.02587654,0.11148302,0.01861929,-0.06329366,-0.06089436,-0.00531162,0.04089517,-0.06565319,-0.03124725,0.03669635,0.03181247,-0.03111584,0.06363086,0.03450232,-0.04751922,-0.05480802,-0.00385868,-0.04720023,-0.0081912,-0.04601939,0.07960962,0.01911934],"last_embed":{"hash":"10sx3sr","tokens":278}}},"text":null,"length":0,"last_read":{"hash":"10sx3sr","at":1751426529581},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.4. 开发者体验：Jock 编程语言**#{1}","lines":[92,99],"size":267,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"10sx3sr","at":1751426529581}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04961631,-0.01513452,-0.06746496,-0.03087625,0.03748583,0.0133526,-0.003874,-0.01248955,0.01550247,-0.02377727,0.05468164,-0.04080218,0.03082857,0.03362039,0.03535411,-0.0145627,0.02990147,-0.04950572,-0.03563779,0.01916561,0.10244957,-0.06317377,0.03188612,-0.0059656,0.07376523,0.06344506,-0.00014696,-0.0240556,-0.04208277,-0.16949387,0.00962353,-0.07347119,0.01958789,0.03036543,0.02663149,-0.04964701,-0.0301534,0.06919608,-0.05508199,-0.02021312,0.00716242,0.00325478,0.00864637,0.01481739,0.03167596,-0.07675894,0.03272079,-0.07901577,0.0036791,-0.05243297,-0.03635598,-0.085847,-0.01484903,0.02897413,0.02223207,0.01851344,-0.01080717,0.0464327,0.02861465,0.02341252,0.0628667,0.04296952,-0.18509866,0.00883402,0.08733455,0.01827496,-0.02044973,0.02360713,0.07417134,0.05366579,0.02399895,-0.00050293,-0.01687667,0.03629323,0.01683677,0.0067588,0.02227896,0.02288677,-0.04198125,-0.05177108,-0.0686333,0.07851318,0.00565063,0.01118314,-0.01867652,-0.01105687,0.00932759,-0.03275557,-0.03971076,0.01526201,0.01348478,0.02469251,0.0536051,-0.00390179,-0.0290668,0.00147957,-0.00705374,0.00829423,-0.09105504,0.11474697,-0.01740647,0.04521683,-0.01867398,-0.06450302,0.03598221,0.01458828,-0.01103626,-0.05739469,-0.00367169,0.01876886,0.02103703,-0.00177055,0.10112181,-0.06237235,0.02191346,0.01664292,-0.0126705,0.00622162,-0.02917523,0.02606145,0.00253361,0.0316603,0.03380478,-0.03729057,-0.02974549,-0.02093074,0.00927814,0.04606407,0.06688274,0.05201734,0.00515917,0.04499055,-0.07196202,-0.0075276,-0.06180517,-0.02392148,-0.02333601,0.03105905,-0.00811138,-0.01655761,-0.03305169,-0.06925058,-0.04028985,-0.11269338,-0.0783267,0.05936767,0.00407453,-0.00057907,0.00729369,-0.0534458,0.00563131,0.07617509,-0.01692236,-0.07534701,-0.01454951,0.00478563,0.03112524,0.15546599,-0.05838392,-0.00382725,-0.02615589,-0.03376629,-0.04754639,0.10585822,0.03116335,-0.11625514,0.04235402,0.03138829,-0.02353701,-0.07543784,0.00610289,0.02106893,-0.05372496,-0.00963034,0.07651888,-0.03336322,-0.01663148,-0.01406976,0.01160977,0.05038644,0.01392689,-0.03639655,-0.08242095,-0.01213174,-0.0226075,-0.02946486,-0.02992879,0.04045362,0.03052425,0.04015585,-0.1132349,0.04726905,0.00427668,0.04861692,-0.02771649,-0.02450754,-0.00278917,-0.0054296,0.01814348,-0.07241359,0.15372929,0.02776775,0.01439406,0.01481387,-0.07544805,0.04840118,-0.01440778,-0.05661013,0.00617904,0.03140755,-0.01418166,0.03043117,-0.04408,0.04968153,-0.01949655,0.01261583,-0.01818609,0.04528984,-0.01740442,0.05318282,0.02390656,0.01085488,-0.09927025,-0.18306717,-0.03270622,0.05561851,-0.00276771,0.02906004,-0.00482573,0.02222086,0.04414254,0.03741664,0.10774805,0.09169627,0.05821292,-0.05277753,0.02483345,-0.01150723,0.04177694,0.04384412,-0.0123745,0.01841599,0.04148818,-0.03049481,0.01828563,0.01771889,-0.02317704,0.05986197,-0.05999931,0.09722907,-0.03076369,0.01396663,-0.00927123,0.09809429,0.00839854,-0.05144062,-0.13531907,0.05222652,0.07055055,-0.02115096,-0.03191622,-0.05563578,-0.02432404,0.0048565,0.01262499,0.03739835,-0.09529011,0.01668181,-0.05891094,-0.02315642,-0.03060803,-0.03194013,0.01818198,-0.00314692,0.02095089,0.01032073,0.11290365,-0.01388271,-0.01339382,0.01842938,-0.04183508,-0.0049964,0.04032843,0.00405967,-0.01513263,-0.02233632,0.00166385,0.01544176,-0.00378087,-0.00663079,0.01886862,0.04378792,-0.0179426,-0.0280984,0.15013361,0.00931162,0.00215729,0.03525305,0.0228993,-0.04432324,-0.09980954,0.06402169,-0.00165798,0.05147798,0.02709068,0.02199443,0.0550648,-0.02696851,0.04520492,0.01660318,-0.02162074,0.02507045,-0.00378645,-0.01142545,-0.05754604,-0.04745836,-0.03043066,0.06434929,0.01613829,-0.29546028,-0.03929481,-0.02518718,0.00107745,0.07069568,0.02024561,0.04526317,0.02345969,-0.08096037,0.02064699,-0.0171908,0.04591002,0.00012308,-0.07837235,0.00442306,-0.03140457,0.04922102,-0.01660121,0.0218258,0.04889916,0.01571186,0.06058266,0.21188654,0.00940886,0.03288961,0.00401271,-0.06237199,0.05247606,0.0347643,0.02301517,-0.0351134,-0.03079308,0.0344371,-0.04215481,0.02427742,0.0295932,-0.01480254,-0.00453576,-0.00325139,0.0117789,-0.05747865,-0.00052217,-0.02637469,0.0137728,0.09729455,-0.01141658,-0.0390562,-0.08359525,0.0023161,0.0624753,-0.05521171,-0.01079126,0.01646061,0.03669977,-0.01164565,0.08462573,0.03281337,-0.04140179,-0.07684974,-0.01933819,-0.03059508,-0.02183899,-0.03879858,0.07649379,0.02048978],"last_embed":{"hash":"tkk6lx","tokens":464}}},"text":null,"length":0,"last_read":{"hash":"tkk6lx","at":1751426529591},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**","lines":[100,148],"size":1912,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"tkk6lx","at":1751426529591}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.1. “公平启动”原则**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05155274,-0.01532481,-0.06602269,-0.03225349,0.03710369,0.01292647,-0.00043795,-0.01030471,0.0220352,-0.01958078,0.05559194,-0.03641378,0.03172005,0.02827137,0.03727156,-0.01081167,0.03435796,-0.05359138,-0.03140635,0.02446633,0.09544165,-0.06298453,0.0303264,-0.00550285,0.07958166,0.05848423,-0.00142083,-0.0278929,-0.04096119,-0.16756795,0.00434499,-0.07530263,0.02451858,0.02577292,0.03109999,-0.04725083,-0.02663304,0.0667151,-0.05487617,-0.02519299,0.00847256,-0.00043268,0.01379614,0.02474329,0.02290647,-0.07369596,0.0403655,-0.08032626,0.00644721,-0.05530705,-0.02624563,-0.08329291,-0.01519195,0.0268369,0.0156134,0.01416615,-0.00586713,0.04284262,0.02918079,0.0211686,0.06478729,0.04222623,-0.18310729,0.01106023,0.08476368,0.02203632,-0.02462123,0.02173179,0.07316499,0.05478294,0.0244059,0.00033753,-0.02228527,0.03437676,0.01935796,0.01015511,0.01974222,0.02169851,-0.04216902,-0.05317617,-0.06987995,0.07473432,0.00709438,0.01589591,-0.02030335,-0.00559687,0.01096586,-0.03108891,-0.02622294,0.01751531,0.01160738,0.02226874,0.06030813,-0.00892894,-0.02612508,0.00442414,-0.00966791,0.00453942,-0.0915014,0.11361088,-0.02270903,0.04738961,-0.02043178,-0.06306497,0.03669859,0.01977195,-0.00178273,-0.05218985,0.00035893,0.02098899,0.02726916,-0.00388198,0.09866439,-0.06287183,0.02406526,0.02009502,-0.00951493,0.01011785,-0.02879217,0.02307263,0.00689039,0.0353076,0.03808915,-0.03277726,-0.03061624,-0.02122,0.01050049,0.04590623,0.06421461,0.0515303,0.00104923,0.04535319,-0.07978094,-0.01273952,-0.05289771,-0.02156396,-0.01892818,0.02923411,-0.00122963,-0.01457634,-0.03030396,-0.06444412,-0.04317532,-0.11151271,-0.07851688,0.05785165,0.00135696,0.00517874,0.00663154,-0.05608581,0.00735647,0.07387082,-0.01739337,-0.07514339,-0.0141506,-0.00274704,0.03210904,0.16239819,-0.06001622,0.00394653,-0.0295369,-0.03080527,-0.04441703,0.10728334,0.03617294,-0.11310318,0.04425687,0.03661766,-0.01626719,-0.07830392,0.00661432,0.01842851,-0.04966156,-0.0158182,0.07207274,-0.03794714,-0.01953472,-0.01273535,0.00425552,0.04769075,0.01008589,-0.04092755,-0.08337015,-0.00575448,-0.01927082,-0.02968103,-0.03230581,0.03505324,0.03764383,0.04488321,-0.11087981,0.04921535,0.00117774,0.04354232,-0.03040704,-0.02160879,-0.00311531,-0.00939396,0.01714878,-0.07255691,0.14044391,0.02895261,0.01735384,0.01490461,-0.07573422,0.04712437,-0.01283136,-0.05695242,0.00636047,0.03208918,-0.01967587,0.03564778,-0.05156883,0.05162051,-0.01707743,0.00850831,-0.0109262,0.04433978,-0.01456594,0.04914936,0.02843485,0.01732793,-0.10080551,-0.18685257,-0.03749266,0.04702778,-0.00502911,0.03120679,-0.0043049,0.02492264,0.04242714,0.04378188,0.10290658,0.09340367,0.06387992,-0.05566775,0.0254276,-0.01147327,0.04449762,0.04525736,-0.01024014,0.01730494,0.03607189,-0.03616833,0.02300384,0.01320402,-0.02086509,0.06573623,-0.05864391,0.09696985,-0.02794732,0.02160598,-0.01196875,0.09699368,0.00782495,-0.04739179,-0.14486876,0.0471186,0.07028256,-0.02574917,-0.04427144,-0.06137428,-0.02675451,0.00733531,0.00481008,0.02758122,-0.09888674,0.01077751,-0.05896623,-0.022654,-0.03357601,-0.02777209,0.01982203,-0.00533193,0.01933333,0.01023288,0.11658134,-0.01051803,-0.00992889,0.01029513,-0.04055205,-0.00389657,0.039476,0.00498407,-0.01354359,-0.02098396,0.00367171,0.01739918,-0.00065289,-0.0073966,0.01696514,0.04702945,-0.01575739,-0.03549468,0.15048684,0.00450511,-0.00613431,0.03218953,0.0242269,-0.04248739,-0.10233137,0.06271055,-0.00250274,0.05795686,0.02481768,0.02802965,0.04933982,-0.02707212,0.05008603,0.01456795,-0.02672518,0.02499275,-0.0055713,-0.01238321,-0.06212566,-0.05197426,-0.03999716,0.06938699,0.01154366,-0.29139611,-0.04418011,-0.02126605,0.00036895,0.07030933,0.02224462,0.04892923,0.01740985,-0.08523859,0.0283879,-0.02039723,0.04932455,-0.00299554,-0.07711968,0.0071712,-0.02967315,0.05395804,-0.0133064,0.01876664,0.04648698,0.017726,0.05618976,0.21381128,0.01298886,0.02884276,0.00553128,-0.05809647,0.0466771,0.01869627,0.02157596,-0.03954798,-0.02895311,0.03224685,-0.04542042,0.02571947,0.03239897,-0.00886985,-0.00562192,0.00018527,0.01229673,-0.05542063,-0.00627286,-0.02842872,0.00961348,0.09591143,-0.00056639,-0.03487258,-0.07988744,0.00125205,0.05835378,-0.05900827,-0.01319913,0.01195093,0.03454352,-0.01227781,0.08981141,0.03044971,-0.04256276,-0.07868583,-0.02019705,-0.0253725,-0.01852419,-0.04231082,0.07344548,0.0147091],"last_embed":{"hash":"lkd203","tokens":339}}},"text":null,"length":0,"last_read":{"hash":"lkd203","at":1751426529602},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.1. “公平启动”原则**","lines":[102,109],"size":324,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"lkd203","at":1751426529602}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.1. “公平启动”原则**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04954593,-0.012765,-0.06680345,-0.03445663,0.03741047,0.01613789,-0.00039242,-0.01000927,0.02100376,-0.02208729,0.05765986,-0.03782911,0.03191562,0.02542561,0.03843116,-0.01184116,0.03543972,-0.05447711,-0.02938209,0.02250845,0.09569508,-0.06283966,0.02961371,-0.00692104,0.08099522,0.05837845,-0.00191924,-0.02859744,-0.03834014,-0.16541785,0.00678746,-0.07852264,0.02529555,0.02328872,0.03009512,-0.04653742,-0.02538866,0.06730611,-0.05798847,-0.02484646,0.01027498,-0.0002077,0.01601637,0.02323208,0.02484527,-0.07808144,0.03910159,-0.08560093,0.00728258,-0.05405984,-0.02830867,-0.0841038,-0.01576798,0.02613033,0.01452583,0.01751911,-0.00789883,0.04196107,0.02634753,0.02191936,0.0639725,0.03927181,-0.18466769,0.01103883,0.08222257,0.01990116,-0.02313813,0.02181786,0.07218393,0.05939792,0.02742215,0.00128027,-0.02011792,0.03423468,0.01877184,0.01031699,0.02000675,0.023585,-0.04181163,-0.05543468,-0.06841372,0.07493985,0.00645173,0.01535743,-0.02018121,-0.00682343,0.00891968,-0.03038576,-0.02833051,0.01830725,0.00855757,0.02307867,0.05926762,-0.0090303,-0.02904267,0.00297327,-0.01130402,0.00224949,-0.09254273,0.1149243,-0.02145107,0.04587366,-0.02159249,-0.06143464,0.04045111,0.02057843,-0.00570105,-0.05189323,0.00635254,0.02506062,0.02434334,-0.00286592,0.09792377,-0.06504826,0.02175244,0.02250632,-0.01212107,0.00924276,-0.02947084,0.02115043,0.00467962,0.0355634,0.03676153,-0.03537956,-0.03049813,-0.02307069,0.01382362,0.04793431,0.0615053,0.05164194,0.00189992,0.04387992,-0.08183493,-0.0135965,-0.0549763,-0.02103975,-0.01818011,0.02924834,-0.00522858,-0.01544985,-0.03173519,-0.06161295,-0.03818055,-0.11108848,-0.07719471,0.05609682,0.00478298,0.00475918,0.00593659,-0.05602165,0.0069278,0.07528435,-0.01631727,-0.07242003,-0.01736484,-0.00781189,0.03160355,0.16028297,-0.0632563,0.00071962,-0.02831455,-0.03463406,-0.04329731,0.10307236,0.03491549,-0.11480367,0.04516128,0.03772807,-0.01581351,-0.07938673,0.00703781,0.0226833,-0.05333302,-0.01522444,0.07419903,-0.03649103,-0.01985474,-0.01148624,0.00684342,0.04880689,0.0090658,-0.04341298,-0.08181788,-0.00946432,-0.02092962,-0.02919113,-0.03053831,0.0367507,0.03886462,0.04279173,-0.10737354,0.05101483,-0.00048032,0.04373305,-0.02812315,-0.01920764,-0.0031639,-0.01065413,0.02160964,-0.07149187,0.14153804,0.02760173,0.01720455,0.01570688,-0.07398438,0.04795568,-0.01547848,-0.05617275,0.00489229,0.02956018,-0.02037602,0.0353801,-0.04796988,0.05114945,-0.0171145,0.00856248,-0.01098349,0.04456294,-0.01733149,0.05003331,0.0302669,0.01794203,-0.09723745,-0.18644989,-0.03701709,0.04973061,-0.00762983,0.0282134,-0.00351481,0.02733431,0.04770885,0.04688786,0.09664918,0.09651073,0.06092985,-0.05166869,0.02474397,-0.00807995,0.04240751,0.04285415,-0.00741492,0.01959788,0.03474399,-0.03703126,0.0199686,0.01135765,-0.02037376,0.06534009,-0.0579138,0.09813506,-0.02708371,0.02403016,-0.0133945,0.09624929,0.00941746,-0.04598363,-0.14556034,0.05126978,0.07017821,-0.01995666,-0.04164876,-0.06196093,-0.02801836,0.00524223,0.00497452,0.03007822,-0.09693784,0.01298085,-0.05878205,-0.02229456,-0.03051489,-0.02912357,0.0181686,0.00069363,0.02011998,0.00840997,0.11634184,-0.00856201,-0.01107003,0.01100899,-0.0408641,-0.00275571,0.03899826,0.00307927,-0.01530844,-0.02639032,0.00257239,0.017937,-0.00319272,-0.00492228,0.01672251,0.04761698,-0.0155502,-0.03592884,0.14869452,0.00234703,-0.00396833,0.03376249,0.02177074,-0.04378616,-0.10381458,0.06562231,-0.00071246,0.05263805,0.02675331,0.02483582,0.04762089,-0.02981583,0.051109,0.01521694,-0.02742808,0.02506507,-0.00499852,-0.01287663,-0.06062786,-0.04838183,-0.04352685,0.06946904,0.01277646,-0.29328805,-0.04416714,-0.02055908,0.00042875,0.07064294,0.01913792,0.04576195,0.0174222,-0.08326512,0.02747414,-0.02189012,0.05001033,-0.00553308,-0.07634887,0.00774893,-0.02889694,0.05317952,-0.01474424,0.01960677,0.04275108,0.0209157,0.05492807,0.21528827,0.01403934,0.03093617,0.0075612,-0.05745342,0.04829111,0.02139528,0.02322733,-0.03647009,-0.03116088,0.03358783,-0.04518738,0.02633604,0.03488954,-0.00846465,-0.00444869,0.00066653,0.01585994,-0.0535367,-0.00271702,-0.02486443,0.00758673,0.09737773,-0.0043596,-0.03479036,-0.08035514,0.00144204,0.057947,-0.05810766,-0.01130845,0.00890581,0.03403336,-0.01197038,0.09017253,0.02965553,-0.0453172,-0.07719705,-0.02029176,-0.02847871,-0.018524,-0.04268237,0.0715367,0.01949676],"last_embed":{"hash":"gko6qt","tokens":336}}},"text":null,"length":0,"last_read":{"hash":"gko6qt","at":1751426529614},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.1. “公平启动”原则**#{1}","lines":[104,109],"size":300,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"gko6qt","at":1751426529614}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.2. 代币供应、发行与效用**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05926093,-0.04137714,-0.0751802,-0.01841337,0.04220385,0.01343548,0.0126355,0.00036769,-0.01118571,-0.01889084,0.07202279,-0.03871597,0.03319876,0.0461605,0.03095734,-0.01345289,0.0160436,-0.03898444,-0.0306057,0.02057035,0.11077473,-0.06047701,0.02101681,-0.00816169,0.07150731,0.06577781,-0.00251089,-0.03559549,-0.03766853,-0.18029755,0.00536773,-0.06532415,0.00661385,0.019095,0.02636475,-0.05167769,-0.02963258,0.04731711,-0.05992847,-0.00311103,-0.00269715,0.01563575,0.02157336,-0.00164592,0.04014041,-0.08092868,0.03248411,-0.06650393,0.00866484,-0.06143934,-0.05268177,-0.08927915,-0.03225997,0.03407333,0.03186989,0.02137567,-0.00283264,0.05596998,0.03760713,0.03234796,0.09462729,0.04980941,-0.18499528,0.01889871,0.09138529,0.02462726,-0.03942478,0.03957405,0.04935189,0.09326001,0.03683371,0.00223014,-0.01790825,0.05393063,0.02566967,0.01150137,0.04465398,0.01741939,-0.02136928,-0.06260794,-0.06802765,0.07263826,0.00504223,-0.01290751,-0.03084899,-0.0267793,-0.0077105,-0.04052949,-0.05363277,0.00587574,0.02345813,0.01617085,0.04860565,-0.00670133,-0.0352273,-0.02081278,-0.00952548,0.02566825,-0.09616978,0.11734341,-0.0143211,0.03199375,-0.00232898,-0.07353059,0.03108603,-0.00836137,-0.02499226,-0.06887846,0.01646767,0.0152404,0.01794963,0.02649501,0.0815711,-0.05717783,0.02579783,0.00819577,0.00526923,-0.00875882,-0.0294944,0.0222436,-0.00360033,0.03721059,0.03170834,-0.02578303,-0.03142614,-0.03134414,0.02569215,0.0423829,0.06584526,0.05015976,0.02738851,0.02574045,-0.0790337,0.00604459,-0.05875139,-0.05244141,0.00303071,0.02489175,-0.03292054,-0.04720289,-0.03735177,-0.07560033,-0.0624876,-0.10802195,-0.06343492,0.07450429,0.00509165,-0.00995451,0.00139593,-0.03692775,0.02132617,0.07603881,-0.03626592,-0.10582576,-0.00728515,0.02359019,0.03924533,0.10918135,-0.06398128,-0.01885001,-0.00420609,-0.0245635,-0.04050911,0.11758738,0.03446101,-0.10372413,0.04896969,0.02840273,-0.01850103,-0.06692115,0.01409279,0.03727568,-0.05884068,0.007207,0.07263415,-0.0384917,-0.02433943,-0.00550235,0.02222277,0.05264252,0.01020356,-0.02162183,-0.07369959,-0.02889648,-0.00326663,-0.02199369,-0.0241857,0.03578331,0.04306742,0.03874667,-0.09550522,0.04906035,-0.00062219,0.04998868,-0.03503893,-0.0019933,-0.00927074,0.00301076,0.00256091,-0.05219766,0.16716632,0.01941872,0.00156539,0.01602683,-0.04904993,0.03429262,-0.03177429,-0.05456029,-0.00348213,0.04620211,-0.01896035,0.01951593,-0.03915675,0.03831314,-0.02294644,0.01949046,-0.00366855,0.05863272,-0.03409824,0.04996404,0.03724221,-0.00385606,-0.07595017,-0.17713618,-0.02560771,0.07280479,-0.00358208,0.01792072,-0.0091259,0.04326241,0.03606907,0.03316292,0.1078541,0.06944213,0.06673936,-0.0469968,0.00695177,-0.01704541,0.05431688,0.04930076,-0.01777837,0.02876193,0.04736828,-0.02020127,0.0194843,0.00345579,-0.05324231,0.04648238,-0.06138232,0.0976264,-0.02868164,0.01547482,-0.01153464,0.07726887,0.01078296,-0.04196198,-0.11538117,0.03180233,0.0625864,-0.01852076,-0.02114139,-0.0389371,-0.03507638,-0.0002507,0.01731776,0.03826351,-0.0832502,0.02261079,-0.07251383,-0.02422177,-0.04688227,-0.01304385,0.02053486,-0.0135979,0.03251754,0.00615757,0.10719091,-0.01991933,-0.00817196,0.02746394,-0.05916837,0.01080382,0.03997558,-0.00464454,-0.00482979,-0.02454699,-0.00498019,0.01915392,-0.00914106,-0.01273135,0.01273388,0.02645958,-0.04377733,-0.01789342,0.13185252,0.04118143,0.00666273,0.03019791,0.02706506,-0.04480343,-0.09525955,0.04642305,-0.00846723,0.02479634,0.01465329,0.03395106,0.0450452,-0.0120968,0.05928794,0.00548319,-0.00697978,0.02722822,0.00877662,-0.00541603,-0.05254919,-0.05688678,-0.03642925,0.05308747,0.01925747,-0.28663668,-0.03428246,-0.01271396,-0.00943509,0.0735903,0.02610762,0.05491579,0.03841981,-0.06391632,0.00233212,0.00039492,0.04637351,-0.0079042,-0.07787631,-0.00163239,-0.02689941,0.03760182,-0.01185933,0.01453158,0.05148936,0.00044206,0.07110865,0.21350309,-0.01518078,0.05621195,0.01310641,-0.07815532,0.04499585,0.04872216,0.01386231,-0.0344293,-0.01576459,0.03641112,-0.04748198,0.0211647,0.01608963,-0.04148608,-0.00336946,-0.01748705,0.01187894,-0.0597082,0.0031914,-0.02480569,0.01960213,0.11270022,0.00695067,-0.05699087,-0.07196511,0.01562199,0.06746413,-0.04200244,-0.00244872,0.02930579,0.04245914,-0.00257368,0.06451388,0.02800721,-0.04369278,-0.07999163,-0.02992295,-0.03536417,-0.02313111,-0.05861874,0.06331178,0.0189919],"last_embed":{"hash":"167tpf0","tokens":267}}},"text":null,"length":0,"last_read":{"hash":"167tpf0","at":1751426529622},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.2. 代币供应、发行与效用**","lines":[110,116],"size":268,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"167tpf0","at":1751426529622}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.3. 市场分析与普遍存在的代币混淆**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05074644,-0.0262056,-0.08421464,-0.01947686,0.04215505,0.02487128,-0.00529218,0.00469466,-0.00367247,-0.02471803,0.04999438,-0.03023742,0.03955343,0.03517967,0.01575708,-0.02848501,0.03968678,-0.04717322,-0.03576712,0.02811436,0.13715556,-0.06099152,0.01837214,-0.0097961,0.07809703,0.07648075,0.0040736,-0.0301659,-0.04262995,-0.17914958,0.00072595,-0.03899865,0.03210809,0.02409172,0.01640216,-0.03475663,-0.0303721,0.05264429,-0.05111501,-0.0071174,-0.0048251,-0.00169971,0.00704019,0.01043112,0.04969403,-0.0954319,0.02704035,-0.07444344,0.02155736,-0.05865603,-0.04465516,-0.08397908,-0.02125778,0.03733399,0.00236988,0.01713858,-0.00984602,0.05142586,0.03699118,0.041503,0.09546102,0.06391122,-0.19196242,0.02252728,0.07978009,0.03674836,-0.02089011,0.00915754,0.05629187,0.06922967,0.03273845,-0.00536261,-0.0254653,0.04875408,0.0595216,0.00226239,0.02066386,0.03378521,-0.02503019,-0.04990651,-0.0470949,0.07872322,-0.00960445,0.0008976,-0.01906397,-0.01937519,-0.01123559,-0.0349505,-0.03527054,0.01205941,0.00730531,0.0299106,0.03969931,-0.021937,-0.0317913,-0.02850728,-0.02180795,0.01961831,-0.09295889,0.11676805,-0.03009322,0.04611512,-0.02031528,-0.05235922,0.03621725,-0.00258909,-0.01527646,-0.06818991,0.04362899,0.03682717,0.02785624,0.00357546,0.07268658,-0.07162073,0.02955761,0.01401582,-0.00378962,-0.0112129,-0.03942501,0.01244,-0.01813706,0.02663253,0.03270404,-0.02959896,-0.03354881,-0.0215369,0.01813648,0.03975951,0.06403811,0.06199491,0.020476,0.04927692,-0.07675979,-0.02613087,-0.05732201,-0.04346043,-0.01438886,0.03272237,-0.02285357,-0.0399844,-0.0342392,-0.0879313,-0.03221297,-0.09258877,-0.0789343,0.02214036,-0.00201542,-0.01313265,0.00310174,-0.03984094,0.003192,0.07888795,-0.03342117,-0.08198649,-0.01631683,-0.00348718,0.04767097,0.11737674,-0.0546499,-0.00750685,-0.00549355,-0.03287847,-0.02998492,0.12854187,0.02912614,-0.1097809,0.05298473,0.03360922,-0.0160459,-0.07074065,0.0213578,-0.01016543,-0.04510497,-0.03200855,0.05209998,-0.02283625,-0.02587806,-0.00389254,0.01176228,0.0579899,-0.00624238,-0.02913708,-0.07288808,-0.0104814,0.01602506,-0.02751275,-0.02397132,0.04153661,0.03562228,0.0220194,-0.10659555,0.05041042,-0.00681982,0.03550586,-0.01527235,-0.0061135,-0.0240686,-0.01057732,0.0041214,-0.04868427,0.12851799,0.03104419,0.03008076,0.01529564,-0.06571801,0.03240038,-0.03707949,-0.059988,-0.00324956,0.03929987,-0.02758641,0.03418437,-0.03912367,0.04158958,-0.0215429,0.01408293,0.00267662,0.04747187,-0.01258981,0.03468483,0.02269525,-0.01108258,-0.08265598,-0.18016221,-0.03262373,0.06981794,-0.02353506,0.02377779,-0.01674819,0.05671614,0.03463418,0.02507785,0.1117226,0.07871915,0.05407567,-0.05313779,0.0130201,-0.01518314,0.05571646,0.0700942,-0.0185388,0.01712883,0.04294932,-0.02607241,0.01163486,0.01994708,-0.05313746,0.03029648,-0.06683796,0.11188607,-0.0469049,0.0152395,0.02117326,0.07475518,0.00293515,-0.03113353,-0.1279135,0.03556255,0.06550415,-0.01203495,-0.01647455,-0.05980708,-0.01432586,0.00294085,0.00442895,0.03657704,-0.09675035,0.02203806,-0.07459115,-0.02696,-0.04938455,-0.00454484,0.03956573,-0.00204814,0.00761784,0.02221099,0.09743121,-0.01047159,-0.01951945,0.01159091,-0.08424199,-0.00409348,0.04209488,-0.0044157,-0.00487201,-0.01655866,-0.01759935,0.02595211,-0.00962701,-0.02126376,0.02485101,0.04299141,-0.03216666,-0.02547327,0.15725094,0.02257557,0.0138973,0.03709316,0.03400227,-0.03206545,-0.09295163,0.04485586,0.02171234,0.03691373,0.02019003,0.03268608,0.05156443,-0.00775947,0.06403914,0.01384144,-0.01775313,0.02796095,-0.01235737,-0.01121939,-0.06789464,-0.05912161,-0.02011995,0.07157879,0.0228146,-0.28148267,-0.04426544,-0.01305727,-0.00587442,0.06619196,0.03383453,0.06713084,0.04608826,-0.07041249,0.01703356,-0.00723149,0.06352141,-0.01752805,-0.06099146,0.01482024,-0.02589042,0.04049728,-0.02239593,0.01233571,0.0224504,0.00848276,0.06353442,0.21129674,-0.01358895,0.02047992,0.00739603,-0.06833272,0.06967391,0.01756295,0.0043571,-0.03446948,0.00026937,0.0256389,-0.04748027,0.02710697,0.05027702,-0.01732126,-0.02726706,0.00402894,0.02628721,-0.02370982,-0.01069418,-0.01197772,0.03463494,0.10802957,-0.0159524,-0.05273912,-0.06409042,0.03594305,0.05105382,-0.05483551,-0.01452085,0.01704534,0.02697639,-0.02663223,0.07090456,0.02557379,-0.02493084,-0.08027125,-0.04816902,-0.03715477,-0.01362433,-0.03200983,0.07637152,0.01398523],"last_embed":{"hash":"g1efas","tokens":451}}},"text":null,"length":0,"last_read":{"hash":"g1efas","at":1751426529631},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.3. 市场分析与普遍存在的代币混淆**","lines":[117,124],"size":493,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"g1efas","at":1751426529631}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.3. 市场分析与普遍存在的代币混淆**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05118585,-0.02678887,-0.08354244,-0.01617559,0.03977463,0.02513758,-0.00411645,0.0060162,-0.0043601,-0.02307867,0.04954262,-0.028624,0.03574609,0.03393221,0.01664365,-0.03203422,0.04276044,-0.05025405,-0.04193861,0.03332094,0.13860266,-0.06293022,0.01897454,-0.0115314,0.07578353,0.0775601,0.0030531,-0.03126133,-0.04399537,-0.18059447,0.00126692,-0.03831108,0.02965734,0.0230445,0.0162889,-0.03413244,-0.03225128,0.05193798,-0.05054434,-0.00811384,-0.00695784,-0.00142616,0.00623033,0.00955258,0.04972768,-0.09824043,0.02562886,-0.0750754,0.02264896,-0.05762843,-0.04422644,-0.08267804,-0.02048799,0.03721592,-0.00198517,0.01460591,-0.00728389,0.05471284,0.04058161,0.04189019,0.09452415,0.06815664,-0.19097055,0.02314458,0.07973713,0.03654131,-0.01727611,0.00468089,0.05631482,0.0691685,0.03771827,-0.00452661,-0.02558973,0.04767867,0.06121213,-0.00179206,0.02104825,0.03171635,-0.02550513,-0.04707178,-0.04628694,0.0791374,-0.0110442,0.00258518,-0.01889393,-0.01676637,-0.00850274,-0.03613799,-0.03820788,0.01237675,0.0052613,0.03106128,0.03676331,-0.02227495,-0.03177801,-0.02753572,-0.02246223,0.01948714,-0.09228694,0.11653757,-0.02642679,0.04551052,-0.0218852,-0.05276958,0.0345931,-0.00015102,-0.01635144,-0.06937,0.04282925,0.04040959,0.02962407,0.0070188,0.07214417,-0.06844183,0.02748466,0.01395192,-0.01001792,-0.0098727,-0.04281582,0.00629793,-0.01868584,0.0239758,0.03820089,-0.02886545,-0.03255746,-0.02326358,0.01866,0.0407588,0.06119421,0.06172086,0.02234007,0.04555384,-0.07723949,-0.02886741,-0.0560489,-0.04271727,-0.01347514,0.03102018,-0.02435663,-0.03507376,-0.03298858,-0.09042624,-0.03031001,-0.08977774,-0.08135144,0.01879416,-0.00193631,-0.01370668,0.00517963,-0.03967442,0.00129723,0.07921237,-0.0347528,-0.07984025,-0.01670446,-0.00331113,0.04749004,0.11789325,-0.05163758,-0.0077891,-0.00711817,-0.03458041,-0.02978296,0.13166243,0.02872673,-0.10979395,0.05240948,0.03553281,-0.01741741,-0.07081607,0.01975966,-0.0120844,-0.04516347,-0.03434913,0.05037434,-0.02406273,-0.02472435,-0.0045274,0.01107094,0.05594948,-0.00581576,-0.03117934,-0.07299136,-0.01263501,0.01799682,-0.02993009,-0.02441242,0.03915018,0.0365851,0.02076268,-0.10559627,0.05337277,-0.00750997,0.03306727,-0.01526775,-0.00720786,-0.02351831,-0.01176335,0.00500511,-0.04805554,0.12749486,0.03146741,0.03534424,0.01760389,-0.06579074,0.03377824,-0.03836112,-0.05792375,-0.00329009,0.03519762,-0.0275778,0.03644291,-0.04060257,0.0419622,-0.02224389,0.01587189,0.00153725,0.0428636,-0.01346157,0.03412478,0.02074125,-0.01134037,-0.07972498,-0.180507,-0.03084773,0.06969529,-0.02350452,0.02311834,-0.0161332,0.05544639,0.03753043,0.02814241,0.11169726,0.08226912,0.0529356,-0.05270691,0.0103934,-0.01611302,0.05842982,0.06795386,-0.01807013,0.01425301,0.04099033,-0.02664877,0.01025247,0.02213354,-0.05145264,0.03070139,-0.06576503,0.11546527,-0.04604762,0.01692626,0.02287157,0.07324574,-0.00140443,-0.03010537,-0.12400857,0.03354042,0.06691401,-0.00967487,-0.01524923,-0.05937969,-0.01547806,0.00101722,0.00639052,0.03659024,-0.09829344,0.01958734,-0.07637049,-0.02388144,-0.04843744,-0.00134106,0.0443457,-0.00222431,0.00713653,0.01920262,0.0932112,-0.00679847,-0.0202476,0.01154317,-0.08624651,-0.00208521,0.04052966,-0.00656596,-0.00356708,-0.01706442,-0.02132694,0.0234783,-0.01343007,-0.02019443,0.02509668,0.0434489,-0.03218765,-0.02543864,0.15586868,0.02357002,0.01500625,0.04124589,0.03228728,-0.03195059,-0.09264353,0.04199925,0.0247096,0.03592237,0.0185367,0.03361104,0.05804938,-0.00743145,0.0630688,0.01706202,-0.02222521,0.02598832,-0.01506216,-0.0100706,-0.0694524,-0.05997372,-0.02025901,0.07251345,0.02200429,-0.27932194,-0.0441708,-0.01126932,-0.00596916,0.06882251,0.03429053,0.06856398,0.04974744,-0.06963895,0.01371481,-0.00853046,0.06508052,-0.01529451,-0.05593218,0.01419281,-0.02482276,0.03932109,-0.02453438,0.01090009,0.01806103,0.0103367,0.06232263,0.21153969,-0.01532179,0.01639901,0.00610563,-0.06763645,0.07282177,0.01399534,0.00749153,-0.03315639,0.00185675,0.02239985,-0.04638415,0.02863874,0.05248974,-0.01758712,-0.02662699,0.00752729,0.02954612,-0.02169999,-0.01049833,-0.01064856,0.03364822,0.10737116,-0.01366869,-0.05395425,-0.05987681,0.03834778,0.05063439,-0.05728872,-0.01385139,0.01609831,0.02657726,-0.02992814,0.07100981,0.02737441,-0.02483933,-0.07882227,-0.04904217,-0.03862217,-0.01055751,-0.03046008,0.07639483,0.01613331],"last_embed":{"hash":"1o1qkpl","tokens":448}}},"text":null,"length":0,"last_read":{"hash":"1o1qkpl","at":1751426529646},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.3. 市场分析与普遍存在的代币混淆**#{1}","lines":[119,124],"size":463,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1o1qkpl","at":1751426529646}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.4. 挖矿经济学**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05493735,-0.00128597,-0.07300858,-0.01184288,0.0197305,0.01825698,-0.01227348,0.01523732,0.02038625,-0.00811904,0.08128938,-0.06351419,0.02452328,0.03173312,0.02089131,-0.01687875,0.01239062,-0.03811524,-0.02740641,0.02645533,0.07989337,-0.08260567,0.03337016,-0.03291187,0.05800228,0.06206615,-0.00217003,-0.02811151,-0.03765726,-0.17724337,0.01389325,-0.0544191,0.00491259,0.01700836,0.01302978,-0.05549274,-0.01929367,0.06718767,-0.06756492,-0.00320049,-0.00354189,0.02737601,0.00495261,-0.0151552,0.05905114,-0.08323725,0.02584918,-0.0694697,0.00514091,-0.06767574,-0.03534842,-0.08413984,-0.00309085,0.03821814,-0.00225604,0.01918128,0.01152448,0.02891122,0.03236979,0.00963921,0.07424857,0.04866578,-0.1818912,0.01088972,0.11114135,0.01223995,-0.03114319,0.0149437,0.046921,0.07146844,0.02206912,0.00007368,0.0062059,0.04557795,0.03907156,-0.00439923,0.03430769,0.02874417,-0.0151285,-0.05800263,-0.04361643,0.07843655,-0.0049041,-0.01916781,-0.01755369,-0.0182975,-0.00893074,-0.03521421,-0.04501973,0.00756164,-0.0051583,0.02442932,0.02667781,0.00193752,-0.02007651,0.0079687,-0.01664174,0.02094547,-0.10414529,0.11481687,-0.00436748,0.03540835,-0.01154189,-0.05062924,0.02116375,-0.01203333,-0.01681444,-0.06033745,0.02080898,0.01710706,0.01987214,0.008486,0.07958402,-0.03941446,0.05760501,0.00565832,-0.02415584,-0.03519426,-0.02197845,0.03403955,-0.00786186,0.03873215,0.00730755,-0.03595791,-0.02628913,-0.03440023,-0.00080695,0.05223528,0.04453024,0.04286302,0.01634043,0.035197,-0.07534257,0.02447191,-0.03688057,-0.03447534,-0.00090045,0.01956766,-0.02110067,-0.02889594,-0.02927484,-0.06119692,-0.02393628,-0.12110668,-0.04620028,0.07067927,-0.00183731,0.01278912,0.01767612,-0.04990752,0.01333128,0.08072689,-0.02571009,-0.07145134,-0.0192776,0.01477522,0.0307744,0.09912155,-0.06693568,-0.00974818,-0.02521047,-0.02609242,-0.04518404,0.12785096,0.05635342,-0.10243089,0.01215846,0.02505045,-0.01731512,-0.06706128,0.00820657,0.03208191,-0.06382658,-0.01213548,0.07505392,-0.04081892,-0.03703646,-0.00122922,0.01964724,0.04312682,0.0199755,-0.01282375,-0.07751308,-0.05130757,-0.01995263,-0.02590837,-0.01641018,0.02840216,0.03456507,0.04044368,-0.11884401,0.04180215,-0.00637716,0.03463369,-0.02362898,0.0067063,-0.00966671,-0.01600958,0.01544828,-0.05034777,0.14231816,0.04432656,0.00090857,0.02413226,-0.06420916,0.02536129,-0.0486654,-0.05181684,-0.01454276,0.03570352,-0.01952594,-0.00156818,-0.03710248,0.05474028,-0.03277141,0.01070087,-0.01205673,0.04926267,-0.03842312,0.0371036,0.04299572,0.00741101,-0.09398978,-0.18522587,-0.04020752,0.06434458,-0.01606192,0.02500891,0.0048264,0.04422503,0.02413188,0.04816371,0.09682409,0.08481224,0.07218045,-0.04903119,-0.01564323,-0.01755512,0.05882984,0.06525722,-0.00748164,-0.00104926,0.02046975,-0.03951051,0.03446016,0.02183959,-0.04680429,0.04726192,-0.06037686,0.09955684,-0.0445821,0.01404379,-0.00323621,0.07916419,0.01437402,-0.04169935,-0.13168532,0.04916716,0.08360863,0.00808577,-0.01032776,-0.05718958,-0.02794909,-0.00573199,0.01418168,0.03427631,-0.09270532,0.0183316,-0.06811411,-0.02363073,-0.03658755,-0.01251295,0.05441453,-0.00280739,0.01629995,0.01693981,0.08519927,0.00966103,-0.00961369,0.0157226,-0.03360785,-0.01125191,0.04492131,0.00274746,-0.0145359,-0.02675795,0.01110099,0.01694572,-0.01590978,-0.03112925,0.03775748,0.03093413,-0.00866558,-0.01047685,0.12961498,0.01579097,0.00678743,0.06166176,0.02631462,-0.03158341,-0.09997037,0.06070155,-0.00394904,0.01343964,0.00666098,0.02876508,0.05741998,-0.01502213,0.07506955,-0.00794951,-0.01684531,0.00848645,-0.00018881,-0.02540976,-0.05479877,-0.05130323,-0.02676751,0.07595387,0.02238738,-0.30580649,-0.0411928,-0.02644939,0.00443112,0.06549782,0.01890836,0.0680517,0.04069373,-0.07896658,0.03187422,-0.01508877,0.05184349,-0.00347662,-0.05289827,-0.00805032,-0.02289341,0.06850647,-0.02458581,0.01849173,0.05714108,-0.00431243,0.06518515,0.22683679,-0.02094559,0.05806959,-0.00048364,-0.05706989,0.06138401,0.04004565,0.03313794,-0.04045272,-0.01762624,0.02829537,-0.04517654,0.03267244,0.04433466,-0.00864649,0.00910441,0.00214873,0.033135,-0.03882282,-0.00888645,-0.02965956,0.00961895,0.10536268,-0.00198085,-0.05225503,-0.09698218,0.0105357,0.06075234,-0.0394248,-0.03857761,0.03584913,0.03177067,-0.00747601,0.07365111,0.03068973,-0.03889228,-0.07210299,-0.00821432,-0.02682356,-0.03236361,-0.04490343,0.08025664,0.00941192],"last_embed":{"hash":"eslj4o","tokens":462}}},"text":null,"length":0,"last_read":{"hash":"eslj4o","at":1751426529657},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.4. 挖矿经济学**","lines":[125,132],"size":470,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"eslj4o","at":1751426529657}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**表 2: $NOCK 代币经济学与分配模型**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03558607,-0.02999668,-0.06755068,-0.03081864,0.03567937,0.00545575,-0.00047727,-0.00172783,-0.00828822,-0.01546039,0.07223667,-0.05387897,0.03822843,0.03365754,0.02894338,-0.00672107,0.00983005,-0.02727839,-0.02027798,0.01153462,0.11327067,-0.06695801,0.02684721,-0.01170851,0.07220381,0.05587969,-0.00446206,-0.02819464,-0.03308947,-0.17991063,0.01638379,-0.04912202,0.0298239,0.02100396,0.03116691,-0.05196939,-0.04610348,0.04758924,-0.05257887,-0.00299772,0.0064315,0.01053469,0.00288297,0.00725028,0.03735207,-0.07737287,0.04185271,-0.07399637,0.00987329,-0.05107168,-0.04383384,-0.08381977,-0.00766358,0.04500772,0.0255079,0.03286831,-0.00107632,0.03895252,0.03326784,0.0417374,0.0944622,0.04954296,-0.19750381,-0.00015636,0.08305064,0.02763415,-0.01719189,0.03660787,0.04563925,0.06889487,0.01980814,0.01298222,-0.00916475,0.04863925,0.02483106,0.02233458,0.03240318,0.02615177,-0.02764862,-0.08415774,-0.06678714,0.08258715,-0.01040611,-0.01197428,-0.01971085,-0.02969182,-0.00490172,-0.03699853,-0.04541169,0.01253788,0.00755498,0.0166635,0.04066378,-0.0013134,-0.04002962,-0.00637888,-0.0160098,0.01985068,-0.08280014,0.11022648,-0.01725787,0.06074255,0.01516864,-0.08389139,0.02802631,0.00481831,-0.03970581,-0.05644622,0.02036485,0.0278962,0.00775022,0.00811519,0.08943164,-0.06366715,0.01988687,0.00208082,0.00085431,-0.0033815,-0.00773649,0.01120612,0.00364563,0.0211666,0.03389239,-0.03389321,-0.03809381,-0.02809767,0.01601722,0.05326677,0.07127876,0.04053662,0.02268108,0.03963722,-0.08806611,0.00153216,-0.06350216,-0.04005215,-0.0185869,0.02558266,-0.016744,-0.02959123,-0.03642594,-0.06014305,-0.04333133,-0.11469274,-0.05693497,0.06284457,0.00285485,-0.00956253,0.01399881,-0.04358958,0.00060327,0.07035019,-0.01816012,-0.08744384,-0.00056351,0.00899166,0.02409547,0.11680529,-0.05581301,-0.02252013,-0.02889154,-0.01483232,-0.04781754,0.13783276,0.05085631,-0.09494111,0.03394276,0.02085693,-0.01942717,-0.08328357,0.01202462,0.03463782,-0.05411439,0.00422315,0.08753444,-0.03461153,-0.01773247,-0.02271132,0.01157325,0.04597214,0.01697212,-0.02262388,-0.07157781,-0.02087382,-0.00712353,-0.02180127,-0.03793123,0.0402586,0.0254073,0.04178887,-0.09346326,0.04772742,-0.00143988,0.02936825,-0.03107343,-0.00620692,-0.00716789,-0.00617681,0.02089494,-0.06305398,0.1656606,0.02992985,0.00742731,-0.00288626,-0.05869729,0.02748596,-0.02403272,-0.03918113,-0.01717545,0.03530203,0.00489635,0.02560809,-0.02909517,0.05555889,-0.03348899,0.00724963,-0.005348,0.05741545,-0.02596982,0.05311499,0.01393657,0.00262933,-0.08844242,-0.19550167,-0.02975115,0.06665096,-0.01272427,0.01480536,0.00780763,0.04864509,0.01233904,0.04715661,0.10158017,0.08031419,0.05260072,-0.04158023,0.02310152,-0.00809873,0.05715252,0.05620203,-0.01248997,-0.00112923,0.04008433,-0.03210403,0.0157971,0.01436614,-0.04321674,0.06171209,-0.0646634,0.09608413,-0.02912378,-0.00330288,0.00508882,0.09060664,0.0161361,-0.04383761,-0.11350899,0.03769962,0.07506333,-0.01662334,-0.00957542,-0.05745183,-0.03218371,0.00321953,0.00807414,0.04525655,-0.07802123,0.01016111,-0.06403302,-0.03674347,-0.03199817,-0.01113327,0.03072353,-0.02044918,0.02529124,0.03642541,0.106264,-0.00773826,-0.0086987,0.02140052,-0.03776954,-0.01384874,0.02499468,0.00015178,-0.01748148,-0.02657954,-0.00990533,0.0171419,-0.01854663,-0.00675745,0.02025378,0.0317337,-0.02392177,-0.03253011,0.11723755,0.03063176,0.00953769,0.03561766,0.02703105,-0.04528749,-0.0767706,0.06188493,0.00707706,0.02776618,0.0117878,0.02824951,0.06054271,-0.01423623,0.07009166,0.02656949,-0.01548484,0.02797522,0.00902613,-0.03353165,-0.05103544,-0.04546663,-0.02352825,0.06301195,0.01591412,-0.30267325,-0.03700281,-0.01402637,-0.01064265,0.06755955,0.03215608,0.03825146,0.01723843,-0.07782383,0.01494388,-0.00049186,0.04282492,-0.00196842,-0.08803722,-0.00638793,-0.03888972,0.06042744,-0.0274387,0.03579197,0.04284897,-0.00912576,0.07540452,0.21202958,0.00931265,0.03152594,-0.01205928,-0.08104247,0.03660951,0.04042059,0.02073304,-0.01324138,-0.02803707,0.04259568,-0.04158032,0.01823865,0.03607478,-0.03574419,-0.00292202,-0.01411994,0.02231369,-0.06256742,0.0009166,-0.04254245,0.03080612,0.10817917,-0.00094715,-0.04986582,-0.08724595,0.00504985,0.05458513,-0.037913,-0.02157624,0.02407159,0.03886992,-0.02000302,0.08320142,0.03376062,-0.03600849,-0.06586362,-0.03197098,-0.02316908,-0.03341701,-0.05697167,0.05503095,0.01566899],"last_embed":{"hash":"1t0d75l","tokens":281}}},"text":null,"length":0,"last_read":{"hash":"1t0d75l","at":1751426529671},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**表 2: $NOCK 代币经济学与分配模型**","lines":[133,148],"size":312,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1t0d75l","at":1751426529671}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**表 2: $NOCK 代币经济学与分配模型**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03490767,-0.0287784,-0.07195432,-0.03063868,0.03656713,0.00728882,0.00141622,-0.00290632,-0.01052811,-0.01647106,0.07377759,-0.05241718,0.03968281,0.03351329,0.02937975,-0.00557719,0.00820073,-0.02815826,-0.02227283,0.01248513,0.11244028,-0.06536578,0.02613165,-0.01084307,0.07198181,0.05731144,-0.00492201,-0.02905169,-0.03246459,-0.17954771,0.01592235,-0.04764698,0.03077742,0.02257684,0.03017139,-0.05132572,-0.04445516,0.04918664,-0.05466114,-0.00295758,0.00544266,0.01041909,0.00410929,0.00673892,0.03757048,-0.07604177,0.04194582,-0.07432663,0.01382811,-0.05099631,-0.04370461,-0.08500569,-0.00885333,0.04389209,0.0261807,0.03110322,-0.00259017,0.04106174,0.03437598,0.04352055,0.0940472,0.0483774,-0.19703652,0.00010711,0.08316697,0.02705847,-0.0180981,0.03662009,0.04366444,0.07016544,0.02083352,0.01142677,-0.01073913,0.04697709,0.02745659,0.02239449,0.0323367,0.02625931,-0.02724527,-0.08444437,-0.06606282,0.08220805,-0.0101752,-0.01313993,-0.01806805,-0.03095559,-0.00425525,-0.03800047,-0.04451763,0.01254398,0.00815677,0.01875891,0.04011656,-0.0025348,-0.03963539,-0.00917142,-0.01721009,0.02010291,-0.08037936,0.11184751,-0.01674009,0.05911612,0.01457936,-0.08323597,0.02899875,0.0023685,-0.03889894,-0.05755277,0.02264858,0.0282306,0.00918816,0.00870315,0.08954851,-0.06452543,0.02067141,0.00128119,0.00119918,-0.00466967,-0.00973831,0.01309195,0.00320086,0.02178307,0.03282858,-0.03490925,-0.03656856,-0.02670651,0.01777862,0.05219251,0.07439475,0.03990461,0.02363846,0.04115374,-0.08811148,0.00143147,-0.06422897,-0.04029139,-0.01807135,0.02668859,-0.01960182,-0.03148694,-0.0364007,-0.05941105,-0.04183161,-0.11350404,-0.05547959,0.06178234,0.00041451,-0.00871614,0.01274277,-0.04340442,0.00139774,0.06932114,-0.01856587,-0.0886746,-0.00102316,0.0069445,0.02402452,0.11445218,-0.05767366,-0.02199087,-0.02938306,-0.01493896,-0.04816412,0.13614593,0.04959249,-0.09421118,0.03679436,0.02071645,-0.01952318,-0.08245336,0.01363418,0.03405365,-0.05203704,0.00378328,0.08738346,-0.03537303,-0.02044471,-0.01899504,0.01340736,0.04652165,0.0167783,-0.02377337,-0.07246779,-0.02207724,-0.00595187,-0.01880604,-0.03655484,0.0409566,0.02679966,0.04119591,-0.09538405,0.04765764,-0.00155919,0.03177572,-0.03250271,-0.00425583,-0.00756895,-0.00651622,0.01971464,-0.06071836,0.16268003,0.02838384,0.00778859,-0.00253632,-0.05840347,0.0301967,-0.02585299,-0.03948087,-0.01733806,0.0334513,0.00384173,0.02402779,-0.02890502,0.05378286,-0.03164616,0.00742068,-0.00565828,0.0581245,-0.02603945,0.05309924,0.01504215,0.00172626,-0.08861503,-0.1928762,-0.03059161,0.06926289,-0.01254198,0.01525391,0.00714158,0.04933264,0.01366566,0.04659354,0.09869872,0.08105602,0.05290214,-0.04064545,0.02104544,-0.00850402,0.05738168,0.05869574,-0.01271896,-0.00108879,0.04031548,-0.03040346,0.01533745,0.01391318,-0.04543538,0.06152238,-0.06161159,0.09640457,-0.03065587,-0.00219107,0.00590614,0.09074181,0.01643858,-0.04404752,-0.11389564,0.0386709,0.07278159,-0.01525411,-0.01146202,-0.05737011,-0.03430487,0.00190012,0.00900115,0.04467593,-0.07875744,0.01033442,-0.06398866,-0.03887234,-0.03273613,-0.0116162,0.02677523,-0.01815207,0.02613497,0.03500063,0.10627188,-0.00708699,-0.00945549,0.01953599,-0.03840222,-0.0148751,0.02671442,0.0006357,-0.01703745,-0.02724417,-0.01138283,0.01823179,-0.01797477,-0.00637778,0.02242696,0.03028229,-0.02447262,-0.03148154,0.11927723,0.03215834,0.00985774,0.03437588,0.02618311,-0.04363761,-0.07801124,0.06395919,0.00692067,0.02797395,0.01062671,0.02864513,0.06159425,-0.01512483,0.07099798,0.02438818,-0.01553061,0.02892169,0.0089207,-0.03581282,-0.0508378,-0.04500045,-0.02303994,0.0634011,0.01567887,-0.30237225,-0.03603623,-0.01713233,-0.01132478,0.06635585,0.03224408,0.03996033,0.01716715,-0.07841986,0.01229703,0.00055872,0.04298021,-0.00219835,-0.08730149,-0.00701895,-0.03954148,0.06042448,-0.02919927,0.0358006,0.04224066,-0.00851811,0.07606981,0.21304174,0.00703983,0.03349077,-0.01301818,-0.08286646,0.03900143,0.04007199,0.02073638,-0.01362691,-0.02788065,0.04348908,-0.04448465,0.01544607,0.0369884,-0.03606343,-0.00318121,-0.01509332,0.02208331,-0.06219843,0.00035654,-0.04173258,0.03174935,0.10920886,0.00025333,-0.04976602,-0.0863032,0.0066942,0.05626328,-0.0392696,-0.01805077,0.02377719,0.03876891,-0.01914233,0.08205593,0.03122515,-0.03826626,-0.06757933,-0.03390733,-0.02638345,-0.03203696,-0.05389303,0.05445471,0.01433892],"last_embed":{"hash":"ryy7oi","tokens":278}}},"text":null,"length":0,"last_read":{"hash":"ryy7oi","at":1751426529681},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**表 2: $NOCK 代币经济学与分配模型**#{1}","lines":[135,148],"size":280,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"ryy7oi","at":1751426529681}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07264759,-0.0570667,-0.03967669,-0.00904783,0.02698554,-0.01455174,-0.00790137,0.02232914,-0.02983254,-0.00399447,0.07249277,-0.0292634,0.0316067,0.01824509,0.02815948,-0.00583825,0.0229583,-0.05220072,-0.02392462,0.0180378,0.08665167,-0.08542024,0.05138252,-0.06439519,0.09114332,0.03612331,0.04385787,-0.04195885,-0.0430015,-0.18081814,0.0054197,-0.04884077,0.01027883,0.02237564,0.05548934,-0.0589407,-0.03713458,0.08048985,-0.05794112,-0.00687871,0.00727959,-0.01280246,0.01306661,0.03122255,0.02973964,-0.08345372,0.01449612,-0.05831177,-0.01783542,-0.04573894,0.01927646,-0.07794195,-0.03903234,0.02918786,0.0078616,0.03026658,0.00240227,0.03613348,0.008155,0.00635402,0.08756553,0.03949212,-0.21098612,0.01755367,0.07914782,0.04131971,-0.08280706,0.01743349,0.04797678,0.07396317,0.03426448,0.02597683,0.0069795,0.03726644,-0.00414931,0.01057725,0.06093942,-0.0254334,-0.02981505,-0.0581689,-0.04604742,0.0679947,-0.02527727,-0.02176191,-0.01549753,-0.02983255,0.00653817,-0.06074843,-0.01913829,-0.00348101,0.00957716,0.00173453,0.08106013,-0.00037184,-0.00123771,-0.01477784,0.03662438,0.01047961,-0.14338376,0.09312767,-0.00725859,0.03119284,-0.02586512,-0.08203717,0.06008082,-0.03260498,-0.00629278,-0.06200388,0.03161457,0.0158642,0.00065836,0.01937835,0.0647473,-0.04067386,0.02063804,0.00798845,0.02646955,0.01023733,-0.02990133,0.01582183,0.00794686,0.03864989,0.03341976,-0.01079253,-0.05584306,-0.04712654,0.02073567,0.05737453,0.06722483,0.03204183,0.03393189,0.01245685,-0.08709597,0.02142721,-0.0295458,-0.035038,0.00935266,-0.00063975,-0.06923079,-0.05198207,-0.07415565,-0.06859343,-0.04724278,-0.11096817,-0.05751942,0.08221239,0.01314275,-0.02518436,0.00300638,-0.01931349,0.02710553,0.04599646,-0.02204695,-0.08063529,-0.00288687,0.03012737,0.04923651,0.10356663,-0.04671347,0.02134855,0.00915937,-0.02576327,-0.0337178,0.11017068,0.06916197,-0.1482138,0.0495318,0.03303551,-0.00777315,-0.03806114,0.01483054,0.02097169,-0.05123989,0.01107786,0.0615274,-0.07450899,-0.01554814,-0.01322882,0.02151689,0.06275055,-0.01462705,0.00095747,-0.08128597,-0.01967675,0.0039758,-0.01813462,-0.00705074,0.01174322,0.04497099,-0.00587287,-0.07538775,0.06024466,-0.02469662,0.01261755,-0.03660947,-0.04183334,-0.01355494,-0.00574433,0.03162626,-0.05449968,0.13221896,0.02739512,0.01676619,0.01598071,-0.03496503,0.03494693,-0.05601757,-0.04119393,-0.00110274,0.04348234,-0.03506247,-0.00638541,-0.04361389,0.01268925,-0.00174809,0.02218897,0.01253598,0.02962903,-0.02272745,0.07664566,0.00374619,0.03291825,-0.06563316,-0.19158314,-0.04672024,0.03273071,-0.00281374,0.01341031,-0.00834841,0.04629721,0.02386016,0.07562441,0.10606311,0.07493968,0.05863762,-0.02947642,0.03019874,-0.01213982,0.02869055,0.04962954,0.00050908,0.03912715,0.04260226,-0.0398124,0.03771339,0.01266996,-0.04721654,0.01565193,-0.07890978,0.10371829,0.00204844,-0.00158054,-0.00457842,0.05961777,0.00515297,-0.01266545,-0.12718681,0.029829,0.04405995,0.00928857,-0.04179531,-0.02607687,-0.0196966,-0.00408694,0.04897819,0.0445043,-0.10462773,0.03943539,-0.08677725,-0.03027142,-0.01283343,-0.02725277,0.03087198,0.00076601,0.03851666,-0.00061687,0.08699341,-0.00995564,-0.02352971,0.03762251,-0.06423689,0.06440875,0.02640753,0.00413634,-0.01668802,-0.01577619,-0.01675398,0.03701802,-0.02186632,-0.00030883,0.03181369,0.0164982,-0.04444024,-0.02144443,0.14196897,0.04209979,0.01287183,0.0566148,0.00020534,-0.02118495,-0.09281429,0.03995563,-0.00547054,0.01981595,-0.00651943,0.04793413,0.04735034,0.02253708,0.0771587,0.02200782,0.0040882,0.00446801,0.00587871,0.04449276,-0.05837573,-0.07010508,-0.03554197,0.04721127,0.00629216,-0.2788817,-0.01649867,-0.0304965,-0.01990603,0.04554473,0.01225323,0.06783522,0.03444729,-0.03470761,0.0304942,-0.02731077,0.02328413,-0.01050986,-0.08334573,0.01698942,-0.00271843,0.03756144,0.01630935,0.00849147,0.05145226,0.01320052,0.06856798,0.2138252,-0.021694,0.0358991,0.05174648,-0.05263636,0.0436578,0.02412807,-0.00451452,-0.03804227,-0.01495128,0.03591892,-0.03241601,0.03154408,0.04598728,-0.02216302,-0.03562868,-0.01793299,0.00680433,-0.03991243,-0.01517062,-0.01462284,0.04352897,0.10550568,-0.03787056,-0.08296256,-0.04485598,-0.0054821,0.04315163,-0.01641112,-0.02510369,0.01031177,0.02328676,0.00503404,0.0530998,0.04857016,-0.02935982,-0.02998996,-0.03521736,-0.01073545,-0.01131377,-0.05976176,0.05280319,0.02207935],"last_embed":{"hash":"3x9mct","tokens":393}}},"text":null,"length":0,"last_read":{"hash":"3x9mct","at":1751426529690},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**","lines":[149,172],"size":1186,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"3x9mct","at":1751426529690}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.1. 创始人简介：Logan Allen**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07579514,-0.0518982,-0.05484843,-0.00459884,0.02186002,0.00097879,-0.00969869,0.02648921,-0.02966712,-0.00350286,0.06673454,-0.03410728,0.02848343,0.01293633,0.02745042,-0.00933394,0.03562134,-0.04578069,-0.03304392,0.00578005,0.08035246,-0.09063903,0.05139519,-0.05636656,0.09784233,0.0508526,0.04381019,-0.03579981,-0.03385395,-0.1807355,0.00360181,-0.06371189,0.01163972,0.02296592,0.0683485,-0.04308303,-0.04138828,0.07171513,-0.05874802,0.00031212,0.01650493,-0.00119485,0.01763596,0.02880524,0.03323216,-0.08934576,0.03289518,-0.05943276,-0.01506539,-0.05030576,0.00535161,-0.06908969,-0.03853041,0.03348543,-0.00617134,0.02825673,0.01729235,0.05062162,0.02173924,0.00600546,0.09206258,0.04862984,-0.20501535,0.01322963,0.08555909,0.03674682,-0.08864681,0.01439884,0.0578251,0.08115306,0.03101862,0.01777518,0.00231225,0.03974337,-0.01055997,0.00371716,0.0632297,-0.03019376,-0.02355751,-0.05315002,-0.04914225,0.06777737,-0.00691737,-0.02757399,-0.02079612,-0.03661016,0.01045687,-0.05464099,-0.02146841,-0.01484728,0.01788978,-0.00491711,0.07753648,-0.00021503,0.00579063,-0.02315887,0.01420632,0.00293677,-0.14667512,0.10728986,-0.00816174,0.02264675,-0.02814933,-0.07546406,0.06120259,-0.03673179,-0.01361234,-0.06981122,0.02649785,0.00494118,0.00634031,0.0269177,0.05562479,-0.04066626,0.03354758,-0.00242038,0.03870831,0.01247698,-0.02285459,0.0265041,0.00758382,0.02667728,0.03715898,0.00488478,-0.05185882,-0.0545268,0.02133735,0.0532613,0.06439568,0.03762154,0.0458259,-0.0121961,-0.09403788,0.00841057,-0.03839123,-0.03348869,0.00740148,0.00941888,-0.06615216,-0.05301339,-0.05328448,-0.07573521,-0.05412496,-0.11058082,-0.05766031,0.0918796,-0.0039373,-0.02150962,-0.00326407,-0.00867048,0.03218845,0.03234461,-0.03834073,-0.0897863,-0.00640889,0.03199105,0.04888292,0.09144913,-0.0388368,0.01819002,0.01252549,-0.02834538,-0.04040816,0.10910899,0.06459432,-0.14137506,0.06019869,0.03741799,-0.00469377,-0.03264742,0.02498531,0.02670328,-0.05023845,0.00502341,0.06527594,-0.07306215,-0.01218103,-0.00865556,0.01810537,0.05891033,-0.01157533,-0.00488311,-0.08095448,-0.01172359,0.00669724,-0.02409267,-0.01041577,0.01593183,0.04304881,0.00374913,-0.06962077,0.05404775,-0.01961981,0.01426076,-0.04033058,-0.03197115,-0.01365133,0.00721085,0.00109795,-0.05339438,0.1350465,0.02794517,0.00879644,0.02029531,-0.01860406,0.03689308,-0.06531544,-0.03595085,-0.0034013,0.04230929,-0.04409318,0.0000155,-0.04644475,0.01738892,-0.02024181,0.01982033,0.01133981,0.02961821,-0.02268585,0.06114226,0.00814557,0.0254337,-0.05379006,-0.20745997,-0.05963423,0.03468926,-0.00220363,0.013604,-0.00686591,0.04561417,0.01709321,0.06713292,0.09910057,0.07310814,0.0687188,-0.02765768,0.01785383,-0.0109811,0.04175496,0.03602435,-0.00375187,0.039126,0.03930894,-0.04990145,0.02620758,0.00549163,-0.05105068,0.016907,-0.07658029,0.10788085,0.01190653,-0.00418067,0.00462086,0.05978807,0.00429602,-0.00357502,-0.12512444,0.02245898,0.04105944,0.00191963,-0.0383672,-0.02810774,-0.02648593,-0.00774674,0.03519362,0.04742341,-0.09726873,0.04093687,-0.07849126,-0.02331997,-0.02571668,-0.00357665,0.0359471,0.01152317,0.03885737,-0.00071355,0.08730543,0.00027531,-0.01873568,0.047011,-0.0536273,0.05691002,0.02514856,0.01001084,-0.01366896,-0.02080469,-0.00809056,0.02614388,-0.01507703,-0.01752224,0.02461742,0.0215495,-0.03209868,-0.02908372,0.13811922,0.04101646,0.01426108,0.0597227,0.01427568,-0.01799648,-0.09121042,0.0375759,-0.02385022,0.01138936,-0.00821403,0.05036741,0.04183605,0.02666091,0.07146408,0.00940994,0.00589314,0.0091443,0.02026197,0.04195783,-0.05830061,-0.0717347,-0.02214797,0.05121982,-0.00673552,-0.27400339,-0.02028109,-0.0269666,-0.00920982,0.04729201,0.01378635,0.07271446,0.02536022,-0.04170313,0.02831534,-0.02182498,0.03103333,-0.02974751,-0.07784749,0.01125549,0.00425705,0.04721948,0.01710994,0.01033527,0.04375502,0.01808408,0.07733095,0.2165595,-0.02698337,0.04783286,0.06442715,-0.05970401,0.03303084,0.03031326,-0.00641775,-0.03785833,-0.01877432,0.02354114,-0.02858518,0.03544151,0.02912343,-0.01897595,-0.0282806,-0.02786826,0.00962143,-0.04726736,-0.00574935,-0.02244952,0.04298252,0.11063783,-0.02760811,-0.07843726,-0.04555693,-0.00944813,0.03777973,-0.01613725,-0.02632764,0.00722501,0.01730572,0.01285249,0.05288989,0.04906255,-0.0224252,-0.03206245,-0.03776017,-0.01374013,-0.01799471,-0.05005,0.0501164,0.02926539],"last_embed":{"hash":"ouk2r1","tokens":279}}},"text":null,"length":0,"last_read":{"hash":"ouk2r1","at":1751426529704},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.1. 创始人简介：Logan Allen**","lines":[151,154],"size":300,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"ouk2r1","at":1751426529704}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.1. 创始人简介：Logan Allen**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07857451,-0.04860665,-0.05477018,-0.00251633,0.01930626,0.00318251,-0.00405757,0.02488296,-0.03146866,-0.00259091,0.07190961,-0.03338292,0.02799747,0.01025697,0.03051273,-0.01038077,0.0385239,-0.04794505,-0.03741012,0.00598973,0.07774393,-0.08826646,0.05033095,-0.05992707,0.09973973,0.0500618,0.04442897,-0.03839736,-0.03193741,-0.18116966,0.00615249,-0.06280405,0.00940477,0.02354852,0.06714565,-0.04025839,-0.03954048,0.07094555,-0.05978916,0.00024542,0.0177825,-0.00222147,0.01786572,0.02958445,0.03497704,-0.09161489,0.03530503,-0.05944412,-0.01456063,-0.04858645,0.01151707,-0.07040109,-0.03943552,0.03157357,-0.00545832,0.02671733,0.01611808,0.05377561,0.02096596,0.00950875,0.09420164,0.04588239,-0.20494913,0.01172138,0.08357292,0.03840663,-0.09112699,0.01469651,0.05680095,0.07888685,0.03434857,0.01656329,0.00410421,0.03760111,-0.00900693,0.00703146,0.06203005,-0.03299206,-0.02560182,-0.05408716,-0.04994611,0.06621988,-0.00535518,-0.02810674,-0.0224794,-0.03469754,0.01237589,-0.05722787,-0.021591,-0.01726271,0.01804169,-0.004511,0.0836947,-0.00332407,0.00663437,-0.02346891,0.01375032,0.00259041,-0.14876719,0.10672748,-0.00509584,0.02453419,-0.03037734,-0.07241242,0.06317154,-0.03911006,-0.01497199,-0.0686918,0.02848235,0.00221227,0.00905941,0.02603893,0.05335433,-0.03734158,0.03205887,-0.00286102,0.03725079,0.01331067,-0.0213361,0.02678175,0.00760206,0.0269278,0.03884622,0.00683221,-0.05680817,-0.05393091,0.02238265,0.05179115,0.06523986,0.03605965,0.04592004,-0.01304743,-0.09185925,0.00643542,-0.03509973,-0.03172153,0.0098737,0.0088621,-0.0676284,-0.05343279,-0.05253385,-0.07735118,-0.05473354,-0.10717855,-0.05579078,0.0917875,-0.00466256,-0.02004033,-0.00458493,-0.00742749,0.03242748,0.03105197,-0.03807935,-0.08647041,-0.00778004,0.02967446,0.04817887,0.08886506,-0.04059751,0.02028931,0.01196728,-0.02712477,-0.03894582,0.10372461,0.06399836,-0.14141379,0.06023651,0.03556203,-0.0041259,-0.03286036,0.02699733,0.0237212,-0.04898711,0.00705118,0.06880755,-0.07541166,-0.01006791,-0.00544065,0.01711486,0.05718547,-0.01500581,-0.00586574,-0.08121987,-0.01627322,0.00719611,-0.02613435,-0.0078234,0.01661137,0.04668149,0.00272256,-0.06499609,0.05598246,-0.0201582,0.0146935,-0.04192894,-0.03534732,-0.01593172,0.00636064,-0.00196545,-0.05436208,0.13031626,0.02646501,0.00843333,0.02148875,-0.01362409,0.0361541,-0.06599508,-0.03644691,-0.00420163,0.04054658,-0.04509159,-0.00043319,-0.04855898,0.01783521,-0.02006028,0.02168164,0.01083281,0.02597859,-0.02163108,0.06375501,0.00851355,0.02497874,-0.05574308,-0.20679602,-0.05717741,0.03359143,-0.00184757,0.0145023,-0.01067587,0.04648637,0.01786276,0.06944933,0.09971391,0.07703414,0.06654861,-0.02770463,0.01908539,-0.00929365,0.04252858,0.03492239,-0.00261855,0.04094315,0.03934669,-0.04838858,0.02583974,0.00474308,-0.05108095,0.01562806,-0.07656406,0.10826497,0.01403639,-0.00179937,0.00607412,0.05599144,0.00354043,-0.00651274,-0.12467519,0.02390503,0.04138123,0.00206352,-0.03989195,-0.02587482,-0.02829452,-0.00724722,0.03595313,0.04515727,-0.09711098,0.04294161,-0.07759867,-0.02637906,-0.02489864,-0.00730727,0.03233101,0.00938254,0.04027006,-0.00453765,0.0854437,-0.00307884,-0.01974451,0.04561727,-0.05566245,0.05934841,0.02793695,0.00882254,-0.01533096,-0.02033191,-0.00952657,0.02911325,-0.01671801,-0.02127851,0.02519129,0.02250878,-0.03305181,-0.02938298,0.13916107,0.04043547,0.01422056,0.0608429,0.01341793,-0.01637005,-0.09171666,0.03540251,-0.01885124,0.01026984,-0.01096301,0.05098284,0.04026155,0.02760456,0.07432676,0.00854115,0.00449567,0.00887598,0.02054062,0.04533266,-0.05999961,-0.07287034,-0.02387764,0.05188707,-0.00887357,-0.2713373,-0.01769135,-0.02758408,-0.01023671,0.0446621,0.01304487,0.07172835,0.02622364,-0.04277195,0.02943819,-0.02293783,0.0300731,-0.02680725,-0.07864327,0.01335338,0.0051099,0.04795487,0.01898791,0.00966959,0.04306829,0.02061145,0.07589476,0.21906005,-0.0250526,0.04676135,0.06136194,-0.06022554,0.03102632,0.03089638,-0.00559643,-0.03736287,-0.01831162,0.02189276,-0.02623714,0.03167795,0.03163251,-0.01996147,-0.0265731,-0.02674013,0.00906607,-0.04819214,-0.0094357,-0.02429433,0.03945864,0.11274225,-0.03012758,-0.07970393,-0.04401514,-0.0103223,0.03666586,-0.01408231,-0.02109859,0.00803181,0.01925465,0.01235695,0.05131875,0.04766176,-0.02064354,-0.02943185,-0.03831503,-0.01274142,-0.01625389,-0.04847176,0.04975321,0.03446387],"last_embed":{"hash":"1nz1r7d","tokens":276}}},"text":null,"length":0,"last_read":{"hash":"1nz1r7d","at":1751426529714},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.1. 创始人简介：Logan Allen**#{1}","lines":[153,154],"size":267,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1nz1r7d","at":1751426529714}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.2. 投资者分析与“公平启动”的困境**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03912712,-0.0190057,-0.0521008,-0.04811718,0.03849241,-0.01764496,-0.00593696,0.00640977,-0.00375244,-0.01524071,0.07930135,-0.02136646,0.04444223,0.03862808,0.02158823,-0.00803998,-0.00795175,-0.05015446,-0.01496055,0.03760118,0.11557357,-0.08119277,0.028169,-0.027486,0.0737076,0.0259981,0.01864045,-0.03417294,-0.04593671,-0.17957993,0.01040372,-0.04180174,0.02072532,0.02793973,0.01770038,-0.06644768,-0.00973263,0.07245451,-0.04985617,-0.00974471,-0.01631042,-0.00147698,0.00048881,0.01543617,0.02465519,-0.08111763,0.00933559,-0.05989386,0.00398739,-0.0633081,-0.00157295,-0.09462836,-0.01344479,0.02964029,0.01312003,0.01177472,-0.01210256,0.01332485,0.00309879,0.02739787,0.06513219,0.02365865,-0.20701638,0.00804012,0.05898704,0.02239092,-0.02117576,0.02167367,0.01202667,0.06640311,0.02345419,0.00650578,0.00461744,0.03377977,0.02087012,-0.00792077,0.04669136,0.0180153,-0.03444044,-0.07452897,-0.0244194,0.08221763,-0.02537627,-0.00120657,0.00551842,-0.00402078,-0.00369817,-0.04611594,-0.01819791,0.02985671,-0.01085654,0.01113439,0.05912061,-0.01166587,-0.03892744,0.00401838,0.01986622,0.03215641,-0.10587043,0.09477422,-0.02398227,0.04592644,-0.0128591,-0.08056033,0.03398549,0.00493772,-0.00181812,-0.04033727,0.04542196,0.04262644,-0.02029346,-0.01561452,0.08052085,-0.0498413,0.01305472,0.02537224,-0.01704402,-0.00055368,-0.04369642,0.00135755,0.00147925,0.0536855,0.01996303,-0.04462516,-0.03726318,-0.01612896,0.01839993,0.06726584,0.05138801,0.03548916,0.01618085,0.05560829,-0.07025359,0.02833387,-0.02096403,-0.03927723,-0.00196418,0.00517447,-0.03371948,-0.0533191,-0.06443433,-0.05904943,-0.02537706,-0.11389568,-0.05040552,0.05500692,0.02243727,-0.01161671,0.01582232,-0.04346648,0.00728884,0.07451772,-0.01053246,-0.08106348,-0.02189308,-0.00132078,0.02206989,0.12122467,-0.07492828,0.01138187,-0.00823221,-0.03367277,-0.02990028,0.13589104,0.03993251,-0.13655709,0.02242697,0.03085121,-0.01744632,-0.06781202,-0.01347707,0.0205293,-0.05454236,-0.01187535,0.07279671,-0.04672823,-0.03779013,-0.00682688,0.00917915,0.05795897,-0.00429096,0.00538753,-0.07565793,-0.0236157,-0.02646699,-0.00225538,-0.00030985,0.02282669,0.01481516,-0.00077447,-0.09438366,0.04139486,-0.0284907,0.0267359,-0.03530124,-0.00636586,-0.0140561,-0.0114687,0.06268676,-0.06427745,0.12654378,0.01848789,0.01710419,0.01984882,-0.0772443,0.02403379,-0.0184494,-0.0572796,-0.01147731,0.0398025,-0.01405719,0.00982074,-0.02320351,0.03183989,0.0136506,0.01953924,-0.00504838,0.04869511,-0.02884601,0.06123336,0.02055106,0.04783794,-0.08542284,-0.18024075,-0.02548943,0.06033957,-0.01732069,0.02837384,0.00708263,0.05365719,0.0445073,0.0646034,0.0867907,0.09324698,0.05055288,-0.04172998,0.02269775,-0.01328961,0.04046393,0.08622465,-0.00041672,0.00166145,0.03109114,-0.0220324,0.04557831,0.02461841,-0.02065914,0.06152998,-0.05635482,0.08376496,-0.02442862,0.01048973,-0.00409192,0.08327329,0.01291039,-0.03306648,-0.13539489,0.04786584,0.04959678,0.00000923,-0.02809151,-0.05257744,-0.01260041,0.00219521,0.0359424,0.03558546,-0.09664935,0.01248521,-0.08183819,-0.03876877,0.00727739,-0.04351591,0.03190902,-0.01135613,0.03351291,0.01584074,0.12154658,-0.00576804,-0.02576441,0.0079247,-0.06554926,0.00651272,0.02504879,-0.00719411,-0.02443126,-0.01955631,-0.03207462,0.03569514,-0.020122,0.03020348,0.04037461,0.04183011,-0.05778052,-0.03759282,0.12940529,0.02695634,-0.00097609,0.05949376,0.01587895,-0.05191638,-0.10082009,0.06513448,0.01950438,0.03737075,0.02894491,0.04006057,0.05465893,0.00686135,0.05807455,0.03664235,-0.01462941,0.0262826,-0.02783293,-0.01181783,-0.05229232,-0.05231164,-0.04094479,0.05917415,0.02525574,-0.30666575,-0.0313109,-0.02556982,-0.02951843,0.05887827,0.03490281,0.0382658,0.04459952,-0.04708455,0.03533931,-0.01717296,0.02065356,0.01082319,-0.06276929,0.0158454,-0.02764978,0.01010897,-0.01155827,0.01893099,0.05847916,0.00075617,0.06794348,0.22281562,0.0317944,0.01074903,-0.00201233,-0.03573914,0.06667548,0.01666293,-0.00009433,-0.03575547,-0.02751843,0.05216838,-0.02792961,0.03014936,0.05418994,-0.02050275,-0.01190657,-0.0022844,0.00699012,-0.04584546,-0.01926808,-0.01263679,0.03242019,0.11261339,-0.01245045,-0.05303378,-0.04828693,0.00698058,0.06337635,-0.0496433,-0.02815812,0.02249221,0.03128585,-0.01659188,0.07685614,0.03425193,-0.05213485,-0.05768202,-0.03718714,-0.02134383,-0.00642192,-0.07808535,0.06882418,-0.00615048],"last_embed":{"hash":"12jlqem","tokens":431}}},"text":null,"length":0,"last_read":{"hash":"12jlqem","at":1751426529721},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.2. 投资者分析与“公平启动”的困境**","lines":[155,166],"size":662,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"12jlqem","at":1751426529721}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.2. 投资者分析与“公平启动”的困境**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04050531,-0.01497015,-0.04864675,-0.04457187,0.0399523,-0.02102929,-0.00385116,0.00802941,-0.00311354,-0.01430808,0.07930025,-0.02294336,0.04811038,0.03530939,0.02436521,-0.00996642,-0.00441119,-0.04438724,-0.01795068,0.03411183,0.11658964,-0.07991399,0.02602719,-0.02609922,0.06989053,0.02881689,0.01573003,-0.03425011,-0.04444411,-0.17944883,0.01158445,-0.0401378,0.02100021,0.03161913,0.01482023,-0.07162815,-0.00962143,0.07568046,-0.05519828,-0.01196327,-0.01392022,-0.00318835,-0.00268557,0.01411965,0.01990797,-0.07909226,0.01472,-0.061138,0.00441724,-0.06437151,-0.00438946,-0.0927296,-0.01026381,0.02874029,0.01622303,0.01557617,-0.01216553,0.01245662,0.00261253,0.02449436,0.06192516,0.02196558,-0.20798884,0.00736368,0.05953542,0.02269361,-0.02253014,0.01979008,0.01074884,0.06256744,0.02482602,0.00919813,0.00330279,0.03392849,0.02435667,-0.00678413,0.04720499,0.01631792,-0.03529321,-0.07367438,-0.02327226,0.08044778,-0.0276088,-0.00038654,0.00466275,0.00024918,-0.00316621,-0.04656068,-0.02056773,0.02748173,-0.00621805,0.01002265,0.05649687,-0.00849313,-0.03423072,0.0031921,0.01921125,0.03324417,-0.10467354,0.0953423,-0.02260606,0.04770342,-0.01258511,-0.08400259,0.03666208,0.00575111,-0.0051368,-0.0389047,0.04507978,0.04329687,-0.01750015,-0.01530849,0.07920796,-0.05005819,0.00966034,0.02475322,-0.01939773,-0.0012606,-0.04248477,-0.00207776,0.00292546,0.05147021,0.01793339,-0.04338754,-0.03534065,-0.01321552,0.01788226,0.06956135,0.04856489,0.03844728,0.01488823,0.0561325,-0.07048059,0.02651392,-0.02041061,-0.03707277,-0.00055892,0.00558572,-0.03502075,-0.05346633,-0.06321653,-0.05863785,-0.02539722,-0.11669448,-0.05261208,0.05602045,0.02452315,-0.01252331,0.01457232,-0.04667786,0.00608014,0.07706276,-0.00918601,-0.07839195,-0.02104306,-0.00693155,0.02331934,0.12369511,-0.07767512,0.00489616,-0.01115522,-0.03396478,-0.02979992,0.13753191,0.0395833,-0.13461135,0.02245229,0.03126483,-0.01682678,-0.06747361,-0.01363953,0.02271227,-0.05758861,-0.00829845,0.07075012,-0.0479983,-0.04063579,-0.00822134,0.01334821,0.05714682,-0.00759156,0.00524078,-0.07382835,-0.02283962,-0.02833125,-0.00405814,-0.00047674,0.02223033,0.01542674,0.00494072,-0.09574778,0.04168721,-0.02519261,0.02302679,-0.03942688,-0.00498562,-0.01393891,-0.0082097,0.06474707,-0.06057666,0.12419557,0.02338277,0.01709647,0.01905912,-0.07815925,0.03045048,-0.02115569,-0.06126289,-0.00665515,0.03980736,-0.01805122,0.01176217,-0.02578593,0.03074391,0.01213211,0.01863794,-0.0068602,0.04492644,-0.03002431,0.06142316,0.02024195,0.04553274,-0.08524405,-0.18010871,-0.02550698,0.05807335,-0.01585544,0.03178927,0.00613373,0.05715451,0.04410865,0.06460925,0.08302407,0.09385152,0.05293867,-0.04552803,0.02044359,-0.0159977,0.03992233,0.08635983,-0.00377691,0.00210217,0.02891071,-0.01917418,0.04533287,0.03023139,-0.02212146,0.06088449,-0.05605521,0.09090817,-0.02491145,0.01305533,-0.00745016,0.08480278,0.01310934,-0.03378341,-0.13722898,0.04755164,0.05293193,0.0004309,-0.0320159,-0.05201652,-0.01072918,0.00469395,0.03217823,0.03211188,-0.09475649,0.01704844,-0.08240284,-0.03198803,0.00606696,-0.04374793,0.03418706,-0.01179329,0.03262788,0.01600132,0.12099666,-0.00916368,-0.0238776,0.00603474,-0.06554219,0.00520151,0.02218719,-0.00332066,-0.02379162,-0.01690965,-0.03573493,0.03508158,-0.01800488,0.03062923,0.03899913,0.04135628,-0.05920817,-0.03741658,0.13074301,0.03038133,-0.00191348,0.05525747,0.01393144,-0.04971305,-0.09855496,0.06473368,0.01846425,0.03663172,0.03171074,0.04199675,0.05739691,0.00588651,0.05701317,0.03853671,-0.01383051,0.02603459,-0.02775571,-0.01197369,-0.05524103,-0.05116379,-0.04406945,0.06288812,0.02544475,-0.30540419,-0.031692,-0.02524175,-0.02951608,0.056819,0.03326453,0.04034272,0.04336488,-0.04772534,0.03183002,-0.01658495,0.01804401,0.01038231,-0.06178015,0.01289572,-0.02957229,0.01127768,-0.01175975,0.0197071,0.05790355,0.0013654,0.06491027,0.22451031,0.03612517,0.01203367,-0.00201697,-0.03605875,0.0632665,0.01176732,0.00444511,-0.03386433,-0.02767631,0.05134207,-0.02912926,0.03069688,0.05564759,-0.02395565,-0.01109645,0.00144468,0.01060407,-0.0454926,-0.01752784,-0.01492422,0.03042071,0.10920386,-0.01715774,-0.05026413,-0.04618102,0.00345058,0.06244354,-0.04975778,-0.02894221,0.02038431,0.03008763,-0.01429315,0.07700422,0.03392338,-0.05313847,-0.05850672,-0.03371349,-0.0219287,-0.00783661,-0.07638863,0.0729952,-0.00578473],"last_embed":{"hash":"w4wlr7","tokens":432}}},"text":null,"length":0,"last_read":{"hash":"w4wlr7","at":1751426529735},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.2. 投资者分析与“公平启动”的困境**#{1}","lines":[157,166],"size":631,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"w4wlr7","at":1751426529735}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05756718,-0.03933647,-0.06543332,-0.02756855,0.03224072,0.01658468,0.00341577,-0.00995615,0.01081832,-0.02817814,0.05168996,-0.04751065,0.04540076,0.01584942,0.0273315,-0.00360257,0.02993497,-0.04793106,-0.01426703,0.02457661,0.10208599,-0.05868385,0.01967029,-0.00768367,0.07422083,0.05468294,0.00405018,-0.02506745,-0.04630966,-0.16881955,0.01158484,-0.08078632,0.00122272,0.01574903,0.0313459,-0.0432365,-0.03496825,0.05116457,-0.04198819,-0.00574293,0.003959,0.02461647,0.01703659,-0.02765069,0.03137345,-0.06714935,0.03155705,-0.06637263,0.00389157,-0.06143048,-0.03591268,-0.06950685,-0.00455918,0.04619207,0.01620831,0.00830659,0.0012352,0.04999773,0.02295612,0.02564189,0.10050108,0.05695848,-0.17704453,0.00628665,0.07775677,0.03556763,-0.02619704,0.01629771,0.05807765,0.06686314,0.00861891,-0.00974674,-0.0400688,0.05706485,0.02745874,0.00024385,0.01711988,0.01812427,-0.04872621,-0.05416384,-0.06455974,0.07839433,0.00920265,0.01332038,-0.03597435,-0.01690711,-0.01208504,-0.0382222,-0.030092,0.01519181,-0.00164725,0.01590583,0.05385746,0.00031504,-0.03356818,-0.01499801,-0.01802381,0.03359769,-0.1099619,0.11902746,-0.03870922,0.03676591,-0.01314461,-0.05526701,0.04239447,0.00553232,0.00118462,-0.07247408,0.01781376,0.02182048,0.02732315,0.00854816,0.07364701,-0.06318855,0.02740768,0.02259227,0.01127735,0.00319145,-0.03207622,0.01765685,0.00730287,0.03987375,0.04792171,-0.03931828,-0.02364585,-0.01012666,0.01086297,0.03748385,0.05692685,0.05920249,0.01385066,0.03647557,-0.06298063,-0.00797946,-0.04383224,-0.04901447,-0.02725413,0.00857013,-0.01713898,-0.05733273,-0.01978286,-0.06264964,-0.02871895,-0.11073802,-0.06701481,0.05659778,-0.01333363,0.01120146,-0.00060438,-0.0539268,0.00789209,0.08745072,-0.03286061,-0.07495467,-0.02786112,0.00845555,0.02523266,0.10173719,-0.06702137,-0.01500182,-0.0062999,-0.0325752,-0.04623638,0.13286056,0.04232291,-0.10274457,0.04794637,0.03914363,-0.01299904,-0.07677399,0.01116752,0.02216993,-0.06390201,0.00273848,0.05141119,-0.0404828,-0.01986121,-0.02433657,0.00444616,0.04403565,0.00427952,-0.03407153,-0.09224406,0.00308579,-0.01834392,-0.02159914,-0.01880155,0.05431802,0.0257674,0.043614,-0.09976136,0.03780562,0.01424859,0.04021141,-0.04022386,-0.01359295,-0.01288459,-0.01798857,0.00325492,-0.06096925,0.15085888,0.04021121,0.01125333,0.00803591,-0.08320162,0.00426907,-0.0244902,-0.05229305,0.00855538,0.04853762,-0.0150865,0.03834463,-0.04452851,0.0480576,-0.0145598,0.01115069,0.00987182,0.06461469,-0.02403603,0.04362776,0.02705353,0.01515744,-0.0852996,-0.18956152,-0.03824415,0.03721604,-0.01578145,0.04960011,-0.01390244,0.02340017,0.02294551,0.0341008,0.07752563,0.08508416,0.07302657,-0.06614645,0.00999882,-0.01728385,0.05696403,0.05121668,-0.01410125,0.00764859,0.04861714,-0.03501746,0.02285214,0.01000261,-0.04391388,0.04592267,-0.06314702,0.11051592,-0.02433928,0.02805232,-0.00391429,0.05881374,0.00455763,-0.0244635,-0.15829988,0.04529256,0.07009016,-0.03712099,-0.00523698,-0.05797256,-0.03324975,0.00247455,0.00384679,0.03941292,-0.10819471,0.03423321,-0.08688849,-0.03812456,-0.02848352,-0.01918858,0.02996259,0.00398265,0.02928402,0.01931187,0.11929724,-0.01119436,-0.01260648,0.0253734,-0.04300389,0.00236973,0.05500656,-0.00983119,-0.00942627,-0.01900304,-0.00703612,0.01333548,-0.02232755,-0.02778381,0.03806119,0.03883011,-0.02309605,-0.03477367,0.16288438,0.01150058,-0.01534745,0.03200839,0.03648013,-0.0570796,-0.09609129,0.04639405,-0.01440231,0.02850729,0.02272422,0.03025673,0.04356261,-0.01867251,0.06162507,0.01140834,0.01350484,0.03692561,0.02499033,-0.00312212,-0.06288347,-0.04579834,-0.05852565,0.06625798,0.02883844,-0.27855104,-0.03432983,-0.01814354,-0.0041486,0.06604505,0.01385287,0.05957243,0.03511663,-0.06105572,0.03022513,0.0013856,0.04179543,0.00031225,-0.06416879,0.00726128,-0.02107895,0.03082063,-0.01157532,0.01409332,0.05435403,-0.00326485,0.06513421,0.2222479,0.00506324,0.04240216,0.01495953,-0.04019871,0.05441884,0.05427099,0.02504332,-0.04469903,-0.01608317,0.04414621,-0.02716644,0.03076405,0.03927667,0.00099497,-0.00709967,-0.00308325,0.00856105,-0.03531811,-0.00982257,-0.00021379,0.0249221,0.11641612,-0.02095239,-0.03702204,-0.08101118,0.01270241,0.05056027,-0.0592126,-0.02389343,0.02329177,0.02562345,-0.0050783,0.06274137,0.03945252,-0.03866439,-0.08147444,-0.00805137,-0.03793155,-0.02786515,-0.04240143,0.06954581,0.00758762],"last_embed":{"hash":"1wx2vgz","tokens":468}}},"text":null,"length":0,"last_read":{"hash":"1wx2vgz","at":1751426529745},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**","lines":[173,195],"size":974,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1wx2vgz","at":1751426529745}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.1. “实验性与未经审计”的现实**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0588828,-0.03847795,-0.06497631,-0.02794972,0.03298872,0.01617145,0.00323553,-0.00554717,0.00859607,-0.02833899,0.0519939,-0.0440878,0.04347593,0.01704625,0.02741519,-0.00230891,0.03122131,-0.0492272,-0.01665399,0.02829158,0.10084765,-0.0588026,0.0198782,-0.00539824,0.0751517,0.05165276,0.00637796,-0.02822764,-0.04360297,-0.16682182,0.01462258,-0.07786911,0.00062807,0.01622259,0.03192623,-0.04377137,-0.0381595,0.04731083,-0.04560882,-0.00789936,0.00637325,0.02367623,0.0212965,-0.02814437,0.0317186,-0.06967734,0.03337204,-0.06912316,0.00508203,-0.06167591,-0.03479416,-0.07346288,-0.00382094,0.0440805,0.01532478,0.01005163,0.00048628,0.05262784,0.02306413,0.02497723,0.10078387,0.05646116,-0.17743394,0.00355421,0.07898274,0.03156152,-0.0267297,0.01743838,0.05284794,0.06896862,0.01203051,-0.01099878,-0.03621501,0.05554837,0.02905639,-0.00386476,0.01884865,0.01523256,-0.04874984,-0.0550708,-0.06122724,0.0800396,0.00834616,0.01170798,-0.03802725,-0.0150168,-0.01177082,-0.03848595,-0.0342733,0.01362821,-0.00137726,0.02079495,0.05247808,0.00064726,-0.03054401,-0.01371002,-0.01794463,0.03409326,-0.11002348,0.12236232,-0.03590719,0.03838402,-0.01460893,-0.05392506,0.04150454,0.00640489,0.00015908,-0.07330631,0.02043235,0.02264024,0.0271045,0.00357595,0.07526368,-0.06268914,0.02512039,0.01957858,0.00928973,0.00134838,-0.02787766,0.01511181,0.00651782,0.03833962,0.04731031,-0.04088118,-0.02439872,-0.00914942,0.01101532,0.03620564,0.05460209,0.0585334,0.013606,0.0356647,-0.06405938,-0.00873961,-0.04379563,-0.04965456,-0.02574084,0.00692763,-0.02199393,-0.05628007,-0.01818306,-0.0646737,-0.03049869,-0.11191169,-0.06832097,0.05583864,-0.01430039,0.0094108,-0.00193765,-0.05291981,0.00946738,0.08785347,-0.03348037,-0.0754528,-0.02660047,0.0075194,0.02588217,0.10156313,-0.06817079,-0.01530351,-0.00587352,-0.03380558,-0.0451079,0.1322929,0.04031711,-0.10352248,0.04705278,0.03981828,-0.01162406,-0.07439279,0.00986453,0.02171151,-0.06361198,0.00333073,0.05485668,-0.03657687,-0.01883668,-0.02304285,0.00615961,0.04576459,0.00424137,-0.03240452,-0.09003174,0.00102755,-0.01729223,-0.02121618,-0.0187816,0.05330165,0.02821455,0.04063433,-0.09859357,0.03710249,0.01234291,0.04039833,-0.04096654,-0.01429433,-0.01294522,-0.01625635,0.00388269,-0.06246924,0.14995094,0.03913913,0.01323257,0.00803399,-0.08048523,0.00623828,-0.02909494,-0.05059007,0.01029205,0.04858397,-0.0143365,0.03853067,-0.04165735,0.04956117,-0.01156653,0.01318987,0.01180338,0.06639515,-0.02675718,0.04350577,0.02926522,0.01489227,-0.08854028,-0.18760325,-0.03866988,0.03932147,-0.01696103,0.04974727,-0.01302079,0.0231737,0.02458833,0.0374601,0.07819487,0.08696395,0.07184015,-0.06620749,0.00970525,-0.0196788,0.05962051,0.05473424,-0.0123936,0.006475,0.04870689,-0.03320376,0.02426097,0.00920324,-0.0480408,0.04487544,-0.0607262,0.11192136,-0.02739213,0.02812622,-0.00479543,0.05992709,0.00306004,-0.0258127,-0.15946728,0.04651793,0.07202577,-0.03661776,-0.00888977,-0.05661635,-0.03371603,-0.00018608,0.00248925,0.03773643,-0.1067512,0.03217472,-0.08607984,-0.04073893,-0.02689089,-0.02028162,0.03212923,0.0043235,0.02738389,0.01445813,0.11951667,-0.01017895,-0.01062003,0.02267846,-0.04398081,0.00175078,0.05400346,-0.01261946,-0.01143115,-0.02044915,-0.00803148,0.011972,-0.02482381,-0.0271363,0.03933779,0.0412399,-0.02365063,-0.03391217,0.16034901,0.0114373,-0.01257736,0.03227143,0.03527549,-0.05605187,-0.09393466,0.04731056,-0.01566141,0.0265929,0.02142884,0.03178897,0.04727656,-0.02075986,0.06159947,0.01361855,0.01130119,0.03610772,0.02342456,0.00021272,-0.06214193,-0.04604618,-0.05741698,0.06798999,0.02858355,-0.27918857,-0.03533652,-0.01976698,-0.00582455,0.06752327,0.01572095,0.05994439,0.04171434,-0.05620917,0.03064581,-0.00184733,0.03907643,-0.00074248,-0.06391862,0.00640319,-0.02259488,0.02822129,-0.0114492,0.0138247,0.05318279,-0.00074684,0.06654856,0.22156847,0.00574515,0.04204383,0.01455753,-0.04235013,0.052136,0.05178254,0.02350815,-0.04217608,-0.01448244,0.0436125,-0.02995126,0.03310734,0.03768896,-0.00272998,-0.00791134,-0.00197189,0.01010593,-0.03730825,-0.01149501,0.0006127,0.02386539,0.1179142,-0.01579534,-0.03673441,-0.07936204,0.01298949,0.05480274,-0.05913461,-0.02292357,0.02457287,0.02571845,-0.00558495,0.06103703,0.03870876,-0.04040208,-0.08326596,-0.00998081,-0.03563089,-0.02471397,-0.04264681,0.06946328,0.00737922],"last_embed":{"hash":"12hbk9p","tokens":495}}},"text":null,"length":0,"last_read":{"hash":"12hbk9p","at":1751426529756},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.1. “实验性与未经审计”的现实**","lines":[175,184],"size":508,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"12hbk9p","at":1751426529756}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.1. “实验性与未经审计”的现实**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05758885,-0.03864987,-0.06321977,-0.03046505,0.03033433,0.01534961,0.00583138,-0.00600658,0.01163868,-0.03002698,0.05259339,-0.04410308,0.04359683,0.01604395,0.0253171,-0.00274819,0.03238682,-0.05016202,-0.01558093,0.02740839,0.10127314,-0.06305572,0.02101037,-0.00488595,0.07728817,0.05294059,0.0046444,-0.02797893,-0.04472596,-0.16832322,0.01568109,-0.07783351,0.00202639,0.01662668,0.0333763,-0.04495581,-0.03351216,0.04880228,-0.04578561,-0.00766087,0.00548031,0.02162875,0.02189197,-0.02810906,0.03215495,-0.07122251,0.03572571,-0.07005228,0.00541809,-0.06077004,-0.03450002,-0.07604827,-0.00375821,0.04153567,0.01647462,0.01074057,0.00211674,0.05157897,0.0246605,0.02724729,0.0983761,0.05824395,-0.17830476,0.00383636,0.0813222,0.03074458,-0.02573155,0.01786918,0.05370224,0.06847546,0.01197278,-0.01095681,-0.03703061,0.05449202,0.02878913,-0.00258933,0.01879121,0.01567971,-0.0487467,-0.05609786,-0.06082977,0.08235151,0.00935699,0.01135303,-0.03904747,-0.01480854,-0.01196253,-0.04136194,-0.03505758,0.01424848,-0.00121226,0.02013619,0.0531357,0.00032544,-0.02841404,-0.01472366,-0.01599999,0.03415743,-0.10858078,0.12135793,-0.03717596,0.0377834,-0.01409244,-0.05328739,0.04159662,0.00526394,-0.00061685,-0.07182366,0.02213275,0.02389598,0.02698637,0.00568296,0.07534117,-0.06443885,0.02713142,0.0195189,0.01021486,0.00175891,-0.03159317,0.01683559,0.00344318,0.03867714,0.04655466,-0.04122477,-0.02479209,-0.01074146,0.01025673,0.03720953,0.05684477,0.0579296,0.0128167,0.03558103,-0.06288581,-0.00935274,-0.0402783,-0.04699151,-0.02295505,0.00889815,-0.0205672,-0.05612958,-0.01884384,-0.06381766,-0.0289589,-0.11029687,-0.06964021,0.05572892,-0.01356125,0.00917881,-0.00114717,-0.05289189,0.00828838,0.08752431,-0.03261021,-0.07316204,-0.02536349,0.00691593,0.02661317,0.10143267,-0.06836402,-0.01271703,-0.00462143,-0.03375815,-0.04273213,0.12913419,0.03984426,-0.10413988,0.04559456,0.03763893,-0.01315244,-0.07585966,0.01035486,0.02055572,-0.06535502,0.00357491,0.05687499,-0.03627545,-0.02009673,-0.02435645,0.00402139,0.04552263,0.0045915,-0.03379649,-0.09024876,-0.00084857,-0.01848811,-0.02125578,-0.01836268,0.05385959,0.02727126,0.04204357,-0.09818742,0.03915373,0.01155431,0.04098633,-0.04303007,-0.01518661,-0.01320818,-0.01516967,0.00443971,-0.06086183,0.14984784,0.03686658,0.0138101,0.00623081,-0.07923577,0.00412163,-0.02856764,-0.04905002,0.00888058,0.04880058,-0.01562737,0.03851282,-0.04334562,0.05274037,-0.00927019,0.01178334,0.01216318,0.06489409,-0.02629465,0.04308714,0.03085443,0.01513385,-0.0897119,-0.18677777,-0.03799937,0.03980764,-0.01700065,0.04791002,-0.01366041,0.02485628,0.024191,0.0387801,0.08021345,0.0883819,0.06989825,-0.06569355,0.00976191,-0.01907033,0.06044703,0.05365114,-0.01225044,0.00633531,0.04714044,-0.02986234,0.02525841,0.01044013,-0.04598381,0.04547148,-0.05968765,0.10957493,-0.02740332,0.0280556,-0.00611266,0.05971895,0.00184419,-0.02513524,-0.15844831,0.04761538,0.07137882,-0.03726044,-0.01175958,-0.0570043,-0.03372981,-0.00071728,0.00249394,0.0403261,-0.10822353,0.03116351,-0.08628019,-0.03778172,-0.02704331,-0.01841553,0.03054729,0.00276863,0.02738401,0.01446217,0.11913361,-0.01131398,-0.0116365,0.0219581,-0.04552628,-0.00051952,0.05368406,-0.01225338,-0.01090211,-0.0227053,-0.0082543,0.01215601,-0.0258646,-0.02875313,0.04074555,0.03880675,-0.0231182,-0.03277718,0.16078211,0.00911059,-0.01194838,0.03325204,0.03680505,-0.05527177,-0.09367,0.04727361,-0.01427865,0.02813707,0.02208888,0.02967595,0.04831738,-0.01957254,0.06158366,0.01390919,0.01055878,0.03541867,0.02240077,0.00011017,-0.06216336,-0.04589896,-0.05518512,0.0662611,0.02856009,-0.28045073,-0.03313307,-0.02066341,-0.00556128,0.06744117,0.01631132,0.06028247,0.0418681,-0.05826224,0.0312327,-0.00130653,0.03902199,-0.0009866,-0.06424394,0.00705522,-0.0237248,0.02808217,-0.01265863,0.01307296,0.05262962,0.00005844,0.06646344,0.22078197,0.00521429,0.04160875,0.01334449,-0.04041829,0.05135424,0.05089342,0.02320294,-0.04334162,-0.01900031,0.04392497,-0.03005563,0.03120656,0.03805829,-0.00154552,-0.00814268,-0.00164972,0.01049218,-0.03564521,-0.01209194,0.00437501,0.02469627,0.11789908,-0.01566751,-0.03605293,-0.07825855,0.01451586,0.05488441,-0.05995318,-0.02347837,0.02593659,0.02445819,-0.00505296,0.06159436,0.03776767,-0.03912675,-0.08536533,-0.01159328,-0.03740646,-0.0246823,-0.04397799,0.0678284,0.00941924],"last_embed":{"hash":"1vghwzy","tokens":492}}},"text":null,"length":0,"last_read":{"hash":"1vghwzy","at":1751426529771},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.1. “实验性与未经审计”的现实**#{1}","lines":[177,184],"size":479,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1vghwzy","at":1751426529771}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.2. 潜在攻击向量**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04613425,-0.01416531,-0.07926362,-0.05307539,-0.00010442,0.01413558,-0.00742115,0.02499905,0.00186578,-0.01626672,0.07885338,-0.04628754,0.00878348,0.03095381,0.01255207,-0.02791896,0.02615256,-0.04779284,-0.00904001,0.02764704,0.07478426,-0.05880761,0.05464353,-0.02576565,0.06483697,0.06564717,0.02442087,0.00598219,-0.04750898,-0.18289886,-0.00641442,-0.06989168,0.00614979,0.01346076,0.00753884,-0.06234906,-0.03612732,0.0467639,-0.0564713,-0.01043297,0.00564653,0.01924275,0.00271981,-0.01757969,0.04354164,-0.10384857,0.04016579,-0.08687545,-0.00494369,-0.05017393,-0.02302205,-0.08605906,0.00335995,0.0261189,-0.00089097,-0.00921145,0.00356091,0.05204955,0.0302943,0.03188573,0.05549205,0.04190274,-0.18044583,0.01154711,0.08028849,0.01514051,-0.03536816,0.0246653,0.05643042,0.06960073,0.01835122,0.02197532,-0.02455861,0.04017248,0.0278536,-0.00999764,0.02077862,0.00559731,-0.0285312,-0.08281466,-0.04425837,0.06732043,0.00238008,-0.00931843,-0.00053449,-0.01734283,-0.01287008,-0.02452291,-0.014924,0.01760615,0.0085378,0.0186792,0.03707372,0.00137305,-0.01547755,0.01227987,-0.00617855,0.0087984,-0.12010775,0.1105364,-0.03641852,0.04184121,-0.0077889,-0.0445593,0.04317022,-0.00132524,-0.01334354,-0.08275566,0.02634021,0.00647207,0.03204735,0.00488387,0.088117,-0.05079066,0.02891351,0.00258782,0.01301398,0.00076829,-0.03030886,0.03062656,0.00740139,0.02176186,0.04281148,-0.01703451,-0.0122477,-0.0257938,-0.0132164,0.04745356,0.06262607,0.01963417,0.01433529,0.02367648,-0.06902835,0.00219877,-0.05072796,-0.03064485,-0.0121887,0.00993135,-0.02245459,-0.05987915,-0.02932469,-0.07015344,0.00887617,-0.11175517,-0.07326315,0.07257933,-0.00824663,0.00705648,0.01394025,-0.05083042,0.01210997,0.08395737,-0.03829042,-0.07670595,-0.01401613,-0.0159231,0.01713312,0.1041926,-0.07514028,0.00675861,-0.00770421,-0.01018249,-0.03182965,0.14663824,0.03058574,-0.09722611,0.05000196,0.0220037,-0.02049023,-0.06031929,-0.00552692,0.03924994,-0.04336597,-0.0057184,0.08173353,-0.03045978,-0.02059707,-0.03730129,0.01017042,0.07526644,0.01496826,-0.0119389,-0.08269519,-0.01115449,-0.00078185,-0.02121067,-0.03800819,0.01869684,0.03518635,0.02667923,-0.09772354,0.03412456,0.002864,0.00721582,-0.0347375,0.00307304,-0.02092354,-0.00562494,0.03285752,-0.06984788,0.15727006,0.04062273,0.0009181,0.01664116,-0.06419037,0.00894749,-0.05359704,-0.04684765,-0.00787166,0.03838518,-0.00116498,0.01817735,-0.02407082,0.03178062,-0.0199489,0.01485791,-0.00335249,0.05031917,-0.01710086,0.04967519,0.03507762,0.02005724,-0.08527855,-0.19167185,-0.04056524,0.03496471,-0.0219757,0.01142982,0.01146059,0.0621896,-0.00398345,0.05767136,0.07599457,0.09688886,0.06037071,-0.06814886,-0.01191575,-0.00910336,0.07108208,0.06617986,-0.0078481,0.00437044,0.03816882,-0.05106108,0.00927515,0.03245868,-0.02921681,0.06759574,-0.06164315,0.08995959,-0.02752854,-0.00530066,0.00161115,0.08742344,0.0331841,-0.02550185,-0.14239761,0.02917135,0.06926063,-0.0102317,0.00787562,-0.0498104,-0.00958695,-0.01951932,0.01051243,0.05295582,-0.10959712,0.02382759,-0.07690382,-0.03552091,-0.02532457,0.00235954,0.02790889,-0.0072414,0.03681506,0.04568345,0.11096559,0.02749684,-0.03256449,0.01745312,-0.02834908,-0.01039421,0.03290165,-0.03396985,-0.01586379,-0.04782578,-0.01575077,0.03101513,-0.00005385,-0.00254771,0.03294754,0.03534773,-0.02233075,-0.03270325,0.14653866,0.02188617,-0.02226733,0.04549209,0.02808156,-0.02484906,-0.08074818,0.04007999,-0.01072066,0.03058125,0.01312647,0.02761752,0.04609818,-0.0211151,0.05282581,0.00334285,0.01398029,0.01510359,-0.00175547,-0.0082402,-0.05642022,-0.04157739,-0.03203573,0.08189534,0.01941849,-0.28571126,-0.03137059,-0.03539044,-0.01707839,0.070837,0.03619849,0.06636067,0.03172139,-0.07466193,0.0419326,-0.03281591,0.04508173,-0.00360561,-0.0631311,0.00545046,-0.01782803,0.03693021,-0.01014109,0.00982385,0.04016424,0.00309088,0.07502257,0.22359137,-0.00888317,0.03516686,0.01330272,-0.02891824,0.06115151,0.04316445,0.02554728,-0.04626871,-0.02041452,0.04313211,-0.03621638,0.02523682,0.03525847,-0.00260661,-0.0335629,0.01225949,0.0140057,-0.02477376,-0.01659913,-0.0155862,0.03338502,0.11977857,-0.00554403,-0.03726739,-0.06719722,0.02878526,0.05209477,-0.08313724,-0.0488454,0.02084709,0.04693956,0.00868535,0.08589908,0.01940286,-0.04308944,-0.08450367,-0.00537503,-0.02825886,-0.01689435,-0.03841133,0.06316173,0.00082194],"last_embed":{"hash":"1dzuo5x","tokens":409}}},"text":null,"length":0,"last_read":{"hash":"1dzuo5x","at":1751426529781},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.2. 潜在攻击向量**","lines":[185,195],"size":441,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1dzuo5x","at":1751426529781}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0397648,-0.02169395,-0.07828122,-0.04548236,0.03178869,-0.01074584,-0.01808485,0.01627901,-0.00591918,-0.02606842,0.07083502,-0.04508964,0.02624237,0.02731945,0.02851948,-0.02211595,0.02675446,-0.04964113,-0.02195396,0.01895268,0.07905956,-0.08342961,0.04320636,-0.02380727,0.07116048,0.05604738,-0.00000248,-0.01425293,-0.05300958,-0.1759364,0.00623147,-0.07764081,0.01250411,0.02400017,0.00058745,-0.03012658,-0.01694536,0.0567154,-0.03650879,-0.00975042,-0.00086871,0.00815057,0.01587394,0.00924403,0.03902761,-0.0907049,0.03257325,-0.05563797,-0.01037357,-0.03893738,-0.02750199,-0.05944042,0.00194282,0.02833016,-0.00637114,0.01995691,0.02268785,0.0553275,0.02035424,0.03649602,0.0773347,0.04225091,-0.18379521,0.01525633,0.08622633,0.00808619,-0.04858914,0.02564562,0.04604403,0.06984037,0.01524012,-0.00453428,-0.01527277,0.0444131,0.00740488,-0.00935389,0.03866287,0.02397802,-0.02247831,-0.07270318,-0.04655553,0.08885714,-0.00354886,-0.00559523,-0.00422151,-0.02529719,0.01154279,-0.02760682,-0.03170957,0.01795674,-0.0077169,0.02658403,0.0728033,-0.01311003,-0.05069323,0.00443646,-0.02802199,0.03555632,-0.11263042,0.11377338,-0.03669002,0.03361816,-0.00626748,-0.06163559,0.04074987,-0.0113031,0.00125771,-0.06877702,0.02224685,0.03411573,-0.00630156,0.00277574,0.06594702,-0.03892936,0.0350258,0.00183063,0.00385561,-0.00232886,-0.04260094,0.03182264,-0.00379644,0.04643075,0.05976306,-0.02481869,-0.02037855,-0.01127174,0.01211427,0.04131747,0.05849737,0.03381513,0.01265314,0.05183304,-0.06483428,0.01352866,-0.05642335,-0.03053589,-0.01490623,0.00245557,-0.03740722,-0.04131299,-0.04069595,-0.09886112,-0.02523743,-0.10668042,-0.07619741,0.06555255,-0.00400866,0.00366164,0.00589824,-0.04105924,0.01364618,0.08200173,-0.04818299,-0.07596632,-0.0095883,-0.01110365,0.03378825,0.08512877,-0.04154114,0.02094045,-0.01041092,-0.03652835,-0.0426957,0.13333191,0.03092598,-0.12645042,0.03861481,0.04583359,-0.00552717,-0.06447417,0.0002717,0.02757016,-0.04404498,-0.02617747,0.06973678,-0.05959752,-0.02205668,-0.02445913,0.03542024,0.06582917,0.00021985,0.00105703,-0.06947625,-0.01247658,-0.01246678,-0.00258057,-0.02039878,0.02941545,0.04091876,0.03798595,-0.09605668,0.0513052,-0.00956949,0.01766116,-0.03646751,-0.02062143,-0.00728506,-0.0029798,0.01886527,-0.06512965,0.14537205,0.03664092,0.01204663,0.02140121,-0.05953264,-0.00326929,-0.02243671,-0.03372663,-0.00609048,0.04436801,-0.02327558,0.03405014,-0.01480969,0.0486396,-0.0178481,0.03070216,0.0179047,0.02512811,-0.03461983,0.05306991,0.0185695,0.0326069,-0.09873819,-0.18009256,-0.03310321,0.06758919,-0.02319126,0.00761138,0.00385923,0.03888866,0.01645359,0.05247642,0.10777444,0.11173112,0.05573828,-0.04747615,-0.00875905,-0.00801579,0.05996307,0.05607485,-0.0108221,0.00952493,0.02808458,-0.05560657,0.00946591,0.02064718,-0.0352161,0.06129068,-0.0686812,0.09095088,-0.00974274,0.01309262,0.01017316,0.09408351,0.01448745,-0.02256213,-0.14782523,0.03899756,0.08730167,-0.00089023,-0.03383918,-0.04916209,-0.0241815,-0.01555225,0.02286223,0.04629355,-0.10451493,0.03477887,-0.08512189,-0.03148797,-0.04398342,-0.02336951,0.03043618,0.0083607,0.03232791,0.00807544,0.12886639,-0.00683839,-0.01377576,0.02895165,-0.05954864,-0.00576121,0.03079272,0.00034966,0.00343532,-0.02031122,-0.00660376,0.02429471,-0.00889038,-0.01982771,0.02064958,0.03883867,-0.0313759,-0.02652262,0.13858968,0.01386023,0.01674259,0.04221554,0.02293069,-0.06187372,-0.10298198,0.06838293,-0.00332049,0.01001122,0.01250986,0.03174085,0.03811831,0.00134216,0.06430769,0.00689262,-0.03280454,0.03875939,0.00313309,-0.00920135,-0.0359861,-0.04126268,-0.02506009,0.067496,0.01743487,-0.29256892,-0.04270934,-0.02637753,-0.02962412,0.05425909,0.0496445,0.07038488,0.0358529,-0.07105085,0.03693943,0.01197706,0.05013491,-0.00836612,-0.05625793,0.02366482,0.00304357,0.03213436,0.00324275,0.00164761,0.0375137,0.00871125,0.06402073,0.21560289,-0.0132736,0.03769451,0.02396751,-0.02710534,0.03993967,0.02030417,0.00399441,-0.04628197,-0.02201068,0.03282475,-0.02865949,0.01125482,0.02647905,0.00548119,-0.0237045,-0.01777564,0.00593209,-0.04957583,-0.01139452,-0.00697518,0.04107963,0.09697431,-0.00859352,-0.05810978,-0.05966002,-0.00267751,0.05213842,-0.06741688,-0.04160786,0.00958602,0.04653633,-0.01938906,0.06785284,0.03742181,-0.05875346,-0.08252501,-0.02532646,-0.03007895,-0.03967656,-0.04495233,0.07360763,0.01809659],"last_embed":{"hash":"1o9gzdq","tokens":451}}},"text":null,"length":0,"last_read":{"hash":"1o9gzdq","at":1751426529795},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**","lines":[196,216],"size":859,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1o9gzdq","at":1751426529795}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.1. 官方渠道与开发者资源**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03539814,-0.02600644,-0.08199424,-0.03631624,0.0283729,-0.01273312,-0.0155454,0.02024958,-0.00781666,-0.0251562,0.07665772,-0.03694353,0.01960984,0.03331173,0.03261959,-0.0172927,0.02451506,-0.05053544,-0.01514837,0.0130804,0.08039916,-0.08238552,0.0391576,-0.01622758,0.06659307,0.06762283,-0.00601223,-0.01409539,-0.05279605,-0.17479572,0.00926172,-0.07766499,0.00975167,0.02227004,0.00842496,-0.0250258,-0.02611037,0.05176921,-0.03622248,-0.01510698,-0.00289806,0.01108591,0.01597952,0.01388919,0.03555408,-0.09030544,0.0335035,-0.04939353,-0.01298916,-0.04644227,-0.02634089,-0.06355305,-0.00118036,0.02791829,-0.0087506,0.01956646,0.02939961,0.05414465,0.02986344,0.03280499,0.08631413,0.03767068,-0.17996341,0.0129171,0.08709364,0.01447781,-0.05120787,0.03108037,0.03885417,0.07420635,0.02031677,-0.00485485,-0.01793746,0.04540842,0.00694113,-0.00676593,0.04004495,0.02159543,-0.02175328,-0.06773721,-0.04375335,0.08622748,-0.00144962,-0.00223055,-0.00377377,-0.02428326,0.01430104,-0.0170042,-0.02094109,0.01515564,-0.00337242,0.02524549,0.07824589,-0.0151922,-0.05152295,0.0021297,-0.02587221,0.03757729,-0.10924493,0.11278144,-0.03013484,0.03425619,-0.00443716,-0.0580029,0.04042374,-0.00442862,-0.00031755,-0.06822068,0.02451343,0.02842623,0.00291718,0.00148367,0.06960577,-0.04141701,0.03278566,-0.00220977,0.00812921,-0.00241787,-0.04834327,0.0322575,-0.00626636,0.04376414,0.06155605,-0.01980946,-0.01730905,-0.01310169,0.01702102,0.04240489,0.06251036,0.04246441,0.01023585,0.05266561,-0.06402657,0.01413179,-0.04911231,-0.02858392,-0.01060946,0.00164057,-0.03554579,-0.04620839,-0.04215872,-0.0989662,-0.02946824,-0.10189237,-0.0770293,0.07043539,0.00097099,0.00876248,-0.00089114,-0.04226608,0.00882167,0.08215128,-0.0489949,-0.08222651,-0.01122114,-0.00865477,0.02942706,0.08233403,-0.03952626,0.01613105,-0.00384348,-0.03435019,-0.04121171,0.13591415,0.03545411,-0.12692918,0.03726704,0.04607537,-0.00342415,-0.064425,-0.0027772,0.03212639,-0.04688841,-0.03362162,0.06365623,-0.06237717,-0.03118034,-0.02227235,0.03553785,0.07092428,0.0027827,-0.00009415,-0.06890843,-0.01381374,-0.01046387,-0.00092753,-0.02394342,0.02667195,0.0340169,0.03980308,-0.10051372,0.04081867,-0.00784735,0.02253592,-0.03728268,-0.02337741,-0.00227074,0.00501728,0.0085155,-0.06903753,0.14603558,0.03943096,0.00797309,0.02767533,-0.05812855,-0.00868462,-0.01246132,-0.03065593,-0.01201277,0.04966102,-0.01958155,0.03872386,-0.01496952,0.0490685,-0.02227882,0.03309875,0.01680722,0.02564656,-0.03010271,0.04903163,0.02418438,0.02802221,-0.09705126,-0.17516209,-0.03096156,0.06974559,-0.02451478,0.0094252,-0.00565592,0.03870212,0.0146335,0.05128224,0.10468217,0.11223277,0.05710226,-0.05099798,-0.010131,-0.00876603,0.06297979,0.05023517,-0.01113776,0.01595226,0.02568132,-0.05667888,-0.00089387,0.01536292,-0.03716942,0.06276159,-0.06970128,0.08960416,-0.00779874,0.011263,0.01490534,0.09574797,0.01470756,-0.02876635,-0.15104939,0.03969865,0.08467711,-0.00232188,-0.03532385,-0.04659982,-0.02974259,-0.01219335,0.0163885,0.04494601,-0.10421312,0.03602863,-0.08744261,-0.03189256,-0.03891116,-0.0178808,0.02734328,-0.00114841,0.032348,0.00797299,0.13260263,-0.00706717,-0.01320387,0.02667248,-0.0595001,-0.00675287,0.03336526,0.00047756,0.00342757,-0.02342491,0.0002484,0.03050602,-0.00912555,-0.0269031,0.01838457,0.04611677,-0.03559756,-0.02247115,0.14146899,0.01112251,0.01529175,0.02869153,0.02900168,-0.06274349,-0.1082098,0.06509113,-0.00294763,0.00911478,0.00482788,0.02813427,0.03591433,-0.00020466,0.06889997,0.00166774,-0.03457182,0.03458291,0.00382881,-0.00725308,-0.0357272,-0.0455537,-0.03219239,0.07076184,0.01760346,-0.28688547,-0.04403121,-0.02140417,-0.03831529,0.05174312,0.047404,0.07040379,0.03708825,-0.07170477,0.03300815,0.0110542,0.04856171,-0.00210074,-0.05424058,0.02251194,0.00794463,0.03576399,0.00532318,-0.00013635,0.03431223,0.00846947,0.06843291,0.21135253,-0.00589833,0.03381699,0.01978274,-0.03188899,0.0413776,0.02600719,0.00794261,-0.05373781,-0.02530675,0.03015453,-0.03798638,0.01362403,0.026699,0.00374758,-0.02583813,-0.018891,0.00773479,-0.05354013,-0.00920143,0.00111179,0.03421466,0.08953825,-0.01369576,-0.05376768,-0.06788165,0.00109983,0.05058187,-0.07820176,-0.03635767,0.00116801,0.04572579,-0.01194118,0.07147996,0.0351573,-0.05435883,-0.09094811,-0.02514,-0.02929646,-0.04121371,-0.04278598,0.06795415,0.01476274],"last_embed":{"hash":"1kg0rs8","tokens":310}}},"text":null,"length":0,"last_read":{"hash":"1kg0rs8","at":1751426529809},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.1. 官方渠道与开发者资源**","lines":[198,204],"size":365,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1kg0rs8","at":1751426529809}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.2. 开发者采纳的障碍**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04980601,0.00100085,-0.05223763,-0.06379642,0.06910623,0.02726305,0.00419302,0.01811964,0.00251074,-0.01415308,0.04427243,-0.07295959,0.04452133,0.02517853,0.00760717,-0.03321759,0.02405884,-0.04140974,-0.03808501,0.03084008,0.09036026,-0.06046373,0.02399397,-0.01411509,0.07988904,0.02359857,0.01450391,-0.03075795,-0.04406765,-0.17729843,0.00056431,-0.06994593,0.03435412,0.03469637,0.00641491,-0.05811255,-0.00913021,0.05517542,-0.0406141,0.02276558,-0.0015258,-0.00453718,0.02949618,-0.00712592,0.02820749,-0.06324518,0.0519234,-0.08203202,0.02382989,-0.0353508,-0.04421259,-0.06338342,0.00543723,0.02016224,0.02704643,0.02032792,0.00009224,0.02885557,0.02728418,0.04240214,0.08969125,0.06976976,-0.19376408,0.01175402,0.07014642,0.01039703,-0.04571567,0.02729413,0.05933557,0.08157469,0.01419553,-0.00672978,-0.01017629,0.04524432,0.03766676,0.00625051,0.01942791,0.03337134,-0.00842686,-0.05265488,-0.08113199,0.08322765,-0.00827633,-0.01991798,-0.0533903,-0.02353426,-0.02359529,-0.05024,-0.05039726,0.03692475,0.00268745,0.02832497,0.02208201,0.02015161,-0.04183014,-0.0262002,-0.0420997,0.00692976,-0.11608497,0.12834696,-0.05224952,0.02554727,0.00276179,-0.05633731,0.03376571,-0.00864988,-0.00472524,-0.05709403,0.01183635,0.03280004,0.01596689,0.02302144,0.05194573,-0.04156622,0.04265814,0.02225846,-0.00924941,0.00695792,-0.02751383,0.0246991,-0.00198853,0.04951282,0.01718394,-0.03304697,-0.04237623,-0.01554764,0.01481314,0.05894428,0.04381743,0.02615108,-0.00586015,0.05039369,-0.0849468,-0.01329953,-0.0575627,-0.03928629,-0.00191596,0.00982121,-0.01517407,-0.00686478,-0.01131206,-0.08612872,-0.03522214,-0.11791614,-0.05785978,0.05281336,-0.00268744,-0.00476212,0.0243202,-0.02706227,0.01182852,0.08481796,-0.05094823,-0.07707831,-0.02269173,-0.01629159,0.0297647,0.10259338,-0.05461361,0.00767734,-0.03565769,-0.03255904,-0.02328577,0.12041054,0.01741896,-0.10504763,0.03575295,0.03631689,-0.01792965,-0.07365543,0.0074087,0.0303846,-0.04257924,0.00662254,0.0812091,-0.02787742,0.00127726,-0.04200735,0.01526871,0.05007923,0.02801346,-0.0308376,-0.07254174,-0.01986107,-0.01861887,-0.02507413,-0.02046716,0.04310422,0.05479594,0.05364342,-0.06632178,0.07275192,-0.0085505,0.00707313,-0.03213401,0.00292242,-0.01460152,-0.01585975,0.04804958,-0.04536591,0.11685123,0.01487936,0.02313111,0.01082712,-0.07436621,0.02988616,-0.05243015,-0.0629488,0.02362736,0.00972312,-0.03547554,0.01504693,-0.02935588,0.05600467,-0.03373614,0.02071146,0.02079968,0.05006963,-0.05389537,0.03589699,0.02179836,0.03400448,-0.07867666,-0.19822586,-0.03351082,0.08931997,-0.01239196,0.00980329,0.0057396,0.0363333,0.02709904,0.06896748,0.11036623,0.096269,0.06713399,-0.05546732,-0.0157456,-0.01707285,0.06375105,0.04553403,-0.02181643,0.00317219,0.04543342,-0.02235578,0.04558284,-0.01316114,-0.03749732,0.06148862,-0.06223391,0.10224728,-0.0028306,0.02621007,-0.03398521,0.07580982,0.02861788,-0.01776777,-0.13431697,0.04372213,0.074633,-0.01916293,-0.03590368,-0.05087439,-0.02605182,-0.01616123,0.00776042,0.03447697,-0.09557647,0.00710462,-0.07419147,-0.02782479,-0.07662093,-0.02177642,0.04044086,0.00303005,0.0172064,0.05273016,0.10986906,-0.00806338,-0.02013428,0.01690434,-0.05877706,-0.00004557,0.04332481,0.00086199,0.00032433,-0.01880545,-0.0162995,0.01398282,-0.01355815,-0.01379012,0.02713633,0.01876643,-0.02918122,-0.03383231,0.10496052,0.02886312,0.00191047,0.03923098,0.01111489,-0.04762929,-0.08999471,0.0549176,-0.00177115,0.04695547,0.02782493,0.04810846,0.04342345,-0.02367396,0.03644045,0.01189073,0.00529621,0.04703749,0.00816749,-0.01134311,-0.03600578,-0.03435237,-0.03738705,0.05621534,0.02184878,-0.28681889,-0.03844601,0.00197438,0.00016919,0.07565496,0.04017083,0.04726481,0.05354203,-0.07195102,0.03685424,0.01613786,0.0464831,-0.01283292,-0.0737845,-0.00291046,-0.02527618,0.03335125,-0.01254901,0.0204549,0.03635958,0.01219394,0.04489621,0.21813256,-0.02750079,0.06026303,0.02537278,-0.04225024,0.03216424,-0.01393513,0.01077383,-0.02966966,-0.00683574,0.03716506,-0.01249869,0.02170609,0.00437644,-0.00984527,-0.00023153,-0.0193077,0.00642599,-0.05972707,-0.0112636,-0.03459285,0.01965099,0.11613145,0.0298968,-0.06268466,-0.05822463,-0.00628996,0.04269106,-0.05431892,-0.04335698,0.03659928,0.04505356,-0.01977599,0.0734665,0.04448659,-0.05356596,-0.06351546,-0.01146707,-0.03702734,-0.01920371,-0.05913422,0.06723505,0.02132929],"last_embed":{"hash":"2uxzkp","tokens":240}}},"text":null,"length":0,"last_read":{"hash":"2uxzkp","at":1751426529820},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.2. 开发者采纳的障碍**","lines":[205,208],"size":206,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"2uxzkp","at":1751426529820}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.3. 社区情绪与目标受众**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06004246,-0.02795696,-0.05169025,-0.02285627,0.04345655,0.02206233,0.00226831,0.01075156,0.0336181,-0.00440443,0.06688965,-0.05248703,0.05033687,0.0211772,0.02467513,-0.01279907,0.01204738,-0.04113485,-0.01896406,-0.00071666,0.09771316,-0.07365274,0.02358782,0.00790334,0.05849408,0.05616862,-0.00698499,-0.02935512,-0.04580317,-0.16269623,0.00267407,-0.05217291,0.0299864,0.01476049,0.04245403,-0.03757892,-0.01942435,0.05385685,-0.04480141,-0.01652719,-0.00564173,0.01765051,0.03243752,0.00916908,0.03688787,-0.05590588,0.03023813,-0.05418675,0.000393,-0.0656672,-0.03659749,-0.07521011,0.00677895,0.03463498,0.00236685,0.01887968,0.02205571,0.04160241,0.04763621,0.01631439,0.09351503,0.04968904,-0.19170234,0.016811,0.08947176,0.00686444,-0.03609111,0.01598465,0.04358104,0.06824545,0.02432998,0.00630083,-0.01738183,0.04488036,0.00757273,0.0073384,0.01651723,0.03561613,-0.01407321,-0.03764079,-0.06188382,0.08448915,0.00345687,-0.00648263,-0.02457022,-0.01945627,-0.0088878,-0.01265496,-0.03744479,0.02423548,-0.0040034,0.03376984,0.03649799,0.00998807,-0.01976937,-0.01685749,-0.01714589,0.00568273,-0.11003069,0.12395792,-0.04513046,0.0265171,-0.0009641,-0.05058721,0.03820981,0.01452916,-0.00640749,-0.06357782,0.01496245,0.01811722,0.00682786,0.00381485,0.07841009,-0.07469517,0.05417635,0.00133021,0.00535908,-0.00086985,-0.04508664,0.01363141,-0.00396606,0.0408783,0.03087152,-0.03163913,-0.01242691,-0.0226776,0.02772392,0.05217075,0.06238696,0.05473794,0.00053643,0.05990391,-0.0765948,0.01390332,-0.05938393,-0.0167006,0.00913812,-0.01620178,-0.0061116,-0.04728897,-0.02500809,-0.07234781,-0.02753486,-0.10109311,-0.04895054,0.08779568,-0.00412408,0.03514478,-0.0237006,-0.04714217,0.00698751,0.0995752,-0.02995231,-0.08376545,-0.0262047,0.0028575,0.02555585,0.11416924,-0.05882895,-0.01652519,-0.00874554,-0.03017438,-0.03479093,0.09101331,0.05993553,-0.12402431,0.03491318,0.03734295,-0.00486506,-0.05858658,-0.00222555,0.02152597,-0.06109487,-0.0100198,0.06872929,-0.04591385,-0.01330581,-0.01742865,0.01769574,0.06360961,0.02317032,-0.03227548,-0.06157663,-0.03140006,-0.01601871,-0.02827018,-0.01809218,0.04069692,0.05142908,0.04634544,-0.1157389,0.05425287,0.01260346,0.04428675,-0.03646772,-0.00200696,-0.01502207,-0.00084694,0.00831521,-0.06426568,0.16062026,0.03353548,0.00475345,0.01645127,-0.063203,0.02792149,-0.0194621,-0.0368202,0.00514057,0.03433607,-0.02759054,0.02069122,-0.02748991,0.05707872,-0.04268485,0.00057996,0.01260397,0.04969962,-0.03484287,0.03307555,0.02868851,0.03787227,-0.10027622,-0.19625275,-0.03138369,0.05990084,-0.01452793,0.01038716,-0.01091952,0.02804793,0.02280241,0.03824543,0.10208376,0.10416123,0.06054714,-0.06086332,-0.00317535,-0.01107096,0.04496504,0.04342763,-0.00575471,0.0013921,0.0279789,-0.04500592,0.0201842,-0.00859948,-0.04023496,0.06178105,-0.03903671,0.10471795,-0.03388899,0.02141664,-0.00716314,0.08526653,0.01566486,-0.04430155,-0.15224956,0.04423516,0.06665706,-0.00291393,-0.03159784,-0.05530718,-0.03497126,-0.02376973,0.00008401,0.03120138,-0.10518903,0.00819708,-0.0771943,-0.02878795,-0.05600195,0.00490329,0.01892026,-0.01316756,0.03341067,0.02865088,0.1210169,0.00396238,-0.00466738,0.00222709,-0.03777073,-0.01083619,0.048299,0.00633405,-0.00581338,-0.03380543,0.02269015,0.01897173,-0.009874,-0.0340367,0.03744328,0.02099925,0.00209023,-0.02651637,0.14294578,0.02663097,0.01513299,0.0277833,0.01481081,-0.04590486,-0.0942784,0.05669104,-0.01368048,0.01305964,-0.0109245,0.03121161,0.04631123,-0.02706853,0.058251,0.02901591,-0.02681034,0.03804385,0.01453107,-0.00575405,-0.0503354,-0.06096336,-0.05241948,0.07509937,0.00762439,-0.29248637,-0.04134499,-0.0037801,-0.033668,0.0722042,0.03076203,0.05527126,0.03716992,-0.07470732,0.0384076,-0.00507613,0.05376656,-0.00406855,-0.07551624,0.02039327,-0.02597149,0.03598678,-0.00750704,0.01743765,0.04162812,-0.00919719,0.05703705,0.2098306,-0.00642057,0.0321501,0.01175976,-0.04620015,0.03257237,0.01411969,0.02652621,-0.04894942,-0.02365935,0.02544895,-0.04207566,0.0167926,0.02787201,-0.01356788,-0.00201368,-0.00803677,0.02024697,-0.05370007,-0.00219168,-0.03158811,0.01147098,0.09344694,0.00266195,-0.06088119,-0.07584929,0.01518405,0.03862738,-0.05490224,-0.03142048,0.01933433,0.04467081,0.01302656,0.07290344,0.03201309,-0.05788445,-0.09037513,-0.01161695,-0.02921382,-0.02847439,-0.04477355,0.0570422,0.01125434],"last_embed":{"hash":"1qpqvea","tokens":267}}},"text":null,"length":0,"last_read":{"hash":"1qpqvea","at":1751426529826},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.3. 社区情绪与目标受众**","lines":[209,216],"size":262,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1qpqvea","at":1751426529826}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.3. 社区情绪与目标受众**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06030823,-0.03037412,-0.05579633,-0.02285966,0.04249791,0.02020716,0.00353969,0.00793564,0.03181575,-0.00594347,0.07082342,-0.05070137,0.05004517,0.02102577,0.02350148,-0.01572824,0.01741206,-0.04121875,-0.01734315,-0.00006091,0.09716815,-0.07118759,0.02344817,0.00666034,0.05906261,0.05643104,-0.00485034,-0.03024177,-0.04792423,-0.16193014,0.00483549,-0.05420554,0.02714942,0.01693053,0.04086697,-0.03863006,-0.02022014,0.05476397,-0.04676061,-0.01688847,-0.00644444,0.01752022,0.03355233,0.0081057,0.03829779,-0.05797911,0.03028355,-0.05219404,0.0009318,-0.06616337,-0.03687655,-0.07390063,0.00381548,0.03550131,0.00376495,0.02119738,0.02166196,0.04491868,0.04868685,0.01617929,0.09189998,0.04941539,-0.19002837,0.01634381,0.09226444,0.00787061,-0.03448431,0.01588392,0.04543066,0.06850223,0.02485147,0.00516977,-0.01784349,0.04438794,0.00814491,0.00654628,0.01662295,0.03255926,-0.01567545,-0.03782829,-0.06304695,0.08329675,0.00176403,-0.00503165,-0.02476791,-0.01875885,-0.00815721,-0.01359971,-0.03884503,0.02490105,-0.00437737,0.03351067,0.03663954,0.01001188,-0.01790813,-0.01693632,-0.01590714,0.0067758,-0.10954925,0.12388142,-0.04276768,0.02847513,-0.00300584,-0.05025379,0.03688853,0.01574069,-0.00449642,-0.06160289,0.01243632,0.01847736,0.00907619,0.0052064,0.0804553,-0.07351976,0.05420413,0.00222743,0.00191452,-0.00011057,-0.04562288,0.01499805,-0.00371101,0.04212094,0.03243213,-0.0321665,-0.01387499,-0.0236556,0.02472911,0.0513154,0.06041382,0.05693889,0.00240023,0.05824564,-0.07497239,0.01183102,-0.05680688,-0.01563229,0.00865406,-0.01595178,-0.0076179,-0.04559292,-0.02420164,-0.0727002,-0.02846058,-0.10195338,-0.05113961,0.08709931,-0.00405883,0.03691473,-0.01977192,-0.04747146,0.0055161,0.09641986,-0.02970005,-0.08307847,-0.0254755,0.00347964,0.02388603,0.11559935,-0.05961035,-0.01471055,-0.0105035,-0.03117983,-0.03415401,0.09155419,0.0602686,-0.12172723,0.03524407,0.03594244,-0.0064016,-0.0578248,-0.00241846,0.02134369,-0.06207799,-0.00810624,0.07145614,-0.04772229,-0.01441281,-0.01613505,0.01713583,0.06128507,0.02106231,-0.03534209,-0.06146618,-0.03027722,-0.01734262,-0.0239379,-0.01846825,0.04152796,0.0525839,0.04611489,-0.11550564,0.05313597,0.01095648,0.04491509,-0.03571569,-0.00566959,-0.01656214,-0.00093057,0.00958897,-0.06364256,0.16035903,0.03310589,0.00604765,0.01742106,-0.06692242,0.02692135,-0.01812377,-0.04030474,0.00287331,0.03492488,-0.02559303,0.01899365,-0.03082966,0.05759189,-0.04124029,0.0030645,0.01258096,0.04919071,-0.03475234,0.03213857,0.02770253,0.03412297,-0.1008756,-0.1947349,-0.03110805,0.06147191,-0.01513637,0.01478519,-0.01074572,0.02686745,0.02380143,0.03642248,0.10317318,0.10328966,0.05910279,-0.06166775,-0.0030641,-0.00967401,0.04770225,0.04195172,-0.00624185,0.00376438,0.02867761,-0.04490697,0.01871518,-0.00719012,-0.04059359,0.06217873,-0.03879657,0.10348074,-0.03293902,0.019107,-0.00692389,0.08561148,0.01579879,-0.04553899,-0.15360826,0.042745,0.06761295,-0.00370309,-0.03321002,-0.05264142,-0.03267917,-0.02428974,-0.00084967,0.03134434,-0.10399177,0.01143602,-0.07820755,-0.02863997,-0.0545881,0.0058305,0.01948071,-0.01326515,0.03428176,0.02563704,0.11884134,0.000383,-0.00887875,0.00440319,-0.0400615,-0.01220528,0.05035872,0.0051551,-0.00584963,-0.03446392,0.01903211,0.01890055,-0.01126064,-0.03455979,0.03663055,0.02305185,-0.0020786,-0.02454116,0.1441519,0.02420279,0.01448114,0.02813409,0.01586187,-0.0467956,-0.09446258,0.05776633,-0.01137128,0.01485715,-0.00805241,0.03074889,0.04852892,-0.02625179,0.05948524,0.02626437,-0.02596381,0.03837837,0.0124352,-0.00368616,-0.05025333,-0.06152736,-0.05079346,0.07371535,0.00774326,-0.29181835,-0.04020298,-0.00366975,-0.03218022,0.07142599,0.03271214,0.05995657,0.03900443,-0.07502203,0.04050667,-0.00546479,0.05406954,-0.00226239,-0.07478882,0.01932802,-0.02716097,0.03539082,-0.00917658,0.01469734,0.04396062,-0.00635475,0.05940257,0.20914002,-0.0080539,0.03229796,0.01189235,-0.04985117,0.03371976,0.01329647,0.02594799,-0.04883471,-0.02397521,0.02219579,-0.04576449,0.0169013,0.02704377,-0.01156052,-0.00341897,-0.00691203,0.02177658,-0.05270752,-0.00274065,-0.03248677,0.0102483,0.09587357,0.00188778,-0.06051012,-0.07790028,0.01547917,0.04074712,-0.05475134,-0.02801925,0.01919775,0.04793061,0.00948779,0.07219806,0.03146625,-0.05738005,-0.09156821,-0.01386627,-0.02788934,-0.02763902,-0.04385986,0.05725848,0.01263333],"last_embed":{"hash":"1x7w9al","tokens":264}}},"text":null,"length":0,"last_read":{"hash":"1x7w9al","at":1751426529835},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.3. 社区情绪与目标受众**#{1}","lines":[211,216],"size":237,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1x7w9al","at":1751426529835}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04319293,-0.04337992,-0.07038026,-0.04871643,0.02425545,0.00755702,0.00871646,0.01457875,0.00008732,-0.0128108,0.0745905,-0.05801143,0.03860666,0.02620354,0.02081358,-0.03854162,0.03142751,-0.02852014,-0.01082792,0.0290276,0.10222162,-0.10825412,0.01269199,-0.05374333,0.08393011,0.0546018,0.03169223,-0.01271644,-0.04777183,-0.20799243,0.00793488,-0.069199,0.02304534,0.02256202,-0.0174484,-0.06112424,-0.03061742,0.080246,-0.044261,0.00417632,0.00631229,-0.0080904,0.01622877,0.00285053,0.00763023,-0.08409189,0.02575639,-0.06595196,0.02050305,-0.04116556,-0.05429,-0.04414651,-0.01555832,0.0197176,0.00608519,0.03497174,0.0161956,0.03664403,0.0458852,0.03692349,0.05072476,0.0379464,-0.19788176,0.00221946,0.05869173,0.02991283,-0.01531485,0.03555252,0.05575151,0.08004575,0.00336164,-0.00805703,-0.01532693,0.03170747,0.01057544,0.00478606,0.01161169,0.01475896,-0.04669192,-0.06625153,-0.08230136,0.07846683,-0.01018968,-0.02161732,-0.02197593,-0.01733957,-0.00537612,-0.02138454,-0.02860846,0.0130586,-0.03587177,0.03231301,0.0528274,0.00752248,-0.0385139,-0.01705122,-0.00996348,0.023134,-0.10598234,0.10295735,-0.0139623,0.05564483,-0.03100048,-0.0647721,0.05093206,0.00818522,-0.00164948,-0.06807505,-0.00112161,0.02957173,0.01887903,-0.00392545,0.07181641,-0.06968313,0.01295926,0.02830596,0.00606028,-0.00102961,-0.00117152,0.02030143,0.01444582,0.03405096,0.04141927,-0.02980081,-0.03544575,-0.02260862,0.01342063,0.0602597,0.06671324,0.03739635,0.0212896,0.04907404,-0.0710562,-0.01446036,-0.06929919,-0.03318796,-0.04720051,0.01124701,-0.00530092,-0.02047543,-0.03181851,-0.07047071,-0.00312423,-0.09182815,-0.07178496,0.08061299,-0.01482532,-0.01940002,0.02267745,-0.05357562,0.02450438,0.06551671,-0.00929683,-0.0746825,-0.01357792,-0.03110388,0.0111388,0.11791365,-0.07549276,-0.00667771,-0.0380541,-0.03102488,-0.04100096,0.15015288,0.03656542,-0.09360004,0.04614509,0.03012393,-0.02334217,-0.07156297,0.01244055,-0.00876281,-0.03592024,0.00361637,0.07514124,-0.04706147,-0.02256789,-0.04385582,-0.00071723,0.04950733,0.00890554,-0.01723207,-0.0695697,0.00073062,0.01341068,-0.01767072,-0.03427754,0.00400843,0.03302312,0.01936425,-0.05883174,0.0566072,0.01967281,0.03304953,-0.01798626,-0.00820835,-0.02709833,-0.00830065,0.01819918,-0.04924706,0.14792356,0.03432181,0.03107005,0.0039085,-0.07004078,0.01528691,-0.05030685,-0.03604252,-0.00624796,0.06565948,-0.00856125,0.01380927,-0.02753446,0.02322256,-0.01205562,0.00357686,0.01943414,0.03761202,-0.04153762,0.04411138,0.01265417,0.02578028,-0.09344362,-0.19067137,-0.02700784,0.0457454,-0.0122,0.02890623,-0.00602887,0.04201735,0.01619519,0.03462288,0.09495992,0.08106747,0.05027237,-0.04979273,0.02135161,-0.00680368,0.04435378,0.0561665,-0.01858168,0.03087743,0.04476131,-0.04199938,0.0148281,0.01012022,-0.01723125,0.05504227,-0.07853381,0.09399316,-0.01828851,0.04553071,-0.01324163,0.07915881,0.0218362,-0.02082877,-0.09794027,0.04624582,0.07359975,-0.01229942,-0.02498799,-0.05041588,-0.01555479,-0.00342874,0.01725216,0.03552876,-0.09378236,0.0351691,-0.05186923,-0.03025374,-0.01877197,-0.04082562,0.02798318,0.00055937,0.04016471,0.02903096,0.10469588,0.01467566,-0.01699749,0.00248409,-0.08338897,-0.000518,0.04874232,0.00341699,-0.01189812,-0.01873577,-0.01926017,0.05062133,0.00399979,-0.01850673,0.03880495,0.04307617,-0.02955715,-0.02919802,0.13268881,0.01199897,-0.01421942,0.05680861,0.04066598,-0.03842038,-0.08086722,0.03373724,0.00999128,0.03041647,0.02173083,0.00073055,0.02540328,0.00409891,0.06200775,0.03735181,0.01845853,0.02431561,0.00904696,-0.0201494,-0.03793267,-0.04129894,-0.01509152,0.08234267,-0.01249675,-0.31175885,-0.02924213,-0.02562387,-0.02706077,0.06396572,0.03717588,0.01665475,-0.00259595,-0.09897313,0.05200088,0.00465246,0.05064645,0.00342371,-0.05611033,0.00177195,-0.02060653,0.04848647,-0.01707924,0.03398966,0.03800059,0.01091398,0.08208185,0.20844342,-0.00960809,0.03243918,0.02702417,-0.03989021,0.05482075,0.01628187,0.01567148,-0.01568132,-0.00035537,0.04236073,-0.01830626,-0.0073549,0.06380809,-0.02518531,0.01778957,-0.00842031,0.03533518,-0.02647031,-0.00120367,-0.03125154,0.04985894,0.09577671,-0.00051001,-0.0433488,-0.07418552,0.03593913,0.04378884,-0.08179779,-0.01492171,-0.00972596,0.03248884,0.00016559,0.06460287,0.01124413,-0.05434309,-0.07598632,-0.03708613,-0.00713602,-0.00212608,-0.05723252,0.07548356,0.00837723],"last_embed":{"hash":"123mjhy","tokens":417}}},"text":null,"length":0,"last_read":{"hash":"123mjhy","at":1751426529842},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**","lines":[217,246],"size":1474,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"123mjhy","at":1751426529842}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**表 3: 竞争分析：Nockchain vs. Aleo vs. RISC Zero**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03968,-0.04406721,-0.06285328,-0.03581557,0.0386737,0.0122612,0.00788415,0.02064016,0.00040571,-0.00681056,0.0759416,-0.05911773,0.03916621,0.02502905,0.02298479,-0.02386476,0.02512907,-0.02046832,-0.01669717,0.03167092,0.10400015,-0.10643736,0.0156738,-0.0612129,0.08031995,0.05289923,0.01996407,-0.01583454,-0.04617215,-0.21022068,0.00398839,-0.05645368,0.02561148,0.01839527,-0.01993987,-0.06208531,-0.03342914,0.08107654,-0.0427667,-0.00339341,-0.0014847,-0.0082854,0.02486838,-0.00480847,0.0026227,-0.08267111,0.03166742,-0.06623694,0.02143558,-0.04129863,-0.05453142,-0.05214024,-0.01512403,0.02622544,0.01308629,0.03671307,0.01372012,0.02766131,0.04552167,0.04059356,0.04832642,0.04798238,-0.2053504,0.00145392,0.06746015,0.02926239,-0.02075534,0.04594834,0.040164,0.07180261,-0.00089974,-0.00947465,-0.02218659,0.0400077,0.00137396,0.00912923,0.02051645,0.01035134,-0.04322864,-0.0689944,-0.07522918,0.07718224,-0.01634782,-0.01798492,-0.02198377,-0.01926694,-0.00468373,-0.0200648,-0.03117651,0.01011095,-0.03750834,0.04271459,0.05721911,0.00583056,-0.03410451,0.00187071,-0.01007013,0.02232856,-0.10899764,0.10478447,-0.00543197,0.05345728,-0.02524324,-0.07156338,0.04390531,0.02266201,0.00202908,-0.06579035,-0.00538681,0.03648192,0.0200328,-0.00322544,0.06482987,-0.06423562,0.01185248,0.03046891,0.0058239,-0.00134277,0.00408181,0.01942952,0.01768636,0.02420294,0.04959268,-0.03323077,-0.03227397,-0.02297937,0.00693945,0.06566629,0.05061749,0.03037037,0.02819505,0.04419542,-0.07633142,-0.00426338,-0.07960353,-0.03924067,-0.04761571,0.00096884,-0.00382018,-0.02377377,-0.02447844,-0.07311726,-0.00010546,-0.09340213,-0.0629992,0.08525823,-0.02581096,-0.01611029,0.02421458,-0.05133297,0.02595972,0.06628851,-0.00265784,-0.0796262,-0.00650654,-0.02868661,0.00651317,0.11196426,-0.06640869,-0.01535481,-0.0358896,-0.03139999,-0.04310725,0.15076725,0.04647772,-0.09310004,0.04452503,0.03306595,-0.02404194,-0.06668787,0.01756155,-0.01073783,-0.03192217,-0.00000707,0.08234087,-0.0429985,-0.01253201,-0.0475778,0.0005867,0.04330969,0.01163416,-0.02118433,-0.06124416,0.00859226,0.00988062,-0.02193005,-0.03999274,0.0055707,0.0353894,0.0157202,-0.06188733,0.05388946,0.01882128,0.03623907,-0.01097169,-0.00661008,-0.0194996,-0.01187044,0.01788215,-0.04771018,0.14608328,0.03939498,0.0387499,-0.00535903,-0.06432896,0.02171174,-0.05280295,-0.03640134,-0.00915461,0.05969535,-0.01031024,0.00939142,-0.02010611,0.02649982,-0.01859214,0.0043356,0.01995806,0.04185425,-0.0482829,0.05079662,0.00642502,0.0244189,-0.08659026,-0.19992192,-0.03851299,0.02588317,-0.0166764,0.0311375,-0.00643349,0.04084106,0.01506314,0.03429428,0.09247865,0.08017494,0.05327735,-0.04210244,0.01922599,-0.00235047,0.04082981,0.0550109,-0.01188712,0.01882446,0.03024668,-0.04144546,0.01544102,0.01254606,-0.02326647,0.0575144,-0.06815655,0.09589262,-0.01421321,0.04679165,-0.01266466,0.07902792,0.01795894,-0.02102999,-0.10484125,0.04731409,0.07273418,-0.01862069,-0.03192165,-0.04658633,-0.01962587,-0.00277424,0.02400704,0.04499174,-0.0802986,0.03335598,-0.05406616,-0.02205719,-0.02076241,-0.03071016,0.02792311,-0.00730945,0.04655698,0.02268522,0.10030226,0.01429942,-0.00747952,-0.00665399,-0.08080643,0.00121467,0.04276682,0.00781627,-0.01438305,-0.02054465,-0.01539164,0.03643212,0.01113119,-0.00817884,0.03510421,0.03709348,-0.02163102,-0.01781549,0.13053554,0.00512685,-0.01414898,0.05832693,0.04388466,-0.04043259,-0.08762839,0.03405497,0.00220161,0.02226112,0.02277758,-0.00047464,0.03126213,0.00723533,0.0545147,0.04520084,0.01079527,0.01274191,0.00052173,-0.02309985,-0.03420267,-0.04413059,-0.01568493,0.08011527,-0.02253732,-0.32132021,-0.03085266,-0.0271973,-0.03223792,0.06887297,0.03225802,0.01269733,-0.00234249,-0.09817483,0.04561907,0.00699997,0.04531687,-0.00101083,-0.05408317,0.00800825,-0.01521274,0.05690653,-0.01834509,0.03432321,0.03806624,0.01118309,0.08194566,0.20849904,-0.0185157,0.03301719,0.02323914,-0.02909486,0.05483175,0.00788264,0.01546072,-0.01148655,-0.00306811,0.04327725,-0.02667055,-0.00765381,0.0574725,-0.03530589,0.01769142,-0.01429202,0.03867529,-0.03569304,0.00167754,-0.03505712,0.05522871,0.09599517,0.01160803,-0.03633369,-0.08191063,0.03332269,0.0513674,-0.0770921,-0.01362912,-0.00634024,0.02982881,0.01701501,0.05834619,0.01642516,-0.06070423,-0.07668044,-0.02471622,-0.00702336,0.00133092,-0.05352676,0.07300962,0.01352609],"last_embed":{"hash":"9tk8o5","tokens":412}}},"text":null,"length":0,"last_read":{"hash":"9tk8o5","at":1751426529855},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**表 3: 竞争分析：Nockchain vs. Aleo vs. RISC Zero**","lines":[221,234],"size":546,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"9tk8o5","at":1751426529855}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**表 3: 竞争分析：Nockchain vs. Aleo vs. RISC Zero**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04069815,-0.04311387,-0.06256676,-0.03934961,0.03578103,0.01066853,0.00787176,0.01795256,0.00014581,-0.00456181,0.07727223,-0.05728704,0.03940858,0.02622218,0.0213537,-0.02460291,0.02723878,-0.02074105,-0.01564579,0.03189851,0.10117158,-0.1061442,0.015649,-0.06072767,0.07932796,0.05167831,0.02257555,-0.0168015,-0.04688778,-0.21199277,0.00497043,-0.05858627,0.02596081,0.02032494,-0.01866518,-0.06069576,-0.03255682,0.08060513,-0.04223307,-0.00446053,0.0009605,-0.00845788,0.02734736,-0.00438003,0.00338997,-0.08521985,0.03076004,-0.06733244,0.02084907,-0.04249165,-0.05375691,-0.05343527,-0.0164685,0.02527945,0.0087121,0.03404224,0.01293222,0.02552799,0.04571619,0.04199449,0.04806996,0.04577355,-0.2040208,-0.00147106,0.0657132,0.02854381,-0.02126351,0.04686767,0.04038637,0.07059122,0.00055134,-0.01120505,-0.02219219,0.03811814,0.00140291,0.00830304,0.02146784,0.00950028,-0.0449073,-0.06916334,-0.07431724,0.0772244,-0.01552126,-0.01910482,-0.01953786,-0.01935269,-0.00628264,-0.02122296,-0.03014986,0.0115891,-0.0360682,0.04147195,0.05990352,0.00345885,-0.03342867,0.0028814,-0.00902441,0.02453168,-0.11062171,0.10565375,-0.0071516,0.05336417,-0.02787256,-0.07309688,0.04455732,0.02264174,0.00203563,-0.06617714,-0.00433194,0.03669835,0.01837039,-0.00200875,0.06612097,-0.06410114,0.01338768,0.03010065,0.00311687,0.00031383,0.00052723,0.01974184,0.01835392,0.02326386,0.05096034,-0.03424948,-0.0315426,-0.02337867,0.00625439,0.06471285,0.05025265,0.02804483,0.02954348,0.04313984,-0.0751538,-0.00371993,-0.07969257,-0.03877919,-0.04749376,0.00064687,-0.00581348,-0.02578626,-0.02616144,-0.07434134,0.00105724,-0.09341459,-0.06348208,0.08464125,-0.02613653,-0.01615672,0.02425655,-0.04974386,0.02639902,0.06732944,-0.00256713,-0.08018471,-0.00499142,-0.03038066,0.0055498,0.11157557,-0.06486247,-0.01274225,-0.03679873,-0.03440798,-0.04139027,0.14835893,0.04657204,-0.09045402,0.04507094,0.03379717,-0.02441861,-0.06559243,0.01754826,-0.01377388,-0.03310961,0.00043724,0.08439074,-0.04375288,-0.01283668,-0.04970054,0.00191097,0.04154942,0.00921208,-0.02161017,-0.06128273,0.00836154,0.01005976,-0.01969385,-0.03949355,0.00512247,0.03416781,0.01456508,-0.0616994,0.05494122,0.01768824,0.03706281,-0.01008986,-0.00564732,-0.01869686,-0.01204635,0.01748974,-0.04911051,0.14317955,0.03607312,0.03920436,-0.00700192,-0.06247303,0.01905429,-0.05234924,-0.0366299,-0.01191031,0.06135879,-0.00800079,0.00885151,-0.01914465,0.02974681,-0.01918715,0.00466732,0.01835788,0.04250658,-0.04845765,0.04882975,0.00636445,0.02870149,-0.08885051,-0.20034195,-0.03776168,0.02694427,-0.01775839,0.03301422,-0.00507053,0.04039002,0.01592992,0.03500408,0.09409958,0.08258197,0.05142828,-0.04000824,0.02055036,-0.00182343,0.04431781,0.05629157,-0.00878232,0.02055719,0.0295014,-0.03987852,0.01488148,0.01288924,-0.02124331,0.05951764,-0.06679824,0.09308618,-0.01466622,0.04556919,-0.00973344,0.07849842,0.01677177,-0.02164059,-0.10623815,0.04543772,0.0728677,-0.01707064,-0.03020035,-0.04656589,-0.01605037,-0.00314811,0.02649554,0.04647474,-0.08010988,0.03588805,-0.05568091,-0.02148073,-0.01877685,-0.0350093,0.02650357,-0.00596306,0.04687133,0.02167528,0.1009674,0.01342903,-0.00958234,-0.0075616,-0.08218729,0.00164576,0.04469982,0.00707219,-0.01547495,-0.02077506,-0.0170971,0.03688222,0.00997251,-0.00603967,0.03591957,0.03969789,-0.02071979,-0.01760267,0.12959288,0.00481928,-0.01560072,0.06126304,0.04424641,-0.04181932,-0.08873966,0.03420418,0.00606598,0.02145946,0.02419118,-0.00264693,0.03206149,0.00940945,0.05481332,0.04479044,0.01096588,0.01093796,-0.00027732,-0.02314107,-0.03620643,-0.04303084,-0.01165367,0.07968134,-0.02245702,-0.32044715,-0.03062474,-0.02906569,-0.03377696,0.06876007,0.03297487,0.01288468,-0.00306102,-0.09745193,0.04786569,0.0052048,0.04501817,-0.00015698,-0.05348336,0.00673938,-0.01680246,0.05445443,-0.01730288,0.03438255,0.0372106,0.01081368,0.08190003,0.20914774,-0.01791501,0.03105226,0.02198977,-0.02898572,0.05521793,0.00848101,0.01418344,-0.01018478,-0.00233564,0.04289664,-0.02596086,-0.00586524,0.05884426,-0.03480338,0.01811341,-0.01586786,0.03900771,-0.03345436,0.00149093,-0.03473393,0.05407596,0.09865546,0.01036425,-0.03593671,-0.07957535,0.0339909,0.05238223,-0.07641003,-0.01029392,-0.00332453,0.03045014,0.01532998,0.05750107,0.01644601,-0.06293187,-0.07564005,-0.02431457,-0.00817746,0.00229555,-0.05235116,0.07290129,0.01413267],"last_embed":{"hash":"19rumnj","tokens":409}}},"text":null,"length":0,"last_read":{"hash":"19rumnj","at":1751426529865},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**表 3: 竞争分析：Nockchain vs. Aleo vs. RISC Zero**#{1}","lines":[223,234],"size":493,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"19rumnj","at":1751426529865}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**7.1. Nockchain vs. Aleo**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04902013,-0.01204526,-0.07695574,-0.02831938,0.01267344,0.00269585,0.02153239,0.01495089,0.00135393,-0.02054754,0.05597515,-0.04923533,0.02288143,0.02421232,0.02835969,-0.03014022,0.02406648,-0.0609009,-0.04061241,0.03034963,0.10133842,-0.08882634,0.03389731,-0.03159026,0.07059579,0.04783712,0.01993772,0.02062749,-0.03785015,-0.19208065,-0.00061488,-0.06450869,0.01628902,-0.00547567,-0.0013004,-0.05742382,-0.00750341,0.05335771,-0.04105339,-0.02400389,0.01581001,0.00740678,0.01850001,-0.00325931,0.02789151,-0.0753047,0.03426097,-0.09355259,0.00417291,-0.05011285,-0.02245895,-0.07553995,-0.01749726,0.03251776,0.00626773,0.02714198,0.00132713,0.03793671,0.04519033,0.0329572,0.07283214,0.02873256,-0.18527047,0.03563388,0.08081418,0.01524901,-0.03005755,0.03902037,0.03400399,0.07058313,0.02378834,-0.02326172,-0.02155827,0.04177832,0.01692881,-0.00106536,0.03905681,0.00602677,-0.03430844,-0.05684636,-0.07938536,0.07838126,-0.01493148,-0.0028126,-0.02045805,-0.03206376,-0.00025383,-0.02308708,-0.02860652,-0.00807714,-0.01190986,0.03236185,0.06261332,0.00443118,-0.01559125,0.00538727,-0.03278312,0.02050129,-0.09292832,0.11430035,-0.04397291,0.06926907,-0.02622824,-0.04296122,0.05100693,0.00910727,0.00596214,-0.08257505,0.03745299,0.03140675,0.03082242,-0.01159096,0.09770928,-0.04464922,0.01518581,0.02040828,0.00791421,-0.01322859,-0.01134268,0.03717972,-0.01010386,0.04252445,0.04419766,-0.00927866,-0.02271846,-0.01584664,0.00505142,0.04533492,0.05742862,0.03406213,0.01574996,0.01774704,-0.08237975,-0.02346184,-0.0617098,-0.0354673,-0.02375644,0.01607877,-0.02250849,-0.03482711,-0.01436439,-0.07361752,-0.00655773,-0.10043304,-0.08722339,0.0811934,-0.00095136,0.0100707,0.00324376,-0.06110347,0.00638099,0.06523672,-0.03203673,-0.09762722,-0.02587032,0.00760754,0.00841466,0.11843134,-0.08146758,0.0112967,-0.03068976,-0.01053649,-0.039424,0.11876205,0.03097397,-0.08803031,0.04921744,0.01809916,-0.00553825,-0.06792883,0.02966826,0.03132943,-0.03548426,-0.01213,0.07002365,-0.05447562,-0.02850959,-0.02857598,0.00647545,0.05927479,0.00023376,-0.01093107,-0.07901064,-0.01150225,0.03162969,-0.02441339,-0.02244315,0.00021204,0.03743236,0.06466373,-0.06449805,0.0488936,0.00675478,0.04960778,-0.02929559,-0.01830186,-0.03443182,-0.02288114,-0.00607348,-0.05282563,0.1377234,0.06012209,0.02314864,0.02783802,-0.0643991,0.00764439,-0.02994287,-0.042216,0.02246212,0.05898884,-0.02817531,0.03185985,-0.04165133,0.03926659,0.00168807,-0.00879254,0.00300539,0.03516999,-0.01743874,0.03959478,0.01510029,0.00800305,-0.06294139,-0.19642769,-0.02256598,0.02645358,-0.01830408,0.03274802,0.00124583,0.04666162,0.0269415,0.02583022,0.09849718,0.07214276,0.05931681,-0.05419352,0.01831256,-0.01976351,0.04867059,0.06326848,-0.02961708,0.01723625,0.03487468,-0.05096039,-0.01125259,0.02497794,-0.04368614,0.06018821,-0.06877866,0.08830167,-0.0469946,0.05811871,0.00050914,0.06925455,0.0103693,-0.01780907,-0.13968299,0.03559866,0.06633201,-0.00949837,-0.04392898,-0.05272091,-0.03381263,-0.01198318,0.01972003,0.0404296,-0.09796134,0.02543447,-0.05162299,-0.0293698,-0.01123006,-0.03842647,0.01267858,-0.00178395,0.0404094,0.01147392,0.12090228,0.00002824,-0.0148112,0.00657919,-0.04132405,-0.00672261,0.03760404,-0.01079303,-0.022971,-0.02761798,-0.00784908,0.03411838,0.02622957,-0.01911058,0.02900497,0.03719477,-0.03320704,-0.02516925,0.1537316,-0.00127411,-0.01833522,0.06057098,0.05229705,-0.02127979,-0.0952355,0.04328154,0.00901618,0.02212339,0.00689284,0.02204681,0.04320577,-0.02197671,0.08062784,0.03671371,-0.01406452,0.0069722,-0.00909589,-0.00133991,-0.04242628,-0.05271981,-0.04700537,0.06885334,0.00678883,-0.2987234,-0.02748579,-0.00393873,-0.0165303,0.05857839,0.03812653,0.03706253,0.02452763,-0.0710586,0.01870405,-0.00951464,0.0697694,0.00633911,-0.04742361,-0.01137306,-0.01670126,0.04604072,-0.00487827,0.02274324,0.0592193,0.03317997,0.07214704,0.20275135,0.00278178,0.03990562,0.03501658,-0.05740932,0.04470512,0.00434713,0.01797691,-0.02895908,0.0149872,0.04251802,-0.03742525,0.00713421,0.06439171,-0.02401558,0.01248657,-0.01634514,0.01798585,-0.03134518,-0.01188399,-0.03161299,0.02971931,0.09445464,0.021439,-0.04490586,-0.06974339,0.01464465,0.02883567,-0.096411,-0.03305051,-0.00393766,0.04343069,0.01153009,0.07131393,0.02319413,-0.03757976,-0.10185432,-0.02354828,-0.00665254,-0.00190367,-0.06006233,0.08452579,-0.00745371],"last_embed":{"hash":"2fs1i1","tokens":305}}},"text":null,"length":0,"last_read":{"hash":"2fs1i1","at":1751426529878},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**7.1. Nockchain vs. Aleo**","lines":[235,238],"size":334,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"2fs1i1","at":1751426529878}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**7.1. Nockchain vs. Aleo**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05329152,-0.0139425,-0.07951467,-0.02811293,0.01234342,0.00367123,0.02352748,0.01534823,-0.00053212,-0.02118175,0.05591391,-0.04643194,0.02050048,0.02276585,0.0282301,-0.02890456,0.02498625,-0.06166722,-0.04116503,0.03062008,0.10199111,-0.0889684,0.02916235,-0.03013197,0.07121078,0.04602237,0.01903657,0.02064868,-0.03998732,-0.19075915,0.0022685,-0.06433072,0.01519006,-0.00787557,-0.00262819,-0.05697503,-0.0080395,0.05238692,-0.04292437,-0.02765007,0.0159157,0.00801604,0.02184891,-0.00271252,0.02689691,-0.07567547,0.03463488,-0.09362779,0.00716003,-0.05204206,-0.0221705,-0.0739089,-0.0174099,0.03144454,0.00428836,0.02797907,0.00240881,0.04119939,0.04733506,0.03539136,0.07368067,0.02591804,-0.18510297,0.03536078,0.07576054,0.01632835,-0.02831177,0.03915797,0.03034015,0.07286648,0.02624383,-0.02479647,-0.0233499,0.04129137,0.02085988,-0.0036871,0.03683226,0.00557788,-0.03457191,-0.05444432,-0.07907285,0.07943541,-0.01419958,-0.00139284,-0.02037541,-0.03048781,0.00291954,-0.02154311,-0.02839787,-0.00894297,-0.0163164,0.03536287,0.06145866,0.00343831,-0.01528578,0.00474704,-0.03366404,0.02022736,-0.08912537,0.11395859,-0.04154817,0.06896308,-0.02799976,-0.03907956,0.050295,0.010482,0.00603156,-0.08484434,0.03672351,0.03387295,0.03101087,-0.01086925,0.09813567,-0.04411357,0.01393704,0.01961166,0.00640202,-0.01503814,-0.01400575,0.03769802,-0.01034175,0.04349647,0.0453859,-0.00813643,-0.02339546,-0.01572175,0.00550497,0.04443786,0.05927292,0.03617901,0.01493461,0.01610169,-0.08227303,-0.02486265,-0.05710692,-0.03574838,-0.02455218,0.01350441,-0.02168772,-0.03685189,-0.01381339,-0.0704615,-0.00784092,-0.09852453,-0.08724023,0.08161388,-0.00029527,0.01111639,0.00200845,-0.05953672,0.00603716,0.06385662,-0.03385676,-0.09702965,-0.0267063,0.00720755,0.00603448,0.11700532,-0.08217769,0.01511455,-0.031136,-0.00737869,-0.03665883,0.12004312,0.02791706,-0.08700672,0.05016293,0.0162726,-0.00676429,-0.06704446,0.02868991,0.03258075,-0.03535144,-0.01062665,0.07135189,-0.05271282,-0.02907229,-0.0278634,0.00365611,0.05911201,-0.0052995,-0.01750429,-0.07970428,-0.01141772,0.03313601,-0.0225068,-0.0213571,0.00059949,0.03723083,0.0658181,-0.06438705,0.05327803,0.00599046,0.05193757,-0.03053376,-0.02017028,-0.03817939,-0.02194664,-0.00617557,-0.05103116,0.13863444,0.05948295,0.02749232,0.03105386,-0.06558484,0.00550456,-0.03058377,-0.04444824,0.02243946,0.0577546,-0.02871911,0.03122742,-0.04437277,0.04378441,0.00269397,-0.00674838,0.00217999,0.03671234,-0.01668438,0.03986062,0.01818704,0.00657205,-0.06157754,-0.19602427,-0.02018698,0.02468254,-0.02017505,0.03313905,0.00029485,0.04523658,0.03066629,0.02635249,0.09460171,0.07260438,0.06095552,-0.05506205,0.01735602,-0.02263435,0.05098686,0.06010091,-0.03016483,0.01610759,0.0366353,-0.05090607,-0.01270753,0.02582237,-0.0475182,0.05946116,-0.06669563,0.09004033,-0.0482951,0.06065965,0.00102415,0.06527759,0.00781027,-0.01685601,-0.13954945,0.03747038,0.06294697,-0.00836283,-0.04340383,-0.05024335,-0.0349765,-0.01292818,0.02059898,0.0404519,-0.09928316,0.02570642,-0.05175792,-0.03004655,-0.01132868,-0.03595896,0.00803446,0.00008226,0.04085968,0.00978883,0.12314001,-0.00021605,-0.01802393,0.00646348,-0.04260629,-0.00525455,0.04118263,-0.01167848,-0.02349412,-0.02934826,-0.00945763,0.0391241,0.02577551,-0.02050881,0.02847664,0.03674122,-0.03546343,-0.02476947,0.15355414,-0.00246368,-0.01896889,0.0609877,0.0531885,-0.02107411,-0.09351651,0.04071805,0.01210389,0.01875538,0.00716389,0.02074028,0.04508989,-0.02207085,0.08126158,0.03740966,-0.01425698,0.00539448,-0.00931391,0.001036,-0.04199746,-0.04954788,-0.04529167,0.0690776,0.00701291,-0.2979008,-0.02617506,-0.0026042,-0.01666724,0.05428227,0.0377369,0.03699327,0.02647784,-0.07082292,0.01616165,-0.01190559,0.07293194,0.00924076,-0.04438532,-0.01189163,-0.01563044,0.04199647,-0.00612635,0.0216268,0.06014382,0.03391903,0.07100756,0.20262228,0.00444441,0.03896505,0.03683956,-0.05915935,0.0469051,0.0055825,0.01791739,-0.02928777,0.01588582,0.04136712,-0.03995947,0.00521022,0.06447513,-0.02125403,0.01252442,-0.01501542,0.02020789,-0.03166785,-0.01284373,-0.03092908,0.02766269,0.0956865,0.0229286,-0.04481234,-0.06676937,0.01715623,0.03065206,-0.10044801,-0.03084165,-0.00500894,0.0429869,0.01160205,0.07016799,0.01937414,-0.03801671,-0.1030642,-0.02275502,-0.00678388,-0.00036473,-0.05978201,0.08269304,-0.00827625],"last_embed":{"hash":"vewrtc","tokens":302}}},"text":null,"length":0,"last_read":{"hash":"vewrtc","at":1751426529889},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**7.1. Nockchain vs. Aleo**#{1}","lines":[237,238],"size":300,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"vewrtc","at":1751426529889}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**7.2. Nockchain vs. RISC Zero**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02973611,-0.04056614,-0.05892599,-0.03243663,0.057121,0.0136168,0.01314722,0.02471995,0.00648374,0.00198564,0.05741224,-0.04620259,0.03401331,0.03132229,0.00058393,-0.02163651,0.03976832,-0.00556981,-0.0176066,0.04345498,0.1164691,-0.09316766,0.00318307,-0.04936058,0.08784062,0.03852007,0.01758865,-0.0406566,-0.03432675,-0.20052873,0.01289177,-0.06678779,0.00552924,0.03526472,-0.02707508,-0.06151589,0.00231828,0.06345847,-0.04839368,0.00147475,-0.01212252,-0.01179519,0.02815073,-0.01044768,0.01189626,-0.06916922,0.02931522,-0.07366724,0.0326247,-0.05739694,-0.07073099,-0.06716458,-0.01604203,0.01351747,-0.00017982,0.04907864,0.00516577,0.013861,0.04596305,0.05421834,0.0769643,0.05166979,-0.21522829,0.01946894,0.06795353,-0.00049704,-0.01883949,0.03747206,0.04362903,0.06077233,0.024059,-0.02338977,-0.03919571,0.04583303,0.02348531,-0.0109005,0.0258013,0.02996778,-0.01974574,-0.04725818,-0.04840887,0.0576513,-0.02775831,-0.01182038,-0.03355197,0.01594499,-0.01935961,-0.05422337,-0.03099125,0.02521392,-0.00605512,0.03520814,0.06753814,-0.00944186,-0.04703254,0.00479795,-0.00918041,0.01415687,-0.10202298,0.11141994,-0.00357669,0.04537069,-0.03985597,-0.03924368,0.04401362,0.03962822,0.00493916,-0.07466925,0.01848851,0.0198114,0.02502005,-0.03536172,0.0407793,-0.06055322,0.01179084,0.04416621,0.00567913,0.00909651,-0.00426456,-0.00553203,0.00102331,0.01460235,0.05819667,-0.0488321,-0.01816734,-0.00073756,0.00167957,0.06426146,0.02813677,0.02502619,0.02643111,0.05786294,-0.06196449,-0.00831298,-0.07457469,-0.02688655,-0.02476122,0.00186462,-0.0093717,-0.02423415,-0.03339061,-0.0782043,-0.02753118,-0.10152466,-0.07277143,0.06566535,-0.02329064,-0.00593653,0.01607849,-0.02776661,0.02501716,0.08547377,-0.03157219,-0.09549862,-0.00288863,-0.04410966,0.01404959,0.10604665,-0.04648347,-0.03797143,-0.02675854,-0.0608251,-0.02930233,0.12101276,0.0562021,-0.09522043,0.05999405,0.04858845,-0.01640892,-0.06132313,0.02167281,-0.02517701,-0.03125443,-0.00808601,0.06197194,-0.03716695,-0.04356037,-0.04542955,-0.00489797,0.03421841,0.01744395,-0.00853493,-0.03007619,0.01168702,0.00982953,-0.0381828,-0.03768837,0.01528971,0.03869114,-0.00075264,-0.08986585,0.03567361,0.01077122,0.06440935,-0.00399742,-0.00443536,-0.01306675,-0.01037224,0.02702736,-0.04314535,0.10952752,0.02236517,0.02738818,-0.0007432,-0.04465963,0.05316557,-0.04776875,-0.04507212,-0.00122385,0.0538009,-0.02320624,0.00959095,-0.02752034,0.02814139,-0.02434416,0.02355595,0.03516625,0.03250975,-0.06326262,0.03878983,0.03353833,0.01376007,-0.09201495,-0.20348413,-0.04396355,0.02918313,-0.04417224,0.06336892,-0.00591789,0.03988922,0.03235416,0.02673719,0.1082057,0.07903203,0.04959189,-0.04107412,-0.005112,-0.01885928,0.03567292,0.05856199,-0.01392769,0.01829367,0.02482932,0.00293083,0.0257911,0.00129845,-0.02425775,0.04651311,-0.05983945,0.10789638,-0.01274722,0.05056439,-0.00166717,0.06747819,0.01945716,-0.02194871,-0.13731751,0.02354457,0.0819659,-0.03136962,-0.03262487,-0.04497532,-0.0085385,0.0034259,0.02288085,0.03680373,-0.07945918,0.0353885,-0.0566842,-0.03026926,-0.02743093,-0.03705209,0.02803794,0.01106499,0.04547274,0.0259097,0.10982951,0.00582025,-0.01616143,-0.01415693,-0.09541466,0.0119136,0.03555039,-0.0138596,-0.00497005,-0.02565775,-0.02246697,0.00866015,0.00127718,-0.02585206,0.04579312,0.04675626,-0.01737137,-0.00618859,0.14238037,0.00176996,0.01500123,0.04278558,0.03924403,-0.05423594,-0.09054483,0.04071203,0.02990902,0.00810138,0.03103983,0.01989741,0.03461949,0.00916195,0.05266641,0.04520387,0.00522831,0.01436615,0.00372021,-0.01797322,-0.04566132,-0.05590946,-0.01852077,0.1105786,-0.02601606,-0.30202284,-0.04446492,-0.0283622,-0.03680869,0.06690548,0.0436646,0.03706844,0.02424219,-0.06066575,0.04692405,-0.00031608,0.03754001,-0.0071481,-0.06557913,0.01240031,-0.02432118,0.04104361,-0.00906368,0.02598732,0.03297634,0.03036392,0.0695819,0.20846005,-0.02931807,0.05175195,0.01211083,-0.02478752,0.05300874,-0.00334008,0.01842399,-0.01929467,0.01461108,0.02526863,-0.03243853,-0.00257897,0.04921307,-0.04612383,0.01952493,-0.01373974,0.00955329,-0.02765853,-0.00357048,-0.03294671,0.04854324,0.0975057,-0.01443765,-0.01172132,-0.08568716,0.03550068,0.04912107,-0.06056699,0.0109381,0.00918619,0.02388914,-0.00706334,0.04724292,0.02116543,-0.07562305,-0.07107893,-0.02716769,-0.01898788,0.02151762,-0.03941823,0.06280251,0.03618588],"last_embed":{"hash":"1mfhhhk","tokens":463}}},"text":null,"length":0,"last_read":{"hash":"1mfhhhk","at":1751426529899},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**7.2. Nockchain vs. RISC Zero**","lines":[239,246],"size":508,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1mfhhhk","at":1751426529899}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**7.2. Nockchain vs. RISC Zero**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03162955,-0.04243749,-0.06090787,-0.02992754,0.05620648,0.01549228,0.01343029,0.02483027,0.00724931,0.00185345,0.05822106,-0.04135393,0.03352579,0.03234982,0.00195649,-0.02293531,0.04174276,-0.00594919,-0.01709774,0.04412885,0.11558668,-0.09451994,0.00075987,-0.05075363,0.08647875,0.03850783,0.01808007,-0.04167183,-0.03432508,-0.19997452,0.01517548,-0.07130013,0.00655072,0.037638,-0.02949054,-0.05955741,0.00163943,0.0638062,-0.04919283,0.00294145,-0.01258384,-0.01187772,0.02908311,-0.00881827,0.01106122,-0.06917375,0.03026807,-0.07196979,0.03198971,-0.05933983,-0.07117935,-0.06762177,-0.01334498,0.01522871,-0.00168559,0.04811807,0.00525227,0.01484578,0.04712093,0.05633551,0.07696317,0.04985017,-0.21398644,0.0178076,0.06625805,0.00071694,-0.01962292,0.03843292,0.04305806,0.06111871,0.02782484,-0.02347015,-0.03885291,0.04556945,0.02387116,-0.01300963,0.02527057,0.02814074,-0.02201911,-0.04498766,-0.04722062,0.05711678,-0.02552847,-0.01087269,-0.03540646,0.01729648,-0.01942969,-0.05386143,-0.03134863,0.02510493,-0.00499404,0.03785325,0.06886987,-0.01233994,-0.04684698,0.00388435,-0.01063192,0.01579124,-0.09984017,0.11244276,-0.00140727,0.04545693,-0.03942627,-0.03838225,0.04484988,0.0391151,0.00301288,-0.072688,0.01801776,0.0214112,0.02596577,-0.03794606,0.03939766,-0.06025074,0.01166631,0.04416635,0.00252613,0.01050747,-0.00357459,-0.00378883,0.00148886,0.01623744,0.05773054,-0.05093367,-0.02028605,-0.00083759,0.00238115,0.06414821,0.02817038,0.0276458,0.02740209,0.05969222,-0.06132825,-0.00924008,-0.07431281,-0.02524789,-0.02392581,-0.00029089,-0.0087645,-0.02759006,-0.03266649,-0.07805811,-0.02860836,-0.10135344,-0.07310209,0.06584657,-0.023531,-0.00572924,0.01404357,-0.02670853,0.02631493,0.08381851,-0.03259087,-0.09444962,-0.00413015,-0.0434945,0.01359363,0.10382912,-0.04426601,-0.03821325,-0.02612859,-0.06160583,-0.03003322,0.12182852,0.05759941,-0.09404906,0.06016723,0.04907738,-0.01615751,-0.06110958,0.02006354,-0.02591672,-0.03171497,-0.0055057,0.06450143,-0.03838636,-0.04401657,-0.04300808,-0.00563314,0.03241442,0.0177122,-0.010406,-0.02815098,0.01154713,0.01004958,-0.03518484,-0.03985503,0.01481762,0.03613947,0.00060909,-0.0893397,0.03524669,0.00787785,0.06452751,-0.00510903,-0.00664088,-0.01370879,-0.00909723,0.02389674,-0.0413217,0.10935895,0.02275362,0.02830307,0.00072799,-0.04380796,0.0511478,-0.04945716,-0.0454197,-0.00340797,0.05223366,-0.02295657,0.0095258,-0.02729749,0.02949236,-0.02514666,0.02454244,0.03578934,0.03032044,-0.06354474,0.03768976,0.03564101,0.01431527,-0.0923112,-0.20284933,-0.04475676,0.02630086,-0.04528498,0.06353007,-0.00586268,0.03769714,0.03418761,0.02613796,0.10607935,0.08093223,0.04938807,-0.04238034,-0.00664728,-0.0184899,0.0367872,0.05730057,-0.01500054,0.01836848,0.02562047,0.00336757,0.02480591,-0.00022469,-0.02478853,0.04630137,-0.05681841,0.10834771,-0.01331603,0.04974028,-0.00066952,0.06584135,0.01898012,-0.02217279,-0.13687176,0.02312647,0.0816296,-0.0311526,-0.03634953,-0.04367538,-0.00970246,0.00286307,0.02270195,0.03508984,-0.07910842,0.03747734,-0.05510869,-0.02938746,-0.02789565,-0.03678382,0.02868204,0.01175341,0.04712619,0.02290663,0.11091091,0.00617182,-0.01672457,-0.01355988,-0.09867048,0.01300559,0.03638556,-0.01520756,-0.00541821,-0.02849072,-0.01877913,0.01019375,0.00093631,-0.02840682,0.04387162,0.04844949,-0.01955994,-0.00423881,0.14082482,0.00213915,0.01469166,0.04075302,0.04021352,-0.0547067,-0.09145845,0.04001578,0.03098918,0.0062587,0.03025937,0.01832383,0.03625893,0.00870017,0.05295679,0.04522164,0.00541898,0.01429129,0.0021269,-0.016082,-0.04505878,-0.05429893,-0.01569712,0.11068062,-0.02505576,-0.30187058,-0.04454805,-0.02904478,-0.0358263,0.06565083,0.04462561,0.03677354,0.02363707,-0.06086744,0.04642702,0.00138043,0.03784098,-0.00398806,-0.06361512,0.01505333,-0.02471984,0.03816191,-0.01111607,0.0253273,0.03374578,0.03013706,0.06969403,0.2107482,-0.02939893,0.05169224,0.01134682,-0.02475726,0.05624391,-0.00409135,0.02091694,-0.01759879,0.01579842,0.02240358,-0.03246523,-0.00194775,0.05013158,-0.04703727,0.01939943,-0.01188419,0.00971687,-0.02717106,-0.00339396,-0.03507559,0.04640433,0.09954736,-0.01355602,-0.0113306,-0.08489195,0.03674312,0.05046721,-0.06164472,0.01312825,0.00576188,0.02391703,-0.01012372,0.04460833,0.01981047,-0.0752827,-0.071624,-0.02892651,-0.01946612,0.02413238,-0.04019979,0.06252153,0.03675457],"last_embed":{"hash":"e3s0f9","tokens":460}}},"text":null,"length":0,"last_read":{"hash":"e3s0f9","at":1751426529914},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**7.2. Nockchain vs. RISC Zero**#{1}","lines":[241,246],"size":469,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"e3s0f9","at":1751426529914}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06520594,-0.03442286,-0.06193367,-0.0364214,0.01320787,0.00900367,-0.00613345,0.00472751,-0.00156281,-0.03621378,0.05528658,-0.04148025,0.06868266,0.0575867,0.03428672,-0.01387382,0.0213203,-0.03565868,-0.01288808,0.02209811,0.09726842,-0.0614169,0.01482504,-0.05918106,0.05459658,0.07975522,0.04087574,-0.03627769,-0.06305491,-0.19201796,0.01304883,-0.0500959,0.02056002,0.00874605,-0.0091235,-0.03195225,-0.02473764,0.07773975,-0.0749855,-0.01009784,-0.01054021,-0.00088796,-0.00649898,-0.01646505,0.01083072,-0.08979255,0.01061181,-0.04344362,-0.00990783,-0.06083713,-0.0116692,-0.05571492,0.01699954,0.02954213,0.00182319,0.0265644,-0.01438203,0.02904577,0.01547982,0.03631125,0.06755108,0.03185161,-0.19809471,0.00745337,0.08237417,0.02679683,-0.0354126,0.02473153,0.0377469,0.06343682,0.01422053,0.0172964,-0.00624108,0.06703674,0.02006342,0.00395061,0.04749923,0.00343608,-0.0186178,-0.04275846,-0.01463425,0.0725197,-0.02263929,0.02543456,-0.00136053,0.00743652,-0.00989104,-0.06139282,-0.01262517,0.01487317,0.00079536,0.03435925,0.06804103,-0.03019544,-0.05164102,-0.02152489,0.00689201,0.06941754,-0.13965003,0.09702924,-0.01134686,0.03429193,0.00049999,-0.07405644,0.05260194,0.00484796,-0.00943825,-0.0653353,0.03064178,0.02974055,0.00773732,-0.02648002,0.02781238,-0.02918401,0.02425052,0.00513707,-0.01727512,-0.00805182,-0.05029077,-0.00623393,0.00898333,0.0350253,0.0512154,-0.02698575,-0.01874832,-0.00926607,0.01446121,0.06535283,0.04702554,0.03163876,0.02359163,0.03885043,-0.05918283,0.01593306,-0.05530294,-0.06743848,-0.01409782,0.01813537,-0.00717978,-0.04952992,-0.03294008,-0.07502976,-0.01098402,-0.09765484,-0.06548824,0.06656632,-0.0072162,0.00257864,0.00850914,-0.05760769,0.02600294,0.07306913,-0.0231182,-0.09107619,-0.01333044,0.00044738,0.02639973,0.10506621,-0.05495182,-0.02504809,-0.00912804,-0.04339086,-0.05399075,0.14686161,0.04658169,-0.1214184,0.04047842,0.07112516,-0.01835984,-0.05896678,-0.00227964,-0.01612298,-0.03799583,-0.00294545,0.07313078,-0.06702343,-0.03819545,-0.01461627,-0.01390706,0.05020323,-0.02116653,-0.00020709,-0.0602559,-0.01278424,0.00200451,-0.0300762,-0.01357907,0.00960757,0.01219082,0.02911991,-0.12325178,0.02278822,0.01563566,-0.00060758,-0.03217098,0.01957528,-0.01368472,-0.00164141,0.03686607,-0.03483959,0.12091849,0.03015537,0.00074589,0.00754369,-0.05040875,0.02965977,-0.02596587,-0.03428463,0.01445933,0.02899171,-0.03356082,0.01696011,-0.01931252,0.03988274,-0.01356757,0.00133306,-0.01006151,0.03694134,-0.04972617,0.03279602,0.0211313,0.05258462,-0.08366192,-0.20299208,-0.03092899,0.02945766,-0.04531456,0.01832844,-0.00484377,0.03755204,0.01100051,0.0355046,0.08029396,0.10911444,0.06004308,-0.06508528,0.00668313,0.00252978,0.02893357,0.06419471,-0.0143416,0.01037224,0.01957285,-0.02693044,0.05171324,0.01242678,-0.00165092,0.04925537,-0.0241609,0.09176855,-0.02221275,0.00855946,0.00763185,0.07060238,0.02718334,-0.01630012,-0.11342083,0.01967065,0.08347759,-0.01696956,-0.01953278,-0.04576097,0.00213423,0.00948551,0.02610041,0.04382579,-0.10083783,0.02815891,-0.06701107,-0.03554445,-0.02344669,-0.04697488,0.04641445,0.01532986,0.0341558,0.0172604,0.12746359,0.03133928,0.02487382,0.00121648,-0.05296871,0.00889349,0.00895515,-0.01484091,0.00651021,-0.00235699,-0.01600075,0.01259717,-0.02237996,0.02029846,0.01959914,0.01892426,-0.04010216,-0.04447092,0.12859623,0.02351489,-0.00461012,0.01356024,0.02267198,-0.05496987,-0.10961185,0.05411387,-0.00917973,0.03901744,0.06099781,0.05286819,0.03963833,0.00295936,0.05514122,0.03984813,-0.00125656,0.04343574,-0.0096394,-0.01550113,-0.08337352,-0.04182779,0.00363806,0.0844757,0.01472309,-0.30540329,-0.04026401,-0.03070397,-0.01864255,0.02478142,0.04375914,0.05605081,0.02401802,-0.05831477,0.0256766,-0.00634837,0.03439953,-0.01707899,-0.09730428,0.02661736,-0.00749669,0.02772441,-0.00507315,0.01697225,0.02924491,-0.03103524,0.05966112,0.23739707,0.01481726,0.04126543,0.01925218,-0.02104704,0.04948958,0.00711365,-0.00384959,-0.00914905,-0.01813149,0.03253759,-0.01234843,0.01976814,0.04551952,-0.01730063,-0.01979387,-0.01754553,0.01918999,-0.01882279,0.00702704,-0.02462764,0.02781601,0.12624054,-0.03004594,-0.02918198,-0.06068344,0.01206876,0.04889058,-0.04711517,-0.01374072,0.02534403,0.03394224,-0.03207849,0.06488315,0.0560243,-0.03607602,-0.05754268,-0.05245819,-0.00614181,0.02401078,-0.05022889,0.06897562,0.00150912],"last_embed":{"hash":"14lh4z0","tokens":431}}},"text":null,"length":0,"last_read":{"hash":"14lh4z0","at":1751426529924},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**","lines":[247,353],"size":16395,"outlinks":[{"title":"https://github.com/zorp-corp/nockchain","target":"https://github.com/zorp-corp/nockchain","line":47},{"title":"https://github.com/0xmoei/nockchain","target":"https://github.com/0xmoei/nockchain","line":48},{"title":"https://www.nockchain.org/","target":"https://www.nockchain.org/","line":49},{"title":"https://icoanalytics.org/projects/zorp-nockchain/","target":"https://icoanalytics.org/projects/zorp-nockchain/","line":50},{"title":"https://urbit.org/overview/history","target":"https://urbit.org/overview/history","line":51},{"title":"https://github.com/zorp-corp/jock-lang","target":"https://github.com/zorp-corp/jock-lang","line":52},{"title":"https://www.globenewswire.com/news-release/2024/12/30/3002639/0/en/Alpha-Sigma-Capital-Research-Unveils-2025-Crypto-Market-Predictions-AI-Agents-Staking-ETFs-and-Decentralized-Infrastructure-Lead-the-Way.html","target":"https://www.globenewswire.com/news-release/2024/12/30/3002639/0/en/Alpha-Sigma-Capital-Research-Unveils-2025-Crypto-Market-Predictions-AI-Agents-Staking-ETFs-and-Decentralized-Infrastructure-Lead-the-Way.html","line":53},{"title":"https://crypto-fundraising.info/projects/zorp-nockchain/","target":"https://crypto-fundraising.info/projects/zorp-nockchain/","line":54},{"title":"https://members.delphidigital.io/feed/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","target":"https://members.delphidigital.io/feed/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","line":55},{"title":"https://podwise.ai/dashboard/episodes/4062914","target":"https://podwise.ai/dashboard/episodes/4062914","line":56},{"title":"https://player.fm/series/the-delphi-podcast/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","target":"https://player.fm/series/the-delphi-podcast/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","line":57},{"title":"https://tracxn.com/d/companies/zorp/\\_\\_VTLVrft6NMSWw6R9NmmNgOwSmC1kn-XXhp2K\\_MTFXxM","target":"https://tracxn.com/d/companies/zorp/__VTLVrft6NMSWw6R9NmmNgOwSmC1kn-XXhp2K_MTFXxM","line":58},{"title":"https://www.zorp.one/","target":"https://www.zorp.one/","line":59},{"title":"https://www.zorp.one/about","target":"https://www.zorp.one/about","line":60},{"title":"https://en.wikipedia.org/wiki/End\\_of\\_the\\_World\\_(Parks\\_and\\_Recreation)","target":"https://en.wikipedia.org/wiki/End_of_the_World_\\(Parks_and_Recreation\\","line":61},{"title":"https://podcasts.apple.com/gr/podcast/the-delphi-podcast/id1438148082?l=el","target":"https://podcasts.apple.com/gr/podcast/the-delphi-podcast/id1438148082?l=el","line":62},{"title":"https://www.chaincatcher.com/en/article/2181453","target":"https://www.chaincatcher.com/en/article/2181453","line":63},{"title":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","target":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","line":64},{"title":"https://cryptorank.io/ico/nockchain","target":"https://cryptorank.io/ico/nockchain","line":65},{"title":"https://cryptorank.io/price/nockchain","target":"https://cryptorank.io/price/nockchain","line":66},{"title":"https://github.com/zorp-corp/nockapp","target":"https://github.com/zorp-corp/nockapp","line":67},{"title":"https://www.everand.com/podcast/864012011/Logan-Allen-Nockchain-s-Global-Competition-for-Useful-ZK-Proof-of-Work-Miners-Kicks-Off","target":"https://www.everand.com/podcast/864012011/Logan-Allen-Nockchain-s-Global-Competition-for-Useful-ZK-Proof-of-Work-Miners-Kicks-Off","line":68},{"title":"https://www.nervos.org/knowledge-base/what\\_are\\_blockchain\\_intents\\_(explainCKBot)","target":"https://www.nervos.org/knowledge-base/what_are_blockchain_intents_\\(explainCKBot\\","line":69},{"title":"https://volet.com/blog/post/understanding-intents-in-blockchain-a-comprehensive-guide-to-intents-01jdpzq69t52gfwxy6z0bx1nmv","target":"https://volet.com/blog/post/understanding-intents-in-blockchain-a-comprehensive-guide-to-intents-01jdpzq69t52gfwxy6z0bx1nmv","line":70},{"title":"https://crypto.com/en/university/intent-centric-design-for-blockchain","target":"https://crypto.com/en/university/intent-centric-design-for-blockchain","line":71},{"title":"https://www.bitget.com/zh-TC/news/detail/12560604756213","target":"https://www.bitget.com/zh-TC/news/detail/12560604756213","line":72},{"title":"https://www.youtube.com/watch?v=G5tE0LFFiTY","target":"https://www.youtube.com/watch?v=G5tE0LFFiTY","line":73},{"title":"https://ethplorer.io/address/******************************************","target":"https://ethplorer.io/address/******************************************","line":74},{"title":"https://phantom.com/tokens/solana/6tCEUogw2uk4VQz5JKb295ixFrj6vAPEQbZqL9VfFXSz","target":"https://phantom.com/tokens/solana/6tCEUogw2uk4VQz5JKb295ixFrj6vAPEQbZqL9VfFXSz","line":75},{"title":"https://www.dextools.io/app/en/solana/pair-explorer/ENDyunqABSwMvfUoRxsNA1N2QLhXeZQTYrnPZUQdnygP","target":"https://www.dextools.io/app/en/solana/pair-explorer/ENDyunqABSwMvfUoRxsNA1N2QLhXeZQTYrnPZUQdnygP","line":76},{"title":"https://web3.bitget.com/en/wiki/nock-wallet","target":"https://web3.bitget.com/en/wiki/nock-wallet","line":77},{"title":"https://m.sosovalue.com/blog/comprehensive-guide-to-bitcoin-mining","target":"https://m.sosovalue.com/blog/comprehensive-guide-to-bitcoin-mining","line":78},{"title":"https://www.cryptominerbros.com/blog/calculate-crypto-mining-profits/","target":"https://www.cryptominerbros.com/blog/calculate-crypto-mining-profits/","line":79},{"title":"https://asicmarketplace.com/blog/how-to-calculate-profit-in-crypto-mining/","target":"https://asicmarketplace.com/blog/how-to-calculate-profit-in-crypto-mining/","line":80},{"title":"https://www.rootdata.com/Projects/detail/Nockpool?k=MTc2Mzg%3D","target":"https://www.rootdata.com/Projects/detail/Nockpool?k=MTc2Mzg%3D","line":81},{"title":"https://en.wikipedia.org/wiki/Mining\\_pool","target":"https://en.wikipedia.org/wiki/Mining_pool","line":82},{"title":"https://podcasts.apple.com/us/podcast/the-delphi-podcast/id1438148082","target":"https://podcasts.apple.com/us/podcast/the-delphi-podcast/id1438148082","line":83},{"title":"https://icodrops.com/nockchain/","target":"https://icodrops.com/nockchain/","line":84},{"title":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA=","target":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","line":85},{"title":"https://github.com/0xmoei/nockchain/security","target":"https://github.com/0xmoei/nockchain/security","line":86},{"title":"https://dysnix.com/blockchain-security-audit","target":"https://dysnix.com/blockchain-security-audit","line":87},{"title":"https://www.lcx.com/how-to-conduct-blockchain-security-audit/","target":"https://www.lcx.com/how-to-conduct-blockchain-security-audit/","line":88},{"title":"https://veridise.com/audits/nft-security/","target":"https://veridise.com/audits/nft-security/","line":89},{"title":"https://immunebytes.com/blog/blockchain-security-audit-the-ultimate-guide/","target":"https://immunebytes.com/blog/blockchain-security-audit-the-ultimate-guide/","line":90},{"title":"https://hacken.io/services/blockchain-security/blockchain-protocol-security/","target":"https://hacken.io/services/blockchain-security/blockchain-protocol-security/","line":91},{"title":"https://quantstamp.com/audits","target":"https://quantstamp.com/audits","line":92},{"title":"https://www.reddit.com/r/CryptoCurrency/comments/1lhoxfc/outside\\_of\\_the\\_crypto\\_community\\_crypto\\_is\\_still/","target":"https://www.reddit.com/r/CryptoCurrency/comments/1lhoxfc/outside_of_the_crypto_community_crypto_is_still/","line":93},{"title":"https://www.reddit.com/r/CryptoMarkets/comments/1l5jqcu/which\\_crypto\\_community\\_still\\_feels\\_genuinely/","target":"https://www.reddit.com/r/CryptoMarkets/comments/1l5jqcu/which_crypto_community_still_feels_genuinely/","line":94},{"title":"https://www.reddit.com/r/CryptoMarkets/comments/1jakyzw/why\\_community\\_sentiment\\_of\\_every\\_crypto\\_on/","target":"https://www.reddit.com/r/CryptoMarkets/comments/1jakyzw/why_community_sentiment_of_every_crypto_on/","line":95},{"title":"https://www.everand.com/podcast/593966517/Launching-the-Mars-Review-of-Books-with-Noah-Kumin-Noah-Kumin-is-the-founder-and-editor-of-the-Mars-Review-of-Books","target":"https://www.everand.com/podcast/593966517/Launching-the-Mars-Review-of-Books-with-Noah-Kumin-Noah-Kumin-is-the-founder-and-editor-of-the-Mars-Review-of-Books","line":96},{"title":"https://podcasts.apple.com/bt/podcast/urbit-nockchain-and-the-current-state/id1195362330?i=1000709342627","target":"https://podcasts.apple.com/bt/podcast/urbit-nockchain-and-the-current-state/id1195362330?i=1000709342627","line":97},{"title":"https://www.nansen.ai/post/best-telegram-channels-for-crypto-chats","target":"https://www.nansen.ai/post/best-telegram-channels-for-crypto-chats","line":98},{"title":"https://ryanschultz.com/2018/07/17/the-telegram-discussion-groups-for-the-various-blockchain-based-vr-projects-have-been-endlessly-entertaining/","target":"https://ryanschultz.com/2018/07/17/the-telegram-discussion-groups-for-the-various-blockchain-based-vr-projects-have-been-endlessly-entertaining/","line":99},{"title":"https://www.reddit.com/r/CryptoCurrency/comments/lz1f54/beware\\_of\\_fake\\_pump\\_coins\\_groups\\_on\\_telegram/","target":"https://www.reddit.com/r/CryptoCurrency/comments/lz1f54/beware_of_fake_pump_coins_groups_on_telegram/","line":100},{"title":"https://asicmarketplace.com/blog/what-is-aleo/","target":"https://asicmarketplace.com/blog/what-is-aleo/","line":101},{"title":"https://risczero.com/blog/zkvm","target":"https://risczero.com/blog/zkvm","line":102},{"title":"https://www.rootdata.com/Projects/detail/RISC%20Zero?k=MjgwMw%3D%3D","target":"https://www.rootdata.com/Projects/detail/RISC%20Zero?k=MjgwMw%3D%3D","line":103},{"title":"https://en.foresightnews.pro/an-overview-of-privacy-solutions-based-on-zero-knowlege-cryptography/","target":"https://en.foresightnews.pro/an-overview-of-privacy-solutions-based-on-zero-knowlege-cryptography/","line":104},{"title":"https://equilibrium.co/writing/privacy-blockchains-and-aleo-deep-dive","target":"https://equilibrium.co/writing/privacy-blockchains-and-aleo-deep-dive","line":105},{"title":"https://www.blockchaincoinvestors.com/blog/risczero-eliminating-tradeoffs-and-unleashing-the-power-of-blockchain-technology-254ec","target":"https://www.blockchaincoinvestors.com/blog/risczero-eliminating-tradeoffs-and-unleashing-the-power-of-blockchain-technology-254ec","line":106},{"title":"https://podcasts.musixmatch.com/podcast/the-delphi-podcast-01hpa65hx48s2p8649mhbr8ke5/episode/logan-allen-nockchains-global-competition-for-useful-01jvmp9agdsppxbp1n6gwtz1ck","target":"https://podcasts.musixmatch.com/podcast/the-delphi-podcast-01hpa65hx48s2p8649mhbr8ke5/episode/logan-allen-nockchains-global-competition-for-useful-01jvmp9agdsppxbp1n6gwtz1ck","line":107}],"class_name":"SmartBlock","last_embed":{"hash":"14lh4z0","at":1751426529924}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.1. 综合分析 (SWOT)**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07066214,-0.0323838,-0.06883688,-0.0378009,0.01499631,0.01353527,-0.00619841,0.00844614,-0.01371508,-0.03830825,0.06188541,-0.04126932,0.07989703,0.05159778,0.04812188,-0.00706268,0.01697747,-0.04580889,-0.02628883,0.02016912,0.09478544,-0.06108624,0.02720727,-0.05530169,0.05181692,0.08257592,0.04110525,-0.03603799,-0.06482954,-0.1945384,0.01010302,-0.05637553,0.01847327,0.00817523,-0.00042506,-0.04107385,-0.02295757,0.0726561,-0.07846056,-0.00594731,-0.00756558,-0.00936062,-0.00871562,-0.0119104,0.00706723,-0.08104171,0.01562509,-0.04821476,-0.00686865,-0.06055287,-0.01322418,-0.05429994,0.01843798,0.02943898,0.00778083,0.02425197,-0.00904018,0.03436366,0.01382093,0.03271958,0.0635697,0.03030948,-0.19785088,0.00667334,0.08626422,0.03191878,-0.03766429,0.00855727,0.03613427,0.06024167,0.01277328,0.01577645,-0.00393247,0.0673522,0.02542945,0.00289605,0.03931929,0.00387782,-0.01774657,-0.04934706,-0.02038719,0.07111888,-0.01311346,0.01999966,0.00190748,0.01161743,-0.00636309,-0.06255776,-0.00506176,0.00784758,-0.01051558,0.03523608,0.06369958,-0.0308026,-0.04929237,-0.03127723,0.0051458,0.06676868,-0.13877694,0.10158407,-0.0059306,0.03973178,-0.00316424,-0.07105201,0.05795251,-0.00113887,-0.00609704,-0.06810943,0.0340856,0.03028207,0.01273715,-0.02343478,0.02505683,-0.03094334,0.02599109,0.01146394,-0.00825517,-0.0037815,-0.04432863,-0.00878074,0.00882008,0.03878709,0.04918617,-0.02364488,-0.00595027,-0.00972098,0.01733247,0.06391411,0.05477152,0.0265143,0.01985852,0.04047776,-0.05617603,0.01166026,-0.05133898,-0.06386249,-0.01553866,0.01631575,-0.01170216,-0.04796358,-0.03645454,-0.07331164,-0.01390774,-0.10424639,-0.05639624,0.0680121,-0.00919134,0.00384607,0.0079014,-0.0631863,0.02967894,0.0691809,-0.02202319,-0.09153026,-0.01174428,0.00846392,0.0260601,0.10533069,-0.06313021,-0.02972793,-0.00701611,-0.04571455,-0.04975675,0.14374447,0.03393262,-0.12236325,0.03859036,0.06630903,-0.01862225,-0.05684435,-0.00775226,-0.00961487,-0.03576198,-0.00553797,0.07013083,-0.06564485,-0.0361056,-0.01352547,-0.01421655,0.05232497,-0.01907012,-0.00530253,-0.05417258,-0.01553712,0.00019803,-0.03241163,-0.0092333,0.01326206,0.00759388,0.03459192,-0.12078516,0.02200248,0.02101116,0.00402467,-0.03033712,0.01770901,-0.01130207,0.00522057,0.03290039,-0.03368227,0.11767666,0.0388115,0.0039402,0.00246679,-0.05412519,0.02409315,-0.03685736,-0.02946267,0.02481663,0.0253121,-0.03859441,0.02149393,-0.02336203,0.04159381,-0.01330281,-0.00400889,-0.01722874,0.04729992,-0.04040395,0.03169812,0.01973795,0.0550603,-0.08931973,-0.20482865,-0.02168105,0.04255902,-0.04680334,0.01994867,-0.01349107,0.03701162,0.00672724,0.03873166,0.06868956,0.11006648,0.06900556,-0.05813704,0.00241406,0.00292133,0.02926513,0.06152952,-0.01659565,0.00218144,0.02383336,-0.03218468,0.05049484,0.01047915,-0.00943048,0.05284116,-0.01329261,0.09404325,-0.02743606,0.01063973,0.00127558,0.06027601,0.02635464,-0.00994406,-0.11354882,0.02208419,0.09320682,-0.00737222,-0.01132977,-0.04526847,-0.00390394,0.00614034,0.02354612,0.04450689,-0.09327644,0.02676427,-0.06564096,-0.03349242,-0.02685647,-0.04272522,0.051385,0.02657598,0.03522653,0.01909484,0.12582137,0.03110931,0.03089798,0.00187967,-0.04745611,0.00684415,0.0074817,-0.01007013,-0.00219711,-0.00579983,-0.01648359,0.01887752,-0.03148896,0.0192655,0.02396934,0.01590404,-0.04809228,-0.05115338,0.13456547,0.02748254,-0.00265301,0.00858469,0.02187095,-0.05417847,-0.10422905,0.05303949,-0.01692544,0.04303926,0.0645566,0.05293715,0.04414345,-0.00257094,0.05905178,0.02981467,0.00206913,0.03914215,-0.00606672,-0.00934706,-0.0792727,-0.03496047,-0.01282462,0.07790239,0.01908689,-0.30145878,-0.03365507,-0.02117322,-0.02106033,0.02596907,0.03866565,0.05251233,0.02444811,-0.05662304,0.03109942,-0.00672831,0.0377373,-0.01488046,-0.0879551,0.03404613,-0.01670725,0.03004549,-0.006621,0.01375621,0.03090908,-0.03992093,0.05038335,0.23927674,0.01434368,0.03771928,0.02982086,-0.02503114,0.04673607,0.00499018,-0.0128044,-0.00915737,-0.01478957,0.02560501,-0.00888434,0.02088639,0.0430504,-0.00947078,-0.03141576,-0.01619209,0.01845275,-0.02099016,0.00794699,-0.01334402,0.02213831,0.13126199,-0.02429399,-0.03306743,-0.05695595,0.00704014,0.0389405,-0.05560462,-0.02028227,0.03336107,0.03535271,-0.03046183,0.06589124,0.05407935,-0.03478338,-0.06156144,-0.05503732,-0.00731666,0.02079516,-0.05724921,0.05913323,0.00118299],"last_embed":{"hash":"1p891qe","tokens":462}}},"text":null,"length":0,"last_read":{"hash":"1p891qe","at":1751426529944},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.1. 综合分析 (SWOT)**","lines":[249,270],"size":599,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1p891qe","at":1751426529944}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.2. 未来路线图与关键里程碑**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05548455,-0.01107189,-0.07442107,-0.04405372,0.01491638,0.02014594,0.00541278,-0.00160486,-0.01183063,-0.02177545,0.07454416,-0.03311894,0.02747707,0.03384483,0.02533898,-0.01705954,0.01531911,-0.05314834,-0.02460331,0.00701019,0.10885907,-0.0629894,0.03824457,-0.00963673,0.07086131,0.06206701,0.00067072,-0.00272153,-0.03654202,-0.17985687,-0.00201766,-0.06350297,0.00206899,0.02096565,0.0201137,-0.03892401,-0.01714799,0.06925485,-0.05116575,-0.00673337,0.00301402,0.02187471,0.00791266,0.02027637,0.03749315,-0.08340131,0.0373783,-0.05061522,-0.00542288,-0.04542558,-0.02228969,-0.08130549,-0.0115944,0.03111481,-0.00062501,0.01702613,0.00594729,0.05630779,0.03008133,0.05064849,0.08646208,0.05001092,-0.17753258,0.01006795,0.09200744,0.01056911,-0.04791997,0.02484543,0.0483014,0.07318044,0.02082871,0.00302749,-0.00844026,0.05590814,0.01686048,-0.00443954,0.02822662,0.02671365,-0.02360716,-0.0613872,-0.05693746,0.07948006,0.02382258,-0.0012628,-0.00892836,-0.02380989,-0.00468952,-0.02979187,-0.03554213,0.02104101,0.00473724,0.02351742,0.05389876,-0.00834212,-0.04484102,-0.00621424,-0.02931571,0.02343863,-0.11613821,0.11817347,-0.03608096,0.03968811,-0.00692678,-0.0576761,0.03091707,0.00037343,-0.00661783,-0.06826556,0.02260307,0.02664583,0.01300907,0.00967635,0.06367955,-0.0482957,0.03466886,0.01021144,-0.00551157,-0.00184671,-0.03570898,0.02890802,-0.01207949,0.02872641,0.04178765,-0.02693707,-0.02644511,-0.02053416,0.01903515,0.03387941,0.03625323,0.03432647,0.02008753,0.03319306,-0.07908588,0.00386429,-0.0396699,-0.04914843,-0.02165221,0.0240832,-0.03096312,-0.04395574,-0.02627094,-0.09559587,-0.04509453,-0.10665305,-0.05950869,0.07034142,-0.01266817,0.0061806,-0.00999684,-0.04278643,-0.0042863,0.07807553,-0.03698601,-0.07319865,-0.02156324,-0.00614345,0.01779663,0.08441927,-0.05332233,0.01020217,-0.01818964,-0.03518206,-0.02986167,0.13776709,0.03220716,-0.12428083,0.0458076,0.05620759,-0.01075218,-0.05545287,0.00445644,0.03122108,-0.06328747,-0.01749977,0.06788318,-0.05111463,-0.02647831,-0.02492772,0.02314913,0.0493236,0.01485543,-0.01780696,-0.08947594,-0.03300586,-0.02812046,-0.01666405,-0.02404577,0.03198253,0.03784429,0.02843107,-0.09191588,0.02874064,0.00484376,0.02535034,-0.02897114,-0.01016063,-0.00403412,-0.01726023,0.02244244,-0.07659788,0.16288693,0.04072163,0.01767249,0.02227348,-0.05544038,0.01155055,-0.04414268,-0.05421584,-0.00677094,0.02923536,-0.02955657,0.03204636,-0.01871886,0.05427194,-0.0341438,0.02600229,-0.00917381,0.05659222,-0.03051078,0.04368072,0.02659634,0.01907698,-0.08764742,-0.18599622,-0.02142371,0.06695642,-0.01929311,0.03359181,0.01524678,0.0485696,0.01425962,0.05244005,0.09135418,0.08561633,0.05653182,-0.05479524,0.00460139,0.00579432,0.05670031,0.05386425,-0.00864271,0.00242335,0.03792456,-0.03860434,0.0159115,0.01181952,-0.05678254,0.04706944,-0.05808674,0.09876937,-0.02222772,0.00943609,0.00718147,0.07083185,0.00861824,-0.0412605,-0.14399295,0.02112236,0.07122605,-0.01053825,-0.00832209,-0.05032779,-0.02446501,0.00322337,0.01358037,0.05898388,-0.10691284,0.03113791,-0.07344959,-0.03469518,-0.02402053,0.0017905,0.03637693,-0.00657988,0.03630113,0.01618014,0.12332442,-0.00519994,-0.00137409,0.03057496,-0.04871065,-0.00897529,0.03581932,-0.00133618,-0.01321922,-0.00010657,-0.00768217,0.01595135,-0.00795727,-0.00204794,0.00707181,0.05445999,-0.02920235,-0.03828732,0.14608404,0.02027525,-0.00680774,0.04918207,0.0349508,-0.06714205,-0.11917263,0.07460203,-0.00590048,0.00885924,0.02373382,0.04133384,0.04617843,-0.01495653,0.0715624,0.01103939,-0.00881736,0.02163979,-0.0111814,-0.0011001,-0.04577459,-0.05571277,-0.02327976,0.0471782,0.00609506,-0.29942456,-0.04273306,-0.01099031,-0.01203524,0.06511269,0.02863965,0.05440151,0.04845659,-0.0637727,0.02513985,-0.01015624,0.03132355,-0.00596951,-0.05924728,0.01611259,-0.00406834,0.04063619,0.00176535,0.01782557,0.0428106,0.00573699,0.06741796,0.22645813,-0.0153794,0.04704482,0.01873824,-0.05733868,0.04037231,0.05647985,0.00390696,-0.04440293,-0.01736677,0.0236435,-0.03145273,0.03834693,0.02423638,-0.0060477,-0.02766166,-0.02826676,0.01039341,-0.04469938,-0.00566519,-0.01661617,0.02331823,0.10561793,-0.00920577,-0.04594706,-0.06540284,0.00519246,0.04546693,-0.04974553,-0.02371035,0.01767676,0.03304309,0.00267153,0.07391081,0.03209313,-0.04226717,-0.06873094,-0.00858765,-0.0399466,-0.02571563,-0.03754823,0.05955613,0.01764786],"last_embed":{"hash":"jep9ez","tokens":310}}},"text":null,"length":0,"last_read":{"hash":"jep9ez","at":1751426529958},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.2. 未来路线图与关键里程碑**","lines":[271,281],"size":300,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"jep9ez","at":1751426529958}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.3. 针对用户的建议**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04539289,-0.0194808,-0.0626855,-0.03229629,0.03442689,0.01811347,0.00011948,0.00735212,0.00134622,-0.01169067,0.06340712,-0.03488822,0.02249925,0.03581339,0.02613796,-0.02212542,0.02246758,-0.04634035,-0.00783507,0.02544465,0.09966338,-0.07310587,0.04511927,0.00011527,0.07489186,0.05678695,0.01672756,-0.01432353,-0.03666043,-0.18115723,0.00419336,-0.06496146,-0.01198542,0.01895534,0.02081146,-0.04895217,-0.02637292,0.05881267,-0.0496285,-0.00008633,-0.00104774,0.01587079,0.00370714,-0.00333632,0.04399175,-0.0769322,0.03314125,-0.05716778,-0.00228602,-0.06028797,-0.03272428,-0.09394376,-0.00772149,0.03022625,0.00185644,0.0119094,-0.00386404,0.03589651,0.02915109,0.03809384,0.08451125,0.05012304,-0.19168928,0.01290101,0.09142911,0.00872224,-0.04381325,0.03229482,0.04134422,0.07608128,0.01593444,-0.00459112,-0.02609623,0.05304687,0.02404216,-0.00420611,0.04171113,0.03405201,-0.01834641,-0.05900721,-0.06053455,0.09013687,0.00241296,-0.01540708,-0.01242766,-0.03876428,-0.00901775,-0.0471424,-0.03801761,0.02001511,0.00058674,0.02596582,0.04814906,-0.0100782,-0.01988882,-0.00502519,-0.01296364,0.02235259,-0.09986518,0.12124051,-0.03994311,0.04337585,0.00341335,-0.06362786,0.03724099,0.00653007,-0.00481611,-0.06358612,0.03025551,0.03447217,0.0094601,-0.00079534,0.06964392,-0.04427971,0.03556977,0.01168141,-0.00380009,-0.0043932,-0.01948768,0.0195607,-0.01384942,0.03350294,0.04078108,-0.03140184,-0.02689088,-0.0245752,0.01294894,0.04707134,0.05166109,0.0333301,0.02452976,0.02830457,-0.07518087,0.00545203,-0.06269616,-0.04713787,-0.00424729,0.0081103,-0.02740568,-0.03845453,-0.03522079,-0.09709377,-0.03244642,-0.11169403,-0.06907908,0.08139603,-0.01283108,-0.00218684,0.01066816,-0.05361789,0.01305738,0.08643675,-0.0370522,-0.08944569,-0.02825273,0.0090172,0.02260057,0.09741109,-0.04862842,0.00025821,-0.00652012,-0.03483223,-0.031675,0.12116946,0.03524911,-0.10672723,0.04255423,0.0339005,-0.01219761,-0.06530888,0.00634843,0.02650056,-0.05522356,-0.00941553,0.07670517,-0.05116896,-0.02791602,-0.02008428,0.01436887,0.04868302,0.00984282,-0.00851055,-0.08546747,-0.0130066,-0.01831152,-0.01139475,-0.02544735,0.04160428,0.03065315,0.03908978,-0.08174038,0.04910047,0.00379567,0.02828253,-0.0278724,0.00059097,-0.00711529,-0.01729894,0.01339139,-0.06489193,0.14903262,0.03763261,0.01658284,0.01024667,-0.06252664,0.01278929,-0.03514196,-0.05109702,0.00006296,0.02543,-0.0187836,0.02045871,-0.03118023,0.04416729,-0.01886642,0.00678752,-0.00279383,0.04466692,-0.0311949,0.04884798,0.02925083,0.02608943,-0.08942883,-0.1792924,-0.03475374,0.05748164,-0.01322422,0.02081921,0.01721655,0.04703982,0.03002244,0.0444458,0.10858703,0.09221852,0.0619287,-0.04912803,-0.00212584,-0.00656394,0.05137148,0.0670116,-0.00646624,-0.00448419,0.03271949,-0.04699742,0.01183342,0.02331752,-0.04946537,0.05649286,-0.06134223,0.09065148,-0.02752331,-0.00450454,-0.00051659,0.08206804,0.01448523,-0.04069468,-0.14900647,0.02862149,0.08689673,-0.01076339,-0.02031128,-0.05914218,-0.02223542,-0.00887029,0.00576215,0.05467488,-0.09089337,0.02094765,-0.06514597,-0.03833683,-0.03592186,-0.01512197,0.03614732,-0.00288831,0.02566505,0.0234007,0.11582052,-0.01227863,-0.00527695,0.01828059,-0.05575796,-0.00461937,0.02655798,-0.01383472,-0.01576193,-0.0140099,-0.00588192,0.02499073,-0.01264263,-0.01018723,0.02826539,0.03622035,-0.01695915,-0.03731677,0.14896165,0.02104837,0.00664642,0.04582426,0.02691212,-0.05084963,-0.09473484,0.06448758,-0.01297695,0.02145597,0.00638925,0.03090719,0.05088266,-0.00190781,0.0584249,0.01667128,-0.01889077,0.02573541,0.0096322,-0.00215091,-0.05889508,-0.0470872,-0.02922782,0.06647044,0.02150681,-0.29214463,-0.04143792,-0.02626445,-0.01638812,0.07968302,0.0362271,0.05421258,0.04260124,-0.05297852,0.01998887,0.00615528,0.02927536,-0.020771,-0.05797473,0.00713937,-0.01245774,0.04725094,-0.0048687,0.02460334,0.05641095,0.00579202,0.07620259,0.22830823,-0.01289527,0.05097661,0.02350604,-0.05263997,0.04559203,0.02676851,-0.00033763,-0.0425933,-0.01350284,0.03143733,-0.02661772,0.03073034,0.03708775,-0.00887409,-0.01641885,-0.01089945,0.0133315,-0.04769075,-0.00770307,-0.01942174,0.0294954,0.11679851,-0.00870604,-0.05109956,-0.083405,-0.00141478,0.04786558,-0.05762261,-0.03155662,0.01955899,0.0325521,-0.00885655,0.0711564,0.03571001,-0.05157107,-0.07290433,-0.0159231,-0.0418087,-0.02275761,-0.05822436,0.06980094,0.00728101],"last_embed":{"hash":"2782l8","tokens":429}}},"text":null,"length":0,"last_read":{"hash":"2782l8","at":1751426529969},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.3. 针对用户的建议**","lines":[282,290],"size":442,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"2782l8","at":1751426529969}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06420559,-0.03985149,-0.05906019,-0.03248769,0.00925502,0.01184426,-0.03165837,0.01947819,-0.01086645,-0.02227749,0.06163267,-0.04247738,0.02478788,0.03855326,0.02634068,-0.03889735,-0.00623159,-0.0423132,0.00483105,0.00892929,0.12433611,-0.11420768,0.02586809,-0.02541086,0.07428194,0.05165206,0.01563833,-0.01158003,-0.02808902,-0.19595154,0.01342248,-0.04447961,-0.00255907,0.01424067,0.02239709,-0.02825769,-0.02778758,0.0489412,-0.05042665,0.01807431,0.00823267,0.00737899,0.001535,0.00870866,0.06895405,-0.09433578,0.0047826,-0.05566904,-0.00242326,-0.02362469,-0.00525491,-0.04876393,-0.01129006,0.02001655,-0.00104595,0.00958152,0.02046568,0.04769424,0.04480131,0.03534604,0.05806744,0.06765713,-0.20459101,0.01515948,0.09231302,0.02327121,-0.05995092,0.02163068,0.04878377,0.07597113,0.01603249,0.00921698,-0.00533719,0.03738554,0.01049015,-0.02225015,0.03019997,0.00603276,-0.02877044,-0.06616587,-0.04347771,0.08912253,-0.00714973,-0.00735324,0.01453307,-0.02649418,0.0130818,-0.02487308,0.00803519,0.00893745,-0.00270351,0.00318754,0.07304908,0.01093712,-0.06296673,0.02004507,-0.00598139,0.02525212,-0.08131921,0.10921872,-0.02947717,0.04856197,0.02143437,-0.05180042,0.03578663,-0.01510057,-0.0219012,-0.04856827,0.02967271,0.03867074,-0.0245096,0.01354683,0.06948821,-0.05267507,0.03027078,-0.01018835,-0.00829394,-0.00622925,-0.00520823,0.01903711,0.01115403,0.04830369,0.05998466,-0.00185807,-0.01366846,-0.01640925,0.00750661,0.04073996,0.05317287,0.03588309,0.03196512,0.0258375,-0.06670643,0.00853286,-0.03871333,-0.02382148,-0.00094241,-0.00604206,-0.0141175,-0.0305007,-0.02916393,-0.09617691,-0.02245409,-0.07914664,-0.06310961,0.09544607,0.00487135,-0.00132926,0.01103101,-0.0504883,0.00586327,0.08353598,-0.04607036,-0.07679897,0.00439949,0.02481172,0.04917887,0.09175406,-0.05384376,0.00429832,-0.02319081,-0.03149327,-0.04322246,0.14186317,0.03806112,-0.12423908,0.02035434,0.03579595,-0.00696513,-0.08790283,-0.00403271,0.00592449,-0.03849304,-0.02154975,0.07887937,-0.04881885,-0.03562852,-0.05079094,0.02372582,0.05108564,-0.01308834,-0.00793653,-0.08375075,0.00927371,-0.04136652,-0.01148069,-0.04166872,0.0298716,0.01966101,0.04815799,-0.08282113,0.04385466,-0.02159582,0.01343245,-0.03016105,-0.0056552,-0.02454291,-0.01609754,0.03470426,-0.09760322,0.09529153,0.01878796,0.02619348,-0.00499941,-0.04085136,-0.00480882,-0.03712242,-0.00580123,-0.00401987,0.03022755,-0.01458534,0.03149811,-0.01418202,0.03868422,-0.03784318,0.01283242,0.02280303,0.03767883,-0.03007019,0.02486825,-0.01357284,0.05789743,-0.10222204,-0.18151014,-0.01038487,0.0853063,-0.01428787,0.02836622,-0.00298506,0.0455212,0.02187349,0.05117906,0.1182443,0.10878256,0.03675807,-0.05245649,0.01544059,-0.01800091,0.05619043,0.0599804,-0.0214157,-0.00636304,0.04033528,-0.02799098,0.01391176,0.00648725,-0.04842751,0.02962929,-0.07292058,0.09651332,0.00504035,0.03499395,0.00436429,0.10189055,-0.00115766,-0.02133891,-0.13963616,0.04587999,0.07551029,-0.00924862,-0.03323462,-0.04310106,-0.01764764,-0.01253403,0.02050544,0.06976642,-0.11727812,0.00949309,-0.06792346,-0.04077227,-0.02720693,-0.04225729,0.03392569,-0.00884529,0.01558114,0.02348785,0.12042572,-0.0043967,-0.01220764,0.00614404,-0.06937008,-0.01295872,0.04198731,0.00627318,0.01556352,-0.02096227,-0.02282559,0.06500662,-0.0286363,0.00931592,0.00001455,0.04588842,-0.03261355,-0.02712195,0.11957008,0.01108708,0.02136287,0.0363952,0.02647848,-0.07478432,-0.09729692,0.02536334,0.01448985,0.02069714,0.02888863,0.0360111,0.02136283,0.02523196,0.05300137,0.03296145,-0.0370896,0.03696903,-0.00608971,-0.02402058,-0.03970045,-0.02052456,-0.01816684,0.04535694,0.03583225,-0.30090123,-0.03644538,-0.01134439,-0.02199166,0.04664397,0.03779233,0.06044425,0.01729994,-0.07780986,0.0394558,0.00489879,0.01257039,-0.01146773,-0.04589595,-0.00362968,-0.00187155,0.05895984,-0.00625335,0.02984923,0.04123879,0.00244894,0.05473258,0.20736495,-0.00036594,0.01122332,0.00864141,-0.03163601,0.04386393,0.02762987,-0.00873342,-0.05204526,-0.02008218,0.05545791,-0.02855189,0.00073318,0.06657158,0.01537232,0.00166664,-0.00638728,0.01413811,-0.05433587,-0.03403423,0.00061737,0.01638293,0.10417508,0.00366364,-0.05937111,-0.06048728,-0.0163822,0.0564085,-0.07651246,-0.00695326,0.01740498,0.0441248,-0.03956022,0.05182981,0.02838099,-0.04768565,-0.06323715,-0.01205898,-0.01997509,-0.02361919,-0.07498962,0.07242209,0.00826383],"last_embed":{"hash":"3x8g8l","tokens":480}}},"text":null,"length":0,"last_read":{"hash":"3x8g8l","at":1751426529982},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**","lines":[291,353],"size":15027,"outlinks":[{"title":"https://github.com/zorp-corp/nockchain","target":"https://github.com/zorp-corp/nockchain","line":3},{"title":"https://github.com/0xmoei/nockchain","target":"https://github.com/0xmoei/nockchain","line":4},{"title":"https://www.nockchain.org/","target":"https://www.nockchain.org/","line":5},{"title":"https://icoanalytics.org/projects/zorp-nockchain/","target":"https://icoanalytics.org/projects/zorp-nockchain/","line":6},{"title":"https://urbit.org/overview/history","target":"https://urbit.org/overview/history","line":7},{"title":"https://github.com/zorp-corp/jock-lang","target":"https://github.com/zorp-corp/jock-lang","line":8},{"title":"https://www.globenewswire.com/news-release/2024/12/30/3002639/0/en/Alpha-Sigma-Capital-Research-Unveils-2025-Crypto-Market-Predictions-AI-Agents-Staking-ETFs-and-Decentralized-Infrastructure-Lead-the-Way.html","target":"https://www.globenewswire.com/news-release/2024/12/30/3002639/0/en/Alpha-Sigma-Capital-Research-Unveils-2025-Crypto-Market-Predictions-AI-Agents-Staking-ETFs-and-Decentralized-Infrastructure-Lead-the-Way.html","line":9},{"title":"https://crypto-fundraising.info/projects/zorp-nockchain/","target":"https://crypto-fundraising.info/projects/zorp-nockchain/","line":10},{"title":"https://members.delphidigital.io/feed/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","target":"https://members.delphidigital.io/feed/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","line":11},{"title":"https://podwise.ai/dashboard/episodes/4062914","target":"https://podwise.ai/dashboard/episodes/4062914","line":12},{"title":"https://player.fm/series/the-delphi-podcast/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","target":"https://player.fm/series/the-delphi-podcast/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","line":13},{"title":"https://tracxn.com/d/companies/zorp/\\_\\_VTLVrft6NMSWw6R9NmmNgOwSmC1kn-XXhp2K\\_MTFXxM","target":"https://tracxn.com/d/companies/zorp/__VTLVrft6NMSWw6R9NmmNgOwSmC1kn-XXhp2K_MTFXxM","line":14},{"title":"https://www.zorp.one/","target":"https://www.zorp.one/","line":15},{"title":"https://www.zorp.one/about","target":"https://www.zorp.one/about","line":16},{"title":"https://en.wikipedia.org/wiki/End\\_of\\_the\\_World\\_(Parks\\_and\\_Recreation)","target":"https://en.wikipedia.org/wiki/End_of_the_World_\\(Parks_and_Recreation\\","line":17},{"title":"https://podcasts.apple.com/gr/podcast/the-delphi-podcast/id1438148082?l=el","target":"https://podcasts.apple.com/gr/podcast/the-delphi-podcast/id1438148082?l=el","line":18},{"title":"https://www.chaincatcher.com/en/article/2181453","target":"https://www.chaincatcher.com/en/article/2181453","line":19},{"title":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","target":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","line":20},{"title":"https://cryptorank.io/ico/nockchain","target":"https://cryptorank.io/ico/nockchain","line":21},{"title":"https://cryptorank.io/price/nockchain","target":"https://cryptorank.io/price/nockchain","line":22},{"title":"https://github.com/zorp-corp/nockapp","target":"https://github.com/zorp-corp/nockapp","line":23},{"title":"https://www.everand.com/podcast/864012011/Logan-Allen-Nockchain-s-Global-Competition-for-Useful-ZK-Proof-of-Work-Miners-Kicks-Off","target":"https://www.everand.com/podcast/864012011/Logan-Allen-Nockchain-s-Global-Competition-for-Useful-ZK-Proof-of-Work-Miners-Kicks-Off","line":24},{"title":"https://www.nervos.org/knowledge-base/what\\_are\\_blockchain\\_intents\\_(explainCKBot)","target":"https://www.nervos.org/knowledge-base/what_are_blockchain_intents_\\(explainCKBot\\","line":25},{"title":"https://volet.com/blog/post/understanding-intents-in-blockchain-a-comprehensive-guide-to-intents-01jdpzq69t52gfwxy6z0bx1nmv","target":"https://volet.com/blog/post/understanding-intents-in-blockchain-a-comprehensive-guide-to-intents-01jdpzq69t52gfwxy6z0bx1nmv","line":26},{"title":"https://crypto.com/en/university/intent-centric-design-for-blockchain","target":"https://crypto.com/en/university/intent-centric-design-for-blockchain","line":27},{"title":"https://www.bitget.com/zh-TC/news/detail/12560604756213","target":"https://www.bitget.com/zh-TC/news/detail/12560604756213","line":28},{"title":"https://www.youtube.com/watch?v=G5tE0LFFiTY","target":"https://www.youtube.com/watch?v=G5tE0LFFiTY","line":29},{"title":"https://ethplorer.io/address/******************************************","target":"https://ethplorer.io/address/******************************************","line":30},{"title":"https://phantom.com/tokens/solana/6tCEUogw2uk4VQz5JKb295ixFrj6vAPEQbZqL9VfFXSz","target":"https://phantom.com/tokens/solana/6tCEUogw2uk4VQz5JKb295ixFrj6vAPEQbZqL9VfFXSz","line":31},{"title":"https://www.dextools.io/app/en/solana/pair-explorer/ENDyunqABSwMvfUoRxsNA1N2QLhXeZQTYrnPZUQdnygP","target":"https://www.dextools.io/app/en/solana/pair-explorer/ENDyunqABSwMvfUoRxsNA1N2QLhXeZQTYrnPZUQdnygP","line":32},{"title":"https://web3.bitget.com/en/wiki/nock-wallet","target":"https://web3.bitget.com/en/wiki/nock-wallet","line":33},{"title":"https://m.sosovalue.com/blog/comprehensive-guide-to-bitcoin-mining","target":"https://m.sosovalue.com/blog/comprehensive-guide-to-bitcoin-mining","line":34},{"title":"https://www.cryptominerbros.com/blog/calculate-crypto-mining-profits/","target":"https://www.cryptominerbros.com/blog/calculate-crypto-mining-profits/","line":35},{"title":"https://asicmarketplace.com/blog/how-to-calculate-profit-in-crypto-mining/","target":"https://asicmarketplace.com/blog/how-to-calculate-profit-in-crypto-mining/","line":36},{"title":"https://www.rootdata.com/Projects/detail/Nockpool?k=MTc2Mzg%3D","target":"https://www.rootdata.com/Projects/detail/Nockpool?k=MTc2Mzg%3D","line":37},{"title":"https://en.wikipedia.org/wiki/Mining\\_pool","target":"https://en.wikipedia.org/wiki/Mining_pool","line":38},{"title":"https://podcasts.apple.com/us/podcast/the-delphi-podcast/id1438148082","target":"https://podcasts.apple.com/us/podcast/the-delphi-podcast/id1438148082","line":39},{"title":"https://icodrops.com/nockchain/","target":"https://icodrops.com/nockchain/","line":40},{"title":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA=","target":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","line":41},{"title":"https://github.com/0xmoei/nockchain/security","target":"https://github.com/0xmoei/nockchain/security","line":42},{"title":"https://dysnix.com/blockchain-security-audit","target":"https://dysnix.com/blockchain-security-audit","line":43},{"title":"https://www.lcx.com/how-to-conduct-blockchain-security-audit/","target":"https://www.lcx.com/how-to-conduct-blockchain-security-audit/","line":44},{"title":"https://veridise.com/audits/nft-security/","target":"https://veridise.com/audits/nft-security/","line":45},{"title":"https://immunebytes.com/blog/blockchain-security-audit-the-ultimate-guide/","target":"https://immunebytes.com/blog/blockchain-security-audit-the-ultimate-guide/","line":46},{"title":"https://hacken.io/services/blockchain-security/blockchain-protocol-security/","target":"https://hacken.io/services/blockchain-security/blockchain-protocol-security/","line":47},{"title":"https://quantstamp.com/audits","target":"https://quantstamp.com/audits","line":48},{"title":"https://www.reddit.com/r/CryptoCurrency/comments/1lhoxfc/outside\\_of\\_the\\_crypto\\_community\\_crypto\\_is\\_still/","target":"https://www.reddit.com/r/CryptoCurrency/comments/1lhoxfc/outside_of_the_crypto_community_crypto_is_still/","line":49},{"title":"https://www.reddit.com/r/CryptoMarkets/comments/1l5jqcu/which\\_crypto\\_community\\_still\\_feels\\_genuinely/","target":"https://www.reddit.com/r/CryptoMarkets/comments/1l5jqcu/which_crypto_community_still_feels_genuinely/","line":50},{"title":"https://www.reddit.com/r/CryptoMarkets/comments/1jakyzw/why\\_community\\_sentiment\\_of\\_every\\_crypto\\_on/","target":"https://www.reddit.com/r/CryptoMarkets/comments/1jakyzw/why_community_sentiment_of_every_crypto_on/","line":51},{"title":"https://www.everand.com/podcast/593966517/Launching-the-Mars-Review-of-Books-with-Noah-Kumin-Noah-Kumin-is-the-founder-and-editor-of-the-Mars-Review-of-Books","target":"https://www.everand.com/podcast/593966517/Launching-the-Mars-Review-of-Books-with-Noah-Kumin-Noah-Kumin-is-the-founder-and-editor-of-the-Mars-Review-of-Books","line":52},{"title":"https://podcasts.apple.com/bt/podcast/urbit-nockchain-and-the-current-state/id1195362330?i=1000709342627","target":"https://podcasts.apple.com/bt/podcast/urbit-nockchain-and-the-current-state/id1195362330?i=1000709342627","line":53},{"title":"https://www.nansen.ai/post/best-telegram-channels-for-crypto-chats","target":"https://www.nansen.ai/post/best-telegram-channels-for-crypto-chats","line":54},{"title":"https://ryanschultz.com/2018/07/17/the-telegram-discussion-groups-for-the-various-blockchain-based-vr-projects-have-been-endlessly-entertaining/","target":"https://ryanschultz.com/2018/07/17/the-telegram-discussion-groups-for-the-various-blockchain-based-vr-projects-have-been-endlessly-entertaining/","line":55},{"title":"https://www.reddit.com/r/CryptoCurrency/comments/lz1f54/beware\\_of\\_fake\\_pump\\_coins\\_groups\\_on\\_telegram/","target":"https://www.reddit.com/r/CryptoCurrency/comments/lz1f54/beware_of_fake_pump_coins_groups_on_telegram/","line":56},{"title":"https://asicmarketplace.com/blog/what-is-aleo/","target":"https://asicmarketplace.com/blog/what-is-aleo/","line":57},{"title":"https://risczero.com/blog/zkvm","target":"https://risczero.com/blog/zkvm","line":58},{"title":"https://www.rootdata.com/Projects/detail/RISC%20Zero?k=MjgwMw%3D%3D","target":"https://www.rootdata.com/Projects/detail/RISC%20Zero?k=MjgwMw%3D%3D","line":59},{"title":"https://en.foresightnews.pro/an-overview-of-privacy-solutions-based-on-zero-knowlege-cryptography/","target":"https://en.foresightnews.pro/an-overview-of-privacy-solutions-based-on-zero-knowlege-cryptography/","line":60},{"title":"https://equilibrium.co/writing/privacy-blockchains-and-aleo-deep-dive","target":"https://equilibrium.co/writing/privacy-blockchains-and-aleo-deep-dive","line":61},{"title":"https://www.blockchaincoinvestors.com/blog/risczero-eliminating-tradeoffs-and-unleashing-the-power-of-blockchain-technology-254ec","target":"https://www.blockchaincoinvestors.com/blog/risczero-eliminating-tradeoffs-and-unleashing-the-power-of-blockchain-technology-254ec","line":62},{"title":"https://podcasts.musixmatch.com/podcast/the-delphi-podcast-01hpa65hx48s2p8649mhbr8ke5/episode/logan-allen-nockchains-global-competition-for-useful-01jvmp9agdsppxbp1n6gwtz1ck","target":"https://podcasts.musixmatch.com/podcast/the-delphi-podcast-01hpa65hx48s2p8649mhbr8ke5/episode/logan-allen-nockchains-global-competition-for-useful-01jvmp9agdsppxbp1n6gwtz1ck","line":63}],"class_name":"SmartBlock","last_embed":{"hash":"3x8g8l","at":1751426529982}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02583107,-0.03612664,-0.07027342,-0.00885698,0.01353602,-0.01209683,-0.01448055,0.03030158,-0.02435407,-0.00401569,0.0751403,-0.06128068,0.017387,0.0386321,0.01866593,-0.0217271,0.00932701,-0.03615661,-0.02634417,0.01642938,0.11543695,-0.10663592,0.03272736,-0.05665695,0.06835462,0.04425734,0.00320273,-0.01548352,-0.03051389,-0.17707118,0.02043376,-0.06616049,0.0030495,0.00058148,0.02915191,-0.03226023,-0.03373998,0.05486947,-0.0392827,0.00242432,0.03620293,0.02106804,-0.04332126,0.01058151,0.0579606,-0.09965938,0.00984527,-0.03751186,-0.02851303,-0.03189495,0.00093269,-0.06317143,-0.00360359,0.03917667,-0.01616601,-0.00523818,0.02747868,0.035469,0.02502623,0.0339347,0.07264885,0.04831641,-0.21386532,-0.00233883,0.07935084,0.01095004,-0.05867193,0.01177382,0.02715918,0.06088367,0.03444995,0.00710852,0.00215118,0.02307726,0.00712231,-0.03158116,0.03449772,0.01961717,-0.02768731,-0.04569813,-0.00978802,0.0775658,-0.00680282,-0.0019742,0.02222527,-0.02155784,0.01659499,-0.00689785,0.02017718,0.00336761,0.00086436,-0.02217639,0.05548315,0.00847236,-0.03710676,0.04015653,-0.00663216,0.0309926,-0.08331148,0.10479485,-0.00714375,0.04988156,-0.01155185,-0.05505697,0.00496697,-0.02290156,-0.03951138,-0.05010336,0.03993485,0.02531157,-0.00846829,0.01019502,0.06458219,-0.05330088,0.02121237,-0.01946519,0.0015033,0.00244063,0.01532242,0.01815528,-0.00652959,0.04234637,0.0663804,0.01189158,0.00953825,-0.01499688,0.00553109,0.07074421,0.02216755,0.032246,0.04032059,-0.00992987,-0.08969304,0.01184592,-0.02237378,-0.01924585,-0.00539341,0.00432146,-0.02511387,-0.02846106,-0.04735935,-0.06659916,0.00530589,-0.09998518,-0.03395618,0.10717576,0.02694601,0.00690362,0.01293871,-0.05734163,-0.01316426,0.07615979,-0.03713772,-0.07193591,0.00851718,0.03309722,0.02752075,0.0693744,-0.06705633,0.01122668,-0.03269212,-0.01657268,-0.04696094,0.15378451,0.04740986,-0.13223423,0.04704222,0.03623232,-0.03227691,-0.08440895,-0.01057881,0.02355146,-0.06374618,-0.01286289,0.0820351,-0.05587395,-0.0373317,-0.04827054,-0.01486035,0.06207058,-0.00990267,-0.00685288,-0.07918112,0.00020606,-0.03608123,-0.02454299,-0.03698889,0.03807275,0.00825424,0.04653822,-0.09048149,0.04234876,-0.04326361,0.02219548,-0.03035572,-0.02129223,-0.02363577,0.00904658,0.02390778,-0.06435733,0.13973072,0.01636819,0.02398169,0.02637276,-0.02717555,0.00548053,-0.03319887,-0.00199778,-0.02204099,0.02925457,-0.01871386,0.04804461,-0.0197752,0.04154889,-0.05529413,0.00201676,0.01432448,0.03303849,-0.02692149,0.05433763,-0.01548756,0.05484463,-0.07834297,-0.20601295,-0.01384517,0.06509492,-0.02894079,0.05050891,0.00968456,0.06169589,0.00480491,0.04993906,0.10159241,0.10295724,0.03794399,-0.03456993,0.0171096,0.01612834,0.05346078,0.05418957,-0.02220805,0.00066153,0.0345487,-0.05269934,0.01494204,0.03576395,-0.0820261,0.02941736,-0.06942127,0.10648128,-0.00073362,0.01559017,-0.0075959,0.07250371,-0.01716091,-0.03402481,-0.12576544,0.04321665,0.06598827,-0.00038929,-0.0013287,-0.04409014,-0.0295876,-0.01796058,0.01875869,0.07354657,-0.11396348,0.02547079,-0.06019798,-0.01588672,0.03858934,-0.02218475,0.0478496,-0.02357743,0.02204747,0.03226705,0.11024334,0.01549369,-0.02468508,0.00978516,-0.03936766,-0.006056,0.03296716,0.00678761,-0.00988534,-0.03676148,-0.02490953,0.05692301,-0.04777815,0.01237564,0.01155915,0.04542733,-0.05116063,-0.02564804,0.09485859,0.00250624,-0.00515336,0.0844731,0.03143306,-0.05984428,-0.10291065,0.01968005,0.0339457,0.01608013,0.00275396,0.02054622,0.02866328,0.00787521,0.08251169,0.03043151,-0.01960475,0.0182644,-0.02437498,-0.01787372,-0.03776605,-0.05031735,0.00037819,0.05543849,0.01856136,-0.29217044,-0.02643748,-0.00804079,-0.01390172,0.05967234,0.04285696,0.06556203,0.02696916,-0.0585958,0.01246964,-0.01306983,0.00163522,-0.0115786,-0.05891356,-0.00697625,0.01582166,0.05531486,-0.02225442,0.03479971,0.04355619,0.00187934,0.06285635,0.22829328,0.03124624,-0.00308596,0.00315589,-0.05711599,0.04582444,0.04477904,-0.02784031,-0.03694475,-0.02673515,0.05039894,-0.02645516,0.01423413,0.06307312,0.02595858,-0.0047545,-0.01030651,0.02843405,-0.03562274,-0.04650457,-0.01388587,0.02043478,0.08866125,-0.01292528,-0.05911252,-0.05429152,-0.01939442,0.06321967,-0.06085081,-0.03182149,0.00676132,0.01611869,-0.01241281,0.07372818,0.02299481,-0.04694762,-0.05498957,-0.01298328,-0.02310706,-0.007333,-0.07704908,0.04074399,-0.00124809],"last_embed":{"hash":"4e9r35","tokens":154}}},"text":null,"length":0,"last_read":{"hash":"4e9r35","at":1751426529998},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{4}","lines":[296,296],"size":218,"outlinks":[{"title":"https://icoanalytics.org/projects/zorp-nockchain/","target":"https://icoanalytics.org/projects/zorp-nockchain/","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"4e9r35","at":1751426529998}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{7}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00329655,-0.0825601,-0.06541231,0.00741129,0.04769761,0.02308822,-0.02038743,0.03834197,-0.00062961,-0.00801632,0.06094685,-0.04039421,0.00811877,0.01286633,0.0428014,-0.01361693,-0.03657607,-0.03518023,-0.02244304,0.0207412,0.11888508,-0.03106256,0.0305794,-0.09291513,0.05849097,0.00996995,0.03573328,-0.06721185,-0.08510927,-0.18417023,-0.01865909,-0.06710087,0.01663412,0.01805137,0.00375957,-0.03148704,-0.01483322,0.07092159,-0.02858827,0.02595009,-0.00380435,0.00226074,0.02018338,0.01406116,0.04153663,-0.07800008,0.00170437,-0.04596532,-0.03430863,-0.02688114,0.00540606,-0.07214138,-0.05890664,0.02819995,0.02023561,0.01273942,-0.02121941,0.01751874,0.03911918,0.04891404,0.0765522,0.02505185,-0.21223532,0.00573051,0.05469807,0.09032356,-0.04644847,-0.00341296,0.0301601,0.05186858,0.048141,0.01519172,0.0161017,-0.03289796,0.05967775,0.03326341,0.00444287,-0.00560733,0.00349015,-0.06187429,-0.02886616,0.07080369,-0.06197137,-0.0014633,-0.07414203,-0.01116308,0.01232404,-0.02162692,0.02809945,-0.0112399,0.00628729,0.0140765,-0.0103083,0.00865375,-0.04208787,0.00042932,0.0153093,0.02811097,-0.06982298,0.10410117,-0.01810046,0.08398076,-0.00488514,-0.05719471,0.01629127,-0.02415584,-0.04509527,-0.06553254,0.0146857,0.02301848,0.0215642,0.0234065,0.04192639,-0.07938848,-0.00118436,0.0089941,0.02868346,0.02967333,-0.01424541,0.00377876,-0.01710435,0.01588177,0.05905423,-0.00721696,-0.03753252,0.02482696,-0.00573154,0.07240137,0.02545527,0.04909081,0.02833294,0.02498213,-0.12217408,-0.01477518,-0.00921321,-0.03126772,-0.0354983,-0.02581007,-0.02833245,-0.02614628,-0.08496055,-0.01463729,-0.01920662,-0.10949057,-0.03628315,0.08749399,0.04327707,0.00387518,-0.00044654,-0.0373121,0.02512436,0.07360087,-0.00189365,-0.0503519,-0.01949127,-0.01702083,0.02734948,0.08585694,-0.12106363,-0.00566093,-0.05283151,-0.03423509,-0.05817282,0.14292166,0.01206341,-0.0894854,-0.03397616,0.00028117,0.02309517,-0.06855288,0.0231641,0.00942235,-0.0526838,-0.02030836,0.04108094,0.00958126,-0.06485707,-0.06542756,0.00845854,0.02970354,-0.01712132,-0.00850242,-0.04868565,0.02417605,-0.03178673,-0.01571154,-0.01637204,0.00444364,0.0195969,-0.02001814,-0.06427574,0.07758562,-0.02382699,0.08613627,0.00798501,0.02276135,-0.03433504,-0.02503155,0.02235609,-0.05488513,0.05030852,0.03943942,-0.00216253,-0.00481151,-0.03473992,-0.00890683,-0.04169104,-0.04070601,0.00024563,0.06858644,0.00198124,0.02253218,-0.005974,0.02989684,-0.05286938,0.02817248,0.01827561,0.02885983,-0.02079113,0.0341665,0.02954043,0.06718341,-0.0721435,-0.1870929,-0.04978072,0.02859247,-0.02332421,0.06003039,-0.00141283,0.07451974,0.03667248,0.05341657,0.10451002,0.04983819,0.0664019,-0.03588242,0.0420737,0.03066906,0.02620266,-0.00663495,-0.03127084,0.01311643,0.07367247,-0.01612801,0.02224214,-0.03984248,-0.08378982,0.04802737,-0.00200813,0.07499976,-0.0312436,0.00722561,-0.03820444,0.0245213,0.01286352,-0.01146851,-0.10269912,0.05577875,0.00547202,0.0583076,-0.00177111,-0.07531287,-0.04197342,-0.01693277,0.05190868,0.02085725,-0.08719309,-0.03718991,-0.04301209,-0.00650606,0.02668709,-0.01422142,0.0466722,0.03532751,0.01881299,0.03951366,0.06144682,0.02959701,-0.03714209,0.04579136,-0.01989394,0.02220435,0.08356748,0.04577783,0.01616835,-0.02398816,-0.02949129,0.06503495,-0.02095416,-0.0061249,0.01378874,0.02406118,-0.02152683,-0.02831322,0.17956035,0.02083845,0.01435179,0.0759193,-0.01595068,-0.04972652,-0.08247283,-0.00435687,0.07736399,0.04740732,-0.03148435,0.07369468,0.06024786,0.02537084,0.09897173,-0.00495067,0.00057809,0.00383573,0.01244339,-0.0016621,-0.04016456,-0.06693481,-0.06628232,0.04393421,0.02135494,-0.28867096,0.00581827,0.02917461,0.00519636,0.0370244,-0.00131867,0.06230759,0.03165149,-0.05394447,0.0381029,-0.04571401,0.05880825,0.02663727,-0.011244,-0.03488124,-0.03847556,0.06949799,-0.02287672,0.04968569,0.02967232,-0.00687311,0.05155351,0.22499333,0.01346323,0.00644235,0.03630219,-0.06118752,0.03849312,0.04347609,-0.03739404,0.02300103,0.00092396,0.02324016,-0.07837033,0.01839896,0.03962547,0.00349722,-0.01694938,-0.01505512,0.00553188,0.01489841,0.01681548,0.02541547,0.0340468,0.10358603,-0.04094245,-0.02827016,-0.04849945,0.00985316,0.04130296,-0.05393299,-0.00598135,-0.00940076,0.01207656,-0.00718504,0.04712566,0.02653755,-0.04450154,-0.05148011,-0.0288,-0.00185782,-0.05414494,-0.08602866,0.00617482,-0.03945122],"last_embed":{"hash":"d5ymcp","tokens":279}}},"text":null,"length":0,"last_read":{"hash":"d5ymcp","at":1751426530005},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{7}","lines":[299,299],"size":602,"outlinks":[{"title":"https://www.globenewswire.com/news-release/2024/12/30/3002639/0/en/Alpha-Sigma-Capital-Research-Unveils-2025-Crypto-Market-Predictions-AI-Agents-Staking-ETFs-and-Decentralized-Infrastructure-Lead-the-Way.html","target":"https://www.globenewswire.com/news-release/2024/12/30/3002639/0/en/Alpha-Sigma-Capital-Research-Unveils-2025-Crypto-Market-Predictions-AI-Agents-Staking-ETFs-and-Decentralized-Infrastructure-Lead-the-Way.html","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"d5ymcp","at":1751426530005}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{9}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06924232,-0.02061068,-0.06436092,0.01467144,0.01561841,-0.01130112,-0.02070299,0.0346084,-0.04868313,-0.01771819,0.03870319,-0.0341988,0.0110233,0.00741096,-0.00101786,-0.02138005,-0.02248593,-0.00807308,-0.01452838,-0.00003679,0.08041665,-0.05918022,0.02261265,-0.04569086,0.11122067,0.06569092,0.01766104,-0.02855889,-0.02308172,-0.20130874,-0.00937496,-0.06483718,0.00262751,0.00154329,0.0536521,-0.06390648,-0.02100454,0.06359541,-0.12067474,0.0006289,0.03300745,-0.00063183,0.01741332,-0.0285673,0.0527035,-0.08038154,0.02213916,-0.05532985,-0.06429087,-0.01558285,0.01688989,-0.05566732,-0.02007423,0.02581484,0.03256978,0.02041155,0.00947066,0.03938133,0.04967432,0.01178666,0.08158724,0.0393995,-0.21176517,0.00827041,0.08948133,0.04395008,-0.03846205,0.04342803,0.03460462,0.10293804,0.04429975,0.01076171,-0.00445776,0.03223798,0.00538249,0.04740886,0.03417836,-0.04002758,-0.06070281,-0.06630263,-0.07044763,0.04936811,-0.01916252,-0.01044864,-0.05848733,0.0018583,0.02444865,-0.03770155,0.0106217,-0.00992747,0.00692316,0.01097332,0.05938223,0.02519338,-0.02977648,-0.01672305,0.0323342,0.04175021,-0.07089755,0.1146513,-0.03037883,0.05584908,-0.02421831,-0.0614218,0.05272246,-0.02346815,-0.01161826,-0.02078379,0.0074865,0.01151624,0.02485344,0.01163936,0.06684256,-0.04224082,0.01471659,0.003488,0.06278846,0.01444949,0.0054851,0.01215726,0.02806393,0.06678423,0.01079984,-0.01597025,-0.06595152,-0.01291908,0.03721541,0.0416091,0.06925406,0.01205212,0.05663748,0.01597483,-0.04395446,0.02015937,-0.02982305,-0.02017384,0.02105256,0.01951569,-0.04553845,0.01869384,0.00242446,-0.06009405,-0.01098863,-0.11301137,-0.02955339,0.05999135,0.0020883,-0.01436887,0.02640996,-0.06657591,-0.00003255,0.04029505,-0.01984735,-0.06575102,0.03352741,0.03665223,0.04636556,0.07918857,-0.05200245,0.00178151,-0.05482601,0.00296747,-0.0460427,0.13364533,0.05639968,-0.11084093,0.02999092,0.03733839,-0.01684943,-0.07797296,0.02621466,-0.00011372,-0.07469279,-0.01995114,0.05206283,-0.07473508,-0.04578104,-0.01137241,0.01338358,0.05567026,0.00836484,-0.01850478,-0.07956998,-0.01741496,0.01667893,-0.02590087,-0.00703932,0.00860927,0.052952,0.02924623,-0.08432043,0.03270084,-0.04588431,0.0159721,-0.03761216,-0.04741316,-0.03901735,-0.04473186,0.03034689,-0.06427452,0.08553675,0.04835854,0.00951757,-0.01638979,-0.05137799,0.00782871,-0.03350255,-0.04365198,0.01725389,0.01372886,-0.0385118,-0.01518697,-0.05327673,0.01357311,0.00125836,0.02132297,0.0358553,0.0129315,-0.0486953,0.01641059,0.02424383,0.01592477,-0.05614416,-0.19572271,-0.07485671,0.00776435,0.03925361,0.05708328,-0.02430902,0.04071092,0.01091256,0.03157402,0.07653451,0.1073628,0.05573814,-0.07138751,0.02478785,0.01457682,0.01688344,-0.00184325,-0.04186727,0.05907212,0.03961872,-0.00113389,0.02468617,-0.01242567,-0.07533402,0.01173768,-0.04862047,0.12474582,0.02578585,0.01289555,-0.02147478,0.02712659,0.03039431,-0.05076193,-0.13608317,0.02905799,0.07933219,-0.00555161,-0.0363703,0.00907732,-0.02678343,-0.01017217,0.06157157,0.0149247,-0.12795375,0.06131947,-0.05390044,-0.04878042,0.03507592,-0.03558509,0.04047788,-0.00854311,0.0604135,0.0507344,0.10476809,-0.00225098,-0.02612471,0.0136671,-0.05509194,0.01366121,0.03307955,-0.02637252,-0.00106971,-0.0520542,0.01206173,0.06219416,0.01224546,-0.00914943,0.01629875,0.04494325,0.00053408,-0.02068676,0.11433846,0.06151161,-0.02433409,0.02556186,0.02807786,-0.03152306,-0.0851768,0.03138976,0.01089611,0.04123041,-0.01681103,0.04923993,0.06072359,0.0141365,0.10266916,0.01668744,0.01993868,0.03530265,0.01142765,-0.0114713,-0.01903941,-0.05764125,-0.06688248,0.05224365,0.00737306,-0.27556691,0.00395817,-0.02175747,-0.01460911,0.01434223,0.00636583,0.06084782,0.00164921,-0.07258143,0.01164955,-0.0272078,0.05173174,-0.00313394,-0.03458151,0.00361111,-0.00038734,0.05888091,-0.01891171,0.04354268,0.03486472,0.00879044,0.07657528,0.19127902,-0.02923913,0.05962682,0.03609079,-0.03608805,0.02635806,0.04735967,0.00586364,-0.00172897,-0.02617186,0.0196105,-0.04204031,-0.00028889,0.08330973,-0.05174261,-0.01822929,0.00993546,-0.01331665,-0.04141035,-0.02768783,-0.00706359,0.02310294,0.12105785,-0.04380566,-0.05991114,-0.09394309,0.00658656,0.01315606,-0.03801133,-0.03724102,-0.00651228,-0.0150008,-0.02562026,0.04120828,0.05773737,-0.02172822,-0.05898667,-0.02384552,-0.00232903,0.01753393,-0.04481445,0.05965916,0.02408551],"last_embed":{"hash":"szmkil","tokens":200}}},"text":null,"length":0,"last_read":{"hash":"szmkil","at":1751426530014},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{9}","lines":[301,301],"size":366,"outlinks":[{"title":"https://members.delphidigital.io/feed/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","target":"https://members.delphidigital.io/feed/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"szmkil","at":1751426530014}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{10}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0673583,-0.04547197,-0.04270557,-0.01329446,0.03283973,-0.00645705,-0.01032807,0.05035814,-0.03260054,-0.00828775,0.05314473,-0.04355567,0.00965194,0.05644732,0.00538062,-0.01927473,0.00835492,-0.00896201,-0.00790153,0.00987259,0.10924652,-0.06187309,0.00634387,-0.05500286,0.06183394,0.06186544,0.04576159,-0.02826268,-0.02500439,-0.21685947,0.01886054,-0.063765,0.01472962,0.01730135,0.04714995,-0.05904201,-0.03985975,0.07553902,-0.10614372,0.00477771,0.02120116,-0.00760667,0.01602395,-0.03762725,0.02662213,-0.08624085,0.02635407,-0.03031486,-0.03354584,-0.00974981,0.00862482,-0.05747696,-0.02289429,0.0200635,0.03855274,0.00524557,0.02273599,0.03089275,0.04892144,0.01610742,0.08479362,0.02405137,-0.20180294,0.0103826,0.09366611,0.05257785,-0.03740018,0.01495443,0.01337844,0.08795706,0.04775657,0.00795442,0.00450985,0.02310423,0.00654615,0.047728,0.02547154,-0.02860921,-0.04448023,-0.0921809,-0.08835471,0.04076548,-0.04090621,0.0185461,-0.03959011,-0.00682461,-0.02057672,-0.04934641,-0.00028136,-0.02046981,0.02107786,0.04184923,0.06700791,0.02127387,0.00154404,-0.02067311,0.02469062,0.02785098,-0.09090123,0.11053753,-0.01303362,0.05000086,0.00036035,-0.07222059,0.05033531,-0.01495258,-0.02032043,-0.03145431,0.01712395,0.00594416,0.02628227,0.02090713,0.08545654,-0.06188182,0.03398808,0.02204393,0.01425059,0.04129709,-0.00148908,0.00561453,0.04388805,0.06762007,0.03047956,-0.04201899,-0.06307308,-0.01590715,0.04293426,0.07375687,0.04718869,0.0089394,0.04248726,0.01351134,-0.05235429,0.02771802,-0.01094928,-0.05481745,0.01232597,0.01114972,-0.06689994,0.00868759,-0.04041824,-0.0672961,-0.02019861,-0.10680483,-0.02675233,0.07131854,0.01519301,0.00329875,0.00875569,-0.03696917,0.01418339,0.03193716,-0.00911236,-0.06622748,0.01453123,0.01955741,0.06863871,0.09810797,-0.07290572,0.01155301,-0.05359903,-0.01602961,-0.02676141,0.1385657,0.06230442,-0.09790338,0.0114439,0.04459786,-1.4e-7,-0.05253238,0.00879323,0.00745352,-0.04370332,0.00458703,0.05706073,-0.08824621,-0.04862076,-0.00753968,0.00306536,0.05406666,0.00954187,-0.01034344,-0.05773811,-0.00235165,-0.01543155,-0.0186476,-0.00024408,0.02135273,0.04659532,0.02799369,-0.04959397,0.02927541,-0.03836846,0.04346252,-0.03032206,-0.04272419,-0.01458903,-0.02965919,0.01572437,-0.02667631,0.07416648,0.05268817,0.01785326,0.00608275,-0.07087335,-0.01282539,-0.06749966,-0.04850242,-0.01305973,0.03678491,-0.04182182,-0.02001172,-0.04284046,0.04926641,-0.01739319,0.01146709,0.01149004,0.006394,-0.03841379,0.00940387,0.02048764,0.04276798,-0.06564879,-0.22103049,-0.04910332,0.03070667,0.03424146,0.02243776,-0.03506634,0.0377664,0.01397521,0.05228537,0.08057885,0.07905331,0.03059192,-0.03473066,0.00250573,0.00860056,0.03505256,0.01452225,-0.00947808,0.02602072,0.04476227,-0.00713979,0.01691841,0.014784,-0.09941647,0.03469386,-0.05398341,0.11389008,0.01991759,0.00751487,-0.00149811,0.03663969,0.02734599,-0.03156168,-0.14889348,0.0162714,0.06796858,0.01983812,-0.05444039,-0.0296336,-0.03265595,-0.05141408,0.05812113,0.02519078,-0.12329706,0.05608039,-0.06372944,-0.06141279,-0.00254107,-0.04320188,0.02257708,-0.00567498,0.06590326,0.02787522,0.0920906,-0.00954266,-0.03480924,0.02336193,-0.05050419,0.02535301,0.0229907,-0.0045504,-0.01446704,-0.04904372,0.017509,0.04060939,0.00113768,-0.00881416,0.02047524,0.02145091,-0.02515372,-0.01818164,0.12228472,0.04548788,-0.01499808,0.03631129,0.0173964,-0.03000455,-0.0718805,0.05932515,0.01419097,0.03274725,-0.00146205,0.03711215,0.06580322,0.04623458,0.10376634,0.03063422,0.01112959,0.05662807,0.01035064,0.00851999,-0.0296682,-0.06710808,-0.06244816,0.07401352,0.0219755,-0.27208874,0.00216546,-0.03448497,-0.00415574,0.00283536,0.00910051,0.0398507,0.0223648,-0.0589334,-0.00675627,-0.01257499,0.0375877,-0.00978031,-0.04726746,0.00004822,0.00461827,0.06922144,0.00919706,0.04299618,0.04864878,-0.01782216,0.07409514,0.19954704,-0.01492063,0.05359161,0.03888819,-0.04732113,0.00986958,0.01632642,0.01085367,-0.03438979,-0.04313549,0.01342907,-0.0403716,0.02125597,0.06067288,-0.04762388,0.00579546,0.01481523,0.00326774,-0.05779278,-0.01856857,-0.00996312,0.03603958,0.09884031,-0.03333714,-0.07590725,-0.06403551,-0.00343122,0.034559,-0.02745482,-0.04556485,-0.0144978,0.01737515,-0.02097342,0.07442722,0.05070473,-0.02203443,-0.06955558,-0.0494244,-0.03321939,0.00071295,-0.03063682,0.07058375,-0.01110328],"last_embed":{"hash":"3ssjkl","tokens":147}}},"text":null,"length":0,"last_read":{"hash":"3ssjkl","at":1751426530021},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{10}","lines":[302,302],"size":240,"outlinks":[{"title":"https://podwise.ai/dashboard/episodes/4062914","target":"https://podwise.ai/dashboard/episodes/4062914","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"3ssjkl","at":1751426530021}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{11}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07686041,-0.02286961,-0.04471386,-0.00410642,0.04310743,-0.02199527,-0.01716158,0.03146944,-0.049363,-0.0085094,0.02605516,-0.02813359,0.002387,0.01538147,0.01220303,-0.02195937,-0.02785259,-0.00952891,-0.00985853,0.00171304,0.07912172,-0.05511272,0.01081429,-0.05252089,0.08459693,0.05497917,0.0276085,-0.03456121,-0.03921196,-0.22330566,-0.01399437,-0.06077556,0.03128743,-0.01097641,0.03990656,-0.07372782,-0.03075816,0.08475599,-0.12315346,0.00999955,0.0306129,-0.01043471,0.00291417,-0.037731,0.03297506,-0.068415,0.02666876,-0.03750962,-0.0400104,-0.00545755,0.01717996,-0.05085091,-0.01956648,0.02488487,0.05411787,0.01069773,0.01050824,0.03087631,0.03185696,0.01737401,0.08691657,0.02855244,-0.22237466,0.02494234,0.07874083,0.07221793,-0.03686002,0.05785662,0.02656124,0.09943999,0.04810254,0.02931023,0.01135427,0.01808392,0.02032124,0.0456519,0.02802997,-0.03970585,-0.05582546,-0.08261728,-0.07277629,0.02917255,-0.0385027,-0.01277656,-0.0384107,0.00424827,0.01318108,-0.05859766,0.00585657,-0.01772174,0.00479232,0.01425493,0.07459264,0.03304806,-0.01638508,-0.00881935,0.0254177,0.05257966,-0.08604696,0.11947146,-0.02104897,0.06465126,-0.01428393,-0.05499512,0.05323521,-0.03763712,-0.0113373,-0.02433825,0.02047088,0.02784043,0.02085713,0.00239497,0.07442263,-0.03500532,0.00105568,0.03528809,0.05587228,0.02464826,-0.0023547,-0.01472477,0.0526589,0.07624318,-0.00322582,-0.02735986,-0.06661263,-0.02561281,0.04561349,0.05726549,0.06968445,0.01156656,0.04717851,0.02170696,-0.03604024,0.03207611,0.00694637,-0.03384727,0.0131174,0.00545498,-0.06394098,0.01350856,0.00662523,-0.06842102,-0.01926333,-0.10233142,-0.02438557,0.06332771,0.00840066,-0.02530435,0.01958524,-0.05003861,-0.0021852,0.03139977,-0.00006441,-0.05907295,0.03296143,0.02160689,0.07289167,0.08064932,-0.05978554,0.00152193,-0.06448665,0.00181483,-0.0181989,0.14522065,0.06487101,-0.09758951,0.01607969,0.04194522,0.00231585,-0.06236755,0.01358149,-0.00525595,-0.07436723,-0.00433069,0.05314245,-0.07978225,-0.03693245,-0.00086373,0.00238348,0.04463362,0.00245064,-0.01450522,-0.06753569,-0.01844675,-0.00098732,-0.04358889,0.00338746,0.008305,0.04580526,0.03812338,-0.07272851,0.03361671,-0.02678663,0.02090007,-0.06260196,-0.06610405,-0.0248425,-0.0460966,0.04017374,-0.03851875,0.04753186,0.04567615,-0.00178676,-0.00046465,-0.08265855,0.0105979,-0.04320618,-0.04671797,0.03792653,0.01903274,-0.04950163,-0.01535167,-0.05018459,0.02150474,0.01256944,0.01768941,0.02297828,0.00951736,-0.06064606,0.01604375,-0.00503115,0.00534616,-0.05038203,-0.20680642,-0.04159929,-0.00179569,0.03034177,0.03410635,-0.03803498,0.05582087,0.0083802,0.04750692,0.08095021,0.08685616,0.03651728,-0.06451593,0.02337233,0.01683098,0.00484393,0.00393318,-0.0455974,0.05091966,0.05153122,0.01898045,0.03353696,0.00001923,-0.08371723,0.00717216,-0.04541621,0.11794125,0.03767297,0.0318738,-0.02016222,0.00593351,0.018349,-0.02076571,-0.13034853,0.00533984,0.07647562,0.03575492,-0.05113683,0.00840516,-0.04116221,-0.03579723,0.07131648,-0.00546129,-0.12716754,0.05061517,-0.05832016,-0.06369172,0.02485108,-0.04686497,0.04384857,0.00218825,0.057483,0.05575747,0.09280062,-0.01798395,-0.01357092,-0.00656637,-0.06723475,0.0325559,0.02327285,-0.02169753,0.01177568,-0.05873468,0.00174543,0.06155507,0.02592899,0.00840124,0.02272754,0.03808314,-0.02162311,-0.02382148,0.08957674,0.06189391,-0.02067656,0.00131611,0.01868036,-0.01172389,-0.09252108,0.04656507,-0.00259016,0.05466719,0.01222084,0.05659892,0.04487399,0.03557334,0.08891854,0.02666705,0.01712004,0.04800915,-0.00301144,-0.01254687,-0.00673086,-0.04849465,-0.07930044,0.0497649,0.00494025,-0.26288748,-0.01183581,-0.00291723,-0.01574595,-0.0025789,0.00198636,0.05454739,0.02311035,-0.07335874,-0.00039183,-0.01970144,0.05081209,-0.0049857,-0.03131687,0.01024488,-0.00942977,0.07090721,0.00272265,0.04252588,0.03727049,-0.0138418,0.06169268,0.18174613,-0.01087031,0.07198317,0.04306889,-0.02439211,0.01713798,0.03120687,0.01547382,-0.01233113,-0.02519939,0.02552148,-0.04342027,0.00485809,0.08089367,-0.06703194,-0.00843832,0.02084261,-0.01212463,-0.02505223,-0.03100922,-0.0008184,0.02874362,0.11145595,-0.05465441,-0.0777199,-0.08340783,0.00100476,0.0134056,-0.03578836,-0.02662101,0.01412799,-0.0161622,-0.02109284,0.06460901,0.0490425,-0.02891883,-0.03886949,-0.02001041,-0.01317076,0.01726104,-0.05202969,0.05741636,0.03172798],"last_embed":{"hash":"1dtd59k","tokens":210}}},"text":null,"length":0,"last_read":{"hash":"1dtd59k","at":1751426530028},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{11}","lines":[303,303],"size":411,"outlinks":[{"title":"https://player.fm/series/the-delphi-podcast/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","target":"https://player.fm/series/the-delphi-podcast/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1dtd59k","at":1751426530028}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{12}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02879103,-0.02879251,-0.07032754,-0.01512297,0.02659413,0.02517685,-0.00941075,0.0204711,-0.01643942,-0.02528186,0.05461316,-0.05462255,0.03715098,0.03374941,0.01071904,-0.03367137,0.01445447,-0.05075951,0.01236479,0.05455671,0.08044629,-0.09132578,0.05075635,-0.018486,0.0814784,0.046115,0.00025507,-0.00651889,-0.03518014,-0.18323973,-0.00269494,-0.07515863,-0.00712901,0.01839357,0.04615031,-0.03214114,-0.04291295,0.07794785,-0.02387786,0.00501457,-0.02183306,0.00740272,0.00682132,0.0111909,0.05706998,-0.09975872,0.019834,-0.03977163,-0.00979266,-0.0408856,-0.01452116,-0.08384438,-0.00264347,0.03979607,0.01123236,0.00557374,0.0048636,0.02286043,0.03977733,0.01842897,0.09839766,0.02747661,-0.21729445,0.01186092,0.06633327,0.06625491,-0.04141208,0.03765796,0.02104428,0.09265495,0.0283747,-0.01586634,-0.01466232,0.02721945,0.00130749,0.00021298,0.03950599,0.02660478,-0.02957215,-0.05031236,-0.03117236,0.06698185,-0.01121465,-0.03785031,0.00183607,-0.01291304,0.03007458,-0.01688547,-0.00132755,0.01496669,0.01627109,0.0425155,0.06187597,-0.00588427,-0.06546206,-0.00571504,0.01178307,0.03394693,-0.12026142,0.11716822,-0.03471947,0.05096576,0.00803678,-0.07457134,0.05539997,-0.00758148,-0.00007262,-0.03007841,0.02437516,0.04384963,0.01194685,-0.00658693,0.05557957,-0.06632241,0.03847758,0.01593504,0.00001741,-0.00204664,-0.03483711,0.00929851,-0.00797134,0.04296108,0.04928756,-0.00701241,-0.04100969,-0.02055297,0.03135452,0.04228459,0.03824644,0.03966479,0.04893815,0.04140126,-0.07775648,0.01044976,-0.04279513,-0.05049808,-0.02821323,0.00194914,-0.04540409,-0.04012775,-0.04120819,-0.09129168,-0.01486767,-0.10281499,-0.06178125,0.06800936,-0.01095445,-0.01400503,-0.00121361,-0.03455649,0.0025908,0.07042347,0.00221568,-0.08368237,-0.02201641,0.02218789,0.0211468,0.09134077,-0.04763066,0.02173043,-0.00616226,-0.05369768,-0.05397833,0.14539987,0.04879706,-0.15003213,0.00861264,0.05862264,0.00951688,-0.0724452,-0.01289433,0.00878691,-0.05473989,-0.0253924,0.05044905,-0.04864407,-0.05521211,-0.03660664,-0.0041853,0.02453211,0.00211801,0.00352461,-0.06758961,-0.00103235,0.01371069,0.01097077,-0.01384766,0.02302349,0.00603028,0.01859077,-0.07053748,0.02837753,-0.01386901,0.02496458,-0.0083339,-0.00075933,0.00005404,-0.01916463,0.02944289,-0.06745782,0.11214554,0.03081963,0.01113142,0.03065239,-0.06390776,-0.00806981,-0.03921692,-0.03409767,0.02920285,0.02589153,-0.0384572,0.02533456,-0.00797947,0.03107845,-0.04321262,0.0356245,0.0134977,0.04756176,-0.03082884,0.04731689,0.01931354,0.07280493,-0.05635003,-0.19157924,-0.01463421,0.05180499,0.00073211,0.00965551,0.017233,0.03128442,0.02966881,0.04575401,0.10728891,0.08439603,0.06787226,-0.04384758,0.01086058,0.01568715,0.05127285,0.03735258,0.0070697,0.02340493,0.01275175,-0.05159408,0.00430156,-0.05294136,-0.0555831,0.07056575,-0.04508524,0.10152738,-0.00274252,-0.01692943,0.01363656,0.07093284,0.01512187,-0.02354183,-0.10972525,0.0630542,0.0529513,0.00079206,-0.02537952,-0.03583235,-0.0370766,-0.00790048,0.0031864,0.03015689,-0.10724927,0.01542237,-0.06944267,-0.02723408,-0.01134498,-0.03722088,0.04607086,0.00664658,0.03417903,0.0273227,0.11998438,0.0260747,-0.04085196,0.00906974,-0.04795525,0.00698902,0.02263296,-0.0171491,-0.00007683,-0.024469,0.00970185,0.03230619,-0.02413188,-0.00709074,0.0257742,0.04127814,-0.03261696,-0.03666863,0.10876524,0.03612432,-0.00370855,0.0573869,0.01706409,-0.07950089,-0.08568661,0.04691597,0.00114887,0.0021164,-0.00995322,0.05569375,0.05148913,0.0004122,0.05213619,0.02391931,-0.00146951,0.06586959,-0.00613807,-0.03124728,-0.06044084,-0.04764352,-0.02774003,0.0659152,0.01785206,-0.29349777,-0.05710163,-0.01889106,-0.03671228,0.0501407,0.04257533,0.04436687,0.02723942,-0.05323899,0.02847416,-0.00358253,0.02773494,0.00867507,-0.03837765,0.01990789,-0.01043441,0.06551963,-0.03593756,0.03119443,0.06113159,-0.00486351,0.04823207,0.22203332,-0.01710562,0.03764181,0.02167473,-0.06919546,0.03735312,0.05889059,-0.01136555,-0.02828613,-0.00471073,0.03427869,-0.03461895,0.00586392,0.03465444,0.00000414,-0.0125898,-0.02078863,0.01337606,-0.03615776,-0.00767947,-0.00681961,0.031588,0.1218565,-0.0225845,-0.04370172,-0.07951341,0.00353925,0.04973497,-0.06777443,-0.02413242,-0.00753031,0.03796973,-0.00499452,0.06444491,0.03385325,-0.05503669,-0.05315157,-0.0476274,-0.0403326,-0.02352016,-0.08503756,0.04421538,0.00123169],"last_embed":{"hash":"1ykxskm","tokens":214}}},"text":null,"length":0,"last_read":{"hash":"1ykxskm","at":1751426530035},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{12}","lines":[304,304],"size":275,"outlinks":[{"title":"https://tracxn.com/d/companies/zorp/\\_\\_VTLVrft6NMSWw6R9NmmNgOwSmC1kn-XXhp2K\\_MTFXxM","target":"https://tracxn.com/d/companies/zorp/__VTLVrft6NMSWw6R9NmmNgOwSmC1kn-XXhp2K_MTFXxM","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1ykxskm","at":1751426530035}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{15}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04061289,-0.03059349,-0.01533372,-0.03096548,0.04042602,0.03117406,-0.0034862,0.00123413,0.00550325,-0.00963703,0.06747299,-0.06949096,0.02001741,0.07640486,0.01173401,-0.03636981,0.00354691,-0.00163614,-0.02402953,0.02239909,0.1420216,-0.05210749,0.01688393,-0.00764102,0.00278224,0.08992035,-0.01300882,-0.03842641,-0.06959619,-0.19199133,-0.02924781,-0.07674032,-0.03360963,-0.00197856,0.00018663,-0.05583309,-0.02606499,0.0423414,-0.0693206,0.04325409,0.02482511,0.0105929,0.03704743,0.00920875,0.04167468,-0.08074276,0.02474233,-0.06660108,-0.00729012,-0.05443189,0.00417069,-0.04505777,-0.01571284,0.0344476,-0.01181463,0.06498253,-0.00093774,0.04162116,0.03704324,0.0248365,0.1154488,0.05291485,-0.22906752,0.0186711,0.06770324,0.04300075,-0.02764243,0.05248724,0.04119189,0.05702036,-0.00274362,0.01590978,0.0146253,0.03406305,0.0130187,0.01615733,0.01121087,0.02448263,-0.03526303,-0.06178105,-0.0618329,0.05834754,0.03037189,-0.01299734,-0.01539107,-0.02879868,-0.01032585,-0.02755712,-0.04956529,0.03530888,-0.00696016,0.01660445,0.09532163,0.00509954,-0.05786909,-0.02330169,-0.00390459,0.00986527,-0.06242479,0.11700384,-0.07666273,0.05307126,-0.00386099,-0.05770745,0.03371689,-0.03247271,-0.02348925,-0.04435984,0.028014,-0.00328998,-0.00414189,-0.00798755,0.06394945,-0.02606284,0.04956613,0.02829872,0.01056823,0.0188706,-0.03767467,0.02343179,0.01257391,0.03172484,0.03287756,-0.01289263,-0.0579487,-0.02741953,0.03791115,0.03055693,0.08013643,0.01329693,0.01801541,0.03803115,-0.08991902,-0.00098703,-0.04410351,-0.0397507,0.02084285,-0.02700265,0.00725979,-0.0380184,0.00855626,-0.12178464,-0.03833257,-0.09814607,-0.05530955,0.09249167,0.00667603,-0.02509994,0.00267116,0.01787228,0.02042753,0.09240964,-0.01009723,-0.05815743,-0.02747717,0.03676051,0.0522232,0.08598228,-0.03632051,-0.00870846,-0.03558367,-0.04111492,-0.0220299,0.11717828,0.04885514,-0.09609827,0.03693658,0.03675094,-0.02319153,-0.09052482,-0.0211798,-0.00661547,-0.0140472,0.00903147,0.10780862,-0.03019987,-0.04715853,-0.03066773,0.03063207,0.04963082,0.00634459,-0.00449776,-0.07141848,0.01347542,-0.01746832,-0.0100141,-0.0148161,0.01996921,0.05539214,0.05784843,-0.04946159,-0.01290442,0.00965607,0.00567989,-0.00713557,-0.02256134,-0.04707439,-0.03625836,0.04928254,-0.07668149,0.10103162,0.04315095,0.00308952,0.01135214,-0.06136127,0.02265529,-0.02787637,-0.03781936,-0.00436383,0.02481275,-0.00573717,-0.00152577,-0.0427698,0.04736357,-0.03309269,0.01131842,0.00116586,0.02407503,-0.05246727,0.02090812,0.01517153,0.00166387,-0.0423334,-0.19517998,-0.04851463,0.03758971,-0.01999432,0.00714324,-0.01967808,0.05170316,0.00476672,0.02347889,0.10284527,0.07438745,0.01008231,-0.02346398,0.02425721,0.04011771,0.099746,0.0406426,-0.02157559,0.02456091,0.02426079,-0.03904612,0.01651029,-0.01228747,-0.05806043,0.04274235,-0.08527602,0.11403569,-0.0008701,-0.00208947,-0.00421472,0.06209265,0.03422386,-0.02682992,-0.12815437,0.07522295,0.06257095,-0.0118476,-0.0311838,-0.06749664,-0.0639677,0.00305358,0.04313629,0.03240434,-0.09658234,0.02690867,-0.05424632,-0.01473324,-0.04353492,0.01014449,0.02633003,0.00393872,0.00294539,0.04719043,0.0868431,0.02127983,0.00596885,0.00331764,-0.0421532,-0.00802357,0.04157123,-0.03777486,-0.01708494,-0.02020546,-0.00151463,0.00960156,-0.01411389,-0.04550331,0.01336227,0.02342962,-0.03041658,-0.03934023,0.1178523,0.02764055,-0.05683342,0.03308057,0.04186322,-0.05569978,-0.07539707,0.02950139,-0.00900904,0.01695212,0.00217427,0.0523505,0.04205543,0.01154484,0.06818479,-0.01484085,-0.0180953,0.01830168,-0.00232032,-0.00890126,-0.02619357,-0.03678972,-0.06429973,0.08455044,0.04119948,-0.29254305,-0.01030929,0.02698234,-0.01460028,0.05789056,0.05120518,0.03840424,0.06432153,-0.0181478,0.01225442,0.0250553,0.04025305,0.00640707,-0.03437285,-0.00587592,-0.03783303,0.04776998,-0.00312271,0.08234193,0.048963,0.00651213,0.04135096,0.18445623,0.01084872,0.03930688,0.01648519,-0.0782295,0.04015987,0.04474332,-0.00029793,-0.06698895,-0.00684678,0.0061596,-0.03436821,0.03709213,0.02645105,0.01722362,0.00541947,-0.0091634,0.01106891,-0.02413257,0.00588256,0.00253581,0.00107704,0.11714537,0.01197179,-0.07982036,-0.07954529,-0.01615257,0.054808,-0.04789888,-0.03056887,-0.03445039,0.03623281,-0.04912439,0.07048907,0.02785492,-0.05091672,-0.03950849,-0.00223094,-0.01282991,-0.03373281,-0.04471309,0.06226084,0.02862285],"last_embed":{"hash":"io4rw0","tokens":161}}},"text":null,"length":0,"last_read":{"hash":"io4rw0","at":1751426530043},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{15}","lines":[307,307],"size":229,"outlinks":[{"title":"https://en.wikipedia.org/wiki/End\\_of\\_the\\_World\\_(Parks\\_and\\_Recreation)","target":"https://en.wikipedia.org/wiki/End_of_the_World_\\(Parks_and_Recreation\\","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"io4rw0","at":1751426530043}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{18}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02314271,-0.03074038,-0.06074701,-0.01848942,0.07168865,0.00342698,-0.05444006,0.02968504,-0.03851395,0.01905944,0.06505867,-0.06683573,0.03090626,0.03134111,0.03059748,-0.02532836,-0.00504382,-0.05229365,-0.02489453,0.01517319,0.14212,-0.06643205,0.02425024,0.00753562,0.08322805,0.09180607,0.00624201,-0.02901588,-0.03065022,-0.18189153,-0.0080621,-0.0341179,0.00520471,-0.00353267,0.03050883,-0.04352673,-0.04559346,0.05749903,-0.03203186,0.01311977,0.01920013,-0.00481425,0.01680634,-0.00901961,0.06770001,-0.07626856,0.00432419,-0.05137742,0.01683008,-0.04072193,-0.00772477,-0.08510956,-0.00220058,0.03374916,0.01759786,0.04206263,0.00908663,0.0326287,0.04466424,0.05418516,0.12946181,0.05971272,-0.21720223,0.01271309,0.08233421,0.05834378,-0.01906913,0.01251277,0.04444061,0.07078411,0.02032498,0.01954888,-0.01916563,0.03139762,0.02561084,-0.00348239,0.03615716,0.02547476,-0.01991825,-0.09190343,-0.01697363,0.08665242,-0.02231371,-0.01668288,-0.01639138,-0.03569742,-0.01804477,-0.00997806,-0.03757756,0.02612682,0.00029288,0.00100847,0.06360029,0.00797338,-0.06098111,-0.0136072,-0.0209503,0.03575912,-0.0911567,0.12477051,-0.0362098,0.0514317,0.01438248,-0.03629437,0.02769463,-0.00909394,-0.01861846,-0.04232305,0.04697826,0.03294883,0.0085723,-0.00040824,0.03124836,-0.06992281,0.0173468,-0.01522515,-0.03470418,-0.00792731,-0.0359909,0.00395518,-0.01487298,0.02851856,0.04649257,-0.02158188,-0.06142765,-0.01746409,0.03437693,0.03935699,0.06999455,0.06780539,0.05021145,0.0350293,-0.10606583,0.00072568,-0.041594,-0.03936925,0.00957841,-0.00778224,-0.02307534,-0.03993711,-0.03016794,-0.11457051,-0.03711248,-0.09020065,-0.08912978,0.07513997,-0.00307182,0.00421077,0.01938745,-0.02288575,0.0143201,0.08070613,-0.00155496,-0.08230122,-0.00976211,-0.00990387,0.03214919,0.07559814,-0.058545,0.01529076,-0.03110739,-0.03739026,-0.04767895,0.14491281,0.03071185,-0.13942033,0.01122625,0.04542647,-0.0070096,-0.06835207,-0.00289647,0.01301873,-0.02298334,-0.02443783,0.07546137,-0.03064374,-0.03188321,-0.04744707,-0.00995105,0.05251588,0.00563164,-0.01093829,-0.07981284,0.00730265,-0.01128396,-0.05495774,-0.02830789,0.03444359,0.00321385,0.027389,-0.06746984,0.01425481,-0.03966849,0.02007913,-0.00095766,0.00127257,0.00254338,-0.02715446,-0.00111296,-0.03234199,0.07153983,0.03524445,0.01437019,0.03209524,-0.03594559,0.02974974,-0.05207624,-0.0205856,-0.01666481,0.01905124,-0.03981623,0.01936013,0.00683718,0.05639912,-0.06421648,0.03027271,0.03079339,0.04349674,-0.0221338,0.05360762,0.00053218,0.02371888,-0.03625288,-0.20347324,-0.04005463,0.05330626,-0.02688232,0.00042659,-0.01809019,0.05519644,0.02269339,0.01954827,0.07618154,0.10448224,0.04765334,-0.02078678,0.01038419,-0.0016919,0.04563553,0.05235241,-0.00466199,0.018653,0.01440077,-0.02496717,0.01482919,-0.01317533,-0.06986029,0.0302467,-0.04856744,0.10434078,-0.02389855,-0.02590138,0.01174192,0.0743255,0.00014661,-0.02976204,-0.14035423,0.0575087,0.01676547,-0.00181536,-0.00424059,-0.08646511,-0.02760741,0.00802577,0.00559164,0.04915005,-0.09667984,0.03145564,-0.08478746,-0.04074877,-0.01284623,0.00151617,0.03194131,-0.02314429,0.02301593,0.03036382,0.1107442,0.00535436,0.00122461,0.02817085,-0.03379367,0.00491876,0.01726661,-0.00093357,0.00897864,-0.00922639,-0.00053876,0.03621868,-0.02239045,-0.03550094,-0.01354982,0.04292143,-0.03460221,-0.01641674,0.11550886,0.05022981,-0.00314473,0.04572294,0.06036053,-0.04905757,-0.10224015,0.03830236,-0.00392534,0.0243435,-0.00109505,0.04722838,0.05314103,0.00527627,0.05737657,0.00773923,-0.0174589,0.03209261,-0.01373428,0.00528752,-0.05804945,-0.03159967,-0.03888628,0.06298384,0.02606069,-0.27689424,-0.02659768,0.01664158,-0.03524252,0.05516955,0.02178605,0.03398923,0.01650818,-0.06060527,0.03215676,0.00393412,0.05268751,-0.00788728,-0.04657165,0.008177,-0.018505,0.07156907,-0.00457372,0.06041018,0.00633361,0.01237725,0.05297587,0.20306826,-0.01383751,0.00166603,0.02130235,-0.08302384,0.03885877,0.05256129,0.02851653,-0.05260208,-0.00752451,0.04472836,-0.02343877,0.01821285,0.04630541,0.01338225,-0.00813138,-0.00928693,0.01803738,-0.05968943,0.00118557,-0.00966216,0.01215998,0.09948894,-0.0079689,-0.06126478,-0.07874256,0.00011564,0.03185175,-0.05130202,0.0109216,-0.01231369,0.03506002,-0.02155392,0.07837199,0.04800883,-0.0161898,-0.04215479,-0.04190888,-0.04143459,-0.0395741,-0.0459827,0.0818269,0.02252401],"last_embed":{"hash":"1ww9bbp","tokens":158}}},"text":null,"length":0,"last_read":{"hash":"1ww9bbp","at":1751426530050},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{18}","lines":[310,310],"size":223,"outlinks":[{"title":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","target":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1ww9bbp","at":1751426530050}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{20}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01204828,-0.05408837,-0.07646776,0.00603398,0.05563958,0.00150904,-0.00603834,0.02003759,-0.00317831,0.01140583,0.07077594,-0.07761997,0.05118426,0.03201205,0.04598795,-0.00906818,0.00186651,-0.054448,-0.03374892,0.04215168,0.13790275,-0.05626873,0.02318814,-0.03736338,0.08166434,0.04310609,-0.00044922,0.01120754,-0.03841904,-0.16126864,-0.00655383,-0.0637496,0.01159991,0.01134662,0.01007791,-0.04181768,-0.02904227,0.02465007,-0.02122854,0.0118755,-0.02860144,0.01608814,-0.00362334,-0.00393095,0.03288899,-0.08547135,0.00508907,-0.0496207,0.01244262,-0.02950328,-0.01279885,-0.05046812,-0.00953361,0.0395759,0.0226788,0.00389913,0.00852827,0.0098926,0.06614641,0.02762539,0.09213709,0.04239193,-0.20707823,0.00899792,0.0603684,0.02376919,-0.00101319,0.01170585,0.02620459,0.07130436,0.0562783,0.0130644,0.00195556,0.00440633,0.0413947,-0.03096034,0.03145771,0.00349397,-0.02087837,-0.06488656,-0.04639466,0.08658724,-0.02421971,-0.01411446,-0.01740528,-0.02737173,0.02586689,-0.01884606,-0.02793511,0.01254682,-0.01891532,0.01413856,0.01261947,0.01585514,-0.02346976,0.02337188,0.01230483,0.04578091,-0.0810332,0.10378778,0.0040753,0.0734356,-0.00294185,-0.08472271,0.01937837,-0.01869746,-0.05747063,-0.04510539,0.01349028,0.04160702,0.0088677,0.0169126,0.06363318,-0.08633239,0.00932921,-0.02883645,-0.02224657,-0.00859802,0.01726619,0.03120671,0.00541024,0.01623397,0.07222556,0.01766683,-0.02375182,-0.00323471,0.02405939,0.06140895,0.02031191,0.0627732,0.04813984,0.00727596,-0.10036227,-0.002339,-0.01388881,-0.0377971,-0.00089428,0.00839167,-0.02648169,-0.03312576,-0.05673132,-0.07198628,-0.01837113,-0.10903375,-0.04471763,0.07643449,0.02344677,0.01599046,-0.0080704,-0.03228128,-0.03037317,0.05819348,-0.04837926,-0.09237523,-0.03016841,0.02876704,0.05697434,0.08251066,-0.06956296,-0.0221338,-0.02532244,-0.03664567,-0.06457209,0.13439545,0.01905163,-0.11308339,0.01508619,0.03097202,-0.02693807,-0.05571668,-0.01174774,0.033453,-0.05676905,-0.0171784,0.108405,-0.0161956,-0.01354092,-0.05057868,-0.0022644,0.02933264,0.00050336,-0.04057783,-0.08249673,-0.01862249,-0.03454913,-0.03257038,-0.04090208,0.04902173,0.01362472,0.0315746,-0.04881242,0.00683386,-0.03321256,0.04822032,-0.03796542,0.01181163,-0.04045203,-0.00453998,0.01840182,-0.05009581,0.13997255,0.04100455,0.01400314,0.02544303,-0.04279467,0.03522448,-0.00551413,-0.02021845,0.00672786,0.03987545,0.01250083,0.05421655,-0.03103842,0.01910632,-0.07725198,0.01513311,0.00740866,0.04806333,-0.01179851,0.02157013,0.00995947,0.00161281,-0.08364526,-0.2078532,-0.03556797,0.05523862,-0.01757453,0.06277518,-0.00998713,0.06588016,0.02052185,0.07053824,0.09578957,0.06645302,0.03777296,-0.03726049,-0.00445698,0.01282381,0.04925343,0.03932854,-0.03347426,0.03760151,0.03555848,-0.0653294,0.01347292,-0.027635,-0.09228,0.08132949,-0.03860776,0.09639328,-0.03717824,0.00626779,-0.02023336,0.07178061,-0.01323698,-0.02757825,-0.09281752,0.04288519,0.05762242,0.02552681,-0.00750116,-0.08870962,-0.04135036,-0.00398809,0.00825789,0.07042124,-0.09769677,0.01248684,-0.04385195,0.00658413,0.03441004,-0.02173249,0.02605298,-0.00623302,0.03112988,0.03511996,0.07918558,0.01102022,-0.04022584,0.0298108,-0.07156695,0.00361109,0.05806387,-0.0005226,-0.00364125,-0.02708735,0.01497564,0.03124396,-0.0164427,0.01438385,0.01273744,0.00739168,-0.0171,-0.02053863,0.1019044,0.02169046,-0.00195813,0.0589461,0.04683628,-0.05790422,-0.07276813,0.02614731,0.04230084,0.01677073,0.01466574,0.02624021,0.06833719,-0.01930916,0.09218584,0.02829784,-0.02716338,0.00326194,-0.00557668,-0.03524968,-0.04523559,-0.04701753,-0.03899043,0.02349808,0.02397204,-0.2940608,-0.03996504,0.01147045,-0.00691172,0.06443545,0.01701647,0.03561854,0.06268569,-0.11304134,0.00355306,-0.0276464,0.05297554,0.01164846,-0.03170963,-0.02995739,-0.03000011,0.0443594,-0.01606074,0.02079692,0.0473998,-0.00142033,0.06420691,0.21766414,0.01260693,0.00529585,0.02607883,-0.06407033,0.06803353,0.09297414,-0.00663724,-0.03770347,-0.02302426,0.01848478,-0.04985214,0.03572102,0.05839654,-0.00117923,-0.00733457,-0.03939152,0.04242836,-0.0153827,0.00813983,-0.00245295,0.02990425,0.10222274,-0.01102462,-0.06534026,-0.07376309,-0.00955466,0.02466059,-0.05208085,-0.03604526,0.02989839,0.00976204,-0.00496702,0.07403528,0.04606475,-0.02626782,-0.05865071,-0.04585385,-0.02614042,-0.02080947,-0.0658306,0.01699826,0.00761685],"last_embed":{"hash":"3bxftn","tokens":142}}},"text":null,"length":0,"last_read":{"hash":"3bxftn","at":1751426530057},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{20}","lines":[312,312],"size":200,"outlinks":[{"title":"https://cryptorank.io/price/nockchain","target":"https://cryptorank.io/price/nockchain","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"3bxftn","at":1751426530057}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{21}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07188755,-0.01674904,-0.06915667,-0.0453266,0.00801588,0.00201137,-0.0000915,0.01671396,-0.03398109,-0.03774068,0.07259012,-0.03158142,0.03675209,0.04088448,0.01022814,-0.01738989,-0.01175124,-0.0072861,0.03687001,0.01359104,0.0840598,-0.08609267,0.02702575,-0.00805013,0.07454888,0.06950431,0.0025387,-0.0007828,-0.02276678,-0.1991685,0.0210269,-0.05968611,-0.03582942,0.01827147,0.00032984,-0.00421872,-0.0094632,0.05773062,-0.04725412,0.00922312,0.0039805,0.04666539,0.01692652,0.02411744,0.05725286,-0.11036413,0.03243417,-0.05420894,-0.0081605,-0.01396705,0.01090849,-0.06187331,-0.02172912,0.00749517,-0.01167715,0.03111153,0.03823053,0.04171547,0.05579175,0.05745312,0.08826909,0.02972157,-0.15317288,0.00870227,0.10195146,0.03800598,-0.06330527,0.00282069,0.04994039,0.07805846,0.02172909,0.00999856,-0.00074479,0.08704948,0.00125611,-0.02810735,0.06160934,0.0129129,-0.02249278,-0.05335265,-0.04035791,0.08266474,0.00434582,-0.00384257,-0.02008929,-0.02137758,0.01727473,-0.04490691,-0.00187934,-0.01827699,0.00818197,0.04435427,0.12445778,-0.01229533,-0.05731741,0.0273937,0.00683389,0.02998337,-0.09153313,0.11663827,-0.01342172,0.04407751,-0.01784074,-0.03385063,0.02597052,-0.01018385,-0.00032637,-0.05435265,-0.0016102,0.03080377,0.00450525,0.00760538,0.05269955,-0.06073898,0.03683011,-0.03262314,-0.01525424,0.0062524,-0.01830766,0.0357214,0.0338545,0.04824745,0.06450143,0.01256689,-0.0125333,-0.01054936,0.01351843,0.02175486,0.02724897,0.00668844,0.03530382,0.04207516,-0.07971051,0.01432962,-0.01684543,-0.01178165,-0.03923421,0.00449474,-0.03987764,-0.04897038,-0.06275272,-0.07027368,0.00494179,-0.09425175,-0.03808868,0.07289823,-0.00054691,0.01796738,0.00493412,-0.02897002,0.00225573,0.05373656,-0.02548853,-0.0405894,0.02503404,0.0150916,0.02289809,0.07135981,-0.03238266,0.02746079,-0.04870892,-0.04293631,-0.03037356,0.14438906,0.01262719,-0.13778682,0.02968273,0.0351814,-0.03155688,-0.06137096,0.01302122,0.02467121,-0.05401913,-0.03529458,0.07165761,-0.04720979,-0.03716985,-0.02880209,0.02932495,0.06092741,0.00068057,0.02345243,-0.07685,-0.03884832,-0.01947172,-0.01648675,-0.05696115,-0.00750076,0.02826992,0.03970851,-0.09011815,0.0340102,-0.00693651,0.0097203,-0.01168489,-0.03201666,-0.0229341,-0.02693946,0.02270801,-0.08368886,0.11966191,0.05103834,0.03392333,0.02215601,-0.02823917,-0.00271359,-0.08798885,-0.04394465,-0.01774778,0.02143343,-0.04899644,0.02169578,0.02036036,0.06829329,-0.04548623,0.04244485,-0.01874007,0.02441046,-0.01343182,0.03919511,0.03815697,0.05536322,-0.07114082,-0.19942671,-0.00732715,0.04037732,-0.0340373,0.04895553,-0.01448409,0.05157641,0.00601656,0.0488255,0.06088271,0.07658805,0.0219238,-0.02513768,0.03016699,-0.00630595,0.05398054,0.0467535,-0.02224066,0.00007778,0.01689881,-0.03594495,-0.04005683,-0.01461691,-0.07981133,0.04201605,-0.03379327,0.08546516,-0.01944789,-0.00358553,-0.00283958,0.0690233,0.00841786,-0.02669513,-0.15163144,0.00160329,0.06938329,0.00193236,-0.03335422,-0.00238935,-0.02267957,-0.01032541,0.03673435,0.04592004,-0.13719077,0.04176942,-0.06595574,-0.0176425,-0.000961,-0.02210521,0.03028644,-0.0036526,0.01319572,0.0056426,0.12293168,-0.00897134,-0.0262563,0.03294929,-0.04442884,0.00277171,0.03614665,-0.01314784,-0.01446945,-0.0399147,0.00390582,0.06498225,-0.01198346,0.00344198,0.01147129,0.06987905,-0.07282514,-0.02289859,0.12368639,-0.01111408,-0.00856937,0.03922755,0.01380763,-0.06269538,-0.10180622,0.05120363,-0.00528225,0.00496586,0.01143481,0.0693503,0.04264494,-0.01418614,0.06956232,0.04569253,-0.03107109,0.02930737,-0.00962208,0.01251534,-0.03812153,-0.05241311,-0.02410868,0.06443481,-0.00275778,-0.29644266,-0.03278179,-0.0128748,-0.00732439,0.02997452,0.04696317,0.0506923,0.01830811,-0.06851905,0.03493515,-0.04436564,0.01753466,0.02207114,-0.04740614,0.03810502,0.01551954,0.05790462,-0.00983236,0.00925189,-0.01587801,0.02841978,0.06641156,0.22397126,-0.03800467,0.03989339,0.02843958,-0.02244854,0.03696791,0.06829894,-0.00118429,-0.03912159,-0.0039874,0.04468234,-0.03673179,-0.00479013,0.03918472,0.00048816,0.00707101,-0.00812449,0.00561629,-0.06231768,-0.05032544,0.00711498,0.0281458,0.10868666,-0.06110784,-0.04905209,-0.06784283,-0.02644418,0.04398482,-0.06185212,-0.04604984,0.01331286,-0.00181642,-0.01319806,0.04812279,0.04161831,-0.03362315,-0.07687993,-0.01904814,-0.01950109,-0.00574481,-0.0089848,0.045501,-0.00001721],"last_embed":{"hash":"si5oor","tokens":149}}},"text":null,"length":0,"last_read":{"hash":"si5oor","at":1751426530064},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{21}","lines":[313,313],"size":204,"outlinks":[{"title":"https://github.com/zorp-corp/nockapp","target":"https://github.com/zorp-corp/nockapp","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"si5oor","at":1751426530064}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{22}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06450942,-0.01489626,-0.07266197,0.01720747,0.03071558,0.00198117,-0.02374857,0.02813143,-0.05046521,-0.00443802,0.02916444,-0.01571468,-0.00599018,0.00430648,0.00702919,-0.00560526,-0.01208098,-0.01572485,-0.02028069,0.00334152,0.07595124,-0.06376614,0.03116993,-0.03918161,0.11352339,0.0554166,0.01791852,-0.03860005,-0.01645366,-0.20473415,-0.01577414,-0.06410635,0.01131029,0.00132231,0.05351271,-0.06129455,-0.03537075,0.06300859,-0.10485209,0.00359285,0.03668787,0.00707733,0.02184356,-0.02768346,0.0481109,-0.07203443,0.02502011,-0.04014806,-0.05162229,-0.01101419,0.0125592,-0.05781968,-0.02868577,0.01241076,0.02928728,0.0205986,0.02191571,0.05360121,0.04412943,0.02219556,0.08551541,0.03325617,-0.20706454,0.01906627,0.08807096,0.046245,-0.03742193,0.05109886,0.03632785,0.09993532,0.03278535,0.00775112,-0.01581538,0.03033726,0.01029385,0.05074833,0.03144158,-0.02051544,-0.04765186,-0.07516875,-0.07899602,0.03823779,-0.02249631,-0.02091631,-0.05642434,0.00023984,0.00072236,-0.0344935,0.01029945,-0.00440745,-0.0012428,0.02806982,0.06354458,0.02081268,-0.02903808,-0.02215949,0.03009308,0.03558157,-0.07532908,0.11292757,-0.03714109,0.05764653,-0.01933195,-0.06494825,0.04838767,-0.02402993,-0.01073836,-0.02479873,0.00862777,0.01276468,0.03835473,0.01262278,0.06711487,-0.04335756,0.03830358,0.01927074,0.0748582,0.01798704,0.023384,0.01216697,0.01417733,0.0674279,0.01702848,-0.01969552,-0.07330246,-0.02775758,0.05183985,0.02905084,0.06845032,-0.00083462,0.06360171,0.00729617,-0.04816438,0.02769563,-0.02737751,-0.02613305,0.02700243,0.02902544,-0.02216717,0.02739326,0.0221426,-0.06449581,-0.02911845,-0.1173333,-0.03053936,0.05042268,-0.00215313,-0.01736021,0.0229485,-0.06619664,0.01031455,0.03392679,-0.01346029,-0.05953171,0.03150403,0.03900784,0.06128631,0.07855768,-0.04970979,-0.00888482,-0.06059812,0.01327127,-0.06460351,0.12897362,0.06540262,-0.11919044,0.0300145,0.03944429,-0.01275859,-0.08074622,0.03089251,-0.00769824,-0.07667167,-0.02327364,0.05917292,-0.07398509,-0.03397439,-0.0008647,0.01516313,0.05101522,0.01873968,-0.02140043,-0.06746481,-0.00879135,0.00928425,-0.03194179,0.00414943,0.01118631,0.05606903,0.02061503,-0.06389471,0.0259602,-0.04164851,0.02519755,-0.01959133,-0.04748768,-0.03783781,-0.06098897,0.01836088,-0.06630542,0.08873021,0.03772757,0.01366951,-0.01320642,-0.04223097,0.00524368,-0.03843861,-0.04229834,0.00883479,0.03158642,-0.0291213,-0.02173699,-0.05359114,0.02291223,0.00058393,0.00746362,0.03869133,-0.00058114,-0.05418523,0.01057566,0.01435412,0.01057123,-0.0575537,-0.19967332,-0.08246026,-0.00585535,0.04850454,0.04833477,-0.01153538,0.03604951,0.01701754,0.03787178,0.08891899,0.09984545,0.04732958,-0.0523994,0.01937449,0.00201616,0.0124129,-0.00996249,-0.03524111,0.0482678,0.03960708,-0.00015981,0.02185377,-0.02396043,-0.07811876,0.00640324,-0.0608323,0.12578923,0.02566133,0.00795858,-0.02323949,0.04074302,0.02523456,-0.04304186,-0.13517243,0.02918036,0.08878867,-0.00707559,-0.0350311,0.00500983,-0.02913778,-0.03937116,0.05542435,0.01710939,-0.13193986,0.03996932,-0.04453424,-0.06558006,0.01388439,-0.03481049,0.03575845,-0.00239415,0.06762628,0.0608649,0.08022996,0.00266408,-0.0374913,0.01739077,-0.0453274,0.01366242,0.04531902,-0.02598058,0.00595191,-0.05313709,0.01873625,0.06048578,0.02229325,-0.01219126,0.01724166,0.04774959,0.00172211,-0.01934655,0.1166474,0.06100095,-0.02233383,0.0176455,0.01939223,-0.02155435,-0.08588339,0.02555119,-0.00035552,0.02613879,-0.03343994,0.0469926,0.04673235,0.01724411,0.10339296,0.01903766,0.02179948,0.03825753,0.01767868,-0.02736503,-0.00948735,-0.05946414,-0.06786282,0.05172313,0.01233009,-0.26519537,-0.00812112,-0.02541654,-0.00705909,0.00404793,0.00325206,0.06597288,-0.00683425,-0.06459054,0.02234496,-0.02552041,0.05273904,0.0007431,-0.02083044,0.01543316,0.0070605,0.07560328,-0.00577208,0.05242668,0.03407232,0.01342797,0.08699535,0.1863618,-0.04231077,0.06632245,0.03680725,-0.04227652,0.00245432,0.04553377,0.00304433,-0.00384952,-0.02280413,0.02547818,-0.05464257,-0.00290546,0.06587027,-0.06441092,0.00114254,-0.00698447,-0.01511468,-0.05267892,-0.05104147,-0.0143547,0.0426086,0.11942322,-0.03209918,-0.05319853,-0.09730264,0.00109159,0.02030863,-0.04589046,-0.0331674,-0.00197779,-0.01707614,-0.0339113,0.0522056,0.05295375,-0.01605368,-0.05808763,-0.02447914,-0.00636489,0.01123213,-0.04896814,0.05669619,0.02847632],"last_embed":{"hash":"104hddn","tokens":210}}},"text":null,"length":0,"last_read":{"hash":"104hddn","at":1751426530071},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{22}","lines":[314,314],"size":377,"outlinks":[{"title":"https://www.everand.com/podcast/864012011/Logan-Allen-Nockchain-s-Global-Competition-for-Useful-ZK-Proof-of-Work-Miners-Kicks-Off","target":"https://www.everand.com/podcast/864012011/Logan-Allen-Nockchain-s-Global-Competition-for-Useful-ZK-Proof-of-Work-Miners-Kicks-Off","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"104hddn","at":1751426530071}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{23}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06400808,-0.0478714,-0.03988094,0.00528478,0.0212668,0.02995679,0.02195389,0.02248463,-0.0081807,0.01701268,0.05509772,-0.0541339,0.03753782,0.03128203,0.02431384,-0.02646421,-0.01059998,-0.05909505,0.00394726,0.04539425,0.13754588,-0.08624589,0.02035548,0.00576522,0.05320166,0.08076572,0.03701829,-0.02850912,-0.03737619,-0.16750619,0.04004591,-0.06168624,0.00271663,-0.01419129,0.01249921,0.00233899,-0.0288263,0.03604709,-0.04816118,0.0317678,-0.00351163,-0.00677087,0.03594564,0.00176971,0.01887072,-0.07526499,0.03780809,-0.03039315,-0.01549857,-0.08554816,-0.03267098,-0.06787512,0.00963235,0.04784629,-0.0016465,0.00259445,0.02043605,0.06689201,0.06420222,0.02267045,0.09892553,0.03563802,-0.19835466,0.01310657,0.08314001,0.0331654,-0.0475428,0.03196823,0.03665689,0.07305593,0.05341385,0.01595221,-0.0191107,0.07010198,-0.01481818,-0.00481209,0.01690783,0.02136878,-0.01370087,-0.05040127,-0.07827347,0.03456287,0.01438477,-0.01295586,-0.01960302,-0.02146449,-0.02509307,-0.00835801,-0.04538054,0.00681288,-0.00506928,0.00856587,0.06022232,-0.00315957,-0.01568714,-0.02398087,-0.00677753,0.01023312,-0.11611389,0.11759488,-0.01999313,0.02573342,-0.00831503,-0.03504111,0.02969975,-0.02127972,-0.0326323,-0.05234739,0.0275429,0.02268798,0.00160679,0.01700421,0.08245578,-0.07862603,0.02986865,0.01566622,0.01293114,-0.00103718,0.0180905,-0.00216993,0.00173899,0.03977988,0.06206886,-0.03252096,-0.03150164,-0.025166,-0.00201163,0.02932994,0.03799529,0.08906309,0.03037484,0.02289325,-0.07455761,0.04937984,-0.01340028,-0.04021299,0.00753808,0.00217734,-0.01599603,-0.04981791,-0.01776403,-0.08191473,-0.01219459,-0.11198481,-0.0430801,0.09136621,0.02834826,0.0201259,0.00849089,-0.01579398,-0.00518635,0.07040531,-0.03305755,-0.08648065,-0.02645073,0.01411221,0.05172565,0.07487754,-0.0799368,0.00875211,-0.01855971,-0.04204088,-0.06631778,0.19020167,0.05266513,-0.11739776,0.00988443,0.02440115,-0.00513041,-0.07172136,-0.01448505,0.01943349,-0.03551554,0.00709318,0.06250131,-0.03234219,-0.01323498,-0.02937316,-0.01061063,0.02373679,-0.00679225,-0.07820591,-0.05740461,-0.01667685,-0.03175341,-0.02948551,-0.02698272,0.00442414,0.03247748,0.01568024,-0.09332039,0.03942825,0.01060435,0.05799755,-0.01614422,-0.01826032,0.00748385,-0.01380778,-0.03952179,-0.044029,0.08584785,0.06330457,-0.02577684,0.0158526,-0.08932686,0.01480866,-0.01724683,-0.01506686,0.03211839,0.00357995,-0.02288168,0.05813426,-0.01830633,0.03530081,-0.03567316,-0.0035875,0.01588082,0.03778186,-0.04458444,0.006594,0.00811542,0.04497699,-0.07285205,-0.20165411,-0.03462981,0.04092048,-0.01846539,0.00492364,0.01815942,0.05910262,0.0211886,0.05083353,0.08694123,0.09733837,0.02618253,-0.0575343,-0.01724852,0.03273228,0.05458239,0.04220213,-0.01181153,0.01347272,0.05146119,-0.02219762,0.00593841,-0.00286173,-0.0927351,0.04527805,-0.04146011,0.09868958,0.0206467,0.00809521,0.00805197,0.06502115,0.03591808,-0.0275921,-0.16316783,0.03984326,0.02750481,0.02829358,0.01073778,-0.02846985,-0.03219412,-0.04741891,0.03602251,0.01955898,-0.072588,0.01418604,-0.05363328,-0.02710793,-0.00825434,0.00650915,0.03392081,0.0115846,0.03752424,0.0028765,0.11736096,0.00695266,0.00002816,-0.00074241,-0.06103365,0.01350956,0.03190288,-0.01393566,0.01979585,-0.03663798,-0.01990073,0.03631911,-0.00277247,-0.02777497,0.03460996,0.02403214,-0.0188747,-0.03671531,0.1369656,0.04134737,-0.03928546,0.02184807,0.00878757,-0.06110606,-0.1000501,0.04411547,-0.01800291,0.00083936,-0.00593873,0.01994022,0.01798133,-0.001013,0.05538696,0.01063442,-0.04394377,0.03202807,0.00775041,-0.00876121,-0.0488039,-0.07096248,-0.05509534,0.06078599,0.04216224,-0.27944246,-0.02762569,0.01260701,0.00416508,0.05130753,0.02153189,0.07483814,0.01043149,-0.08764381,0.01371521,-0.01063308,0.03350122,0.00255815,-0.04487264,-0.02489311,0.0007173,0.04085107,0.00645489,-0.0034879,0.03165495,0.00897827,0.07206017,0.19798429,0.00802742,0.04873943,0.02878284,-0.04252297,0.02218831,0.05368609,0.0419452,-0.00635952,-0.0059711,0.01199582,-0.02137088,0.01616357,0.06392866,-0.00201734,0.02543913,-0.0047522,0.00509199,-0.04957911,-0.03484218,-0.00403537,0.02452736,0.12734473,-0.00964156,-0.0853964,-0.09415486,0.01647056,0.05963534,-0.0715555,-0.02308232,0.00869149,0.03260408,-0.01357319,0.0667505,0.00370494,-0.03429837,-0.09817535,-0.01256765,-0.01109248,0.02261644,-0.03211512,0.01948055,-0.00950321],"last_embed":{"hash":"19dt7qo","tokens":172}}},"text":null,"length":0,"last_read":{"hash":"19dt7qo","at":1751426530078},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{23}","lines":[315,315],"size":260,"outlinks":[{"title":"https://www.nervos.org/knowledge-base/what\\_are\\_blockchain\\_intents\\_(explainCKBot)","target":"https://www.nervos.org/knowledge-base/what_are_blockchain_intents_\\(explainCKBot\\","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"19dt7qo","at":1751426530078}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{24}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0619715,-0.05391788,-0.0522472,-0.03016655,-0.00836803,0.0295299,0.04370526,0.03757023,0.00868513,0.0058263,0.05581376,-0.03933594,0.03103665,0.02223507,0.01456825,-0.04913129,0.00791471,-0.03671421,-0.01382133,0.03168362,0.13494323,-0.07651577,0.02945498,0.01197413,0.05195745,0.07451631,0.03866814,-0.03837658,-0.04905585,-0.19062731,0.02758423,-0.03170317,-0.00051022,-0.02571819,0.00628842,-0.02029104,-0.05456501,0.06749141,-0.05515659,0.02764516,0.01954056,0.00046329,0.03226506,-0.0084905,-0.00688287,-0.11020601,0.03237432,-0.04525462,-0.00326044,-0.07899777,-0.02660679,-0.06353521,-0.00417458,0.04651754,0.0120071,0.00732924,0.03370653,0.072502,0.05837039,0.02486586,0.12236904,0.04540133,-0.19970444,0.03391551,0.07928238,0.04341919,-0.04399141,0.03039459,0.07112283,0.06523819,0.04804698,0.02396148,-0.03272328,0.06937003,0.01302106,-0.01592093,0.04737715,0.02644994,-0.01829708,-0.07208154,-0.05545256,0.05578043,0.00584275,-0.03347101,-0.02400349,-0.01708006,-0.0189764,-0.02581532,-0.01615746,-0.00131394,0.01018392,0.00759559,0.02969871,0.00220957,-0.0125875,-0.01090639,-0.00776097,-0.01168177,-0.10327189,0.12241691,-0.007684,0.04019711,-0.00434138,-0.0291867,0.02474277,-0.00188161,-0.021857,-0.04658348,0.03028152,0.01798413,0.02218582,-0.00575754,0.06862127,-0.06709863,0.00677955,0.02223353,0.01833961,-0.00960069,0.05507413,-0.01143588,-0.01614885,0.02468943,0.07304922,-0.00945281,-0.04139942,-0.03787307,0.00525544,0.03059526,0.04111328,0.07388241,0.05122145,0.05226428,-0.08234276,0.01047218,-0.01277136,-0.02499669,0.00448154,-0.01866645,-0.02669809,-0.04058056,-0.01386544,-0.05084552,0.00227009,-0.11636092,-0.04263803,0.11637457,0.02487723,0.02735843,0.00219236,-0.02436503,-0.00306495,0.07161883,-0.01862072,-0.07431819,-0.00602959,0.00599213,0.03265675,0.08605637,-0.08161613,-0.01103052,-0.02626421,-0.02566537,-0.07604931,0.17440696,0.04233707,-0.10470761,0.01896952,0.02602939,-0.00941406,-0.08062342,0.00532241,0.00601865,-0.04256583,0.01206637,0.06654507,-0.01924035,-0.03186552,-0.04018848,0.00234025,0.02260872,0.02745602,-0.0689092,-0.0437224,-0.02479422,-0.01943087,-0.04314123,-0.03097812,0.00914091,0.03023622,0.04504198,-0.08798257,0.0501251,0.00272514,0.04510128,-0.01895775,-0.02373768,-0.02279086,-0.03084406,-0.03603515,-0.05943801,0.09219848,0.03627957,-0.03038655,0.02815162,-0.07319435,0.01690488,-0.02263503,-0.03412345,0.02605843,-0.00416013,-0.02924266,0.05734715,-0.0090238,0.04360966,-0.02428121,0.00342289,0.03005075,0.04685077,-0.03206724,0.02573871,0.01120708,0.02606721,-0.04656514,-0.2061996,-0.07533386,0.05246198,-0.00551167,-0.00364038,0.00525108,0.05081565,0.0080237,0.03679175,0.07058027,0.09579957,0.0418294,-0.04328981,-0.00750011,0.03446629,0.07351591,0.02884305,0.003949,0.01523318,0.05142844,-0.01247665,0.0097728,-0.03172733,-0.10521957,0.05603514,-0.04733639,0.09309223,-0.00860878,0.00615929,-0.00525012,0.06826181,0.0422815,-0.0026736,-0.17551403,0.03018538,0.0307027,-0.00122051,0.02508702,-0.00038686,-0.03317901,-0.03954765,0.01888835,0.01964971,-0.08226534,0.00902916,-0.07119872,-0.02718474,0.01091482,0.0110049,0.03056231,0.01235546,0.00686372,0.0091835,0.09775534,0.0151596,0.00757093,-0.00251542,-0.04971771,-0.01236354,0.03171254,-0.02337883,-0.00619313,-0.03751193,-0.00648808,0.02499473,-0.00949766,-0.01357197,0.03559357,0.00763594,-0.02541547,-0.04859689,0.11897027,-0.00758999,-0.03750445,0.00941472,0.01614638,-0.0276243,-0.0759941,0.0298547,-0.00883237,-0.0080351,0.00641899,0.02896827,0.00682543,-0.01620638,0.06826702,0.00093533,-0.04242235,0.03284191,-0.00955913,0.00351127,-0.07311761,-0.05162662,-0.06360873,0.06325315,0.04759018,-0.27248183,-0.03224454,0.01018323,-0.00630868,0.04847286,0.03210744,0.08773732,-0.00383821,-0.0629032,0.01554974,-0.02715719,0.04393297,-0.00407322,-0.05151971,-0.01222772,-0.00404536,0.06398598,0.02144763,-0.00081249,0.03885675,0.01058674,0.06813911,0.21022749,0.0055289,0.01519282,0.00915623,-0.04321067,0.01903578,0.05820895,0.02692918,-0.01198577,-0.00543975,0.03692321,-0.01633678,0.0179192,0.07915809,-0.00991308,0.02321847,-0.00324138,0.02125487,-0.0438099,-0.0266678,0.01767752,0.01694668,0.11895655,-0.00547105,-0.08377187,-0.07366895,-0.00522015,0.05039296,-0.0673757,0.00386488,-0.01408364,0.02880643,-0.00771444,0.05631426,0.00873348,-0.04831611,-0.09883738,-0.01618313,0.00311741,0.0074528,-0.01785997,0.03836391,0.00433906],"last_embed":{"hash":"1x21ggm","tokens":217}}},"text":null,"length":0,"last_read":{"hash":"1x21ggm","at":1751426530084},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{24}","lines":[316,316],"size":359,"outlinks":[{"title":"https://volet.com/blog/post/understanding-intents-in-blockchain-a-comprehensive-guide-to-intents-01jdpzq69t52gfwxy6z0bx1nmv","target":"https://volet.com/blog/post/understanding-intents-in-blockchain-a-comprehensive-guide-to-intents-01jdpzq69t52gfwxy6z0bx1nmv","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1x21ggm","at":1751426530084}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{25}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04771733,-0.01895928,-0.04858225,-0.01801738,-0.00452389,0.03094124,0.00628147,0.05397039,-0.00758541,0.01837398,0.06414351,-0.02699097,0.04039874,0.0035289,0.02555668,-0.02645036,0.00774086,-0.05213337,-0.01106095,0.03062889,0.14671037,-0.06910942,0.03189721,-0.01662753,0.05103896,0.07797769,0.01422152,-0.02246175,-0.04163785,-0.18356581,0.02430803,-0.01585215,0.03059405,-0.0265712,0.01331108,-0.03239135,-0.00744637,0.08198436,-0.04231748,0.02134065,-0.02847576,0.02458592,-0.00330655,0.01125331,0.02270168,-0.05895158,0.02229123,-0.05785257,-0.01332604,-0.04916783,0.02090432,-0.08279846,-0.02409464,0.05203739,-0.00787735,0.00918169,0.02614663,0.04456469,0.04854719,0.02267064,0.09957823,0.04671413,-0.17976342,0.00921401,0.0708655,0.0240457,-0.0266986,0.0123903,0.04019092,0.08101005,0.04558517,-0.00121159,-0.02728598,0.05369536,0.02090771,-0.00998747,0.02890981,0.01153299,-0.02869305,-0.06660324,-0.07114135,0.04215703,-0.01937669,-0.00484092,-0.06100557,-0.02735487,-0.01277588,-0.02079899,-0.0223273,0.02646511,0.0066716,0.00370661,0.04122201,-0.00506505,-0.00833486,0.00193149,-0.03416587,-0.00528059,-0.13727908,0.13141689,-0.01686931,0.04983238,0.01421249,-0.05386049,0.05589224,-0.00788472,-0.00780337,-0.05114825,0.02564486,0.01534914,0.01038967,0.02297477,0.03787379,-0.04200416,0.03394565,0.00080695,-0.01585568,-0.00182159,0.04114442,0.01320623,-0.02070043,0.02851792,0.05277903,-0.02165271,-0.06058289,-0.03693235,0.00343632,0.04478587,0.0259919,0.06647853,0.05186214,0.03065811,-0.06497378,-0.01039618,-0.03952996,-0.01500694,-0.00424459,-0.00059042,-0.03938299,-0.03909197,-0.02155005,-0.03139506,0.00480391,-0.12763572,-0.03890729,0.0773638,0.03322783,0.03032506,0.01930845,-0.01497936,-0.0039152,0.04309124,-0.02597594,-0.05112753,-0.0238118,0.00560616,0.01759356,0.08213414,-0.05463204,0.01369,-0.02417612,-0.01254634,-0.06895841,0.17625704,0.01307961,-0.11806412,-0.00334413,0.04645527,-0.00090772,-0.06641731,0.00321677,0.03421933,-0.04627535,-0.00322088,0.0757882,-0.01979917,-0.04415657,-0.06316215,0.01067669,0.03313749,0.03158615,-0.01472554,-0.05011106,-0.02318079,-0.04107263,-0.03732152,-0.00883831,0.03104669,0.02333176,0.04301224,-0.07610408,0.06335888,0.01207563,0.07006355,-0.03268277,-0.04985449,-0.00026468,-0.04350824,-0.04552788,-0.04715454,0.08787825,0.05656684,-0.01996193,0.00547758,-0.06522244,0.0105733,-0.00932917,-0.03561974,0.01813153,0.02060414,-0.03447899,0.05895306,-0.01784074,0.03261985,-0.02931754,0.00803516,0.00865152,0.03615217,-0.00693783,0.00863317,0.00721835,0.05641559,-0.07423525,-0.21379085,-0.06571582,0.02073726,-0.04171949,0.03513975,0.03136946,0.0241803,0.02215601,0.0258343,0.0661066,0.1145397,0.05806801,-0.09435847,0.0059619,0.01003175,0.05627858,0.04723267,-0.00617824,0.01121018,0.01875756,-0.01634249,0.01956464,-0.03437314,-0.11485942,0.06095342,-0.0286745,0.10287146,0.00520553,-0.02888319,0.00324921,0.07015237,0.01448193,-0.02574157,-0.18288182,0.052301,0.05635465,0.0093158,0.01867663,-0.0293432,-0.04018565,-0.03398169,0.03949611,0.02865297,-0.08257946,0.00483355,-0.07833562,-0.04552664,-0.00265428,-0.00254041,0.02955597,0.01810636,0.03329573,0.01348727,0.12272869,0.01691213,-0.02506727,0.0047277,-0.05798086,0.02865499,0.05862036,-0.00814664,-0.00415543,-0.00126523,-0.01358054,0.00607049,0.00604015,-0.01793712,0.00999665,0.01066049,-0.02040253,-0.02162264,0.14958185,0.01436789,-0.06582912,0.01526273,0.00983401,-0.01224222,-0.06864104,0.0681599,0.00005047,0.014882,-0.00900916,0.0312334,-0.01644084,-0.00357068,0.07139179,-0.00324784,-0.05410438,-0.00019278,-0.0336249,-0.0241988,-0.06334155,-0.04349083,-0.04056813,0.02886286,0.01059817,-0.27865908,-0.02429897,-0.00181756,-0.00237537,0.05117022,0.04742492,0.06924302,0.02111645,-0.10226236,0.01558445,-0.02454132,0.03725363,0.00768293,-0.02852036,0.00303938,-0.01792651,0.07031771,-0.02346189,0.02579065,0.02543793,0.01980847,0.07009718,0.210473,0.00304102,0.01691369,0.02531019,-0.0405707,0.05044573,0.07025418,0.03962074,-0.03983703,0.01634264,0.0197576,-0.03117422,0.00086261,0.0661963,-0.02619282,0.0049216,-0.02893231,0.02425201,-0.03593542,-0.00520948,0.01327978,0.01126012,0.12880291,-0.00225763,-0.0607205,-0.06042016,-0.0023728,0.04144722,-0.06344967,-0.01935567,-0.01793716,0.01585314,-0.00394575,0.06260227,0.00737851,-0.01092127,-0.10103767,-0.00058089,-0.01045428,0.00845671,-0.00697026,0.04599905,0.02372413],"last_embed":{"hash":"8pba1h","tokens":161}}},"text":null,"length":0,"last_read":{"hash":"8pba1h","at":1751426530092},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{25}","lines":[317,317],"size":268,"outlinks":[{"title":"https://crypto.com/en/university/intent-centric-design-for-blockchain","target":"https://crypto.com/en/university/intent-centric-design-for-blockchain","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"8pba1h","at":1751426530092}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{27}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05157019,-0.02458724,-0.08515642,-0.00344335,0.02153434,-0.0054924,-0.01331418,0.03317618,-0.04603013,-0.01300468,0.05838092,-0.04959435,-0.0045451,0.02397458,0.00721238,-0.0032068,0.00489354,-0.02564413,-0.02199668,0.01270701,0.09460187,-0.05438193,0.03865033,-0.03987096,0.1027936,0.0628533,0.02084884,-0.0273327,-0.02290177,-0.20521492,0.00081998,-0.06869046,-0.00690139,0.01134597,0.04590523,-0.06480661,-0.02956547,0.07344093,-0.09344839,-0.00303562,0.0165006,-0.00258958,0.01174058,-0.02336133,0.05851824,-0.077582,0.03017452,-0.05582538,-0.04002996,-0.02122164,0.00835635,-0.05586375,-0.02766077,0.02473836,0.01587302,0.01096654,0.01826284,0.04772284,0.0601953,0.03357789,0.09263406,0.02995764,-0.20057116,0.00009248,0.09647174,0.04405273,-0.03857734,0.03163295,0.02526394,0.10383438,0.03889463,0.00429378,-0.01948166,0.04168996,0.00510946,0.04505935,0.03974086,-0.01307386,-0.04814763,-0.07041333,-0.06058137,0.03568067,-0.00503369,-0.01056449,-0.05069598,-0.00589349,-0.00536337,-0.017709,0.00172134,0.0020055,0.0102826,0.0263193,0.06043151,0.01015151,-0.01968673,-0.01604319,0.01653114,0.03128153,-0.08350269,0.10780995,-0.02723779,0.05193077,-0.00283296,-0.07163683,0.05194481,0.00182052,-0.02486301,-0.00937379,0.02125366,0.01226305,0.04008224,0.03028287,0.07190421,-0.04632565,0.03590177,-0.00242562,0.03105307,0.01383905,0.0005432,0.02059907,0.02165199,0.05260672,0.02614249,-0.02360796,-0.05972512,-0.02136566,0.03572875,0.04737904,0.05970272,0.0123592,0.04054228,0.01217893,-0.05045684,0.02810385,-0.04476944,-0.02781092,0.01563702,0.02351649,-0.02579067,-0.00617572,-0.01611304,-0.06774599,-0.03295023,-0.11978772,-0.04063377,0.052991,-0.00256186,-0.02470217,0.03239639,-0.05592133,-0.00086689,0.04097869,-0.02383744,-0.05825916,0.01944598,0.02144274,0.05131026,0.0777911,-0.05889936,0.02012866,-0.04475458,0.00793362,-0.06059404,0.12560792,0.06130998,-0.1218161,0.02001272,0.04103721,-0.01495082,-0.09228332,0.01486517,0.00652929,-0.0659622,-0.01995504,0.06731769,-0.06903827,-0.027206,-0.01196366,0.00508003,0.05597917,0.00867757,-0.0203853,-0.06202399,-0.0019983,0.00347687,-0.02517186,0.00180303,0.02173264,0.04830942,0.03408652,-0.08303078,0.02896757,-0.04344729,0.01712006,-0.01695896,-0.03570462,-0.04362446,-0.04991113,0.02586936,-0.05321509,0.10540814,0.05564255,0.01875072,0.00321315,-0.05085043,0.00489062,-0.04407059,-0.04920699,-0.00669056,0.03723924,-0.00664442,-0.02194527,-0.0433429,0.02331469,-0.01561561,0.01256451,0.02577181,-0.00445576,-0.04132614,0.01841535,0.01481097,0.0153473,-0.07295322,-0.20108527,-0.08353717,0.01375169,0.03946958,0.04857495,0.00842599,0.0319795,0.01471499,0.04286182,0.09444536,0.10331415,0.06155444,-0.06249773,-0.01425853,0.00930324,0.03350154,0.01041449,-0.0157127,0.04169069,0.02849749,-0.02852218,0.00489579,-0.02000635,-0.08177097,0.0276279,-0.06911063,0.11251748,0.0131708,0.0000566,-0.00978017,0.05993098,0.02339784,-0.03278323,-0.1485911,0.02603953,0.09382036,0.01732297,-0.04077625,-0.0037462,-0.0248778,-0.02559177,0.04150682,0.03254092,-0.12727857,0.05112978,-0.05159617,-0.05100156,0.00574951,-0.04411002,0.02477849,-0.00652076,0.05949397,0.03599213,0.09155137,-0.00367692,-0.03788628,0.02569308,-0.04809549,0.01565966,0.03497102,-0.01139826,-0.01082216,-0.04450584,0.0269537,0.04963724,0.00953462,-0.01495837,0.01381233,0.04060919,0.00501623,-0.00795907,0.12095049,0.05000382,-0.02932375,0.04079079,0.03201408,-0.04810785,-0.09695595,0.04552157,0.0166455,0.02471044,-0.00358766,0.03419031,0.06119383,0.01760938,0.10505367,-0.00026597,0.01479531,0.02974751,0.00548728,-0.00806763,-0.03229088,-0.06493685,-0.07611397,0.06334732,0.00101625,-0.26932177,-0.01406618,-0.0270534,-0.01250919,0.01978094,-0.00019543,0.07027338,0.01622945,-0.07282138,0.02550222,-0.02185079,0.04759537,-0.00783926,-0.03046702,0.005312,-0.00542984,0.05518802,-0.00197414,0.03034462,0.05705687,0.00873971,0.08909998,0.20566729,-0.03779413,0.06174523,0.03124874,-0.04411173,0.01068319,0.0524766,0.00060501,-0.01401141,-0.02021287,0.02539674,-0.04319401,0.00494154,0.0424936,-0.0414174,0.00471283,-0.00163922,0.00778384,-0.04845972,-0.03328295,-0.01615032,0.03717213,0.12055777,-0.01442895,-0.05631664,-0.09241636,-0.00409603,0.02688801,-0.04340091,-0.03848317,0.00141442,0.0149077,-0.02684821,0.05405922,0.0501073,-0.02131406,-0.06051844,-0.0338485,-0.00894524,-0.00515238,-0.04475728,0.06652764,0.01125885],"last_embed":{"hash":"1cjpkxs","tokens":148}}},"text":null,"length":0,"last_read":{"hash":"1cjpkxs","at":1751426530099},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{27}","lines":[319,319],"size":205,"outlinks":[{"title":"https://www.youtube.com/watch?v=G5tE0LFFiTY","target":"https://www.youtube.com/watch?v=G5tE0LFFiTY","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1cjpkxs","at":1751426530099}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{28}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04516084,-0.04705996,-0.05070462,-0.00602385,0.00923637,-0.0262379,0.00185845,0.01460514,-0.01036646,0.01496697,0.07945655,-0.06405857,0.03096656,0.00262647,0.00965646,-0.03173475,-0.0294489,-0.03366134,-0.01736374,0.02417413,0.13792153,-0.06966721,0.02751326,-0.04123795,0.02058288,0.0789219,-0.00964584,-0.03200082,-0.03861317,-0.16118617,0.00421656,-0.05327379,0.01172086,-0.00869343,0.02619379,-0.0593567,-0.04059594,0.05696946,-0.05589879,-0.01946251,-0.01125639,0.02167874,-0.00045763,-0.03498433,0.05987439,-0.07174653,0.03096372,-0.03598726,0.00706045,-0.05773114,0.00224504,-0.06520279,0.0114634,0.02876261,0.01433953,0.02050791,0.01861151,0.01780366,0.04191516,0.02585043,0.10653839,0.05627153,-0.18941697,0.03323035,0.06220897,0.02290826,-0.04276039,0.00008891,0.00903848,0.0596451,0.02239163,0.01305227,-0.04535109,0.05195248,0.02469631,0.01976686,0.03558334,0.04089542,-0.01185806,-0.0581019,-0.0664536,0.05718992,-0.02321145,-0.01423697,-0.00110387,-0.03915505,0.00642241,-0.01108929,-0.01220614,0.00575894,0.00519457,0.00727806,0.0454244,0.01005015,-0.02915561,-0.00876299,0.01661678,0.01427717,-0.13275732,0.11998746,-0.0369567,0.06151974,-0.00753593,-0.06380238,0.0430662,-0.01763289,-0.00217211,-0.03924627,0.02640328,0.00633873,-0.00413629,-0.00701294,0.05327437,-0.0571652,0.03781632,0.00116049,-0.00372464,0.00517944,0.00346722,-0.00851608,0.00736783,0.00642694,0.02109756,0.00190402,-0.05166564,-0.03286223,0.00848383,0.04665521,0.04569894,0.09215894,0.03979555,0.01768776,-0.07015148,-0.01438789,-0.04375824,-0.01210708,-0.02122009,0.00534096,-0.01573218,-0.02144003,-0.0080641,-0.08327844,-0.0671036,-0.07604165,-0.04280437,0.10204417,0.01476207,-0.00510593,-0.01973203,-0.05303778,0.02110385,0.08032595,-0.01270203,-0.07570537,0.00742694,0.03269814,0.03866087,0.08416777,-0.07019532,-0.00941407,0.00603737,-0.01865717,-0.05068462,0.14751063,0.02774521,-0.13789882,-0.0069074,0.0363574,-0.01204016,-0.05954066,0.03509311,0.02431773,-0.03881573,-0.01874861,0.05675189,-0.03932281,0.02401028,-0.04529662,0.00808314,0.05261932,-0.02378238,-0.03204281,-0.04731861,0.00093744,0.02332722,-0.00237815,-0.04232639,0.05664252,0.01579025,0.01782052,-0.07326478,0.03845727,-0.02071394,0.04608304,-0.0081704,-0.03903949,-0.02344077,-0.00516457,0.04224128,-0.04093761,0.19581741,0.07081826,-0.00576964,0.02974741,-0.04056112,0.01240218,-0.03213878,-0.04562694,0.00473958,0.03098855,-0.02227757,0.04544599,0.01414328,0.0123713,-0.03803063,0.01145259,0.01569961,0.0546053,0.01076024,0.03353137,-0.00265466,0.02978204,-0.0453015,-0.19016275,-0.02604487,0.03209842,-0.01081814,0.03124681,0.00141452,0.05540917,0.00989573,0.07855856,0.07384337,0.08885916,0.06405512,-0.07658085,-0.00360463,-0.01083367,0.062181,0.05361752,-0.02688149,0.03286748,0.04011472,-0.05254412,0.01250588,-0.03428169,-0.05376147,0.06755111,-0.05478622,0.11714739,-0.02301271,-0.01453148,-0.03828428,0.04205623,0.03905313,-0.01367284,-0.17135079,0.04303596,0.06894212,0.01982019,0.00645322,-0.00583919,-0.03983714,-0.0368154,0.00996156,-0.00046999,-0.06308919,0.01714328,-0.08346744,-0.02949536,-0.00948277,-0.0176167,0.05294568,0.00355702,0.0155357,0.01811747,0.08466395,0.03057128,0.00263852,0.03812112,-0.05256988,0.02142755,0.04242115,-0.00705414,-0.00309411,-0.01379676,-0.00060848,0.04778269,-0.00848981,-0.01017903,-0.0083829,0.0315971,-0.03371743,-0.02418108,0.13034745,0.02918478,0.00315617,0.05918393,0.01563991,-0.04812389,-0.07730328,0.02473573,-0.00154975,-0.0001009,-0.03742054,0.07209869,0.02814062,-0.03836428,0.08073231,0.02347657,0.03746125,0.01653625,-0.00664352,-0.02132067,-0.04680851,-0.055662,-0.04516117,0.04847885,0.0217455,-0.28759876,-0.04715534,-0.02481387,-0.00360017,0.073609,0.05551181,0.03697041,0.036302,-0.08793442,-0.003261,-0.03953421,0.02345056,-0.01923778,-0.04091983,-0.03089326,-0.00437531,0.01785059,-0.03999322,-0.02955419,0.03620132,-0.00908614,0.03300963,0.20084506,0.01155795,0.01155206,0.03107548,-0.06044526,0.09152831,0.06546186,-0.00389421,-0.03340353,-0.0039635,0.03549251,-0.03984269,0.02428586,0.03912109,0.00709927,-0.04145608,0.01056946,0.02491329,-0.01420597,-0.03915544,0.00433498,0.02980253,0.07576634,0.01405887,-0.07459167,-0.04693019,0.05976652,0.04048168,-0.0783605,-0.03376629,0.01669472,0.06895361,0.00599508,0.08825651,0.01709337,-0.04654637,-0.10463089,-0.01770958,-0.04143629,-0.02562275,-0.04673991,0.04723269,0.01717498],"last_embed":{"hash":"17bivst","tokens":241}}},"text":null,"length":0,"last_read":{"hash":"17bivst","at":1751426530105},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{28}","lines":[320,320],"size":287,"outlinks":[{"title":"https://ethplorer.io/address/******************************************","target":"https://ethplorer.io/address/******************************************","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"17bivst","at":1751426530105}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{29}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03091306,-0.03869912,-0.06160225,0.00204693,0.03402562,0.00031082,0.00790174,0.05806474,-0.01208959,0.01081415,0.05659504,-0.05062256,0.01959666,0.0607858,0.00556416,-0.01517598,0.02894511,-0.05784491,-0.05967389,0.04103068,0.11299488,-0.07624546,0.02989265,-0.01936643,0.07098975,0.05187511,-0.00212296,-0.02346275,-0.05465817,-0.18278518,0.00294252,-0.04013183,0.01183109,0.01195616,0.01957135,-0.04787588,-0.03906155,0.04615581,-0.00890017,-0.01509447,-0.01511669,-0.01463467,-0.0262081,-0.00971199,0.05079304,-0.10096755,0.01974263,-0.06583899,0.02206608,-0.02199046,-0.03963468,-0.07867319,-0.02793038,0.03856322,0.00039358,0.00331727,-0.00604994,0.02249796,0.06685144,0.04045866,0.08598626,0.04701946,-0.2155067,0.02708292,0.04607654,0.01428936,-0.02371485,-0.00512049,0.0149697,0.06861981,0.03715423,0.00967777,-0.00276851,0.02499211,0.07323811,-0.04517439,0.0278092,0.01429725,-0.01197685,-0.06884526,-0.02758667,0.0926303,-0.02662193,0.00606778,-0.00845518,-0.02285348,0.01688371,-0.02807987,-0.03793821,0.03358301,-0.00415785,0.04428589,0.00322258,-0.02018056,-0.06553173,-0.00964949,0.00895604,0.03202368,-0.05681951,0.11653274,0.00059969,0.05383898,-0.03811593,-0.08004992,0.00589064,-0.00239259,-0.03766136,-0.03390093,0.03118393,0.05394135,-0.00470404,0.03914618,0.06062568,-0.07046609,0.00475068,0.00159765,-0.02055897,-0.02369408,-0.00497542,-0.04896536,-0.00634279,0.01900885,0.11157875,-0.00784778,-0.02632236,-0.01394486,0.01569337,0.05762266,0.0264829,0.05217906,0.0646804,0.00004549,-0.07457826,-0.02025905,-0.02600708,-0.02608062,0.00239556,0.0101205,-0.02430533,-0.00204578,-0.06404702,-0.09944026,-0.03349443,-0.06971161,-0.08295567,0.00911688,0.00313387,-0.01405234,0.01262207,-0.01305633,-0.04814879,0.08056106,-0.03334091,-0.05332471,-0.00737639,0.02192717,0.05115334,0.11592861,-0.0661623,-0.0039982,-0.01514521,-0.05264745,-0.05722293,0.14262703,0.04122585,-0.11973198,0.00900932,0.02469381,-0.03152515,-0.07078567,-0.01737371,0.00170074,-0.03913209,-0.02185504,0.09388562,-0.01578754,-0.02076185,-0.03771039,-0.01432775,0.04031291,-0.02870414,-0.01882255,-0.05878849,0.01540447,0.01017404,-0.05158272,-0.04335819,0.04104369,0.03785041,0.01630665,-0.0595993,0.03429627,-0.02885959,0.03757379,-0.02711733,0.01768551,-0.04216607,-0.0164393,0.03924023,-0.05190647,0.14168261,-0.00014313,0.03520375,-0.00000651,-0.0560233,0.0421492,-0.02555044,-0.0068079,0.00557262,-0.01274846,-0.00381646,0.05043531,-0.0157182,0.02459275,-0.08831837,0.02540432,-0.00110153,0.02499947,-0.02783038,0.03271778,-0.01833879,-0.00056763,-0.06929357,-0.20716672,-0.00056447,0.0665861,0.00665815,0.01928584,-0.01120518,0.06714283,0.0453877,0.07659682,0.13244602,0.10922769,0.02920321,-0.04668451,-0.01081699,0.01617004,0.03907902,0.02419591,-0.01166797,0.00125239,0.03356174,-0.03518702,0.00512772,0.00099898,-0.05281971,0.0569848,-0.02885112,0.14096379,-0.0197818,0.00854976,0.00728911,0.07080074,-0.03026995,-0.02542372,-0.07361045,0.03924745,0.08845913,0.02022718,0.00872162,-0.07207301,-0.01943162,-0.02570975,-0.00466359,0.04265284,-0.10911244,0.00351394,-0.05823916,0.00632462,0.01198962,0.00575605,0.05902439,-0.02712801,0.01844312,0.01690731,0.05019725,0.00985806,-0.03563794,0.01410787,-0.0865133,0.0216256,0.05089395,-0.00935459,0.01146767,-0.0231832,-0.03269204,0.02656907,-0.01638859,0.0239335,0.00059274,0.02137479,-0.00906235,-0.01467509,0.11362184,0.02512851,0.00587373,0.0778053,0.02527291,-0.04732346,-0.02690573,0.01480757,0.0655283,0.03646835,-0.00280522,0.00519933,0.09000904,-0.0041502,0.06701879,0.04568003,-0.04011868,0.01790893,-0.04677525,-0.01400603,-0.05765494,-0.04944964,-0.02507041,0.07128534,0.02448542,-0.28394517,-0.03321596,-0.00722414,0.00870186,0.05255802,0.04368126,0.0802054,0.06436146,-0.11270238,-0.00745395,-0.01588481,0.04226014,-0.00204821,-0.05600907,-0.00054539,-0.00987113,0.03598201,-0.02922884,0.03104107,0.00554443,-0.00433855,0.03791795,0.2141307,0.00266128,-0.0210216,-0.00529688,-0.07042253,0.05733573,0.03904185,0.02319526,-0.02091222,0.00818212,0.02891273,-0.03124887,0.02634713,0.01262343,-0.00678481,0.00152615,0.00565974,0.04855029,-0.01637263,-0.0207292,0.00652402,0.02001957,0.09879854,-0.00016412,-0.06368771,-0.03726462,0.01966444,0.04110334,-0.06242561,0.00850591,-0.00101968,0.00396701,-0.03229212,0.08111978,0.00556523,-0.024243,-0.04547978,-0.03747567,-0.04096105,0.02172238,-0.04057036,0.05634085,0.01200384],"last_embed":{"hash":"wj8no","tokens":194}}},"text":null,"length":0,"last_read":{"hash":"wj8no","at":1751426530114},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{29}","lines":[321,321],"size":242,"outlinks":[{"title":"https://phantom.com/tokens/solana/6tCEUogw2uk4VQz5JKb295ixFrj6vAPEQbZqL9VfFXSz","target":"https://phantom.com/tokens/solana/6tCEUogw2uk4VQz5JKb295ixFrj6vAPEQbZqL9VfFXSz","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"wj8no","at":1751426530114}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{30}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04542555,-0.05253585,-0.05747304,0.01507693,0.06467105,-0.00140959,-0.02505647,0.03002451,0.00082009,-0.00778832,0.05104993,-0.04915446,0.0465118,0.06902128,0.00791297,-0.02210852,0.00815441,-0.04694709,-0.03905491,0.06884541,0.13933402,-0.06776057,-0.0012852,-0.04523805,0.05947265,0.05683641,0.01261416,0.00903087,-0.03279897,-0.1899028,-0.02029769,-0.05121326,0.01989039,-0.01501808,-0.02358786,-0.02021796,-0.02782216,0.0297681,-0.01346527,0.0035248,-0.01657229,0.02041887,-0.02118069,-0.00295673,0.04221877,-0.1132751,-0.01068083,-0.04450575,0.01843374,-0.01961531,-0.01616927,-0.06191739,-0.00397042,0.02266357,0.02751896,0.04451406,-0.02236524,0.00559182,0.05535738,0.05444458,0.07428975,0.0477861,-0.21938246,0.02203909,0.02499758,0.03683228,0.00285879,-0.00072231,0.03371808,0.06053334,0.06904774,0.00273068,0.02232651,0.00591017,0.03563069,-0.04042404,0.04706076,0.01759759,0.00101213,-0.05675061,-0.01810947,0.07401267,-0.02656288,-0.00021473,0.00024479,-0.02680136,0.04102049,-0.03406213,-0.02865214,-0.00411897,-0.00926514,0.0146394,0.0282032,0.00486662,-0.04739082,0.02009857,0.01632731,0.025211,-0.09065116,0.11724215,0.01504191,0.05780172,0.00334145,-0.06973864,0.01982419,-0.05540669,-0.0708502,-0.04149005,0.01108237,0.02301849,0.00390061,0.01462277,0.06755609,-0.05845155,0.04343592,0.00698652,0.00754544,-0.00734652,0.00472529,0.00919805,-0.01766749,0.01407843,0.06943711,0.0081883,-0.02442386,-0.00464803,0.04641023,0.0688065,0.02292953,0.05864519,0.04864867,0.01349848,-0.08603603,-0.0127066,-0.02866721,-0.02927518,0.00273901,0.01263057,-0.02903977,-0.01984163,-0.0674396,-0.08083957,-0.03246611,-0.09813538,-0.05549171,0.04241881,0.02950398,-0.00107022,-0.00008852,-0.02694752,-0.03903878,0.07895015,-0.0335588,-0.07991972,-0.01176301,0.01689924,0.04854808,0.0723727,-0.06241032,-0.0287257,-0.02813695,-0.0382342,-0.07017112,0.1152808,0.03426021,-0.12024494,0.03102834,0.02691618,-0.02731226,-0.06079483,0.01794939,0.02124797,-0.02845605,-0.02786359,0.10472197,-0.0175445,-0.01425134,-0.03103058,-0.01060956,0.0190702,-0.00979477,-0.02890336,-0.05591392,-0.02271506,-0.02943644,-0.02902156,-0.01008365,0.02850781,0.01087095,0.03988167,-0.02476754,-0.01021937,-0.00582281,0.05230979,-0.02187373,0.02435392,-0.03983206,-0.0037887,0.01857238,-0.03640908,0.14145954,0.00782835,0.01442196,0.0138462,-0.02813923,0.04957512,-0.02684259,-0.01600114,-0.02134384,0.01161778,0.0344267,0.03014805,0.00107331,0.02033893,-0.08450184,0.01553481,-0.00100115,0.0622963,-0.02999667,0.06290484,0.00742472,-0.01638015,-0.04913259,-0.20565547,0.01645732,0.06954719,-0.01235426,0.05923619,-0.0042933,0.08413674,-0.00098611,0.05906882,0.12045377,0.06647644,0.02567654,-0.02793679,-0.00977076,0.03001218,0.05661081,0.04121554,-0.01954567,0.02680597,0.03124605,-0.05268598,0.00971006,-0.02958805,-0.09422566,0.07432098,-0.0390498,0.10613804,-0.03905521,0.00968307,-0.03149423,0.04869071,0.00382656,-0.01662492,-0.09203307,0.02981132,0.07510965,0.04670363,-0.02694226,-0.0680784,-0.02962841,-0.02109565,0.00428446,0.06605649,-0.10798134,0.04159175,-0.03541972,0.01113686,0.01823037,-0.01879258,0.05944962,-0.00829153,0.00950276,0.05298688,0.04749301,0.01093175,-0.04211426,-0.01293275,-0.09821812,-0.01439821,0.04253812,0.00403388,0.00621751,-0.01704141,-0.01688522,0.04283141,-0.01904043,0.01857253,-0.0211792,0.01315118,-0.01838095,-0.01445664,0.09317023,0.01277492,0.02922533,0.03003697,0.03329396,-0.02071032,-0.09231186,0.01930904,0.04157929,0.00927582,0.05166071,0.03377444,0.08820461,0.02302415,0.0691563,0.05213686,-0.0308058,0.01914041,-0.01817221,-0.02933068,-0.07610793,-0.042357,-0.03587934,0.06760945,0.01844672,-0.29568994,-0.02419263,-0.01089301,-0.01079587,0.05808966,0.01153101,0.04054961,0.05359327,-0.09469081,0.00319976,-0.02538945,0.07097515,-0.00069737,-0.03856853,0.00414098,-0.03957792,0.0419137,-0.02442872,0.01060427,0.01325928,-0.0073401,0.03763839,0.18709791,0.01586925,0.02466173,0.02020004,-0.10456075,0.07922695,0.07133465,0.00815699,-0.04526782,0.0050748,0.00084125,-0.05691863,0.03488441,0.07558582,-0.00217953,-0.01869858,-0.02168373,0.05143904,0.00169462,0.00112494,-0.02954837,0.06527011,0.10979818,-0.01492384,-0.04154417,-0.06638429,0.0109721,0.02514991,-0.06395127,-0.02311144,0.02670014,0.0312917,-0.0320943,0.06330186,0.01544497,-0.02023057,-0.07342854,-0.07918132,-0.04270164,-0.0228346,-0.06921711,0.02612386,-0.01789588],"last_embed":{"hash":"1ums59x","tokens":218}}},"text":null,"length":0,"last_read":{"hash":"1ums59x","at":1751426530122},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{30}","lines":[322,322],"size":302,"outlinks":[{"title":"https://www.dextools.io/app/en/solana/pair-explorer/ENDyunqABSwMvfUoRxsNA1N2QLhXeZQTYrnPZUQdnygP","target":"https://www.dextools.io/app/en/solana/pair-explorer/ENDyunqABSwMvfUoRxsNA1N2QLhXeZQTYrnPZUQdnygP","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1ums59x","at":1751426530122}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{32}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05769473,-0.03475022,-0.06517014,-0.00271483,0.04096724,-0.01114226,-0.02079465,-0.01751739,-0.03139267,-0.01705664,0.07484249,-0.05932555,0.00554983,0.02336367,0.02954779,0.0361099,0.01431769,-0.00704616,-0.0336968,0.00990875,0.12069102,-0.04433182,0.02856431,-0.03664163,0.09056876,0.03559669,-0.00602666,0.00011677,-0.05205775,-0.18075296,0.00886548,-0.02364979,0.01704404,-0.02092129,-0.00355662,-0.0203312,-0.04265253,0.0693327,-0.04850823,0.02619212,-0.00901167,0.01452277,0.01174392,-0.02953048,0.02018024,-0.07200663,0.03929922,-0.04284247,0.02925778,-0.07278568,-0.02572583,-0.00951296,-0.01528099,0.02132919,0.04056315,0.02753146,-0.01167316,0.04172977,0.00669206,-0.02451798,0.11004864,0.03845235,-0.18575281,-0.00188122,0.07553129,0.05305558,-0.01841604,0.02024075,0.02647719,0.07411005,0.07732443,0.02972425,-0.02204893,0.01919733,0.00749072,-0.00925204,0.04568102,0.02919848,-0.02606164,-0.0518239,-0.0624653,0.05669517,-0.01976607,-0.01699688,-0.02035332,-0.03434471,0.02287676,-0.01284225,0.00024364,0.00078549,0.0273684,0.03183647,0.03185005,0.02352258,-0.01372544,0.01159562,0.01555446,0.01016781,-0.08569812,0.11494388,0.01150732,0.0476229,-0.01153638,-0.07431503,0.02142194,0.01764808,-0.02346779,-0.02697247,0.03067778,0.00062531,0.02686172,0.01017826,0.10020271,-0.09383849,-0.00871005,-0.00199892,-0.03590301,0.01632724,0.02076521,0.0477291,-0.01536052,0.02740938,0.05744395,-0.02110595,-0.01441682,-0.02100259,0.0412859,0.03806967,0.06246855,0.0775886,0.02329491,0.04465646,-0.10632375,0.03214695,-0.0541147,-0.06762552,-0.02933634,0.00233994,0.01936609,0.0019148,-0.03905799,-0.02526749,-0.01377004,-0.12253313,-0.01000719,0.04992812,0.03080687,0.01652324,-0.01157509,-0.08434635,-0.02280973,0.03680278,-0.01290839,-0.08000092,-0.04472702,0.01550049,0.04341142,0.08411892,-0.09668642,-0.02306674,-0.02582967,-0.03334374,-0.05503359,0.14593373,0.04815578,-0.09328423,-0.01057114,0.02531297,-0.015255,-0.10984888,-0.00130427,0.02937977,-0.05455657,-0.01812699,0.06130618,-0.02163042,-0.04859155,-0.04778979,-0.01543478,0.02248733,0.01510219,-0.03811386,-0.06501827,0.01140802,-0.01407424,-0.04084131,-0.03489635,0.02857392,0.02836893,0.04454849,-0.0826498,0.02307409,-0.04240443,0.02011603,-0.01346034,-0.01985346,-0.03462243,-0.02457347,-0.01766701,-0.01622454,0.10242266,0.05494122,-0.0361052,0.04469159,-0.05057213,0.01404407,-0.05259341,-0.04896194,0.00998328,0.00983514,-0.01071806,0.02395559,-0.02485781,0.04828383,-0.05812754,-0.00804395,0.07793023,0.04523674,-0.06687552,0.0417374,0.04750874,-0.00222956,-0.08118717,-0.19768803,-0.05912145,0.00889171,0.03194519,0.03473085,0.0092618,0.0511463,-0.00071326,0.04456757,0.0347754,0.08607467,0.03954805,-0.05287822,0.00407916,0.02905745,0.04427749,0.02061635,-0.0684545,0.03761571,0.05605904,-0.04203256,0.01213119,-0.00401644,-0.07030089,0.03446337,-0.04781734,0.12654787,-0.03635523,0.00550436,-0.01730717,0.01628537,0.0372443,-0.02318242,-0.15045381,0.01493917,0.05276503,0.02368104,0.00637039,-0.02553069,-0.02553142,-0.01769681,0.00788613,0.02632189,-0.08286273,0.01945754,-0.04440544,-0.01453322,0.03136324,0.00692579,0.03317637,0.02839538,0.03246825,0.0374076,0.10066823,-0.00875684,-0.05263745,0.02076231,-0.03999463,-0.03327014,0.03685261,-0.01528448,-0.05318052,-0.022573,0.01392147,0.052531,-0.0350121,-0.01314564,0.01841507,0.02128657,-0.02604314,-0.02917759,0.11550071,-0.00427079,-0.04789233,0.03472822,0.04167079,-0.04182258,-0.09263828,0.04474155,0.01987187,0.01278304,-0.0078394,0.05132999,0.09908472,-0.01359783,0.09371306,0.010542,-0.00649906,0.01198954,0.02047549,-0.04892729,-0.04195353,-0.10495739,-0.05677861,0.07281928,0.04182857,-0.26351362,-0.02013603,0.0025652,-0.01186339,0.06640895,0.00939548,0.09716301,0.0129349,-0.03318033,0.04418093,-0.00755296,0.04777891,0.01632791,-0.07234143,-0.03447254,-0.01544892,0.03187996,0.00904204,0.03262265,0.05862779,0.00498154,0.11818498,0.1948507,-0.00022541,0.02489975,0.06131437,-0.07504124,0.05311456,0.05369316,0.01423319,0.01225108,-0.00904765,0.0321007,-0.05400795,0.01964477,0.07221458,-0.01226858,0.02165771,-0.01199242,0.00803418,-0.01985902,0.0187427,-0.00481882,0.01977262,0.10054308,-0.04809676,-0.03847561,-0.08442519,0.00848913,0.01644336,-0.07297183,0.00572345,0.0225408,0.04036231,-0.02192129,0.07898671,0.0376651,-0.01673289,-0.05125504,-0.02655729,-0.02936302,-0.0243255,-0.04599775,0.05246786,-0.01450545],"last_embed":{"hash":"159rp5","tokens":161}}},"text":null,"length":0,"last_read":{"hash":"159rp5","at":1751426530131},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{32}","lines":[324,324],"size":265,"outlinks":[{"title":"https://m.sosovalue.com/blog/comprehensive-guide-to-bitcoin-mining","target":"https://m.sosovalue.com/blog/comprehensive-guide-to-bitcoin-mining","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"159rp5","at":1751426530131}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{33}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03129274,-0.03469652,-0.0664933,-0.00364723,0.016673,-0.00128358,0.02740529,-0.00707799,-0.0044642,0.01761557,0.08290644,-0.06648511,0.02895302,0.01302643,0.02080248,-0.00756289,-0.01013733,-0.02840799,-0.06688106,0.00887214,0.14142051,-0.05674034,-0.00909683,-0.03394721,0.07247078,0.06545955,-0.03759902,0.0198701,-0.05482863,-0.16742365,-0.00807281,-0.05343933,0.05003311,-0.02721868,0.01464823,-0.01752054,-0.0396607,0.08435406,-0.04337388,0.00688066,0.00850352,0.01065738,0.02714069,0.02707515,0.0382343,-0.0895998,0.01332887,-0.02360601,0.01138598,-0.01995776,0.00078998,-0.05911097,-0.00567916,0.02873892,0.05028677,-0.01880522,-0.00034772,0.02200411,0.02501902,0.00789295,0.10404664,0.03516362,-0.20573029,-0.0042299,0.07944477,0.02193435,-0.03922423,-0.00381733,0.02736374,0.11567644,0.06974227,0.00545836,-0.01728552,0.01028955,0.01901455,0.01268007,0.0304017,0.04119105,-0.03029468,-0.06129687,-0.05315389,0.05371113,-0.01786637,0.01363417,0.0109165,-0.01358271,0.01349747,0.00429427,-0.00980653,0.00483837,0.02357799,0.03425965,0.02237657,-0.01295503,-0.0145657,-0.01518836,-0.00475163,0.03065952,-0.0673881,0.12597908,0.01036405,0.0575145,-0.01083115,-0.09686466,0.01067443,0.00326662,-0.04693555,-0.01137338,0.02050245,0.00337437,0.00339586,0.03611723,0.06862191,-0.08076776,0.00969346,0.02051969,-0.0108645,0.03646005,0.00567493,0.0183035,0.01283177,0.01973145,0.04676327,0.02887175,-0.02872181,-0.02660828,0.04955846,0.05073968,0.02924841,0.06581349,0.03534837,0.04035001,-0.10823429,0.00023025,-0.04150937,-0.05014086,-0.01275777,0.00431217,-0.01754106,-0.00186638,-0.01309375,-0.05667708,-0.02125646,-0.08574157,-0.01046023,0.06168956,-0.00431705,0.00463776,0.01252106,-0.0985518,-0.01579687,0.04990748,-0.02530446,-0.09049378,-0.02811001,-0.01529373,0.05110825,0.05971534,-0.06649131,-0.02420941,-0.05864262,-0.01283267,-0.04801097,0.12508982,0.05626231,-0.06971655,0.01309843,0.03519906,0.00620911,-0.11210226,0.02023015,0.03559317,-0.06326723,-0.02586219,0.08823777,-0.04632445,-0.01864858,-0.03808426,0.00530858,0.0523791,0.02588429,-0.02836005,-0.07145584,0.00492559,-0.03319668,-0.03498854,-0.01368004,0.02516877,-0.00851587,0.05737069,-0.07612943,0.00210491,-0.03714666,0.02451405,-0.02154388,-0.03625573,-0.04680838,-0.02087932,-0.01197962,-0.0430323,0.12359133,0.03248472,0.01323189,-0.00744073,-0.08962765,0.0377216,-0.01577051,-0.04318165,0.03228701,0.03652903,-0.01625447,0.0155184,-0.02009943,0.00886211,-0.03999404,0.03562722,0.0511804,0.04433645,-0.04630249,0.04707858,0.01540544,-0.01180452,-0.06862071,-0.1908491,-0.07552575,0.05205044,-0.00290535,0.04348059,-0.01178069,0.05723611,0.00108987,0.04307431,0.07338709,0.0775319,0.03046222,-0.06174872,-0.00098284,0.02680038,0.06796122,0.01780571,-0.04319026,0.00925112,0.04153505,-0.02832132,0.0001568,0.00989181,-0.09977747,0.04532332,-0.04671032,0.08971191,-0.00239693,0.0330946,-0.01526521,0.06816227,0.01053869,-0.04841121,-0.0794494,0.02771303,0.02873961,-0.00253425,-0.01886694,-0.06344593,-0.02944873,-0.03138008,0.01210676,0.03655282,-0.06193851,0.00777227,-0.02786416,-0.02932677,-0.00540094,0.02246093,0.0432896,0.01746997,0.05715478,0.04130519,0.08617904,0.00653169,-0.04629616,0.02024801,-0.04390943,-0.03400306,0.05725122,0.01529039,-0.04623125,-0.03716229,0.00231089,0.07331546,-0.0268141,-0.00952013,-0.01946917,0.05443146,-0.00927988,-0.02314032,0.13675186,0.02260089,-0.03432412,0.04553183,0.01247367,-0.04451255,-0.11928941,0.0353886,0.04487566,0.01611102,-0.02736153,0.05204755,0.05005316,-0.00299729,0.06534202,0.0127373,0.00836138,0.02240347,-0.01448705,-0.03139137,-0.05011676,-0.06760795,-0.03498856,0.02270414,0.04904344,-0.29357013,-0.03210513,-0.01688844,-0.01174375,0.07414655,-0.01624688,0.08104035,0.01884704,-0.0666298,0.01687226,0.02166547,0.0439004,-0.00893842,-0.093633,-0.00941521,-0.03471304,0.00446514,-0.00828844,0.0532841,0.04287615,0.05127618,0.08201591,0.21121329,0.01611219,0.04289798,0.02323024,-0.03858453,0.03155001,0.0853302,-0.00448914,-0.01937892,-0.01890038,0.02931075,-0.05546086,0.03374639,0.05722053,-0.00222252,0.00030017,-0.01192701,0.01546193,-0.00036527,-0.01473298,-0.01404957,0.0329413,0.14483574,-0.04687937,-0.03879906,-0.08443558,0.02319583,0.03581346,-0.08683701,0.00562718,0.01124192,0.05257473,-0.01762742,0.0730442,0.0089933,-0.01553206,-0.05415423,-0.04298085,-0.05152527,-0.01753145,-0.03192712,0.02885028,-0.01202035],"last_embed":{"hash":"9acv9p","tokens":150}}},"text":null,"length":0,"last_read":{"hash":"9acv9p","at":1751426530138},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{33}","lines":[325,325],"size":227,"outlinks":[{"title":"https://www.cryptominerbros.com/blog/calculate-crypto-mining-profits/","target":"https://www.cryptominerbros.com/blog/calculate-crypto-mining-profits/","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"9acv9p","at":1751426530138}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{34}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03234484,-0.00810433,-0.06134027,0.00728863,-0.00880062,-0.00329612,0.03553364,0.00614318,0.00292348,0.01841401,0.09068014,-0.0756371,0.04335982,0.01670047,0.0057812,-0.00832145,-0.00304415,-0.01895263,-0.07415012,0.00795264,0.13840817,-0.05515857,-0.01171466,-0.02924684,0.06126988,0.04281661,-0.03970917,0.01308822,-0.05891805,-0.17508774,0.00182731,-0.0472851,0.05884295,-0.02325392,0.00466471,-0.01163573,-0.05621893,0.07411861,-0.05030945,0.01976205,0.02172363,0.02136181,0.00219283,-0.0009133,0.05087752,-0.10034832,0.02768121,-0.0414457,0.01980788,-0.01979978,-0.00023948,-0.04908683,-0.00082611,0.03511806,0.03797058,-0.01532159,0.01225679,0.00336178,0.01066965,-0.00593538,0.10406107,0.03873864,-0.20862514,0.006762,0.09218349,0.01202912,-0.0478531,-0.00756,0.01789565,0.10440244,0.07835997,0.00517208,-0.01227403,0.01689819,0.02175939,0.02342283,0.03368878,0.04317228,-0.01942575,-0.0708138,-0.04306332,0.05070991,-0.02795357,0.01029618,0.00549978,-0.01192241,0.03264374,0.00899719,0.01189429,-0.02393586,0.03293556,0.03096538,-0.01033067,-0.02501607,-0.00383288,-0.00402317,-0.03229018,0.02540005,-0.05008936,0.1254923,0.03248778,0.0646913,-0.03714276,-0.09785352,-0.00526527,-0.00959523,-0.05374818,-0.00475834,0.03183012,-0.01418973,0.00887308,0.05964974,0.06248557,-0.07910622,0.0037364,0.03456691,-0.01397949,0.03199874,0.01442058,-0.00043784,0.03612171,0.01226148,0.01935212,0.0332136,-0.01443153,-0.02799025,0.04882619,0.05614064,0.02591779,0.04380622,0.05377826,0.02759845,-0.0970135,0.00419543,-0.01818512,-0.03976012,-0.01575796,-0.00450716,-0.0097746,-0.00696155,-0.02309001,-0.04752289,-0.01561769,-0.07369922,0.0048819,0.08636456,0.00888734,0.00802666,-0.01184947,-0.08469097,-0.05196941,0.06750123,-0.02184761,-0.07579692,-0.00713928,0.00414232,0.05149416,0.04966513,-0.07489141,-0.05127014,-0.05112833,-0.02980887,-0.05845443,0.14701584,0.04193936,-0.05834619,-0.00112309,0.02779654,-0.00067644,-0.10057342,0.03591285,0.04896611,-0.05498635,-0.01888579,0.0924898,-0.03392624,-0.02591203,-0.03190875,0.00615305,0.02063614,0.01941732,-0.02939385,-0.07454486,-0.02237056,-0.03819244,-0.03414228,-0.01278757,0.03024195,-0.00933018,0.0451346,-0.09132417,-0.01149195,-0.05080728,0.02871418,-0.01251034,-0.00183678,-0.03751801,-0.03221181,-0.01580329,-0.01846942,0.10714012,0.03597652,-0.02610927,0.00942077,-0.07419492,0.03817511,-0.02238623,-0.03817535,0.02157595,0.05163598,-0.04806892,0.0130418,-0.02366476,0.02783807,-0.05367931,0.01984471,0.05125891,0.02638474,-0.04644312,0.05579342,0.0160797,-0.00768845,-0.05082874,-0.19122301,-0.06407624,0.05249341,0.01687379,0.03935351,-0.02283213,0.03167849,0.01062907,0.04342946,0.06863102,0.07905201,0.04318267,-0.04354843,0.02275104,0.00533388,0.04683096,0.01195744,-0.04592576,0.03640513,0.0472271,-0.02776177,0.00508681,0.04440426,-0.09416869,0.0669849,-0.03715243,0.10340581,-0.00751699,0.04948465,-0.02660415,0.04879199,0.00030033,-0.06215144,-0.10015249,0.0290097,0.04265771,0.00073482,-0.00446135,-0.06699214,-0.03911436,-0.03316515,-0.00524809,0.03272916,-0.06151276,0.00797499,-0.02105886,-0.03660038,0.00668123,0.01721176,0.06852449,0.01213998,0.05601729,0.04105829,0.08871949,-0.00019449,-0.04332711,0.03295942,-0.05607179,-0.04203163,0.04125807,0.01929281,-0.03946859,-0.02703651,0.01368529,0.06982991,-0.05010664,-0.0177225,-0.014118,0.04510424,-0.02355211,-0.02617733,0.1246449,0.00303291,-0.03907996,0.04535663,0.00617678,-0.05302558,-0.11272263,0.03246264,0.05151383,0.012229,-0.03097954,0.05331909,0.06354152,0.01134434,0.08018014,-0.00372441,0.00146732,0.00964552,-0.01780277,-0.03330666,-0.04302195,-0.07250942,-0.02454584,0.04284428,0.042544,-0.29537594,-0.02341417,-0.01370515,0.0185576,0.04764536,-0.02470568,0.08193776,0.01041394,-0.06731508,0.00780211,0.01758638,0.05025285,0.01036683,-0.08904351,-0.01820997,-0.05833953,0.00803848,-0.02596403,0.05971173,0.03001101,0.04602965,0.08207732,0.20380248,0.00878411,0.04721174,0.04387033,-0.04371827,0.04039082,0.05066195,0.02171376,-0.0015062,-0.00882903,0.02721904,-0.02703529,0.06275718,0.05555481,-0.01283625,0.03359089,-0.00787802,0.0272116,0.0017185,-0.00900526,-0.02042297,0.01429742,0.12568325,-0.0473038,-0.03188188,-0.09736513,0.02520909,0.02144339,-0.08010239,-0.00952821,0.04313323,0.03358382,-0.02720162,0.0696298,0.01408837,-0.0057534,-0.04437328,-0.03300201,-0.04583351,-0.02957216,-0.02894567,0.02749972,-0.01268419],"last_embed":{"hash":"wzieac","tokens":157}}},"text":null,"length":0,"last_read":{"hash":"wzieac","at":1751426530145},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{34}","lines":[326,326],"size":240,"outlinks":[{"title":"https://asicmarketplace.com/blog/how-to-calculate-profit-in-crypto-mining/","target":"https://asicmarketplace.com/blog/how-to-calculate-profit-in-crypto-mining/","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"wzieac","at":1751426530145}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{35}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01821079,-0.02729771,-0.06821003,-0.01455982,0.06428385,0.00205492,-0.04018894,0.01698646,-0.02476109,0.00824353,0.06671598,-0.07719425,0.04121029,0.03053095,0.01448063,-0.00944156,0.00732107,-0.04355557,-0.00410693,0.03520957,0.13137399,-0.06790862,0.03151881,-0.00961598,0.06392349,0.07913055,0.01054361,-0.01584462,-0.0374365,-0.18790722,-0.00016724,-0.03015063,-0.01904207,0.00402299,0.02168246,-0.04117166,-0.03948414,0.05142557,-0.04574848,0.01005455,0.0085528,-0.00072705,0.01864792,-0.01426219,0.06474411,-0.08081436,0.01251437,-0.03129596,0.02666369,-0.0334762,-0.01294661,-0.0748487,-0.00073818,0.03305424,0.03711843,0.03704313,0.01029246,0.02592153,0.04534003,0.03953444,0.10943149,0.04145175,-0.20591405,0.00445099,0.08352635,0.06214886,-0.01826542,0.02482094,0.04821014,0.07518891,0.03480783,0.02389144,-0.02142855,0.02377754,0.03239775,0.00171147,0.03982487,0.02921407,-0.02318038,-0.08753496,-0.02753792,0.06555216,-0.01014409,-0.02691117,-0.00337485,-0.02613736,-0.00785644,-0.01507383,-0.02959586,0.01556614,0.01390521,0.01420573,0.04622641,-0.00706862,-0.05497193,-0.00596239,-0.00202575,0.02065652,-0.10172009,0.12534831,-0.02232443,0.06141282,0.01066246,-0.05303893,0.02314926,-0.00839087,-0.02085895,-0.02521637,0.03737986,0.02221453,0.00603308,0.02068675,0.05621816,-0.07469688,0.02014474,-0.00775553,-0.04896192,-0.01814575,-0.03845464,0.02970846,-0.01402377,0.02548618,0.05321356,-0.02445252,-0.05290191,-0.02305372,0.0121346,0.04000871,0.07813717,0.06645124,0.0471901,0.02987212,-0.09858785,0.0105514,-0.04467251,-0.04925822,0.02752612,-0.00727766,-0.01987524,-0.02821125,-0.03800679,-0.09979258,-0.04798999,-0.10427219,-0.08996448,0.07343369,-0.01278038,-0.01772095,0.02353178,-0.0245133,0.00758453,0.09346779,0.00236319,-0.07781788,-0.00503067,-0.01409402,0.0380684,0.08979174,-0.05902411,0.02292319,-0.03833555,-0.04970643,-0.05575185,0.14310701,0.02947609,-0.14141963,0.01922621,0.02442485,-0.00795647,-0.0651504,-0.01396277,0.03050601,-0.02233773,-0.03758173,0.05777156,-0.03117595,-0.0507655,-0.03203727,-0.00514514,0.0498887,-0.00337549,-0.00118467,-0.07810999,-0.01125302,-0.00354778,-0.04750505,-0.04048012,0.03425175,0.00795554,0.02604263,-0.09930912,0.00253817,-0.02905595,0.01851701,0.00516208,-0.01423429,-0.00658224,-0.01723505,0.00053328,-0.04586437,0.09152599,0.05218996,0.03003363,0.03842777,-0.0602859,0.03318799,-0.0478583,-0.03150812,-0.0313657,0.04125573,-0.02530617,-0.01547617,-0.00884019,0.05586749,-0.06206996,0.03386974,0.04175517,0.04150499,-0.02591437,0.05550552,0.01282452,0.01883496,-0.05624368,-0.19468932,-0.04317651,0.04599611,-0.02280386,0.00628736,-0.01153578,0.05903266,0.02920355,0.0161116,0.06842925,0.08864482,0.04566741,-0.02316371,-0.00986081,-0.00622977,0.05212379,0.05269469,-0.00371495,0.01983869,0.01697522,-0.02735915,0.02034942,0.00240738,-0.05026422,0.04802725,-0.06298482,0.11016843,-0.01788912,-0.03990242,0.04664397,0.07361845,0.00046872,-0.03669171,-0.1460121,0.05194518,0.04491945,0.00484201,0.01086259,-0.07478701,-0.01788164,0.00249967,0.0067191,0.04117419,-0.11230133,0.02732978,-0.06954651,-0.02750314,-0.01307538,-0.00223339,0.00616571,-0.0297898,0.019253,0.03257421,0.10100028,0.00516191,0.01068777,0.01324738,-0.02359931,0.00448008,0.00291863,-0.00280008,-0.00028698,-0.00431159,0.00653057,0.02825472,-0.03589015,-0.04579062,-0.00072853,0.01901818,-0.03668864,-0.02371938,0.10347546,0.05180513,-0.01030811,0.04490501,0.05023644,-0.04945285,-0.1055219,0.03892911,-0.00381561,0.00283588,-0.0069093,0.04817958,0.06493205,0.00546557,0.06915989,-0.02290871,-0.01069196,0.01530811,-0.00553491,-0.0081071,-0.05879207,-0.04193413,-0.0337518,0.07164025,0.03345932,-0.27593523,-0.03150301,0.01503238,-0.01943017,0.05048983,0.02309418,0.06199989,0.03485677,-0.06734341,0.03257096,0.01060196,0.03749323,-0.00009886,-0.03126442,-0.00505279,-0.01428337,0.08097586,-0.01630852,0.04784706,0.04054439,0.02666885,0.05852927,0.20955369,-0.01775608,0.02064684,0.02497626,-0.08809728,0.05202229,0.0589372,0.02498703,-0.03684032,-0.01006235,0.03971815,-0.03921936,0.03021686,0.03530241,0.01563717,-0.01654253,0.00273247,0.01504147,-0.06315158,0.0040422,-0.01580895,0.01323537,0.09066766,-0.00878933,-0.07433495,-0.0865984,0.00912625,0.03828368,-0.04741762,0.00842596,-0.00306855,0.04257529,-0.01699333,0.08536817,0.03271925,-0.03140817,-0.04581551,-0.03537113,-0.04808844,-0.02844395,-0.02666619,0.06198944,0.01902164],"last_embed":{"hash":"1mcp1d4","tokens":161}}},"text":null,"length":0,"last_read":{"hash":"1mcp1d4","at":1751426530152},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{35}","lines":[327,327],"size":220,"outlinks":[{"title":"https://www.rootdata.com/Projects/detail/Nockpool?k=MTc2Mzg%3D","target":"https://www.rootdata.com/Projects/detail/Nockpool?k=MTc2Mzg%3D","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1mcp1d4","at":1751426530152}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{39}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02248798,-0.03230744,-0.06440999,-0.01882012,0.07010151,0.00572852,-0.05235145,0.02937484,-0.03720978,0.01931474,0.06736161,-0.06618829,0.03128288,0.03177902,0.03471993,-0.02452208,-0.00276371,-0.04962445,-0.02303574,0.01525201,0.14280252,-0.06546087,0.03104745,0.01136422,0.08319065,0.09706499,0.00811089,-0.02607229,-0.02893788,-0.18558101,-0.01635149,-0.03277359,0.00268641,0.00077091,0.03227834,-0.04302742,-0.04285832,0.05680686,-0.03125642,0.01641104,0.01944877,-0.00383056,0.01828308,-0.00802332,0.06882286,-0.07690553,0.00799647,-0.05039235,0.01677945,-0.03749708,-0.00912121,-0.08323476,-0.00349213,0.03408832,0.02008105,0.0431633,0.01140965,0.03595993,0.039968,0.05261254,0.13038972,0.06127914,-0.21637785,0.0141014,0.08073907,0.05666123,-0.02538083,0.01244209,0.04297848,0.06944046,0.02271327,0.02092708,-0.01958934,0.03245645,0.0244585,-0.00482043,0.03557901,0.02407155,-0.02210205,-0.08813985,-0.0134019,0.08697718,-0.02180282,-0.01741341,-0.01665959,-0.03678133,-0.01674641,-0.01184503,-0.03576767,0.02411321,-0.00115952,0.00163615,0.06552057,0.00592925,-0.0655048,-0.01337375,-0.01936378,0.03227293,-0.09179014,0.12477611,-0.03709779,0.05490425,0.01091666,-0.03895892,0.02974505,-0.00552659,-0.01812262,-0.04340209,0.04550102,0.03273784,0.00695482,0.0026353,0.03073245,-0.06962971,0.01706811,-0.01242232,-0.03155597,-0.01470409,-0.03873511,0.00360038,-0.01564622,0.02690346,0.0464021,-0.02450669,-0.05928119,-0.01709986,0.03025702,0.03779773,0.07143794,0.06834816,0.04571666,0.03350818,-0.10851645,-0.00112814,-0.04483073,-0.03819863,0.0140044,-0.00137909,-0.02272327,-0.04054883,-0.02921918,-0.11411931,-0.03460381,-0.09053455,-0.0896605,0.07407874,-0.00355196,0.00704363,0.01224993,-0.02560934,0.013194,0.08641146,0.0019593,-0.08485483,-0.01186889,-0.0141967,0.03572761,0.07570772,-0.05701683,0.01300306,-0.02787329,-0.04103136,-0.04618881,0.1415693,0.03420052,-0.13475993,0.01067514,0.04488697,-0.00476142,-0.06653916,-0.00793375,0.00788512,-0.02206353,-0.02677601,0.07481712,-0.02956787,-0.03344883,-0.04209867,-0.00891314,0.05710705,0.00268006,-0.00925422,-0.08397268,0.00608903,-0.01093614,-0.05453127,-0.03235177,0.03793987,0.00049508,0.02843907,-0.068744,0.01238266,-0.03608781,0.0223955,-0.00113697,0.00052056,0.0045978,-0.02560519,-0.00528269,-0.03221543,0.07412585,0.03688733,0.01346449,0.02994404,-0.03776691,0.03143577,-0.04995133,-0.01734106,-0.0127966,0.01616134,-0.03667009,0.01938778,0.00720683,0.05482475,-0.06665609,0.03217625,0.03414007,0.04553565,-0.02322386,0.05887257,0.002006,0.02565287,-0.03730424,-0.20468371,-0.03957269,0.05473895,-0.0270917,0.00096713,-0.01988449,0.05467817,0.02353803,0.01729795,0.0737578,0.10179839,0.04536873,-0.02097588,0.00925997,-0.00518395,0.04345304,0.05085457,-0.00465605,0.01823062,0.01221497,-0.02410558,0.01558129,-0.01496895,-0.06790219,0.03236873,-0.04889463,0.10440711,-0.02397194,-0.02146816,0.01369523,0.07632224,0.00095486,-0.0318124,-0.13850845,0.05681333,0.01956076,-0.00106188,-0.00263039,-0.08757573,-0.02667447,0.01089703,0.00437793,0.045205,-0.09407179,0.03203339,-0.08200084,-0.038046,-0.01346569,0.00043279,0.03610816,-0.02587056,0.01972642,0.03297609,0.11160434,0.00034718,-0.00333095,0.02330068,-0.03512186,0.00120272,0.01853137,-0.00315674,0.00855936,-0.00835318,-0.00225081,0.03575499,-0.02313965,-0.03827523,-0.0126298,0.038055,-0.03238436,-0.01509425,0.11126038,0.04533912,-0.00677197,0.0468099,0.06054727,-0.0481824,-0.10176729,0.03698217,-0.00373479,0.0236521,-0.00418758,0.04244183,0.05361926,0.00183471,0.05833062,0.00821031,-0.01630796,0.03049817,-0.00705899,0.00323612,-0.05931188,-0.03434492,-0.03740999,0.06473276,0.03167587,-0.27483118,-0.03029574,0.01713316,-0.03760306,0.05203607,0.02169399,0.03519846,0.0191277,-0.05909995,0.03120075,0.00540262,0.05211905,-0.00448443,-0.04656114,0.00595875,-0.01955762,0.06745838,-0.00319294,0.06451996,0.00659542,0.01284082,0.05034067,0.20314655,-0.01347397,0.00356534,0.02044479,-0.08223986,0.04056345,0.0510318,0.02813371,-0.05467401,-0.01049892,0.04500771,-0.02155033,0.01548239,0.04750069,0.01595844,-0.00264833,-0.0070506,0.01971748,-0.05825574,0.00131835,-0.0139933,0.00724511,0.10239856,-0.00665507,-0.05869995,-0.08331911,-0.00045521,0.03208195,-0.05403515,0.0107755,-0.01286654,0.03606986,-0.02450513,0.07816179,0.04740769,-0.01908752,-0.043989,-0.04306026,-0.04556749,-0.04124936,-0.04243461,0.0840342,0.02207609],"last_embed":{"hash":"95ix0t","tokens":157}}},"text":null,"length":0,"last_read":{"hash":"95ix0t","at":1751426530159},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{39}","lines":[331,331],"size":221,"outlinks":[{"title":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA=","target":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"95ix0t","at":1751426530159}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{42}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02491799,-0.02297013,-0.0444466,-0.01431832,0.00417991,-0.00123712,0.03280307,-0.00536226,-0.00689996,-0.01795299,0.03045423,-0.03951897,0.02555034,0.01351386,0.04211239,-0.03806292,0.01010077,-0.03891211,0.00356284,0.0198259,0.12980674,-0.04415408,-0.00157304,-0.0223746,0.03786616,0.06110847,-0.00253152,-0.01946637,-0.06852744,-0.16221236,0.00832146,-0.07765302,-0.02207842,-0.01442155,0.03569848,-0.06009116,0.00292698,0.06322733,-0.08332996,0.00051439,0.01078201,0.01994608,0.01881292,-0.0522942,0.04453702,-0.10183267,0.03137268,-0.03321081,0.04432855,-0.06883568,-0.03835043,-0.05282347,0.02972586,0.0311499,-0.00064656,0.00416474,0.02296762,0.02945036,0.06237288,0.02747118,0.10003152,0.05970274,-0.18698175,0.03431436,0.06188762,0.07134921,-0.05930734,0.03957888,0.05720863,0.07610959,0.00532744,-0.02205229,-0.06579003,0.03498001,0.03400593,0.01199939,0.01108676,0.02162746,-0.04501468,-0.06244183,-0.0726399,0.04032079,0.02386937,-0.00517264,-0.00724521,0.00332179,0.02384635,-0.02384912,0.00112299,0.03480469,0.0169706,-0.01067501,0.04320823,-0.00056375,-0.05997177,-0.03903826,0.00151149,0.01614873,-0.10675175,0.13250256,-0.0349554,0.06960881,-0.02178597,-0.03635634,0.04230918,-0.00587486,0.0111564,-0.04315957,0.01765429,-0.01290562,0.04561307,0.02810547,0.05281417,-0.06181285,0.01012609,0.01545496,-0.0019595,0.00900701,0.02084901,0.03895113,0.03861157,0.02689007,0.05494887,0.00313364,0.01417854,-0.02512531,0.0226758,0.04939156,0.06609064,0.08414107,0.02242299,0.03576291,-0.07516653,-0.02957083,-0.0260879,-0.01739218,-0.03115138,0.00855606,0.02837311,-0.04244478,-0.00575913,-0.06271905,-0.0151985,-0.08974986,-0.04824325,0.05372372,-0.02588963,0.00875996,-0.02593261,-0.05383803,0.01790136,0.05649751,-0.02072387,-0.10711513,-0.00207302,-0.00890223,0.05648194,0.0911167,-0.07093573,0.0235185,-0.00434667,-0.0061818,-0.0497207,0.18990465,0.06336743,-0.07060878,0.02986896,0.03885765,-0.00477843,-0.07594418,-0.01264085,0.01100194,-0.06212433,0.00778442,0.05281036,-0.04158879,-0.03948855,-0.0382313,0.00252383,0.03045495,0.02575869,-0.04477832,-0.07201153,0.00048825,0.00630173,0.00784292,-0.00407784,0.00210171,0.035513,0.02860253,-0.13732257,0.04065007,0.02467226,0.07679277,-0.05548146,-0.00272041,-0.04762629,-0.04591254,-0.02361932,-0.05475991,0.11082768,0.04422738,-0.01454406,0.0390483,-0.06606405,0.02273176,0.00542534,-0.06033023,0.00807453,0.03848954,-0.03514031,0.0489249,0.0094273,0.0478461,-0.0259798,0.04006996,0.02410515,0.03918204,-0.02472471,-0.0140872,0.03790082,0.02735617,-0.07195112,-0.19974446,-0.0141165,0.04484928,0.00545904,0.01039993,-0.01330435,0.0326164,0.02318719,0.00475836,0.09778959,0.10100839,0.05189477,-0.0731812,0.02231159,0.00239426,0.06524314,0.02616156,-0.01709175,0.00673363,0.04231015,-0.04575713,0.00250175,0.01063042,-0.054513,0.08630116,-0.03442941,0.09826194,-0.02505095,0.02382145,-0.04211366,0.06358609,0.0122843,-0.03226116,-0.14971773,0.06354776,0.01225122,-0.02176985,-0.00869224,-0.03077904,-0.04577619,-0.00364783,-0.0005503,0.01096153,-0.08220103,0.01137133,-0.08656834,-0.00903565,-0.02962349,-0.00456493,0.01919419,0.00659104,0.03584233,0.02203785,0.10012114,-0.01190011,-0.05384804,0.02996365,-0.07399308,-0.00246767,0.04713293,-0.00776746,0.01398484,-0.03635975,-0.04096956,0.01328126,-0.00824659,-0.01094132,0.01830855,0.0219372,-0.03673111,-0.0469525,0.15032981,0.03230032,-0.06671584,0.01950631,0.04545691,-0.07679461,-0.07830654,0.00166948,0.00547075,0.03371066,0.02728984,0.04061562,0.01905698,-0.00540656,0.05892969,-0.04108467,0.01538547,0.02965129,-0.00098531,-0.02811291,-0.06729082,-0.07324607,-0.0748237,0.03510217,0.02355317,-0.27878064,-0.04288528,0.01733267,0.005761,0.02598459,0.01406846,0.07383987,-0.01472,-0.0916483,0.01761452,-0.01806552,0.04807754,0.02350065,-0.02770617,0.00902766,-0.04684516,0.02217977,-0.02444123,0.00194708,0.03715433,-0.01090165,0.05497258,0.18779124,0.02346792,0.04988987,0.0078974,-0.022066,0.052818,0.07153763,0.00011474,-0.00287276,0.00704853,-0.01839994,-0.00313651,0.03751593,0.04042254,0.01436706,0.00657515,-0.0114619,0.01363652,-0.01780714,-0.01097593,-0.00995438,0.03182694,0.08720995,-0.00547144,-0.04502975,-0.06387446,0.02524838,0.05229698,-0.05994119,0.01879994,0.0559876,0.06284986,-0.03893083,0.07039782,0.00506204,-0.048052,-0.07803967,-0.01048599,0.01121791,-0.0221949,-0.0390793,0.02754949,0.01172595],"last_embed":{"hash":"1ut5hz1","tokens":147}}},"text":null,"length":0,"last_read":{"hash":"1ut5hz1","at":1751426530165},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{42}","lines":[334,334],"size":200,"outlinks":[{"title":"https://www.lcx.com/how-to-conduct-blockchain-security-audit/","target":"https://www.lcx.com/how-to-conduct-blockchain-security-audit/","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1ut5hz1","at":1751426530165}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{44}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03963373,-0.035662,-0.052922,-0.01357261,0.03050141,0.01757067,0.0156479,0.02035717,-0.00414685,-0.01397969,0.05250515,-0.03864555,0.03640609,0.01367899,0.0222301,-0.02363807,0.00714764,-0.02655826,-0.01072986,0.03770981,0.12492235,-0.05728158,0.03045201,-0.01428121,0.07096355,0.06484292,0.01739901,-0.01378767,-0.06794566,-0.16984141,-0.02003823,-0.08978453,-0.01741108,-0.0092072,0.01980247,-0.05957948,-0.01760943,0.04755477,-0.02389948,0.00856308,-0.00787367,0.01461708,0.00744776,-0.03692092,0.02918549,-0.0927735,0.02352612,-0.050056,-0.00670093,-0.08215957,-0.05263699,-0.05032527,-0.01058342,0.04647405,0.01121891,-0.02206501,0.01555891,0.03078376,0.04197335,0.03476239,0.10372813,0.05077954,-0.20099358,0.01782419,0.09203926,0.04669218,-0.0479168,0.00854861,0.06413151,0.05691416,0.01698178,-0.00758538,-0.03819655,0.04773648,0.01121361,0.00494809,0.01851502,0.0171348,-0.02149615,-0.04452808,-0.07572313,0.0650745,0.0193533,-0.04483571,-0.01157891,-0.00843608,0.00770216,-0.04406139,-0.00777136,0.03231903,0.02556919,-0.00638642,0.06491292,0.00737654,-0.03073433,-0.03680997,0.00754902,-0.00210567,-0.10397946,0.13450834,-0.03598193,0.04827693,-0.01332358,-0.03934151,0.0356977,0.00800181,-0.00354474,-0.06674555,0.00868724,0.02104234,0.04519579,0.0008877,0.06648522,-0.06647147,0.04029616,0.02299949,0.01214918,0.01089274,0.02488535,0.02798563,0.02395096,0.01961101,0.08478237,-0.02686715,-0.01582024,0.00721968,0.0111144,0.05740273,0.06389627,0.07129857,0.05834761,0.04490714,-0.07520621,-0.00090381,-0.03514251,-0.03408613,-0.01565453,-0.00365414,0.03020143,-0.04533567,-0.0211289,-0.05819845,-0.02549887,-0.11499838,-0.05211126,0.07586799,-0.01269874,0.00839056,-0.02494928,-0.06009421,0.0216897,0.0917109,-0.0156455,-0.09848223,-0.00104611,0.00993653,0.02856923,0.11225814,-0.06188229,-0.01040536,-0.00735156,-0.04259601,-0.06730415,0.14509144,0.04466502,-0.08471334,0.03606324,0.03991557,-0.00763844,-0.08043043,-0.00162744,0.00091092,-0.05806247,-0.0075759,0.05383513,-0.02920048,-0.02106559,-0.05498425,0.00402252,0.02495735,0.0069361,-0.05857845,-0.09410744,0.01439223,0.00173555,-0.01421797,0.00227155,0.01440826,0.04144494,0.0399729,-0.09981119,0.0260425,0.00219307,0.0520361,-0.04684131,-0.00233275,-0.03743412,-0.03444685,-0.0251001,-0.07408266,0.10977798,0.03117923,-0.0001197,0.00031586,-0.04584641,0.02656279,-0.01452636,-0.05565291,0.04548386,0.01479467,-0.03417565,0.06658836,-0.0054789,0.06659546,-0.02951091,0.01718852,0.03229501,0.03788843,-0.03694188,-0.00807004,0.02864471,0.05011372,-0.09498826,-0.19165707,-0.03940179,0.01108621,0.00502016,-0.00970855,0.00703927,0.01966397,0.03927945,0.01914341,0.0730043,0.1294663,0.07946124,-0.0412884,0.01984445,0.00373889,0.08162732,0.03586227,-0.0281592,-0.01924747,0.06657314,-0.02393078,0.02802282,0.00918748,-0.04320217,0.08705745,-0.0388696,0.10626861,-0.00983036,0.0297465,-0.0093892,0.0390932,0.01325784,0.00367987,-0.14811005,0.02978244,0.04425815,-0.01725475,0.00095756,-0.0387798,-0.05635707,-0.00145356,0.02591246,0.02418019,-0.08483198,0.01109725,-0.07309394,-0.01401794,-0.01349594,-0.01701977,0.00520351,0.00580315,0.04347465,0.02642371,0.11734314,-0.01857701,-0.02912519,0.01170017,-0.08201639,-0.01143878,0.07376033,-0.00546813,-0.0079753,-0.05167419,-0.03244229,0.0055291,-0.00941099,-0.01503648,0.04862899,0.03146769,-0.02956519,-0.04880714,0.13482238,0.0364975,-0.05553134,0.01477756,0.03551384,-0.04317277,-0.08710373,0.00125503,-0.01377286,0.01776366,-0.00741777,0.03216849,0.0377201,0.00223957,0.05653916,-0.00819605,0.00354554,0.0428738,0.00366844,-0.01321448,-0.08028127,-0.07689264,-0.08139852,0.05575387,0.02671559,-0.26300582,-0.03657468,0.01314641,-0.00642086,0.03533943,0.00675796,0.0509931,0.00258826,-0.06277379,0.01501764,-0.00812513,0.0566795,0.01156469,-0.04847805,-0.00914837,-0.0279002,0.03756012,-0.01824006,0.02724337,0.0432271,0.00299161,0.05012626,0.20677599,0.03066201,0.01469393,0.02393185,-0.03303245,0.03853023,0.03209879,0.01540494,-0.0089442,-0.01225578,-0.00694311,0.00474778,0.02635589,0.06188055,0.00478929,0.00103425,-0.01145299,0.017156,-0.0462634,-0.00361646,0.00802601,0.03581898,0.10467004,-0.02876608,-0.05813641,-0.0719272,0.02037244,0.04207206,-0.04726798,0.01617532,0.0384478,0.0315082,-0.01508866,0.05897371,0.02688508,-0.05131572,-0.07764413,-0.03009316,-0.01049422,0.00724371,-0.06842737,0.02957306,0.01874883],"last_embed":{"hash":"pe27k5","tokens":151}}},"text":null,"length":0,"last_read":{"hash":"pe27k5","at":1751426530171},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{44}","lines":[336,336],"size":239,"outlinks":[{"title":"https://immunebytes.com/blog/blockchain-security-audit-the-ultimate-guide/","target":"https://immunebytes.com/blog/blockchain-security-audit-the-ultimate-guide/","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"pe27k5","at":1751426530171}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{45}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03165889,-0.04722496,-0.06618886,-0.04553007,0.03101263,-0.0013847,0.01345039,0.01163241,0.00036743,-0.01464148,0.05384646,-0.05165437,0.02714331,0.02031625,0.02958267,-0.03504922,0.013346,-0.04812636,0.01627896,0.01179364,0.12226222,-0.06329273,0.02869223,-0.01523173,0.04879451,0.07546403,-0.01627017,-0.02263169,-0.06791737,-0.17095622,0.01800651,-0.09177414,-0.00770101,0.00896963,0.03899521,-0.05165609,-0.01556078,0.05016576,-0.03672606,0.00629789,0.01996012,-0.00340255,-0.00239211,-0.05106629,0.01909913,-0.08751621,0.0348937,-0.04775712,0.00066699,-0.059766,-0.04772773,-0.05683824,0.01512974,0.02942176,0.03334239,-0.00831398,0.0111423,0.02983582,0.04192414,0.0110262,0.09073618,0.04351699,-0.19385856,0.01460755,0.08272983,0.0644767,-0.05767169,0.00466609,0.06791127,0.042078,0.02620197,0.00702886,-0.03641229,0.03635791,0.0283964,0.0234412,0.01031573,0.02919939,-0.04815995,-0.0498925,-0.07444661,0.05353902,0.0243744,0.00214393,-0.03272705,0.00060975,-0.00399752,-0.01748161,0.00466952,0.02605765,0.0036515,0.00241954,0.05977764,0.00411625,-0.01512329,-0.0268054,-0.00866321,0.01996241,-0.10998748,0.13044265,-0.05010403,0.05237525,-0.01189851,-0.04298996,0.04265757,-0.01188572,-0.01758424,-0.07462916,0.05024387,0.03232279,0.02847108,0.01075502,0.06921579,-0.07058269,0.02649617,0.02042494,0.00570777,0.02224606,0.01185276,0.02386372,0.02993774,0.05038239,0.0777586,-0.02160645,-0.01863421,-0.02034075,0.02656716,0.04707474,0.04900279,0.07965955,0.0123037,0.04071308,-0.08169626,-0.0133996,-0.04383826,-0.04852497,-0.00457697,0.0089103,-0.00986961,-0.04205903,-0.02410363,-0.06116401,-0.01429335,-0.09643825,-0.03161623,0.06691489,-0.00337344,0.04047048,-0.01077574,-0.05946337,-0.00509905,0.0941284,-0.03272225,-0.1033764,0.00550571,0.01168352,0.02067732,0.0954488,-0.04188271,0.00703107,0.00237329,-0.02250277,-0.0549139,0.14638251,0.03874514,-0.08046026,0.02341427,0.048112,-0.02026551,-0.06602607,-0.0148414,0.02186124,-0.04889451,-0.00894683,0.04752891,-0.05367628,-0.0221678,-0.0493406,0.00504952,0.03245818,0.00392606,-0.02639444,-0.07406566,0.01536154,0.02173788,-0.02433715,-0.00105932,0.02126409,0.02543194,0.05228278,-0.12949884,0.03500228,0.00381322,0.05802905,-0.04231911,-0.01596427,-0.04018071,-0.02578442,-0.03232244,-0.06789289,0.1342584,0.05244188,0.01153617,0.00951207,-0.0676465,0.01225508,-0.02410168,-0.02959627,0.00418317,0.03578152,-0.03206639,0.04796473,-0.02713814,0.03737723,-0.00833435,0.0077258,0.02737199,0.04799689,-0.0135009,0.00502237,0.02752222,0.02432088,-0.08734847,-0.18161663,-0.04402579,0.04161542,-0.00648726,0.02731808,-0.00439182,0.03972506,0.02097435,0.01470985,0.07165781,0.10273808,0.08953504,-0.08250006,0.02394707,0.02021842,0.07347766,0.03397869,-0.02700696,0.00745135,0.07005704,-0.02585276,0.0278103,0.00498405,-0.03134964,0.06676384,-0.04833313,0.11367967,-0.03101572,0.02774796,-0.00886546,0.02822432,0.00421938,-0.01182337,-0.13969061,0.07194164,0.05816631,0.00602017,0.0180296,-0.05796448,-0.03858132,-0.03112068,-0.00766831,0.02722304,-0.09798369,-0.00087459,-0.08034889,-0.02318192,-0.0285166,-0.01333386,0.00462977,-0.0005024,0.04015589,0.04537062,0.11291501,-0.00462441,-0.02670073,0.01949099,-0.07520673,0.00890513,0.05917438,-0.01606643,0.00608474,-0.05313488,-0.04531294,0.02770231,-0.01701517,-0.03220806,0.02083109,0.02766964,-0.03791267,-0.03325091,0.15950103,0.02609092,-0.04251264,0.02711459,0.05655976,-0.04873708,-0.10007869,0.01641307,0.00613945,0.02609713,-0.00124577,0.01972814,0.02453255,-0.03664611,0.0622522,-0.01305112,0.0142307,0.03909511,0.00682762,-0.02842472,-0.08185437,-0.06943338,-0.07845172,0.02564741,0.04104386,-0.26588583,-0.03900084,0.01560579,0.00402789,0.05187206,0.02246799,0.03784624,0.00746063,-0.07027375,0.01170446,-0.00301381,0.04569648,0.03622077,-0.04927342,-0.00631395,-0.037856,0.04575189,-0.02897357,0.02620951,0.0372471,0.00793561,0.0749745,0.21075046,0.01116973,0.0395029,0.01546444,-0.03562994,0.04222742,0.05575364,0.01061633,-0.02557829,-0.0073144,-0.0013427,-0.01020639,0.00317421,0.0583467,0.01407576,-0.00165726,0.00619973,-0.00049924,-0.04158294,-0.00281757,-0.01009082,0.01541435,0.09355335,-0.01067249,-0.05471022,-0.07500767,0.03489364,0.05077337,-0.05953417,-0.0181847,0.03161018,0.05620068,-0.00274434,0.05352851,0.01535685,-0.02422,-0.0922382,-0.00142335,-0.00165166,-0.01403918,-0.03539075,0.02941728,0.00928779],"last_embed":{"hash":"12350os","tokens":152}}},"text":null,"length":0,"last_read":{"hash":"12350os","at":1751426530178},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{45}","lines":[337,337],"size":245,"outlinks":[{"title":"https://hacken.io/services/blockchain-security/blockchain-protocol-security/","target":"https://hacken.io/services/blockchain-security/blockchain-protocol-security/","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"12350os","at":1751426530178}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{47}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03622561,-0.02832355,-0.07872754,-0.01253423,0.04439735,0.01948104,-0.00702966,-0.00440214,-0.02306787,-0.00470431,0.07267175,-0.04769803,0.02953928,0.01930135,0.03319595,-0.0032815,0.0158241,-0.06340556,-0.02491634,0.00673552,0.1306468,-0.08169255,0.03238814,-0.0278334,0.06253064,0.04138133,-0.01404342,0.01299885,-0.06055112,-0.16770072,-0.03676381,-0.05930123,0.01318579,-0.00054395,0.02513306,-0.04921122,-0.01435177,0.03503073,-0.06113252,0.01945963,-0.02214295,0.00174984,0.0088427,0.01752033,0.04729911,-0.06086534,0.02322913,-0.04843853,0.00012667,-0.05746963,-0.00061189,-0.03988684,-0.00292096,0.03556534,0.00623988,0.01181856,0.00265005,0.02373624,0.07394513,0.0103359,0.11458261,0.06213516,-0.16402946,0.02069629,0.10096036,0.05812636,-0.00655835,-0.02223029,0.059449,0.03132498,0.05806131,0.02002584,-0.04053395,0.04410414,0.03794606,-0.01109356,0.00818428,0.0275433,-0.05845692,-0.06212222,-0.0311709,0.08232649,-0.02128994,-0.02998559,-0.02072176,-0.0305611,0.01288836,-0.00882589,-0.03115814,0.0227994,-0.00601971,0.05521646,0.07624668,0.00853338,-0.01567819,-0.02731814,0.0240289,0.02920063,-0.10595876,0.11308186,-0.01933761,0.06727696,0.01241306,-0.07100096,0.0357812,0.02057088,0.00922229,-0.03152636,0.03033845,0.03802969,0.01184378,0.01707154,0.05803573,-0.06030088,0.0294615,-0.01890137,0.01650721,0.0078046,-0.01643444,0.01128367,-0.01130556,0.0125189,0.07682875,-0.01252137,0.00315542,-0.01170532,0.01415457,0.05230353,0.06336711,0.05245952,0.01864821,0.0448039,-0.07910512,-0.00602788,-0.04159953,-0.03068718,-0.03296194,0.01445684,0.01735761,-0.04694771,-0.02997886,-0.08866166,-0.01137759,-0.09736586,-0.02674434,0.04736714,-0.00989561,0.00781947,-0.00015099,-0.03872198,0.0093285,0.05294548,-0.03530848,-0.06689605,-0.01094961,-0.03398472,0.04978837,0.08577301,-0.07322378,-0.00985937,-0.00151484,-0.0250759,-0.04381982,0.13912131,0.05627512,-0.10517085,0.01068767,0.0218003,0.00139059,-0.06065845,0.01155749,-0.00565746,-0.03032832,-0.02790315,0.0671047,-0.02843302,-0.0369399,-0.03546685,-0.0151405,0.06429634,-0.00960588,-0.03592691,-0.07385862,-0.00316559,-0.01378465,-0.0144705,-0.02458893,0.02691773,0.01564195,0.05737313,-0.10396118,-0.00693408,-0.03463404,0.02962034,-0.03901567,-0.01890675,-0.00281917,-0.05295296,0.01394897,-0.0343946,0.1054807,0.04546649,0.01360735,-0.01694482,-0.10588859,0.01117216,-0.00771816,-0.03723719,0.01425235,0.02978848,-0.04894837,0.03853939,-0.04399534,0.02843796,-0.01837322,0.01561339,0.03522856,0.03659876,-0.01265335,0.00122741,0.00000951,0.0024116,-0.09398488,-0.20084096,-0.04783749,0.03839844,-0.04853306,0.05766592,-0.02266332,0.06795131,0.03714622,0.05550468,0.08158223,0.10861952,0.04241651,-0.06418712,0.01594322,0.01160079,0.06855202,0.05767578,-0.03123364,0.03541225,0.04173289,-0.04246911,-0.00556567,0.00394407,-0.06445805,0.0639753,-0.06602579,0.11702752,0.02118779,0.02382658,-0.0231742,0.04528446,0.00880723,-0.01009769,-0.14650583,0.04120168,0.0382909,-0.01565578,-0.02231868,-0.06064137,-0.00527828,-0.00656501,0.01337696,0.02796737,-0.08353648,-0.00135614,-0.0627732,-0.01541317,-0.01786137,0.00855888,0.02551824,-0.01241764,0.02416998,0.02004477,0.10694578,0.02204973,-0.02987109,0.02671889,-0.07848853,0.01872585,0.08641946,0.00330017,0.00782485,-0.03572357,0.00260686,0.05373222,-0.01182287,-0.02700509,0.00957892,0.0659508,-0.01176352,-0.02955583,0.1457748,0.01576016,-0.05096851,0.03242314,0.00918221,-0.0388695,-0.07926622,0.01488948,0.02021811,0.03893724,-0.02923728,0.05081908,0.04950162,-0.0234144,0.0758049,-0.03769754,-0.00710936,0.01427883,-0.01997517,-0.02833124,-0.03511053,-0.06182687,-0.09369963,0.02462733,0.04373088,-0.27916229,-0.043291,0.03332714,0.00671585,0.05806261,0.05019232,0.10589734,0.04511695,-0.09619992,0.00687609,0.00413102,0.09644049,0.01884799,-0.0501543,-0.04154988,-0.01511,0.01113743,-0.02352403,0.01359731,0.03507378,-0.00634743,0.0649214,0.20039551,0.00115816,0.01160399,0.02391584,-0.0275822,0.04947201,0.0138172,-0.00888158,-0.0385042,0.01694381,-0.02564179,-0.05672492,0.00225967,0.01996014,0.00231675,0.0215639,0.0142795,0.02733542,-0.02105916,0.0083645,-0.03205159,0.0308593,0.12306633,0.00344978,-0.04493919,-0.08845153,0.02157482,0.02615944,-0.06829818,-0.02726769,-0.00403493,0.05458244,-0.00832595,0.08688743,0.00585167,-0.03461256,-0.05401467,-0.01538489,-0.02658091,-0.0081412,-0.04686999,0.02329316,0.01959668],"last_embed":{"hash":"1fkjo9o","tokens":207}}},"text":null,"length":0,"last_read":{"hash":"1fkjo9o","at":1751426530184},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{47}","lines":[339,339],"size":340,"outlinks":[{"title":"https://www.reddit.com/r/CryptoCurrency/comments/1lhoxfc/outside\\_of\\_the\\_crypto\\_community\\_crypto\\_is\\_still/","target":"https://www.reddit.com/r/CryptoCurrency/comments/1lhoxfc/outside_of_the_crypto_community_crypto_is_still/","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1fkjo9o","at":1751426530184}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{48}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06219383,-0.0382349,-0.06769533,0.0063286,0.00306957,0.02550105,0.00323338,-0.01720122,-0.02297457,-0.00286151,0.06017483,-0.07315892,0.02833415,0.02628395,0.06807552,0.00028635,0.03558845,-0.07583318,-0.02523226,0.05399536,0.09113722,-0.08195839,0.04380814,-0.02362716,0.06908138,0.02857895,0.00939483,-0.0052461,-0.06148314,-0.16882785,-0.05878447,-0.02989851,0.02333755,-0.01873686,0.06332123,-0.03851129,-0.01896211,0.03029485,-0.04465156,0.0179946,-0.01348167,-0.01238379,-0.00209985,0.02552649,0.07671681,-0.09124961,0.03276898,0.00867329,0.0170696,-0.02883787,-0.00346186,-0.0201159,0.00611968,0.04801793,0.0044575,0.03478047,-0.00484532,0.03709466,0.08940111,0.00121107,0.10329847,0.04975793,-0.15629663,0.02478293,0.04809856,0.03230998,-0.01706171,-0.04444654,0.04470483,0.05028205,0.04281767,0.01203497,-0.04730621,0.04755473,0.02681962,-0.00874864,0.05447761,0.01310893,-0.06108871,-0.05212937,-0.05126525,0.08772289,-0.01856324,-0.03990135,-0.04887097,0.00427065,0.02947504,-0.02900278,-0.05257183,0.01521848,-0.01118359,0.03972882,0.06346785,0.02055144,-0.02750891,0.00191427,-0.00752232,0.04917263,-0.09939554,0.1251834,-0.04033832,0.0544316,-0.00152007,-0.09537547,0.02331163,0.0209822,-0.00631533,-0.02798529,0.00334721,0.02623067,-0.02870899,0.04442199,0.0880554,-0.05803806,0.03848805,0.00882355,0.03405431,0.00316446,-0.01348091,0.01374852,-0.0087866,0.03746026,0.07602376,-0.02945843,0.00207025,-0.03304804,0.02573535,0.02738652,0.04418851,0.06396401,0.0066173,0.04257176,-0.0802037,0.00259716,-0.03140392,-0.02763935,-0.04950268,0.00609433,0.00558165,-0.02041933,-0.02261121,-0.07299071,-0.02019388,-0.1110239,-0.04769313,0.03346045,-0.02742017,0.01953773,0.02058069,-0.01117097,0.00464501,0.05581174,-0.03313005,-0.05856194,0.00769451,-0.03046514,0.04648012,0.08001985,-0.05971193,-0.04314353,0.02992479,-0.04058183,-0.05305322,0.13772325,0.04625168,-0.11369459,0.01521241,0.00908463,-0.010566,-0.05224654,0.00850235,-0.01624781,-0.02483818,-0.02425066,0.07617886,-0.0443704,0.02283852,-0.04352453,-0.0119692,0.06863985,0.01607083,-0.04816723,-0.07120927,0.02175488,-0.01859397,-0.02856167,-0.0242776,0.02698539,0.01421375,0.00554503,-0.056617,-0.04153993,-0.0183869,0.04520001,-0.03407795,-0.02539881,-0.0175093,-0.05445703,0.00325393,-0.04760615,0.07766312,0.03388232,0.02894868,-0.03239823,-0.06504157,0.0501027,0.02619265,-0.01901674,0.02226871,0.03490675,-0.03583978,0.03942716,-0.01826615,0.01087407,-0.00899141,-0.01081792,0.02861192,0.04809098,-0.02414774,0.01691727,-0.0428741,0.02625472,-0.09981846,-0.20507944,-0.02192448,0.02915712,-0.01476108,0.09536006,-0.0123801,0.04272017,0.01908022,0.06642733,0.07375465,0.11445865,0.02844447,-0.04042912,0.01682337,0.04908735,0.06066522,0.03958147,-0.00540449,0.04429284,0.02071095,-0.05533544,0.00218621,-0.02495268,-0.09241164,0.06228033,-0.0432177,0.11822373,0.02297352,0.00190803,-0.01235361,0.03429956,0.01050484,0.01646532,-0.18816836,0.04675533,0.03410866,-0.01565735,-0.03321653,-0.02283452,-0.01629828,0.01356164,0.00404484,0.00939933,-0.10635977,0.00803752,-0.07197468,0.02087497,-0.00123265,-0.01803634,0.01473307,0.01461498,0.01716613,0.03708047,0.08883186,-0.00283537,-0.03892146,-0.02180394,-0.05749374,0.0193463,0.09342399,-0.01762662,0.01343329,-0.04444536,-0.00639978,0.05019274,-0.00209038,-0.01926089,0.02520741,0.05705795,-0.0060849,-0.02798818,0.1432454,0.00063279,-0.07507437,0.03853132,0.02428859,-0.03883544,-0.06347036,0.04184682,-0.01294095,0.02384776,-0.02965295,0.03675899,0.05602787,0.0070777,0.08474077,-0.00879888,0.00102778,0.01863809,-0.01820383,-0.01785599,-0.02398285,-0.05725031,-0.09750989,0.028529,0.01281398,-0.26409522,-0.03353699,0.01453477,0.00271215,0.02681773,0.04480933,0.09121746,0.03063059,-0.14079158,0.00187622,0.00560681,0.10041952,0.03433461,-0.02820713,-0.01240889,-0.00799807,0.00828896,-0.0499684,0.00247908,0.0259844,-0.01935935,0.05961163,0.21082506,0.00667855,0.02380611,0.01714493,-0.00535369,0.03912449,0.0366914,-0.03052664,-0.05895429,-0.00194793,-0.0275083,-0.02979067,0.00269203,0.00689991,-0.01016926,0.00225172,-0.03306352,0.02096406,-0.01758318,0.01711653,-0.00687866,0.03917221,0.12926869,-0.01496278,-0.03700732,-0.07999159,-0.00715628,0.00889746,-0.04244948,0.00142595,0.01913168,0.02118694,-0.0032253,0.0849208,0.01786623,-0.0466293,-0.06661835,-0.02333008,-0.06015185,0.02328403,0.00496671,0.03152296,0.0122977],"last_embed":{"hash":"wvf4lg","tokens":189}}},"text":null,"length":0,"last_read":{"hash":"wvf4lg","at":1751426530192},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{48}","lines":[340,340],"size":324,"outlinks":[{"title":"https://www.reddit.com/r/CryptoMarkets/comments/1l5jqcu/which\\_crypto\\_community\\_still\\_feels\\_genuinely/","target":"https://www.reddit.com/r/CryptoMarkets/comments/1l5jqcu/which_crypto_community_still_feels_genuinely/","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"wvf4lg","at":1751426530192}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{49}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01509293,-0.01773779,-0.08243081,0.01058042,0.02260166,0.00287705,0.00909791,0.01140509,0.00346616,-0.02121818,0.06294474,-0.04145644,0.05332939,0.02329719,0.05650163,0.03442255,0.02639055,-0.06591383,-0.02987503,0.02349303,0.09795933,-0.06896953,0.04952336,-0.05235322,0.0788797,0.00994358,-0.02002908,0.0044028,-0.07677331,-0.16552849,-0.00862207,-0.02963571,0.07761368,0.00120307,0.05499885,-0.02631782,-0.01303553,0.03834507,-0.06443632,0.01565371,-0.02578997,0.02120772,0.00263761,0.01438498,0.07952356,-0.09156028,0.0314253,-0.02890484,-0.01922986,-0.00451325,-0.00126534,-0.05255211,-0.01129105,0.01287593,0.01369232,0.00811758,0.00899319,0.0409026,0.0579897,-0.00078127,0.11652616,0.05487921,-0.18797146,0.0226539,0.09794302,0.01112841,-0.00335533,0.02258075,0.00214667,0.0567832,0.05829918,0.02170405,0.00125304,0.02684806,0.01441659,0.00174852,0.03060058,0.01831415,-0.04014612,-0.05759144,-0.01889087,0.06671101,-0.04524172,-0.02886496,-0.02031297,-0.06623469,0.02681159,0.00047182,-0.03880562,0.01292136,0.02706754,-0.00308938,0.05803056,0.01096875,-0.03026828,-0.00179545,0.01562866,0.02573479,-0.1068817,0.12064333,-0.01242171,0.05095904,-0.0103314,-0.10163741,0.05333477,0.00665225,-0.0083018,-0.0403406,0.02223443,0.00764867,-0.00369726,-0.00047413,0.08021044,-0.05589009,0.01884917,-0.0427805,-0.01777664,0.00765733,0.01333794,0.00281411,-0.01784004,0.02235807,0.05092067,-0.01283849,0.01716577,-0.03598121,0.00630729,0.05753287,0.07263996,0.026319,-0.00620758,0.06125066,-0.09247309,0.01229856,-0.03089104,-0.00045808,-0.03134226,-0.01820106,0.00819553,-0.02954239,-0.0323775,-0.07482943,-0.03969987,-0.10889397,-0.03726308,0.04988541,0.0296032,0.000587,0.0153011,-0.01379993,-0.04194473,0.03935103,-0.06764154,-0.06181367,-0.01487122,-0.00057704,0.03875785,0.07884244,-0.08907548,-0.00962151,-0.01594109,-0.0178052,-0.04728216,0.10754197,0.03938103,-0.10305204,0.01876474,-0.00412986,0.00489418,-0.03956417,0.00855757,0.02075261,-0.04679925,-0.01834796,0.04729396,-0.02457813,-0.01361029,-0.05579224,0.0044199,0.05102137,0.03349703,0.00935999,-0.07407369,0.00549595,-0.02886593,-0.01253716,-0.02888387,0.01675952,0.01023321,0.02240597,-0.08082245,-0.04230488,-0.01515503,0.05699735,-0.05461187,-0.00165721,-0.00091773,-0.03621214,0.01372252,-0.03657231,0.06180146,0.03660116,-0.01214381,-0.00912573,-0.07735372,0.04349444,0.02521571,-0.05047546,0.00368046,0.02532909,-0.01863785,0.04209746,-0.04321659,0.03608068,-0.0314378,-0.00703617,0.0201143,0.05329108,0.0243657,0.00843594,0.01628558,0.04437432,-0.11651795,-0.21686172,-0.06595729,0.04151348,-0.01326497,0.07522031,-0.02216586,0.0834795,0.01729839,0.05495017,0.0850058,0.11523536,0.03663898,-0.04137324,-0.0124128,0.04491759,0.04400896,-0.01754529,-0.01405545,0.01687901,0.04142772,-0.02951073,0.02221209,0.02260374,-0.092268,0.08318754,-0.03879612,0.11424023,0.03378746,0.00452646,-0.00139325,0.05447623,-0.00862746,-0.02520242,-0.15470098,0.06197507,0.03333227,0.02327353,-0.01786574,-0.07894946,-0.01032897,-0.00103081,0.02925199,0.02968549,-0.07857561,-0.02848445,-0.06277075,0.02388817,0.00302361,-0.02252725,0.0562892,-0.00442879,0.03470029,0.04250071,0.12669501,0.04258508,-0.02866291,-0.00390456,-0.0600578,0.02598474,0.08294132,0.02780997,0.00839832,-0.00481901,-0.01626119,0.02940921,-0.00285598,-0.00997323,0.00557882,0.01237231,0.01632392,-0.06651637,0.1489556,0.00775976,-0.03697226,0.02303951,0.02137656,-0.05959861,-0.05913575,0.02469236,0.00558793,0.03010243,-0.02006717,0.02956844,0.07711378,-0.03444001,0.05267597,-0.01734252,-0.03497349,0.01615872,-0.01130169,-0.05735796,-0.01859797,-0.07957292,-0.08280081,0.0473941,0.03476303,-0.27859628,-0.05600519,0.00950074,0.01771701,0.07635099,0.05233034,0.08346353,0.00798386,-0.10161149,0.02710121,-0.00599608,0.10994909,0.00370502,-0.06087518,-0.01719812,-0.03895627,0.01020711,-0.01038826,0.03657679,0.02372916,-0.0097039,0.05271158,0.19239022,0.0319963,0.00493029,0.02301022,-0.02045849,0.01941612,0.01446837,0.00401308,-0.03214337,-0.00252087,-0.00593824,-0.06245124,0.01409184,0.01579183,-0.02950029,-0.0176375,-0.01347921,0.01766223,0.00463144,0.03176103,-0.00179823,0.02646664,0.11144502,-0.00163796,-0.02568113,-0.09768294,0.0198938,0.01077205,-0.06401305,-0.03082706,-0.02122065,0.01334841,-0.01401231,0.09316815,-0.00768131,-0.04090375,-0.01656359,-0.00362136,-0.02758668,-0.03653137,-0.0251252,0.05402778,0.00676979],"last_embed":{"hash":"1ph1gaa","tokens":194}}},"text":null,"length":0,"last_read":{"hash":"1ph1gaa","at":1751426530199},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{49}","lines":[341,341],"size":315,"outlinks":[{"title":"https://www.reddit.com/r/CryptoMarkets/comments/1jakyzw/why\\_community\\_sentiment\\_of\\_every\\_crypto\\_on/","target":"https://www.reddit.com/r/CryptoMarkets/comments/1jakyzw/why_community_sentiment_of_every_crypto_on/","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1ph1gaa","at":1751426530199}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{50}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0531297,0.01047322,-0.05837526,-0.00723553,0.0450588,0.02058961,-0.04257371,0.03659197,-0.01830781,0.02689487,0.03723298,-0.02461481,-0.00721638,0.04367648,0.03390515,-0.01566076,-0.01713511,-0.06471988,-0.02903494,-0.00418277,0.08062389,-0.05461173,0.07309324,-0.0326121,0.05624161,0.07667834,0.01190689,-0.04804333,-0.0573948,-0.2121481,-0.02373052,-0.08286933,0.04770594,-0.00346057,0.0018023,-0.00998264,0.00083983,0.01647798,-0.05669762,0.03553727,0.03550623,0.00465753,0.03705346,0.00478735,0.04255073,-0.05757793,0.03853403,-0.04189508,-0.01086175,-0.04477045,-0.05513966,-0.0796739,0.02087573,-0.0058646,0.02235641,0.02205951,0.01316672,0.05104844,0.02312463,0.07853017,0.08293188,0.02256872,-0.23522949,0.03691653,0.07273263,0.03556139,-0.04275618,0.01806876,-0.00923299,0.04128702,0.01946349,0.00347682,0.03717614,0.03912184,0.01731415,0.02668348,0.03148565,0.02050017,-0.02811373,-0.07381446,-0.07755101,0.04028592,0.00131256,-0.04737182,-0.01865463,0.00782165,-0.02755568,-0.01971431,-0.03323347,0.01785573,0.00724446,0.01712744,0.10330188,0.00829761,-0.05491371,-0.02726628,0.00829174,-0.00812582,-0.10488582,0.11748706,-0.04433868,0.08315619,-0.01872257,-0.03959367,0.0523317,-0.00576112,0.02068712,-0.07257044,0.04079388,0.03861646,0.00961713,0.01503679,0.07107607,-0.09712061,0.05896767,0.05977364,0.04506095,0.05164822,-0.01430403,-0.02588757,0.03112554,0.01608067,0.0527882,-0.04036393,-0.02098065,-0.03673248,0.02457622,0.01886238,0.00991954,0.0263435,0.06598622,0.02934999,-0.09475663,-0.01048711,-0.01874835,-0.03640456,-0.0339662,-0.02262628,0.00995638,-0.03568699,-0.02484548,-0.0584676,-0.08808422,-0.09170147,-0.08872423,0.02078089,0.04561681,0.02365116,-0.01175689,-0.01639006,0.01439732,0.0438922,-0.0055297,-0.06832304,-0.0197503,-0.00878163,0.06073656,0.09838745,-0.09144242,-0.00836035,-0.01458735,-0.03330136,-0.0668762,0.1298801,0.04032095,-0.08368624,0.03063686,0.03406106,-0.02659076,-0.03860679,0.03358977,0.01233783,-0.07339282,0.07136661,0.09532207,-0.03878287,-0.00729962,0.01074319,0.02411334,0.0175737,0.00361844,-0.03330962,-0.05126545,0.01433779,-0.05289305,-0.04877936,-0.00481651,0.03417462,0.01344978,0.04841692,-0.01092303,0.04110688,0.04582739,0.07394376,-0.02394911,-0.03339743,-0.00728171,-0.04547387,-0.00444522,-0.08770866,0.06286827,0.00131891,-0.03130107,0.01211277,-0.08105406,0.00456761,-0.05043015,0.01081348,0.04871764,0.00406439,-0.0333119,0.01274294,-0.04098796,0.03455197,-0.0380066,0.01981899,0.01029705,0.04704794,-0.04586772,0.02362046,0.04158166,0.0147321,-0.08691337,-0.21271867,-0.01296578,0.02470925,-0.01996873,0.02301749,-0.01858373,0.0685836,0.02637651,0.05874406,0.11547503,0.06018391,0.02222693,-0.03128803,0.04055636,0.00710635,0.03557814,-0.00596163,0.00628737,0.00509108,0.0390092,-0.02370409,0.02273311,-0.00827918,-0.08506285,0.02331935,-0.04146278,0.11001939,0.05984356,0.03819253,-0.0307709,0.04336124,0.03182731,0.00971747,-0.17297751,0.03853392,0.07043692,0.02587098,-0.03457396,-0.07347154,-0.02491566,-0.02501913,0.04763632,0.03541722,-0.07632978,-0.02302942,-0.06480002,-0.02933396,-0.02750514,-0.01955316,-0.01355162,0.00562783,0.02947108,0.00160962,0.0601309,-0.00624698,-0.02442248,-0.0129899,-0.04446588,-0.00297505,0.05352924,-0.00639727,0.02173157,-0.04161288,-0.00907187,0.05107785,-0.00838578,-0.04035982,0.04966306,0.05890105,-0.067823,-0.00679336,0.09719657,-0.00994455,0.03654296,0.02513717,0.01445386,0.00009689,-0.08065198,0.02921999,-0.0424512,-0.01332618,0.01117345,0.06638651,0.02715188,0.00146087,0.04955501,-0.00569111,-0.02509605,0.05501431,-0.01621294,-0.0231611,0.02575039,-0.05149219,-0.03629697,0.08256961,0.05269542,-0.25271514,-0.03768699,-0.02566188,0.02958126,0.0616291,0.03606924,0.06948717,0.0424854,-0.0205065,-0.00127573,0.01601304,0.06464537,-0.00856308,-0.03606836,0.0012069,0.01156515,0.04350641,-0.01531424,0.01790422,0.00796027,0.01531753,0.03845256,0.18624635,0.01942963,-0.00412416,0.02023341,-0.08784795,0.05764684,0.03157074,0.03025979,-0.00180002,0.01004503,0.03558428,-0.03610115,0.00140596,0.02252788,-0.01340497,0.00016574,0.0176285,0.00196007,-0.00577994,-0.01886145,0.00562019,0.04603407,0.09452226,-0.04980931,-0.06562291,-0.06292756,0.00496836,0.01418004,-0.0259204,-0.04295665,0.03029162,0.06711311,-0.02518092,0.07924706,0.04150482,-0.03830262,-0.07392501,-0.03553479,-0.04310604,-0.05729517,-0.02906994,0.05441741,0.01794883],"last_embed":{"hash":"1gmm3wl","tokens":231}}},"text":null,"length":0,"last_read":{"hash":"1gmm3wl","at":1751426530207},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{50}","lines":[342,342],"size":427,"outlinks":[{"title":"https://www.everand.com/podcast/593966517/Launching-the-Mars-Review-of-Books-with-Noah-Kumin-Noah-Kumin-is-the-founder-and-editor-of-the-Mars-Review-of-Books","target":"https://www.everand.com/podcast/593966517/Launching-the-Mars-Review-of-Books-with-Noah-Kumin-Noah-Kumin-is-the-founder-and-editor-of-the-Mars-Review-of-Books","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1gmm3wl","at":1751426530207}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{51}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03959333,-0.03787688,-0.03290066,-0.0392615,0.07365437,0.02718518,0.01326341,0.01716234,-0.00603894,0.03319573,0.03404725,-0.05930975,0.03266551,0.04498219,0.02841636,-0.01905086,0.02419163,-0.06051071,0.00215393,0.02310281,0.14043687,-0.06293512,0.00966853,0.00696202,0.02167882,0.07914927,0.02541889,-0.01986963,-0.02796297,-0.20011047,-0.00768366,-0.05097615,-0.00824581,0.00418479,0.01759758,-0.06392545,-0.04114607,0.04166288,-0.04910622,0.00299233,0.02765047,0.01857279,0.01700589,-0.01308204,0.03973721,-0.07205512,0.02175206,-0.02570943,0.01121568,-0.05517463,-0.00800904,-0.03717325,-0.00303056,0.01655576,0.02728436,0.01357032,0.02787264,0.03721448,0.05542302,0.03717925,0.08692866,0.04901531,-0.22161877,0.05402865,0.10016295,0.05553639,-0.00461131,0.02379403,0.0255614,0.04120458,0.01606276,0.02885634,-0.01826804,0.0250664,0.01021231,0.01846485,0.05077907,0.04540867,-0.01862873,-0.06403866,-0.08162042,0.08517741,-0.03133428,-0.02492496,0.00683171,-0.06738891,-0.0302883,-0.0295528,-0.0275915,0.00492969,-0.03991884,0.03450986,0.10742817,0.00430844,-0.02847634,-0.00133841,-0.00329821,0.01904584,-0.1107938,0.11935743,-0.03898088,0.06188139,-0.00083621,-0.04419577,0.06030696,0.00983769,0.00359842,-0.05070439,0.02796171,0.04205545,0.03383541,0.02285677,0.08090375,-0.04500234,0.0441785,0.03104288,0.02250303,0.03729772,0.00076402,0.01075565,-0.01121391,0.04510485,0.04363985,-0.03082957,-0.01792483,-0.03759313,0.03478938,0.03272993,0.02259975,0.04224642,0.02629774,0.0126073,-0.07123072,-0.01216194,-0.00709257,-0.03827013,-0.01729291,-0.02543324,-0.00072712,-0.02525419,-0.03308697,-0.09288329,-0.03132464,-0.08673996,-0.05408382,0.0724198,0.02070803,0.0143233,-0.02280001,0.002076,0.00436638,0.08604113,-0.00399933,-0.09667167,-0.00426889,0.00936591,0.04898505,0.09203997,-0.04117582,-0.00968237,0.0113187,-0.05647728,-0.04366526,0.1456199,0.06923585,-0.08613728,0.0273935,0.05224429,0.00864327,-0.0378372,-0.01347128,0.02385275,-0.01760118,-0.00542853,0.07062476,-0.0522579,-0.00587182,-0.03625597,-0.01137417,0.04551275,-0.0024703,-0.0609429,-0.08631667,-0.00169727,-0.02934161,-0.03798778,-0.03766905,0.0279226,0.02363368,0.0200206,-0.02471951,0.04726341,-0.00952729,0.08394247,-0.05117667,-0.0356803,0.0025313,-0.01410273,0.01643932,-0.04550805,0.10637005,0.03063877,0.02115965,0.00130764,-0.04592377,0.00502651,-0.0514443,-0.02590923,-0.01562927,0.03344879,-0.01324127,0.03298189,-0.00691239,0.03300287,-0.05205835,-0.03225802,0.0147063,0.03226057,-0.03213973,0.02111356,-0.00289418,0.01967079,-0.08932704,-0.20656043,-0.0000881,0.02515077,-0.04219731,-0.00826715,-0.01552244,0.04968172,0.03422502,0.05887271,0.10135938,0.04935284,0.03879278,-0.00676499,0.01674263,0.01879093,0.02559894,0.06450003,0.01155755,0.00337539,0.08375575,-0.05112018,-0.01472112,-0.03892468,-0.09647483,0.04489939,-0.05556155,0.10071937,0.01271268,-0.00019019,0.00904072,0.06867954,0.04162248,-0.01277834,-0.18896143,0.00907877,0.06625793,-0.00533785,-0.01866595,-0.0537923,-0.05538082,-0.05493026,0.03773367,0.04577339,-0.10569683,0.02749681,-0.080571,-0.04345023,-0.02819815,-0.01399514,-0.0048588,0.02335542,0.02646747,-0.00300682,0.09156127,-0.03333719,-0.01992551,-0.00601537,-0.0760041,0.00313049,0.07060211,-0.01979746,0.02833945,-0.053401,0.00211962,0.03520499,0.01818553,-0.05139545,0.00810992,0.02937458,-0.04118052,-0.01169991,0.12104031,0.02622574,-0.02265979,0.01463753,0.02430937,-0.01406176,-0.05187196,0.02115928,-0.0342415,0.02406491,0.01560936,0.06115011,0.02239971,-0.00292867,0.05975485,0.04514531,0.01126321,0.05534627,-0.0154228,0.00822131,-0.03201862,-0.03465844,-0.0644306,0.02953647,0.02210329,-0.26002961,-0.00172131,-0.00151865,-0.03055267,0.04107386,0.00753645,0.09764946,0.04399952,-0.09367047,0.00664576,-0.02697573,0.06068439,-0.00353786,-0.03644047,0.00176809,-0.00848001,0.06938895,0.00518781,-0.01566481,0.00311413,-0.01370432,0.05976759,0.18139274,0.00376854,0.03913723,0.00035027,-0.05483989,0.04034135,0.0161439,0.00992578,-0.05278937,-0.01226192,0.03230418,-0.04876556,0.00199629,0.01459139,-0.02147253,-0.02174069,-0.01351659,0.01552492,-0.03864957,-0.03695364,0.00531203,0.05786843,0.08264851,0.00002757,-0.06342658,-0.07184629,0.03124692,0.02998711,-0.04894437,-0.01283947,0.01127347,0.0179599,-0.00749518,0.10170095,0.01560894,-0.04782408,-0.08358941,-0.04713452,-0.04670542,-0.02051873,-0.05335815,0.05062972,0.03030784],"last_embed":{"hash":"1y35y7z","tokens":199}}},"text":null,"length":0,"last_read":{"hash":"1y35y7z","at":1751426530216},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{51}","lines":[343,343],"size":322,"outlinks":[{"title":"https://podcasts.apple.com/bt/podcast/urbit-nockchain-and-the-current-state/id1195362330?i=1000709342627","target":"https://podcasts.apple.com/bt/podcast/urbit-nockchain-and-the-current-state/id1195362330?i=1000709342627","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1y35y7z","at":1751426530216}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{52}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0286234,-0.02114384,-0.10067941,-0.05088067,0.0334354,0.00218137,0.01557378,0.02210944,-0.02011487,0.02209795,0.04643632,-0.07545808,0.01223546,0.03683553,0.05042726,0.00135409,0.02249191,-0.06389093,-0.03903197,0.02244049,0.11976065,-0.06360666,0.02884818,0.00164786,0.08293847,0.04520032,-0.00477975,-0.01353029,-0.05031376,-0.16650018,-0.03530226,-0.06945655,0.02314162,-0.00141191,0.00470469,-0.03127488,-0.02060243,0.01635094,-0.0308245,-0.00206617,-0.02580257,0.00638973,0.01901451,-0.01775758,0.04864226,-0.07987062,0.04285796,-0.05709732,-0.00010886,-0.0416904,0.00705909,-0.03964878,0.01653905,0.04854583,0.0247791,0.02167991,0.01010603,0.06093899,0.04316611,0.05711363,0.08943935,0.03920636,-0.19176304,0.04331272,0.04092601,0.05426243,-0.00545723,0.02864513,0.03087312,0.08242024,0.05307804,0.00855895,-0.03761717,0.01537458,0.03260946,0.02435258,0.0221186,0.03103343,-0.00735242,-0.04837665,-0.04172892,0.05077524,-0.00035427,-0.03222069,0.00044676,-0.03562646,-0.00750861,-0.02330091,-0.05693127,0.02738768,-0.00710708,0.04852893,0.06411878,0.00345874,-0.04476672,-0.0091994,-0.0164932,0.02541041,-0.08862779,0.12280951,-0.03503246,0.04103517,0.0039275,-0.07429012,0.04010728,0.00273858,-0.00866391,-0.07485844,0.00311839,0.02629932,-0.01847585,-0.00695767,0.09244107,-0.06052358,0.01388076,0.00587982,0.05584304,-0.00866733,-0.02532589,0.04056467,-0.01416956,0.02716646,0.07150182,-0.02138374,-0.00710034,-0.01419398,0.02909923,0.05800568,0.0270816,0.06061025,0.03007578,0.06488492,-0.06475905,0.00693032,-0.03440142,-0.03785314,-0.02092248,-0.03069103,-0.01746334,-0.06512385,-0.00928087,-0.09559625,0.01165976,-0.10316886,-0.05679788,0.03199526,0.00152083,0.01370008,-0.01177346,-0.03454,0.01326559,0.04770012,-0.03125428,-0.08489703,-0.00092183,-0.0159072,0.01064034,0.09218954,-0.09557276,0.00058939,0.00307746,-0.04679358,-0.03845295,0.12636383,0.0404616,-0.13543537,0.00262027,0.03111373,-0.00227923,-0.06840374,-0.00919921,0.00885364,-0.04718298,-0.01046543,0.05802374,-0.03294717,-0.03398055,-0.02283164,-0.00780577,0.0359774,-0.01867522,-0.0246489,-0.02357105,0.00981667,-0.00711031,-0.01664731,0.01711091,0.0306389,0.05336259,0.00963709,-0.08504991,0.00290883,0.03706248,0.03993281,-0.04345406,0.01361523,-0.0324776,-0.01943628,-0.01727597,-0.04382148,0.09491777,0.00626716,-0.01959639,-0.00875666,-0.06995764,0.00895991,-0.0025845,-0.0515332,0.04502397,0.02430791,-0.01396122,0.03353495,0.00314744,0.02105048,-0.04865733,-0.00779157,0.02828559,0.06523208,-0.04971556,0.0284698,0.02406882,0.03736155,-0.09119222,-0.19985904,-0.07880235,0.06093954,-0.03140489,0.05393356,-0.02248248,0.07652072,0.00393612,0.06625196,0.10689812,0.09948979,0.06294906,-0.06003446,-0.03268151,0.03600481,0.06894867,0.01952813,0.00469362,0.01955486,0.00831775,-0.0478049,0.01256185,0.02194964,-0.08200632,0.05136909,-0.02839206,0.09295703,0.00249173,0.01155896,0.00344529,0.07388487,0.01412712,-0.00742782,-0.12866278,0.06175872,0.03478172,0.03768808,0.02868257,-0.0339784,-0.04652519,-0.00533825,0.01783075,0.04944819,-0.10559472,-0.02554697,-0.09722199,-0.03429348,-0.04513768,-0.0186294,0.0040404,-0.02430303,0.05836177,0.03260848,0.09136993,0.03841472,-0.00113499,0.01327619,-0.08915342,-0.00038259,0.0806756,-0.01203859,-0.00717125,-0.0609052,-0.0059104,0.01269771,0.01253052,0.01128195,0.03483402,0.0262205,-0.00719274,-0.01056742,0.14509171,-0.01016071,-0.00193736,0.02294299,0.04779638,-0.02212357,-0.09483376,0.02085766,0.01183095,0.04004434,0.00276594,0.04865788,0.04945739,-0.01777509,0.07956918,0.01762168,-0.01042992,0.06297067,0.01780291,-0.00925651,-0.02549252,-0.05466529,-0.06961022,0.04188176,0.00589069,-0.27627119,-0.04906944,0.00059615,-0.04623385,0.04609079,0.0388713,0.06686787,0.01261301,-0.11348798,0.05230734,-0.02660887,0.06529355,-0.00340285,-0.03518767,0.01215427,-0.00918799,0.02295709,-0.01309852,-0.00251217,0.02350726,0.01975778,0.01860722,0.19296998,0.0258663,0.00564773,0.01341576,-0.07916652,0.06091976,0.04512358,-0.01239122,-0.02116654,0.02222364,-0.00535606,-0.04981292,0.02149432,0.04505172,-0.01655721,-0.02217544,-0.02137491,-0.00808646,-0.01572445,0.0269046,-0.03637721,0.02533641,0.10930983,0.0175428,-0.0735684,-0.04901199,0.01430558,0.03286725,-0.09513517,-0.02377562,0.01315399,0.03951382,-0.00835822,0.07967853,0.03701027,-0.04046651,-0.06559937,-0.00935244,-0.02474566,-0.04121465,-0.02880393,0.05442068,-0.0009422],"last_embed":{"hash":"82550w","tokens":149}}},"text":null,"length":0,"last_read":{"hash":"82550w","at":1751426530224},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{52}","lines":[344,344],"size":212,"outlinks":[{"title":"https://www.nansen.ai/post/best-telegram-channels-for-crypto-chats","target":"https://www.nansen.ai/post/best-telegram-channels-for-crypto-chats","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"82550w","at":1751426530224}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{53}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01793196,-0.00176426,-0.0787231,-0.03524943,-0.00127392,0.00500665,-0.01817706,0.02632362,-0.01380656,0.03358021,0.04540503,-0.05087009,0.03745183,0.04856895,0.03134813,0.00604947,0.0226875,-0.07380843,0.00592136,0.01020217,0.06608473,-0.09900159,0.04642828,0.00422706,0.0423365,0.05615082,-0.01130272,-0.06802988,-0.02759939,-0.16789389,-0.03225181,-0.02464578,0.03148016,-0.00307162,0.01171716,-0.0284191,-0.00262798,0.04087108,-0.05645113,0.0225341,-0.00799262,-0.00927997,0.01429992,-0.03367595,0.05253981,-0.05520485,-0.00267848,-0.06867612,-0.02572127,-0.0411736,-0.02663124,-0.08033682,-0.01162046,0.0139215,-0.01638383,0.01537975,0.02687484,0.02850206,0.04821774,-0.01536223,0.10006835,0.00546343,-0.19326493,0.05574017,0.04980586,0.06541283,-0.02506356,-0.00278214,0.04279025,0.05949839,0.05291574,0.04344783,-0.01780382,0.01640731,0.06318883,0.03029674,0.03101299,0.03649255,-0.02061897,-0.0589932,-0.01309135,0.06980342,-0.03632611,-0.00579446,-0.00420434,-0.03300038,0.0178192,0.01004795,-0.02843961,0.02419549,-0.03514582,0.04636862,0.05048888,0.01944141,-0.03901113,-0.02945123,0.01434809,0.00720023,-0.07141067,0.1355622,-0.04287802,0.03050057,0.03896666,-0.07269557,-0.00354935,0.01893419,-0.01479918,-0.0895518,0.0386981,0.03796132,-0.02504279,0.04869569,0.09841982,-0.08548117,-0.01661383,0.0076796,0.03233536,0.01591095,-0.020137,0.00366177,0.01098172,0.06179502,0.06736469,-0.02889512,-0.007532,-0.01522085,0.02696726,0.0599602,0.0968393,0.05289712,0.04122022,0.07573353,-0.08884239,0.01102786,-0.05121041,-0.06417338,-0.00843939,-0.03673796,-0.01508444,-0.03728635,-0.01275631,-0.02045019,0.01777733,-0.11239873,-0.05129036,0.05200315,0.03782813,-0.01025706,-0.01435652,0.00534135,-0.00090416,0.05194208,-0.05197808,-0.05643102,-0.02020923,-0.05031714,0.02223573,0.10562807,-0.09621827,0.02806921,-0.00042541,-0.04801638,-0.04453636,0.1580648,0.03064597,-0.13815671,0.0159786,0.01575111,0.02624182,-0.08090201,-0.02370533,0.02245246,-0.018024,-0.01406321,0.08578606,-0.01483083,-0.08623678,-0.02917717,-0.02721259,0.058123,0.00690051,-0.01916109,-0.06418097,-0.00958148,-0.02075813,-0.04586857,0.00828387,0.01171162,0.03135741,0.01336069,-0.06906314,-0.04729602,-0.01887371,0.06480786,-0.00792168,0.01908828,-0.01210627,-0.02187568,0.00139691,-0.01424224,0.02996934,0.00518109,-0.01280975,0.03747181,-0.04944005,0.00436031,-0.00946601,-0.03690682,0.03288906,0.05711792,-0.02073995,0.01990435,-0.02189863,0.03601561,-0.05138632,-0.00164951,-0.01386713,0.04465086,-0.05526707,0.02739392,0.02782229,0.02969283,-0.06372072,-0.20069848,-0.06984949,0.03715166,-0.01581303,0.00924453,-0.03770863,0.08315415,-0.01311945,0.04030845,0.09565602,0.08776604,0.03763909,-0.05401231,-0.0122584,0.03781043,0.05932283,0.02217109,0.00138359,-0.0044932,0.01877932,-0.01728978,0.00803641,-0.02051405,-0.11126173,0.05831787,-0.03795916,0.11740309,0.03130948,0.00705927,0.04019504,0.09477259,0.05583013,-0.01999789,-0.16726807,0.04902556,0.02358615,0.05164989,-0.01034641,-0.05911352,-0.03174331,-0.00611332,0.02221947,0.01689389,-0.11513177,-0.01119794,-0.04944947,-0.03299419,-0.01688592,-0.0042035,0.0241081,0.00690862,0.00552312,0.02902988,0.09572507,0.02239469,-0.01027259,0.00322175,-0.08865909,0.03266582,0.08103409,-0.0280384,0.0151083,-0.04562959,0.01144217,0.04755476,0.0288236,-0.00867846,0.02157476,0.04459122,-0.03016498,-0.01624105,0.15912183,0.00423469,-0.01834527,0.01880486,0.00705471,-0.03291627,-0.05590614,0.04792199,0.00420522,0.0223012,0.00910789,-0.00426557,0.06115918,-0.01263767,0.03136206,-0.01468139,-0.00879855,0.01402721,-0.03870308,-0.00026918,-0.03420167,-0.05749072,-0.07098186,0.08661932,0.00601663,-0.24319866,-0.0321104,0.03807789,-0.05698116,0.04190242,0.04145277,0.04814043,0.0212362,-0.07059355,0.0048919,0.03127554,0.06190644,-0.00288864,-0.02966841,0.02103381,-0.01060629,0.03671087,-0.03111309,0.02308201,0.00344643,-0.00753515,0.0473606,0.21824735,0.00544298,0.01019671,0.00470972,-0.06452332,0.0599892,0.01284912,0.00709112,-0.06956372,0.00406957,0.03076565,-0.04814386,-0.01138307,0.0156312,-0.02145117,-0.01314898,-0.00332578,0.01120952,-0.00354664,0.00306817,0.00670987,0.00717237,0.12632895,0.06089901,-0.05235297,-0.06523852,0.00234259,0.01306769,-0.06403028,-0.05097207,0.01129378,0.02282841,0.00763995,0.08814792,0.00809353,-0.05148368,-0.02388157,-0.03029992,-0.0460296,0.00525603,-0.0231671,0.06009155,0.00463938],"last_embed":{"hash":"1nvyii2","tokens":209}}},"text":null,"length":0,"last_read":{"hash":"1nvyii2","at":1751426530231},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{53}","lines":[345,345],"size":444,"outlinks":[{"title":"https://ryanschultz.com/2018/07/17/the-telegram-discussion-groups-for-the-various-blockchain-based-vr-projects-have-been-endlessly-entertaining/","target":"https://ryanschultz.com/2018/07/17/the-telegram-discussion-groups-for-the-various-blockchain-based-vr-projects-have-been-endlessly-entertaining/","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1nvyii2","at":1751426530231}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{54}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02690003,-0.03625929,-0.07812703,-0.00234482,0.04690714,-0.02038394,0.00848454,-0.0027981,-0.03125738,-0.00710662,0.08858671,-0.05662497,0.04842123,0.02328193,0.02852037,-0.02934565,0.02452988,-0.08146846,-0.01771206,0.02659552,0.12670934,-0.06380891,0.03378165,-0.02540909,0.0581275,0.05994754,0.0067937,-0.01867438,-0.08520503,-0.15285002,0.00175383,-0.05664136,-0.03347306,0.01845709,0.03309893,-0.04342253,-0.01896212,0.05365453,-0.0431049,0.00506651,-0.03606765,0.01108762,0.00854401,0.0027916,0.05550297,-0.05378656,-0.00854226,-0.00214016,0.00075198,-0.0313439,-0.01787948,-0.0469735,0.00279686,0.04699641,0.00427838,-0.02982118,0.00157634,0.01396822,0.07981588,-0.00687781,0.10214347,0.03496688,-0.1720912,0.01372302,0.02931605,0.05461316,0.0189199,0.00180406,0.05661917,0.05790668,0.03839956,0.001409,-0.04812142,0.01752007,0.01111205,0.02029194,0.01382802,0.04760875,-0.0645762,-0.02323879,-0.0299481,0.03339889,0.01648372,-0.04093457,-0.00845162,-0.05708703,0.02246546,-0.00392969,-0.01574627,0.01631713,0.00029148,0.05170019,0.04504431,0.00729351,-0.02831961,-0.0144179,-0.00517699,0.03803275,-0.08110157,0.12549672,-0.02277459,0.03774616,0.01546416,-0.05974167,0.0339144,-0.0213998,-0.02185725,-0.01095842,0.00179624,0.00786974,-0.02010446,0.02453535,0.09592343,-0.07450534,-0.00715377,-0.01373515,0.00820848,0.00153951,-0.04292469,0.04307611,-0.00693674,0.05200626,0.06828136,-0.0030274,-0.02196101,-0.03426337,-0.01285698,0.06869474,0.05811296,0.0874076,-0.00150122,0.07361699,-0.06345472,0.01606575,-0.00823896,-0.04069605,-0.04074962,-0.0029937,0.02408076,-0.04084991,-0.04988474,-0.08988402,-0.01165833,-0.0999085,-0.0412398,0.03520451,-0.00283919,-0.00009028,-0.01761754,-0.00973308,-0.01436369,0.03178179,-0.03869598,-0.05698554,-0.01759048,-0.03369967,0.05958607,0.11575174,-0.07110323,-0.01193366,-0.00227427,-0.03221978,-0.0432534,0.12390959,0.06723553,-0.10512232,0.02720842,0.01078107,-0.00983592,-0.06350376,-0.00145352,0.00837448,-0.03879755,-0.00893892,0.04292061,-0.03432109,-0.00921408,-0.05637425,-0.00708035,0.04339314,-0.04159127,0.00398566,-0.04999349,-0.00425694,-0.00931937,-0.01109597,-0.04248662,0.0555201,0.02643443,0.0519462,-0.10165276,-0.03757674,-0.03280289,-0.00511156,-0.04006362,0.00655702,-0.02773693,-0.0219123,0.00614459,-0.05515518,0.08432818,0.03424459,-0.0063184,0.01228972,-0.07299546,0.05491003,-0.00802526,-0.04716293,0.00447597,0.02645292,0.00284065,0.04149114,-0.04836746,-0.00268922,-0.0254672,0.00761046,0.0189534,0.02182786,-0.03510063,0.01330073,0.00706006,0.02061128,-0.09644768,-0.20597047,-0.08483489,0.07685148,-0.0181127,0.05153693,-0.0128437,0.07289873,0.01161602,0.05314019,0.09273183,0.08498917,0.0511946,-0.0594282,0.01517438,0.05164603,0.09024055,0.03124078,-0.00576994,0.03988391,0.03913896,-0.0717887,0.02973404,0.02344482,-0.04548177,0.04370711,-0.03249605,0.12639455,0.06290452,-0.02545736,0.00887876,0.06408454,0.03555533,0.00047618,-0.118184,0.0789542,0.03272259,0.00273616,-0.02429012,-0.00506278,-0.03552308,-0.0148534,0.01044378,0.01996111,-0.09330571,0.01275738,-0.08426635,-0.04552952,0.01294348,-0.02204641,0.0576982,0.01993783,0.04274645,0.03892655,0.11195015,0.01374809,-0.03110413,0.0111026,-0.08567248,-0.00175942,0.04107852,-0.01779308,-0.00459537,-0.02611485,-0.00970434,0.02951041,-0.03268475,-0.05634767,0.02248413,0.06285837,0.02243747,-0.01384382,0.17422916,0.01952842,-0.03576904,0.0323603,0.02140619,-0.04928849,-0.13267155,0.02782173,0.01701592,0.01366852,0.00668611,0.03419042,0.07023878,-0.00097718,0.04725202,-0.04220585,0.00004624,0.04410808,-0.00955621,-0.03709648,-0.02145301,-0.0493881,-0.04136292,0.05573181,0.02915653,-0.27744076,-0.0607022,0.02573596,0.0350861,0.05240702,0.02552254,0.06911958,0.01312763,-0.08162155,0.02004639,-0.01629415,0.07537594,0.00625496,-0.02000053,-0.01678249,-0.0179704,0.01113887,-0.06882417,0.01329082,0.00967262,-0.01305249,0.03838233,0.21878341,0.03212663,0.0049474,0.04309048,-0.02243576,0.0736875,0.04854926,0.00490853,-0.028442,-0.0028249,-0.01826262,-0.05370025,-0.00057072,0.00553974,-0.01506471,-0.02301653,-0.01580341,-0.00742157,-0.02903499,0.01674761,-0.01671028,0.01636017,0.10565971,-0.01495619,-0.04024849,-0.08556575,0.02517825,0.05544864,-0.04410115,-0.03370297,-0.01292378,0.02555033,0.01148658,0.11170127,0.00755275,-0.06359581,-0.02499589,0.00229557,-0.04139471,-0.02140309,-0.01500912,0.04838961,0.00525222],"last_embed":{"hash":"3sw8h8","tokens":207}}},"text":null,"length":0,"last_read":{"hash":"3sw8h8","at":1751426530238},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{54}","lines":[346,346],"size":327,"outlinks":[{"title":"https://www.reddit.com/r/CryptoCurrency/comments/lz1f54/beware\\_of\\_fake\\_pump\\_coins\\_groups\\_on\\_telegram/","target":"https://www.reddit.com/r/CryptoCurrency/comments/lz1f54/beware_of_fake_pump_coins_groups_on_telegram/","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"3sw8h8","at":1751426530238}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{57}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01714003,-0.04705153,-0.0587825,-0.03752221,0.07265718,0.00311731,-0.04823157,0.04462275,-0.02763778,0.02187437,0.07052892,-0.04368174,0.02445163,0.02942143,0.01708322,-0.02416689,0.0263419,-0.02495213,-0.00372193,0.02927364,0.13831347,-0.08215113,0.00918108,-0.03499017,0.07643197,0.07344468,0.02617758,-0.03443052,-0.03715027,-0.21696913,-0.00117054,-0.04918724,0.02217941,0.01805782,0.01749057,-0.01967708,-0.0420878,0.07377861,-0.03716861,0.01397401,0.0141304,-0.01548003,0.03244911,-0.01418682,0.04409542,-0.08037705,-0.00244564,-0.05464345,0.02209432,-0.03926678,-0.02320587,-0.06850598,-0.00871737,0.02415429,0.00715909,0.05482936,0.00410687,0.01268907,0.03412439,0.06429485,0.09955961,0.06022069,-0.22974297,0.00950537,0.0675923,0.0491496,-0.00096339,0.00484513,0.04159146,0.07695344,0.03312178,0.01911885,-0.01740445,0.03627331,0.03554699,0.00081523,0.03601959,0.0165525,-0.0172978,-0.07769333,-0.02270106,0.08321653,-0.03026357,-0.02155926,-0.01628129,-0.00612057,-0.01358288,-0.01351987,-0.03756712,0.03241441,-0.00576153,0.00773096,0.06419422,-0.01187682,-0.06115877,-0.00146267,0.00241763,0.0295966,-0.10851531,0.12378795,-0.01605601,0.05381303,0.02094716,-0.03802098,0.02223515,0.01852863,0.00556748,-0.03278526,0.01834901,0.03552449,0.00963727,0.00160781,0.02234448,-0.08166406,0.02682928,0.00355466,-0.03322912,0.00690469,-0.01722755,-0.00393111,0.01858352,0.00672291,0.04893379,-0.04415343,-0.05143476,-0.0156168,-0.00022377,0.06087172,0.06299607,0.04597808,0.04040093,0.03875712,-0.08549707,0.003292,-0.07662688,-0.0440141,-0.0000442,0.00310521,-0.02137042,-0.04796565,-0.03683676,-0.09462746,-0.03624477,-0.09878501,-0.08933368,0.08537502,-0.01958035,-0.01277958,0.01722194,-0.01812864,0.02635217,0.08374099,-0.00301878,-0.07758462,-0.00155905,-0.0317884,0.02399156,0.07260731,-0.04485555,-0.01264549,-0.03638753,-0.07086297,-0.05076507,0.14370704,0.0473906,-0.13691506,0.04573888,0.05111269,0.00813312,-0.07334412,-0.0132836,-0.02925275,-0.01668081,-0.03222308,0.06875648,-0.02113227,-0.02016159,-0.04648797,-0.01882408,0.0409387,0.0045704,-0.02589775,-0.06717015,0.00821407,0.0064282,-0.04235775,-0.03483561,0.02920386,-0.00895945,0.002767,-0.07533927,0.02008632,-0.00872431,0.02812922,0.01395968,-0.01256199,-0.00416856,-0.01009371,0.00600842,-0.03743453,0.08181816,0.02449764,0.03246444,0.01141766,-0.02577661,0.06080288,-0.03929749,-0.0188693,-0.02940712,0.03430308,-0.02652478,-0.00215758,0.01810319,0.03067155,-0.06578383,0.03575394,0.03146806,0.03882707,-0.05624126,0.04699674,-0.00442672,0.03551718,-0.06293409,-0.20098871,-0.04680786,0.02737063,-0.03906268,0.01628172,-0.00488772,0.06337926,0.00961907,0.00338642,0.09087897,0.09090704,0.0236552,-0.02247358,0.01034916,0.00786008,0.04187329,0.03489828,-0.00893109,0.0181512,0.00364929,-0.01591513,0.01773984,-0.00822983,-0.04559272,0.04304171,-0.03593696,0.10394995,-0.00499245,-0.01541292,0.02030302,0.07359956,0.01452907,-0.02857325,-0.14422749,0.03808826,0.04573122,-0.0055373,-0.01661628,-0.06941967,-0.00913175,0.00458864,0.00604637,0.03939186,-0.08232211,0.0277339,-0.06493162,-0.03099933,-0.0159387,-0.01782579,0.0258918,-0.02971329,0.03242887,0.04551175,0.11723711,0.01077292,0.00392085,0.01455067,-0.06352195,0.01078988,0.03081928,-0.00075115,0.00130436,-0.01542908,-0.01252047,0.0306389,-0.0213879,-0.0376313,0.01469061,0.05356596,-0.02886533,0.00146396,0.12215408,0.03253938,-0.0200313,0.05070897,0.05060789,-0.05602545,-0.09967928,0.00829965,0.01847108,0.00458353,0.02863964,0.01335681,0.05777457,0.02083881,0.05785444,0.03251132,-0.01064466,0.02084733,0.00279992,0.00446189,-0.04346992,-0.04143939,-0.01370077,0.08890834,-0.02709688,-0.29528007,-0.02992887,-0.01368291,-0.04397343,0.05306121,0.03807799,0.03902034,0.00916124,-0.06768407,0.0441018,0.00604257,0.03152555,0.00649651,-0.04461448,0.02858585,-0.00910553,0.0688626,-0.01407886,0.05241315,0.01586795,0.0202118,0.04808284,0.20275745,-0.03253107,0.01826994,0.00811462,-0.06424086,0.06056703,0.03745257,0.0314614,-0.01992834,0.00769286,0.03149612,-0.02000021,0.02912511,0.04876149,-0.01837403,-0.00585385,-0.00494091,0.03805047,-0.03947616,0.00452373,-0.02584331,0.02554852,0.09933943,-0.01779734,-0.04754734,-0.09568392,0.01785759,0.03527936,-0.06216388,0.0051105,0.00052177,0.01948092,-0.01458991,0.0719802,0.02820528,-0.04466369,-0.05241403,-0.03432902,-0.03624498,-0.00691749,-0.03332574,0.05641646,0.02505201],"last_embed":{"hash":"1iifbwq","tokens":171}}},"text":null,"length":0,"last_read":{"hash":"1iifbwq","at":1751426530246},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{57}","lines":[349,349],"size":231,"outlinks":[{"title":"https://www.rootdata.com/Projects/detail/RISC%20Zero?k=MjgwMw%3D%3D","target":"https://www.rootdata.com/Projects/detail/RISC%20Zero?k=MjgwMw%3D%3D","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1iifbwq","at":1751426530246}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{58}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03787342,-0.01904588,-0.05756251,-0.02785137,0.02756177,0.03978269,0.04340762,0.01918227,0.03311158,0.004581,0.06103728,-0.03010729,0.02799514,0.04932468,0.00937625,0.00119246,-0.00287436,-0.01412111,0.00581687,0.02787803,0.1203099,-0.07617117,0.00740224,-0.05201705,0.02358051,0.07481151,0.00399886,-0.03624551,-0.05292462,-0.18753368,0.00578788,-0.06894509,-0.00927837,0.00899015,0.03290502,-0.06333971,-0.01312215,0.06176967,-0.05365233,0.00395292,0.01289753,0.00988245,0.00671785,-0.00927657,0.03914617,-0.07589293,0.02731358,-0.02730818,-0.05880331,-0.11252456,-0.01348218,-0.04190416,-0.0233455,0.07024215,0.00912054,0.03563185,0.03019911,-0.00184969,0.0215712,0.03113195,0.09595345,0.04267721,-0.19196065,0.02754695,0.07274404,0.08227053,-0.01752522,-0.03057155,0.03601962,0.01447416,0.02326422,0.0374093,-0.00330325,0.03713135,0.02058128,0.02334782,0.00466493,0.01581651,-0.0522833,-0.03804216,-0.03687727,0.0704006,0.03225759,-0.0255977,-0.04459114,-0.0319769,-0.00467376,-0.06147045,0.00176597,-0.029151,-0.00464929,0.01316766,0.04547111,-0.02887,-0.00336148,-0.03123496,-0.01063551,0.01777595,-0.07686748,0.11257195,-0.02259929,0.05487783,0.01317916,-0.06250414,0.03890422,-0.00273026,-0.01100829,-0.06672247,0.02559821,0.03089964,0.01980954,-0.01166502,0.07966705,-0.04959532,0.02255856,0.01476356,-0.00604061,0.02757131,-0.00329726,-0.01332168,0.02952713,0.01544623,0.06984941,0.00962156,-0.02844351,-0.00560563,0.01833584,0.07424604,-0.00760859,0.04765805,0.04149757,0.00314336,-0.06429113,-0.00396885,-0.03176795,-0.0178817,-0.0167544,0.02659508,-0.01473497,-0.01945321,-0.01878855,-0.05372714,0.00621406,-0.08440711,-0.0538795,0.11206066,0.04761477,0.05038271,0.00323511,-0.04682491,0.04394064,0.08062972,-0.01082231,-0.08675308,0.00145719,-0.01758995,-0.00890466,0.06008908,-0.09613692,0.03520418,-0.00838925,-0.02861627,-0.02889624,0.19741108,0.05419011,-0.12149028,0.04079442,0.0427075,-0.0198231,-0.08730336,-0.01101313,-0.0065812,0.00768009,-0.00223293,0.05232883,0.01089853,-0.00590492,-0.05020913,-0.02399106,0.03385734,-0.03448394,-0.04826027,-0.05334462,-0.0379555,-0.01848903,-0.03983863,0.01059672,0.04367535,0.01020167,0.04759964,-0.1265455,-0.00164323,-0.01405603,0.02964324,-0.02998133,-0.02675986,-0.04314348,-0.03081003,0.02043266,-0.06210783,0.10085714,0.04820691,0.01988763,0.00350706,-0.03919246,0.01161394,-0.03462202,-0.05292406,0.00492738,0.01182405,-0.04338953,0.06214809,-0.01616205,0.01516701,-0.03819209,0.01504885,0.00112293,0.02540316,-0.0457322,-0.00624339,0.03461283,-0.0035124,-0.075106,-0.18771769,-0.04601203,-0.00315312,-0.02371513,0.02938077,-0.00981212,0.05304253,0.00084296,0.03143144,0.08088199,0.09690782,0.04365835,-0.06127811,0.03474754,-0.01276381,0.03522339,0.02873031,-0.02283251,-0.00189797,0.02249206,-0.04031144,0.03283938,0.00873909,-0.09596902,0.04206121,-0.03223917,0.10706466,-0.00936323,0.01246256,-0.0253952,0.03125196,-0.00512306,-0.00577444,-0.15002817,0.02757434,0.0295582,-0.01087244,-0.01112311,-0.03386909,-0.06445047,0.02370459,0.00303626,0.03164778,-0.08073208,-0.01144192,-0.09621149,-0.05352227,0.00126342,-0.03451642,0.02731088,0.00593727,0.04937236,0.03613142,0.09099361,0.01055265,-0.03296546,0.02798359,-0.05709966,-0.0076551,0.06563957,-0.00069322,-0.00538193,-0.04343987,-0.02163444,0.03937459,-0.01539322,-0.00016139,0.01825184,0.03461434,-0.03637252,-0.02100892,0.1488568,0.01386999,-0.02725041,0.02898806,0.05974883,0.01444668,-0.10762648,0.00162864,0.01490461,0.04768132,0.03298416,0.02459567,0.05754077,0.01307185,0.06965811,0.02605671,-0.00096705,0.00682882,-0.04350671,0.00859725,-0.04692511,-0.02431624,-0.06902314,0.06148187,0.02320008,-0.29061913,-0.03703849,-0.0250917,-0.01316676,0.0346275,0.01959984,0.04987038,0.0274307,-0.07042579,-0.00223568,-0.03041657,0.07703203,0.01056486,-0.04572722,0.00293726,-0.00750731,0.05224852,-0.04676889,0.01471216,0.03753446,0.02193747,0.06715312,0.24055749,0.01482947,-0.00263721,0.00307028,-0.02290764,0.04079228,0.03380261,0.02601515,-0.01485013,-0.00374154,-0.00838633,-0.01236623,0.00365166,0.06030745,0.02152395,-0.0108148,-0.00125378,0.05139251,0.00322023,-0.02618938,0.02208656,0.0344444,0.12850663,0.01738272,-0.057354,-0.0534451,0.00437216,0.01035518,-0.05295159,-0.00453319,0.02957051,0.02355282,0.00756897,0.06598245,0.03075117,-0.04473934,-0.07071488,-0.00897688,-0.0452034,0.00148897,-0.0489445,0.04298988,0.01032828],"last_embed":{"hash":"titdlg","tokens":171}}},"text":null,"length":0,"last_read":{"hash":"titdlg","at":1751426530253},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{58}","lines":[350,350],"size":295,"outlinks":[{"title":"https://en.foresightnews.pro/an-overview-of-privacy-solutions-based-on-zero-knowlege-cryptography/","target":"https://en.foresightnews.pro/an-overview-of-privacy-solutions-based-on-zero-knowlege-cryptography/","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"titdlg","at":1751426530253}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{59}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04437367,-0.01305688,-0.04533988,-0.01439492,0.0125091,0.01498622,0.04330151,0.00089118,0.02264103,-0.01392524,0.06100316,-0.05124099,0.04774718,0.00615827,0.04911178,-0.00014909,-0.00134931,-0.04559702,-0.02865437,0.00735753,0.11855686,-0.08873574,0.01540192,-0.05401134,0.02252035,0.04518499,-0.01074544,0.01533373,-0.03132426,-0.17096248,0.00329538,-0.07134772,-0.01545369,-0.02830364,0.04581571,-0.03201757,-0.03196608,0.02443174,-0.07255446,0.00693165,0.00947847,0.01184994,0.00977081,0.00313609,0.03086784,-0.05666548,0.03062154,-0.07854237,-0.02627816,-0.05365935,-0.0179459,-0.07186082,-0.01577861,0.06066008,0.02025394,0.02796514,0.03154646,-0.01499054,0.02760155,0.04771164,0.10296904,0.05619365,-0.19854124,0.03612226,0.07573178,0.05294644,-0.04552964,0.01209329,0.02982053,0.03794807,0.04272971,0.03298334,-0.00674381,0.01670911,0.02765976,0.02917926,0.02392483,0.01465138,-0.02455781,-0.05534742,-0.04207668,0.05028759,0.02189872,-0.00128196,-0.02153346,-0.04721005,0.03587499,-0.02246132,-0.03109912,-0.00098601,-0.03804081,0.00965194,0.05851082,0.00173089,-0.0094991,-0.00226645,-0.03591643,0.03196235,-0.08137293,0.10551278,-0.02503427,0.08108583,-0.01830998,-0.04165396,0.03863508,0.02719421,-0.00863814,-0.06578176,0.0363427,0.03383253,0.03622905,-0.01303432,0.10930261,-0.05582301,0.00208394,0.0128529,0.00118584,0.00273407,-0.01187115,0.01175516,0.00883724,0.03266954,0.05482509,0.03277124,-0.00025691,-0.00374744,0.01153765,0.05156457,0.05962946,0.04396354,0.03794116,0.01776263,-0.06648806,0.00331137,-0.02578986,-0.04436471,-0.03648144,0.0015462,0.00220415,-0.03078817,-0.02041948,-0.02662874,-0.00720295,-0.10098534,-0.04836036,0.1055892,0.05705142,0.01690783,-0.00013541,-0.04324764,0.01576579,0.06248599,-0.0073356,-0.07879403,0.0073443,-0.01321439,-0.00062013,0.12013122,-0.09189977,0.0253862,-0.02321357,-0.00172688,-0.0133949,0.14455639,0.03841605,-0.11397745,0.02546107,0.06878559,-0.00388085,-0.07368946,0.02925025,0.01515178,-0.00595111,-0.02242859,0.04103529,-0.01721416,-0.03517717,-0.02099904,-0.0387288,0.04024272,-0.03103952,-0.05510883,-0.07503536,-0.04954869,0.01461225,-0.022802,-0.01742636,-0.00076771,-0.00168951,0.08291269,-0.104341,0.01407233,-0.01202711,0.06804803,-0.02820028,-0.00519402,-0.03595512,-0.03074808,-0.0252605,-0.03904373,0.09537285,0.06532478,0.03612098,0.01281935,-0.05835075,0.03744169,0.0040898,-0.02931225,0.02680914,0.03357917,-0.04261592,0.06381652,-0.06534906,0.0234537,-0.03463355,0.0017312,0.00697432,0.03967207,-0.02163956,0.00409267,0.01794541,-0.00014479,-0.08261008,-0.20961975,-0.01120383,0.00635023,-0.02449629,0.01948475,-0.00530897,0.05907242,0.01570329,0.05173886,0.08890152,0.10495333,0.04013875,-0.07267331,0.01926966,-0.00779071,0.04754346,0.05040753,-0.01567316,0.01766862,0.04982666,-0.03720033,-0.01488772,0.02313807,-0.09588832,0.05159129,-0.0270279,0.10464438,-0.02645549,0.04769385,0.00973934,0.04653176,0.00548796,-0.02005873,-0.17083171,0.04453506,0.03762095,-0.00300009,-0.0045366,-0.04700572,-0.07015892,-0.00596802,0.0102635,0.02060337,-0.08372774,0.0124205,-0.05979644,-0.0254424,-0.0037107,-0.04609611,-0.03437604,0.00254164,0.04747593,0.0211061,0.09392709,0.00173731,-0.00562001,0.00589979,-0.08028067,-0.02446918,0.05001547,-0.01173729,-0.00703583,-0.04490248,-0.02346117,0.00576196,-0.0055894,-0.0181216,-0.00585017,0.03641805,-0.021774,-0.0289544,0.13292614,0.04576044,-0.01885401,0.03736484,0.0231066,-0.01374876,-0.07710432,-0.00362946,-0.00669512,0.02915103,0.004134,0.02132816,0.07664511,-0.0214155,0.08072077,0.05106393,-0.04396128,0.00991576,-0.0072417,-0.0059419,-0.04028808,-0.06188325,-0.0587113,0.06925515,0.03501818,-0.28120214,-0.04726501,-0.02440904,0.00458682,0.06600543,0.02511249,0.06299166,0.02035318,-0.06655892,-0.00184607,-0.02781268,0.08661131,0.00886859,-0.01469544,-0.02618858,-0.02618154,0.05903882,-0.0130829,0.03973954,0.06378775,0.02499883,0.04153494,0.20933306,0.01974983,0.00057808,0.03580233,-0.05607215,0.05200541,0.00016649,0.02108201,-0.02559522,-0.0124643,0.00636488,-0.06021736,-0.01076443,0.09639207,-0.00627853,-0.01599843,0.00493153,0.04673244,-0.03273986,-0.02463667,0.0033419,0.02625533,0.08466452,0.02616841,-0.07376342,-0.07215559,0.00920194,0.01487356,-0.07920054,-0.00922971,-0.01739782,0.03765032,0.03090509,0.06683718,0.01287455,-0.04183583,-0.07301171,-0.02833519,0.00086337,0.01238459,-0.08016227,0.03712084,-0.01945361],"last_embed":{"hash":"1p1fq0a","tokens":149}}},"text":null,"length":0,"last_read":{"hash":"1p1fq0a","at":1751426530261},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{59}","lines":[351,351],"size":240,"outlinks":[{"title":"https://equilibrium.co/writing/privacy-blockchains-and-aleo-deep-dive","target":"https://equilibrium.co/writing/privacy-blockchains-and-aleo-deep-dive","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1p1fq0a","at":1751426530261}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{60}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02499816,-0.03150937,-0.02314506,-0.00627799,0.02597342,-0.02314406,0.00578151,0.0146457,0.00333556,0.01607465,0.08616652,-0.02295103,0.01895015,0.00963382,0.00449984,-0.01195512,0.03195279,-0.00129262,-0.02646134,0.02963471,0.14075519,-0.11139502,-0.00874649,-0.01474526,0.1099078,0.03241944,-0.01686809,-0.04915637,-0.04523193,-0.22885312,0.02109824,-0.06679889,0.01226626,0.00945166,0.00510676,-0.0073235,-0.00843548,0.04387664,-0.08552755,0.01464465,0.02191523,-0.00869628,0.02811179,-0.01112543,0.02195057,-0.07037035,0.00786561,-0.05502616,0.01874312,-0.05441178,-0.02112933,-0.05106085,0.0019778,0.01725509,0.0228831,0.01020018,-0.00713009,0.0209081,0.06563465,0.04027612,0.09973247,0.07652028,-0.22563002,0.04684467,0.09939383,0.02510065,-0.00524167,0.02509286,0.04242867,0.06538327,0.02519703,-0.01014839,-0.05420406,0.03121242,0.00140155,0.01691196,-0.00006473,0.0124739,-0.02690329,-0.04802055,-0.01850984,0.01088748,-0.00339591,-0.0180322,-0.05089543,-0.01939304,0.00043987,-0.00390372,-0.02877347,0.01259052,-0.02013231,0.03493724,0.09374986,-0.01942722,-0.03105986,-0.0053035,0.00546268,0.03501134,-0.10568231,0.10585384,-0.02013406,0.03085471,0.0022011,-0.06429327,0.00927916,0.04841304,0.00209714,-0.04456998,-0.02044153,-0.00300032,0.03871461,-0.01938794,0.02883386,-0.05905188,0.04784336,0.02311536,0.00464728,0.03285092,-0.00718045,0.03334474,-0.0049826,0.02814928,0.07767898,-0.03101802,-0.02164857,-0.02139673,0.01615071,0.07214925,0.0160828,0.05196058,0.03731427,0.02580459,-0.0615932,-0.00017147,-0.0121626,-0.03541018,-0.02726274,0.00998065,-0.00868345,-0.00020543,-0.01410538,-0.06265645,-0.04007754,-0.11970678,-0.02080859,0.08140376,0.01997187,-0.00474922,0.01835829,-0.03502834,0.00351473,0.07627724,-0.01410448,-0.12061507,0.00661379,-0.00031736,0.03321243,0.09902726,-0.04874054,-0.03343788,-0.03292683,-0.02790707,-0.06687745,0.15500258,0.04551535,-0.09525917,0.05154821,0.05530276,0.00221577,-0.05489697,-0.01400013,-0.04859388,-0.03333043,-0.03573759,0.06344533,-0.02408386,-0.03160283,-0.0725793,-0.0227219,0.0192184,0.01446416,-0.02712724,-0.04958242,0.00837506,0.02211445,-0.01224145,-0.02326847,0.01422798,0.01904967,0.00434612,-0.11185259,0.01328834,0.00691649,0.05889105,-0.00481614,0.01716959,-0.03838329,-0.0099356,-0.01522279,-0.04802516,0.08094037,0.00448796,0.00674974,0.01014628,-0.0170748,0.07551518,-0.01136053,-0.01468488,-0.0066975,0.01984858,0.03377312,-0.00595821,0.01967251,0.04674834,-0.05473514,-0.00391667,0.05039841,0.0237301,-0.052575,-0.026715,0.02687251,0.04756362,-0.09767149,-0.20034021,-0.04459106,-0.02346734,-0.04654018,0.03362844,-0.04077296,0.03865438,-0.00703005,0.03166246,0.09876988,0.0490693,0.04845408,-0.05835436,0.02301562,-0.0004168,0.04422006,0.05323,-0.00459874,-0.00506692,0.01452436,0.00357023,0.02574174,-0.01057703,-0.06111153,0.04246749,-0.01507849,0.10247514,-0.00239776,0.0372259,0.0433155,0.06668799,0.03063183,-0.04337812,-0.07446373,0.00447204,0.05551817,-0.02028973,-0.04043248,-0.00799431,0.0212162,-0.02507857,0.02325226,0.00573631,-0.10864104,0.03163328,-0.04283737,-0.03183733,-0.0174325,0.00821911,0.04753335,0.000704,0.01388594,0.03518432,0.10823689,0.05171195,-0.01719695,-0.0372524,-0.07223482,0.01386251,0.07258295,-0.02127127,-0.00797625,-0.04292169,-0.01900658,0.02226002,0.01065541,-0.03449297,0.0494215,0.04797019,0.01587884,-0.0154025,0.15806083,0.03819579,-0.01571307,-0.00440623,0.04616573,-0.05471135,-0.08918868,-0.01028451,0.02771771,0.01408711,0.04871056,0.04870434,0.05834045,-0.01257896,0.0485802,0.03079087,-0.01917987,-0.00165735,-0.01594963,0.00247504,-0.02965681,-0.05547153,-0.02513316,0.07474662,-0.03037337,-0.27817765,-0.02089518,-0.01486989,-0.05500709,0.04734592,0.05786881,0.05199568,0.0108986,-0.07039052,0.04673756,-0.08608297,0.04786782,0.01667257,-0.03971729,0.00222428,0.00319084,0.04872848,-0.03970348,0.04347092,0.04406222,0.02570852,0.02203664,0.18958674,-0.02091486,0.025854,0.01760618,-0.04737945,0.06516167,0.02198575,0.00187682,-0.03252557,0.02881652,-0.01349991,-0.03190846,0.0052964,0.07172465,-0.05768031,-0.0069826,0.00519911,-0.00664085,-0.03868118,-0.00282586,0.02030497,0.0145958,0.12296991,-0.03293655,-0.0265647,-0.10390869,0.04079265,0.04155163,-0.05865789,0.01642178,-0.00517446,0.03127181,-0.02922812,0.03167871,0.01738198,-0.06117917,-0.02715757,-0.06759758,-0.00537276,0.02328575,-0.07655522,0.02479643,0.05009002],"last_embed":{"hash":"naaicw","tokens":197}}},"text":null,"length":0,"last_read":{"hash":"naaicw","at":1751426530268},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{60}","lines":[352,352],"size":371,"outlinks":[{"title":"https://www.blockchaincoinvestors.com/blog/risczero-eliminating-tradeoffs-and-unleashing-the-power-of-blockchain-technology-254ec","target":"https://www.blockchaincoinvestors.com/blog/risczero-eliminating-tradeoffs-and-unleashing-the-power-of-blockchain-technology-254ec","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"naaicw","at":1751426530268}},
"smart_blocks:web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{61}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07341368,-0.02435147,-0.04940522,-0.00764518,0.04056206,-0.01882087,-0.01190667,0.02957314,-0.04605223,0.00002256,0.03067449,-0.03782757,0.00861683,0.02901852,0.01822234,-0.00389041,-0.01147897,-0.01020063,-0.01211509,0.01053794,0.08299595,-0.06732996,0.01533377,-0.05605607,0.07695476,0.05068287,0.02563918,-0.04548509,-0.0302549,-0.23622699,-0.00019751,-0.05983166,0.04284689,-0.01662493,0.02262008,-0.06062426,-0.04646016,0.07189547,-0.10432492,0.01587262,0.035723,-0.00531241,0.00415729,-0.02933326,0.0285717,-0.08531275,0.02688739,-0.02863428,-0.03210267,-0.00810086,0.00534964,-0.06975637,-0.0111884,0.01431506,0.0432333,0.00780367,0.01848498,0.03042552,0.02446609,0.01655393,0.07269899,0.03576959,-0.22812212,0.0393025,0.08772856,0.08287277,-0.03690062,0.06124911,0.01597217,0.08466581,0.05511161,0.02272372,0.01786531,0.01198249,0.01589732,0.05134128,0.04001866,-0.03365955,-0.04914971,-0.08156738,-0.07825594,0.0331172,-0.04315969,-0.01781979,-0.0257343,-0.00885148,0.00442038,-0.0584458,0.01113894,-0.0168534,0.01072283,0.02723462,0.07602254,0.02639556,-0.00355453,-0.01736721,0.0224165,0.04700676,-0.09138569,0.11899474,-0.00903813,0.06443964,-0.0092203,-0.06218506,0.0598257,-0.03252268,-0.01387055,-0.02922168,0.03560204,0.02664214,0.03792306,0.0085571,0.07954384,-0.04823158,0.01914429,0.03954396,0.0465152,0.01520308,-0.00946103,-0.00629871,0.04122962,0.06736702,0.0013828,-0.03000259,-0.06082424,-0.0274677,0.04147243,0.05923074,0.05727391,0.01196147,0.0547018,0.02563435,-0.03643524,0.03259332,0.00186994,-0.03662581,0.01116014,0.00255862,-0.06002167,0.00687658,0.00397998,-0.06395758,-0.03987715,-0.09504251,-0.03021296,0.07717501,0.00727199,-0.02692408,0.00092737,-0.05592318,0.00608034,0.02293613,0.01103331,-0.0612875,0.03249233,0.02417753,0.07312929,0.08101734,-0.05338722,-0.00118515,-0.05209824,0.00300625,-0.0223115,0.14930388,0.07168317,-0.10312805,0.00650237,0.03753959,0.01290011,-0.05287094,0.01889062,0.00481875,-0.06726182,-0.00126943,0.04551248,-0.08414564,-0.05598338,0.01448505,0.0074123,0.0435178,0.00936639,-0.01088212,-0.06501103,-0.00947779,-0.0063201,-0.04085108,-0.00396165,0.00900079,0.03752372,0.048729,-0.06913116,0.03534692,-0.02635649,0.03565425,-0.05938855,-0.05721178,-0.01501785,-0.03946651,0.03185292,-0.02628845,0.05436649,0.04560835,-0.01382059,0.01094782,-0.08096039,0.00863655,-0.05136374,-0.05284618,0.03839001,0.0438178,-0.03902371,-0.01193289,-0.0495004,0.04235611,0.00661823,0.01290797,0.02997669,0.01241068,-0.05432997,0.00833794,-0.00978697,0.00358571,-0.05235738,-0.21140969,-0.02727487,0.01124366,0.03585627,0.03563657,-0.03593959,0.07119267,0.01786726,0.0424128,0.08829282,0.07323005,0.03511778,-0.04996788,0.00803489,0.01890321,0.0097903,0.0087901,-0.0181587,0.04177408,0.04605221,0.00930579,0.03569142,0.00637701,-0.0770924,0.03033593,-0.04338021,0.11272464,0.02141805,0.02534438,-0.00659875,0.02082342,0.01439073,-0.01309762,-0.12922047,-0.00666893,0.08714751,0.05126622,-0.04025742,0.00249791,-0.04853463,-0.05547103,0.05896302,-0.00475997,-0.12615339,0.03512641,-0.06474786,-0.06378485,0.00418239,-0.05063893,0.04250392,-0.00523562,0.04756415,0.05762447,0.0853729,-0.01602504,-0.01638954,-0.00378399,-0.08339341,0.02212332,0.02402576,-0.0183928,0.00834999,-0.05048893,0.00862225,0.05334135,0.02310629,0.01247444,0.02052256,0.03104797,-0.034985,-0.02723564,0.09383852,0.05347383,-0.01602723,0.00517735,0.02365956,-0.01992286,-0.09512345,0.04731154,-0.01648237,0.04946521,0.01871273,0.05399831,0.05256444,0.04295049,0.09453682,0.03220581,0.01479935,0.06155248,-0.00557457,-0.01753595,-0.02386077,-0.05181449,-0.08085464,0.04342207,0.00681603,-0.26084098,-0.01449509,-0.00416578,-0.02301106,-0.0035471,-0.0052745,0.06502865,0.01972035,-0.07055577,0.0018301,-0.01121515,0.04774337,-0.01424143,-0.03838658,0.01413118,-0.01263733,0.06222113,0.02769528,0.02514519,0.05473052,-0.01646624,0.06860961,0.17282708,-0.01052532,0.06707249,0.03286,-0.0318556,0.00802318,0.04523988,0.00862746,-0.02705721,-0.00943842,0.02101591,-0.04467487,0.00593853,0.06170348,-0.06630821,-0.02008499,0.01035623,0.0062168,-0.02063132,-0.03472947,0.01308491,0.02021935,0.11089717,-0.06023074,-0.06151129,-0.09267384,0.00423587,0.01424165,-0.04396638,-0.02624165,0.01625683,-0.01056309,-0.02253718,0.06279908,0.04581723,-0.01945737,-0.05083528,-0.03186222,-0.01721223,-0.0020151,-0.058277,0.05526892,0.00936159],"last_embed":{"hash":"1kpccu0","tokens":275}}},"text":null,"length":0,"last_read":{"hash":"1kpccu0","at":1751426530276},"key":"web3/report/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{61}","lines":[353,353],"size":496,"outlinks":[{"title":"https://podcasts.musixmatch.com/podcast/the-delphi-podcast-01hpa65hx48s2p8649mhbr8ke5/episode/logan-allen-nockchains-global-competition-for-useful-01jvmp9agdsppxbp1n6gwtz1ck","target":"https://podcasts.musixmatch.com/podcast/the-delphi-podcast-01hpa65hx48s2p8649mhbr8ke5/episode/logan-allen-nockchains-global-competition-for-useful-01jvmp9agdsppxbp1n6gwtz1ck","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1kpccu0","at":1751426530276}},

"smart_sources:web3/report/Nockchain.md": null,

"smart_sources:web3/summary/research/Nockchain.md": {"path":"web3/summary/research/Nockchain.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07275173,-0.01932815,-0.04920657,-0.03351859,0.04836593,0.0305918,0.00445503,0.00735194,0.01793349,-0.01981179,0.05472401,-0.0740912,0.05371221,0.03046246,0.02814274,-0.02737349,0.00964113,-0.03932364,-0.02903375,0.01882442,0.11004816,-0.0620123,0.03850145,0.0005847,0.06262733,0.02449453,0.02121849,-0.03943216,-0.03236825,-0.17554531,0.00357351,-0.05204145,-0.00589797,0.0173687,0.01731957,-0.04301868,-0.00079174,0.06181129,-0.05321549,0.00921401,0.00569755,0.00157952,0.00095416,-0.00260319,0.05618236,-0.0650556,0.02187068,-0.06203769,0.01817573,-0.06028484,-0.03026834,-0.08165681,-0.00524068,0.03281935,0.00739823,0.02620974,-0.00002783,0.04629908,0.02441131,0.04537725,0.07489607,0.06740891,-0.19393659,0.03286908,0.08988846,-0.00363578,-0.02452764,0.01974974,0.05007826,0.07890593,0.0193585,0.01871426,0.00155199,0.07391389,0.03211997,0.00594611,0.02641541,0.02047596,-0.01655851,-0.03316047,-0.06909658,0.09748883,0.01617436,-0.00490174,-0.03046612,-0.030145,-0.00255458,-0.05822936,-0.05957619,0.01600193,-0.00730609,0.01125099,0.02079642,-0.00359407,-0.00618924,-0.0260637,-0.00311146,0.00124056,-0.0658736,0.12481257,-0.05089287,0.04152469,0.00462525,-0.03731395,0.05898334,0.01162289,0.00427981,-0.0542402,0.01520156,0.04624125,-0.00055904,0.02524954,0.05978821,-0.03522788,0.02069354,0.0244726,-0.00826795,-0.00953998,-0.00553018,0.01407698,-0.02603937,0.06011889,0.03878942,-0.04197466,-0.04031077,-0.02386354,0.0078831,0.04474947,0.04484699,0.02737971,0.02625984,0.03850291,-0.07320631,-0.01347376,-0.06830831,-0.02076247,-0.01668393,-0.0019244,-0.03677918,-0.02718347,-0.02835741,-0.07073627,-0.01524506,-0.1230052,-0.07594315,0.08174845,-0.00086034,0.00250419,0.00081678,-0.03632275,0.0229941,0.07952288,-0.04992551,-0.07806964,-0.03591716,0.00373112,0.03715047,0.12771714,-0.04763059,-0.02146964,-0.01576362,-0.04581078,-0.03138923,0.11703931,0.02061885,-0.10982515,0.05508531,0.02606177,-0.01263676,-0.06881212,0.01400737,0.02151287,-0.06425574,0.01545356,0.07720652,-0.03682217,-0.02789401,-0.01991414,0.01274064,0.04589755,0.00213827,-0.0316666,-0.08812808,-0.02665785,-0.02397074,-0.02838193,-0.03081389,0.043218,0.02974235,0.06060689,-0.07212926,0.0469426,-0.00808865,0.03062745,-0.02860892,-0.00576434,-0.03726727,-0.0277526,0.01734382,-0.03371442,0.13376865,0.01905922,0.02117311,0.00663656,-0.05892063,0.0294217,-0.02582024,-0.06254487,0.01017143,0.01851492,-0.01722918,0.02717651,-0.04352745,0.04213156,-0.02270856,-0.00513355,0.02849709,0.0371981,-0.01384616,0.06722273,0.01675464,0.0198365,-0.09939761,-0.17814492,-0.04829004,0.04559932,-0.01223546,0.02645237,0.00908386,0.03225823,0.03352483,0.0498457,0.08474942,0.09337384,0.0736243,-0.05507211,0.00225552,-0.03374054,0.04976677,0.05331995,-0.01123035,-0.0145285,0.03569702,-0.0374614,0.03517808,-0.00051254,-0.04545705,0.04004459,-0.05625474,0.10539484,-0.02692978,-0.00250654,-0.00700443,0.06561265,0.00683561,-0.04929019,-0.17421861,0.01964198,0.07473534,-0.02336124,-0.0080924,-0.05088409,-0.02647295,-0.02849716,0.01375192,0.05436032,-0.09730984,0.00418453,-0.06399725,-0.05779286,-0.03858768,0.00923878,0.01098427,0.02894896,0.02859825,0.02612298,0.1065366,-0.03087461,-0.01123375,0.02658418,-0.04966894,-0.00880577,0.04325478,-0.01822804,-0.01516966,-0.03131302,0.01163401,0.02117632,-0.01728683,-0.01597302,0.02490931,0.01580016,-0.01709064,-0.03568065,0.1437525,0.03196939,0.00725753,0.04038726,0.00514102,-0.04031906,-0.08206631,0.03052806,-0.00209691,0.0271002,0.00823923,0.03130933,0.0480904,-0.01627149,0.04319471,0.02640387,-0.01207537,0.01631711,0.00547772,-0.01017494,-0.03880551,-0.04689551,-0.04204894,0.07044479,0.02277734,-0.2926532,-0.02362196,-0.02024982,-0.01871555,0.07663701,0.03157155,0.06359884,0.01354813,-0.0584434,0.01347997,0.02189543,0.05024622,-0.00932326,-0.06276457,0.00074774,-0.00855143,0.05527918,-0.00367169,0.02264648,0.05775753,-0.00646827,0.06885639,0.22576812,-0.03709798,0.05753827,0.03660522,-0.05954201,0.04094335,0.03581256,0.01394582,-0.05423721,-0.00291227,0.03879063,-0.03015113,0.02451603,0.02210422,-0.01110495,-0.00707467,-0.00742361,-0.00016898,-0.02514056,-0.01616968,-0.03132198,0.03741072,0.12313776,-0.00706637,-0.06321661,-0.07482459,0.0132421,0.04884072,-0.04532594,-0.01837388,0.012706,0.0413923,-0.01183994,0.07031219,0.03477036,-0.05168015,-0.07746861,-0.00659457,-0.05401504,-0.00496301,-0.04580114,0.06604996,0.02001915],"last_embed":{"hash":"14fdg2n","tokens":464}}},"last_read":{"hash":"14fdg2n","at":1751815146765},"class_name":"SmartSource","last_import":{"mtime":1751382188000,"size":44627,"at":1751815127337,"hash":"14fdg2n"},"blocks":{"#**Nockchain 综合研究报告：可验证计算的新范式**":[3,353],"#**Nockchain 综合研究报告：可验证计算的新范式**##**执行摘要**":[5,67],"#**Nockchain 综合研究报告：可验证计算的新范式**##**执行摘要**#{1}":[7,67],"#---frontmatter---":[15,66],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**":[68,99],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.1. 共识引擎：zkPoW \\- 有用工作量证明**":[70,75],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.1. 共识引擎：zkPoW \\- 有用工作量证明**#{1}":[72,75],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.2. 虚拟机：基于 Nock ISA 的新型 ZKVM**":[76,79],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.2. 虚拟机：基于 Nock ISA 的新型 ZKVM**#{1}":[78,79],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.3. 应用层：NockApp 框架与意图模型**":[80,89],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.3. 应用层：NockApp 框架与意图模型**#{1}":[82,89],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.4. 开发者体验：Jock 编程语言**":[90,99],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.4. 开发者体验：Jock 编程语言**#{1}":[92,99],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**":[100,148],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.1. “公平启动”原则**":[102,109],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.1. “公平启动”原则**#{1}":[104,109],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.2. 代币供应、发行与效用**":[110,116],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.2. 代币供应、发行与效用**#{1}":[112,112],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.2. 代币供应、发行与效用**#{2}":[113,113],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.2. 代币供应、发行与效用**#{3}":[114,114],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.2. 代币供应、发行与效用**#{4}":[115,116],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.3. 市场分析与普遍存在的代币混淆**":[117,124],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.3. 市场分析与普遍存在的代币混淆**#{1}":[119,124],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.4. 挖矿经济学**":[125,132],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.4. 挖矿经济学**#{1}":[127,127],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.4. 挖矿经济学**#{2}":[128,128],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.4. 挖矿经济学**#{3}":[129,129],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.4. 挖矿经济学**#{4}":[130,130],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.4. 挖矿经济学**#{5}":[131,132],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**表 2: $NOCK 代币经济学与分配模型**":[133,148],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**表 2: $NOCK 代币经济学与分配模型**#{1}":[135,148],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**":[149,172],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.1. 创始人简介：Logan Allen**":[151,154],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.1. 创始人简介：Logan Allen**#{1}":[153,154],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.2. 投资者分析与“公平启动”的困境**":[155,166],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.2. 投资者分析与“公平启动”的困境**#{1}":[157,166],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.3. 治理模型**":[167,172],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.3. 治理模型**#{1}":[169,172],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**":[173,195],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.1. “实验性与未经审计”的现实**":[175,184],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.1. “实验性与未经审计”的现实**#{1}":[177,184],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.2. 潜在攻击向量**":[185,195],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.2. 潜在攻击向量**#{1}":[187,188],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.2. 潜在攻击向量**#{2}":[189,189],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.2. 潜在攻击向量**#{3}":[190,190],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.2. 潜在攻击向量**#{4}":[191,191],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.2. 潜在攻击向量**#{5}":[192,193],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.2. 潜在攻击向量**#{6}":[194,195],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**":[196,216],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.1. 官方渠道与开发者资源**":[198,204],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.1. 官方渠道与开发者资源**#{1}":[200,200],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.1. 官方渠道与开发者资源**#{2}":[201,201],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.1. 官方渠道与开发者资源**#{3}":[202,202],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.1. 官方渠道与开发者资源**#{4}":[203,204],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.2. 开发者采纳的障碍**":[205,208],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.2. 开发者采纳的障碍**#{1}":[207,208],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.3. 社区情绪与目标受众**":[209,216],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.3. 社区情绪与目标受众**#{1}":[211,216],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**":[217,246],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#{1}":[219,220],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**表 3: 竞争分析：Nockchain vs. Aleo vs. RISC Zero**":[221,234],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**表 3: 竞争分析：Nockchain vs. Aleo vs. RISC Zero**#{1}":[223,234],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**7.1. Nockchain vs. Aleo**":[235,238],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**7.1. Nockchain vs. Aleo**#{1}":[237,238],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**7.2. Nockchain vs. RISC Zero**":[239,246],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**7.2. Nockchain vs. RISC Zero**#{1}":[241,246],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**":[247,353],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.1. 综合分析 (SWOT)**":[249,270],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.1. 综合分析 (SWOT)**#{1}":[251,255],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.1. 综合分析 (SWOT)**#{2}":[256,260],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.1. 综合分析 (SWOT)**#{3}":[261,264],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.1. 综合分析 (SWOT)**#{4}":[265,270],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.2. 未来路线图与关键里程碑**":[271,281],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.2. 未来路线图与关键里程碑**#{1}":[273,276],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.2. 未来路线图与关键里程碑**#{2}":[277,277],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.2. 未来路线图与关键里程碑**#{3}":[278,278],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.2. 未来路线图与关键里程碑**#{4}":[279,279],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.2. 未来路线图与关键里程碑**#{5}":[280,281],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.3. 针对用户的建议**":[282,290],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.3. 针对用户的建议**#{1}":[284,284],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.3. 针对用户的建议**#{2}":[285,285],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.3. 针对用户的建议**#{3}":[286,286],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.3. 针对用户的建议**#{4}":[287,287],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.3. 针对用户的建议**#{5}":[288,288],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.3. 针对用户的建议**#{6}":[289,290],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**":[291,353],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{1}":[293,293],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{2}":[294,294],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{3}":[295,295],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{4}":[296,296],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{5}":[297,297],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{6}":[298,298],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{7}":[299,299],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{8}":[300,300],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{9}":[301,301],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{10}":[302,302],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{11}":[303,303],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{12}":[304,304],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{13}":[305,305],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{14}":[306,306],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{15}":[307,307],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{16}":[308,308],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{17}":[309,309],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{18}":[310,310],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{19}":[311,311],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{20}":[312,312],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{21}":[313,313],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{22}":[314,314],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{23}":[315,315],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{24}":[316,316],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{25}":[317,317],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{26}":[318,318],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{27}":[319,319],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{28}":[320,320],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{29}":[321,321],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{30}":[322,322],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{31}":[323,323],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{32}":[324,324],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{33}":[325,325],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{34}":[326,326],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{35}":[327,327],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{36}":[328,328],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{37}":[329,329],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{38}":[330,330],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{39}":[331,331],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{40}":[332,332],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{41}":[333,333],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{42}":[334,334],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{43}":[335,335],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{44}":[336,336],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{45}":[337,337],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{46}":[338,338],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{47}":[339,339],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{48}":[340,340],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{49}":[341,341],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{50}":[342,342],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{51}":[343,343],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{52}":[344,344],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{53}":[345,345],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{54}":[346,346],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{55}":[347,347],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{56}":[348,348],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{57}":[349,349],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{58}":[350,350],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{59}":[351,351],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{60}":[352,352],"#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{61}":[353,353]},"outlinks":[{"title":"https://github.com/zorp-corp/nockchain","target":"https://github.com/zorp-corp/nockchain","line":293},{"title":"https://github.com/0xmoei/nockchain","target":"https://github.com/0xmoei/nockchain","line":294},{"title":"https://www.nockchain.org/","target":"https://www.nockchain.org/","line":295},{"title":"https://icoanalytics.org/projects/zorp-nockchain/","target":"https://icoanalytics.org/projects/zorp-nockchain/","line":296},{"title":"https://urbit.org/overview/history","target":"https://urbit.org/overview/history","line":297},{"title":"https://github.com/zorp-corp/jock-lang","target":"https://github.com/zorp-corp/jock-lang","line":298},{"title":"https://www.globenewswire.com/news-release/2024/12/30/3002639/0/en/Alpha-Sigma-Capital-Research-Unveils-2025-Crypto-Market-Predictions-AI-Agents-Staking-ETFs-and-Decentralized-Infrastructure-Lead-the-Way.html","target":"https://www.globenewswire.com/news-release/2024/12/30/3002639/0/en/Alpha-Sigma-Capital-Research-Unveils-2025-Crypto-Market-Predictions-AI-Agents-Staking-ETFs-and-Decentralized-Infrastructure-Lead-the-Way.html","line":299},{"title":"https://crypto-fundraising.info/projects/zorp-nockchain/","target":"https://crypto-fundraising.info/projects/zorp-nockchain/","line":300},{"title":"https://members.delphidigital.io/feed/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","target":"https://members.delphidigital.io/feed/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","line":301},{"title":"https://podwise.ai/dashboard/episodes/4062914","target":"https://podwise.ai/dashboard/episodes/4062914","line":302},{"title":"https://player.fm/series/the-delphi-podcast/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","target":"https://player.fm/series/the-delphi-podcast/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","line":303},{"title":"https://tracxn.com/d/companies/zorp/\\_\\_VTLVrft6NMSWw6R9NmmNgOwSmC1kn-XXhp2K\\_MTFXxM","target":"https://tracxn.com/d/companies/zorp/__VTLVrft6NMSWw6R9NmmNgOwSmC1kn-XXhp2K_MTFXxM","line":304},{"title":"https://www.zorp.one/","target":"https://www.zorp.one/","line":305},{"title":"https://www.zorp.one/about","target":"https://www.zorp.one/about","line":306},{"title":"https://en.wikipedia.org/wiki/End\\_of\\_the\\_World\\_(Parks\\_and\\_Recreation)","target":"https://en.wikipedia.org/wiki/End_of_the_World_\\(Parks_and_Recreation\\","line":307},{"title":"https://podcasts.apple.com/gr/podcast/the-delphi-podcast/id1438148082?l=el","target":"https://podcasts.apple.com/gr/podcast/the-delphi-podcast/id1438148082?l=el","line":308},{"title":"https://www.chaincatcher.com/en/article/2181453","target":"https://www.chaincatcher.com/en/article/2181453","line":309},{"title":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","target":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","line":310},{"title":"https://cryptorank.io/ico/nockchain","target":"https://cryptorank.io/ico/nockchain","line":311},{"title":"https://cryptorank.io/price/nockchain","target":"https://cryptorank.io/price/nockchain","line":312},{"title":"https://github.com/zorp-corp/nockapp","target":"https://github.com/zorp-corp/nockapp","line":313},{"title":"https://www.everand.com/podcast/864012011/Logan-Allen-Nockchain-s-Global-Competition-for-Useful-ZK-Proof-of-Work-Miners-Kicks-Off","target":"https://www.everand.com/podcast/864012011/Logan-Allen-Nockchain-s-Global-Competition-for-Useful-ZK-Proof-of-Work-Miners-Kicks-Off","line":314},{"title":"https://www.nervos.org/knowledge-base/what\\_are\\_blockchain\\_intents\\_(explainCKBot)","target":"https://www.nervos.org/knowledge-base/what_are_blockchain_intents_\\(explainCKBot\\","line":315},{"title":"https://volet.com/blog/post/understanding-intents-in-blockchain-a-comprehensive-guide-to-intents-01jdpzq69t52gfwxy6z0bx1nmv","target":"https://volet.com/blog/post/understanding-intents-in-blockchain-a-comprehensive-guide-to-intents-01jdpzq69t52gfwxy6z0bx1nmv","line":316},{"title":"https://crypto.com/en/university/intent-centric-design-for-blockchain","target":"https://crypto.com/en/university/intent-centric-design-for-blockchain","line":317},{"title":"https://www.bitget.com/zh-TC/news/detail/12560604756213","target":"https://www.bitget.com/zh-TC/news/detail/12560604756213","line":318},{"title":"https://www.youtube.com/watch?v=G5tE0LFFiTY","target":"https://www.youtube.com/watch?v=G5tE0LFFiTY","line":319},{"title":"https://ethplorer.io/address/******************************************","target":"https://ethplorer.io/address/******************************************","line":320},{"title":"https://phantom.com/tokens/solana/6tCEUogw2uk4VQz5JKb295ixFrj6vAPEQbZqL9VfFXSz","target":"https://phantom.com/tokens/solana/6tCEUogw2uk4VQz5JKb295ixFrj6vAPEQbZqL9VfFXSz","line":321},{"title":"https://www.dextools.io/app/en/solana/pair-explorer/ENDyunqABSwMvfUoRxsNA1N2QLhXeZQTYrnPZUQdnygP","target":"https://www.dextools.io/app/en/solana/pair-explorer/ENDyunqABSwMvfUoRxsNA1N2QLhXeZQTYrnPZUQdnygP","line":322},{"title":"https://web3.bitget.com/en/wiki/nock-wallet","target":"https://web3.bitget.com/en/wiki/nock-wallet","line":323},{"title":"https://m.sosovalue.com/blog/comprehensive-guide-to-bitcoin-mining","target":"https://m.sosovalue.com/blog/comprehensive-guide-to-bitcoin-mining","line":324},{"title":"https://www.cryptominerbros.com/blog/calculate-crypto-mining-profits/","target":"https://www.cryptominerbros.com/blog/calculate-crypto-mining-profits/","line":325},{"title":"https://asicmarketplace.com/blog/how-to-calculate-profit-in-crypto-mining/","target":"https://asicmarketplace.com/blog/how-to-calculate-profit-in-crypto-mining/","line":326},{"title":"https://www.rootdata.com/Projects/detail/Nockpool?k=MTc2Mzg%3D","target":"https://www.rootdata.com/Projects/detail/Nockpool?k=MTc2Mzg%3D","line":327},{"title":"https://en.wikipedia.org/wiki/Mining\\_pool","target":"https://en.wikipedia.org/wiki/Mining_pool","line":328},{"title":"https://podcasts.apple.com/us/podcast/the-delphi-podcast/id1438148082","target":"https://podcasts.apple.com/us/podcast/the-delphi-podcast/id1438148082","line":329},{"title":"https://icodrops.com/nockchain/","target":"https://icodrops.com/nockchain/","line":330},{"title":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA=","target":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","line":331},{"title":"https://github.com/0xmoei/nockchain/security","target":"https://github.com/0xmoei/nockchain/security","line":332},{"title":"https://dysnix.com/blockchain-security-audit","target":"https://dysnix.com/blockchain-security-audit","line":333},{"title":"https://www.lcx.com/how-to-conduct-blockchain-security-audit/","target":"https://www.lcx.com/how-to-conduct-blockchain-security-audit/","line":334},{"title":"https://veridise.com/audits/nft-security/","target":"https://veridise.com/audits/nft-security/","line":335},{"title":"https://immunebytes.com/blog/blockchain-security-audit-the-ultimate-guide/","target":"https://immunebytes.com/blog/blockchain-security-audit-the-ultimate-guide/","line":336},{"title":"https://hacken.io/services/blockchain-security/blockchain-protocol-security/","target":"https://hacken.io/services/blockchain-security/blockchain-protocol-security/","line":337},{"title":"https://quantstamp.com/audits","target":"https://quantstamp.com/audits","line":338},{"title":"https://www.reddit.com/r/CryptoCurrency/comments/1lhoxfc/outside\\_of\\_the\\_crypto\\_community\\_crypto\\_is\\_still/","target":"https://www.reddit.com/r/CryptoCurrency/comments/1lhoxfc/outside_of_the_crypto_community_crypto_is_still/","line":339},{"title":"https://www.reddit.com/r/CryptoMarkets/comments/1l5jqcu/which\\_crypto\\_community\\_still\\_feels\\_genuinely/","target":"https://www.reddit.com/r/CryptoMarkets/comments/1l5jqcu/which_crypto_community_still_feels_genuinely/","line":340},{"title":"https://www.reddit.com/r/CryptoMarkets/comments/1jakyzw/why\\_community\\_sentiment\\_of\\_every\\_crypto\\_on/","target":"https://www.reddit.com/r/CryptoMarkets/comments/1jakyzw/why_community_sentiment_of_every_crypto_on/","line":341},{"title":"https://www.everand.com/podcast/593966517/Launching-the-Mars-Review-of-Books-with-Noah-Kumin-Noah-Kumin-is-the-founder-and-editor-of-the-Mars-Review-of-Books","target":"https://www.everand.com/podcast/593966517/Launching-the-Mars-Review-of-Books-with-Noah-Kumin-Noah-Kumin-is-the-founder-and-editor-of-the-Mars-Review-of-Books","line":342},{"title":"https://podcasts.apple.com/bt/podcast/urbit-nockchain-and-the-current-state/id1195362330?i=1000709342627","target":"https://podcasts.apple.com/bt/podcast/urbit-nockchain-and-the-current-state/id1195362330?i=1000709342627","line":343},{"title":"https://www.nansen.ai/post/best-telegram-channels-for-crypto-chats","target":"https://www.nansen.ai/post/best-telegram-channels-for-crypto-chats","line":344},{"title":"https://ryanschultz.com/2018/07/17/the-telegram-discussion-groups-for-the-various-blockchain-based-vr-projects-have-been-endlessly-entertaining/","target":"https://ryanschultz.com/2018/07/17/the-telegram-discussion-groups-for-the-various-blockchain-based-vr-projects-have-been-endlessly-entertaining/","line":345},{"title":"https://www.reddit.com/r/CryptoCurrency/comments/lz1f54/beware\\_of\\_fake\\_pump\\_coins\\_groups\\_on\\_telegram/","target":"https://www.reddit.com/r/CryptoCurrency/comments/lz1f54/beware_of_fake_pump_coins_groups_on_telegram/","line":346},{"title":"https://asicmarketplace.com/blog/what-is-aleo/","target":"https://asicmarketplace.com/blog/what-is-aleo/","line":347},{"title":"https://risczero.com/blog/zkvm","target":"https://risczero.com/blog/zkvm","line":348},{"title":"https://www.rootdata.com/Projects/detail/RISC%20Zero?k=MjgwMw%3D%3D","target":"https://www.rootdata.com/Projects/detail/RISC%20Zero?k=MjgwMw%3D%3D","line":349},{"title":"https://en.foresightnews.pro/an-overview-of-privacy-solutions-based-on-zero-knowlege-cryptography/","target":"https://en.foresightnews.pro/an-overview-of-privacy-solutions-based-on-zero-knowlege-cryptography/","line":350},{"title":"https://equilibrium.co/writing/privacy-blockchains-and-aleo-deep-dive","target":"https://equilibrium.co/writing/privacy-blockchains-and-aleo-deep-dive","line":351},{"title":"https://www.blockchaincoinvestors.com/blog/risczero-eliminating-tradeoffs-and-unleashing-the-power-of-blockchain-technology-254ec","target":"https://www.blockchaincoinvestors.com/blog/risczero-eliminating-tradeoffs-and-unleashing-the-power-of-blockchain-technology-254ec","line":352},{"title":"https://podcasts.musixmatch.com/podcast/the-delphi-podcast-01hpa65hx48s2p8649mhbr8ke5/episode/logan-allen-nockchains-global-competition-for-useful-01jvmp9agdsppxbp1n6gwtz1ck","target":"https://podcasts.musixmatch.com/podcast/the-delphi-podcast-01hpa65hx48s2p8649mhbr8ke5/episode/logan-allen-nockchains-global-competition-for-useful-01jvmp9agdsppxbp1n6gwtz1ck","line":353}],"last_embed":{"hash":"14fdg2n","at":1751815145115}},"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07172667,-0.01854359,-0.05071301,-0.0349285,0.04891902,0.02803732,0.00491366,0.00710991,0.01708694,-0.01802686,0.05577595,-0.07324268,0.05393654,0.02933737,0.02869019,-0.02988808,0.0090585,-0.03785322,-0.02578855,0.01783977,0.10777315,-0.06233511,0.03793021,-0.00019537,0.06445802,0.02465882,0.01984466,-0.0391379,-0.03214835,-0.17506208,0.00417036,-0.05129486,-0.00402354,0.01796017,0.01774929,-0.04213848,-0.00010183,0.06288745,-0.05625394,0.00901453,0.00863174,0.00116372,0.00166226,-0.00391977,0.05616866,-0.06589425,0.02301534,-0.06336578,0.01941874,-0.05957126,-0.0283443,-0.08401677,-0.00701358,0.03290174,0.00978317,0.02843652,-0.00087246,0.04395461,0.0217256,0.04198148,0.07507876,0.06735835,-0.19437094,0.03048743,0.08955961,-0.00367326,-0.02513866,0.0208206,0.05081902,0.07963702,0.02138587,0.01893006,0.00273008,0.07357483,0.03245481,0.00919374,0.02632606,0.02230103,-0.01743742,-0.03423728,-0.06969483,0.09648227,0.01457147,-0.00473237,-0.02985461,-0.02966876,-0.00428534,-0.05722651,-0.05863983,0.0166124,-0.00682337,0.01319751,0.02263419,-0.00529846,-0.00768596,-0.02688638,-0.00189489,-0.00006367,-0.06427119,0.12576166,-0.05211553,0.04180254,0.00566349,-0.03776978,0.05724262,0.012779,0.0033899,-0.05469428,0.01678116,0.04625444,-0.00039112,0.02442689,0.06196679,-0.03604774,0.02131283,0.02114257,-0.00940491,-0.01124079,-0.00390446,0.01401038,-0.02603765,0.06092326,0.03792422,-0.04135296,-0.03860004,-0.02310907,0.00696768,0.04320179,0.04649503,0.0244523,0.02714108,0.04244155,-0.07321475,-0.01486285,-0.06794578,-0.02057366,-0.01624194,-0.00107652,-0.03781926,-0.03199211,-0.02758588,-0.07016173,-0.01346165,-0.12289036,-0.07593286,0.07868198,-0.00153926,0.00241957,0.00033286,-0.03609291,0.01992997,0.07980107,-0.0484034,-0.07673627,-0.03594498,0.0026014,0.03431553,0.12677298,-0.04962043,-0.01876176,-0.01562319,-0.04357073,-0.03117097,0.1174055,0.02114185,-0.11231424,0.05390751,0.02477766,-0.01260595,-0.06885457,0.01451176,0.02214129,-0.06352635,0.01511241,0.07899424,-0.03551414,-0.02747305,-0.02092649,0.01405142,0.0477156,0.00109629,-0.03131171,-0.08786025,-0.02760145,-0.02275963,-0.02604906,-0.0309649,0.04191044,0.02951713,0.06038351,-0.0727051,0.04805021,-0.00715888,0.0296177,-0.03022747,-0.00584023,-0.03788716,-0.02633474,0.01883597,-0.03512721,0.13331905,0.02116236,0.02117767,0.00832279,-0.06076718,0.03055056,-0.02805506,-0.06500475,0.01012983,0.01964471,-0.01900597,0.02602019,-0.04227125,0.04165775,-0.02338548,-0.00366348,0.0275039,0.0382241,-0.01477664,0.06778298,0.01761839,0.01943732,-0.09935752,-0.17748526,-0.05005675,0.04534249,-0.01000897,0.02457553,0.00933892,0.03482395,0.03216497,0.05075243,0.08239205,0.0923306,0.07067753,-0.05528846,0.0024502,-0.03221942,0.0525091,0.05507717,-0.01199276,-0.0127858,0.03441281,-0.03721439,0.03398659,-0.00162055,-0.04562627,0.0402384,-0.05445078,0.10592867,-0.02902011,-0.00139541,-0.0066974,0.0669881,0.00865111,-0.05050366,-0.17095201,0.019839,0.07294167,-0.02281853,-0.00687817,-0.05227865,-0.02462109,-0.02708334,0.01250882,0.05483739,-0.09838697,0.00283761,-0.06608973,-0.05642024,-0.03477183,0.0098274,0.0097981,0.02804156,0.02985455,0.0276409,0.1083728,-0.02882268,-0.01306348,0.02743555,-0.04790009,-0.00854228,0.04463191,-0.01756285,-0.0168394,-0.03319724,0.0101177,0.02139283,-0.01624505,-0.01701487,0.02409165,0.01712088,-0.01960124,-0.03540917,0.14324124,0.03334599,0.00677457,0.04086686,0.00592645,-0.03910625,-0.08030621,0.02945989,-0.00174801,0.0254292,0.00697341,0.03060408,0.04942598,-0.01724383,0.04270956,0.02523405,-0.00854802,0.01649425,0.00491083,-0.00851429,-0.03921219,-0.04691592,-0.04446942,0.06883252,0.02303175,-0.29328671,-0.02347974,-0.01903406,-0.01969301,0.07608309,0.03105412,0.06097972,0.01600215,-0.05805074,0.01437483,0.01895235,0.04931375,-0.00852103,-0.06446649,-0.00103946,-0.00841902,0.05592474,-0.00411953,0.02190711,0.05452392,-0.00390351,0.0697179,0.22735998,-0.03671809,0.05694052,0.03473355,-0.06133884,0.04369513,0.03494651,0.01329426,-0.05417438,-0.00408478,0.04027259,-0.03115065,0.0270665,0.02309967,-0.01308002,-0.00867806,-0.00624633,0.00005798,-0.02698549,-0.01658366,-0.03328861,0.03807655,0.123771,-0.00855937,-0.0655344,-0.07213201,0.01369384,0.05048876,-0.04512649,-0.01905595,0.01311062,0.04265228,-0.01098141,0.07132535,0.03349778,-0.05144333,-0.07755169,-0.00662173,-0.05258354,-0.00418353,-0.04627993,0.06556857,0.01875724],"last_embed":{"hash":"rr8a7o","tokens":456}}},"text":null,"length":0,"last_read":{"hash":"rr8a7o","at":1751815145143},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**","lines":[3,353],"size":27863,"outlinks":[{"title":"https://github.com/zorp-corp/nockchain","target":"https://github.com/zorp-corp/nockchain","line":291},{"title":"https://github.com/0xmoei/nockchain","target":"https://github.com/0xmoei/nockchain","line":292},{"title":"https://www.nockchain.org/","target":"https://www.nockchain.org/","line":293},{"title":"https://icoanalytics.org/projects/zorp-nockchain/","target":"https://icoanalytics.org/projects/zorp-nockchain/","line":294},{"title":"https://urbit.org/overview/history","target":"https://urbit.org/overview/history","line":295},{"title":"https://github.com/zorp-corp/jock-lang","target":"https://github.com/zorp-corp/jock-lang","line":296},{"title":"https://www.globenewswire.com/news-release/2024/12/30/3002639/0/en/Alpha-Sigma-Capital-Research-Unveils-2025-Crypto-Market-Predictions-AI-Agents-Staking-ETFs-and-Decentralized-Infrastructure-Lead-the-Way.html","target":"https://www.globenewswire.com/news-release/2024/12/30/3002639/0/en/Alpha-Sigma-Capital-Research-Unveils-2025-Crypto-Market-Predictions-AI-Agents-Staking-ETFs-and-Decentralized-Infrastructure-Lead-the-Way.html","line":297},{"title":"https://crypto-fundraising.info/projects/zorp-nockchain/","target":"https://crypto-fundraising.info/projects/zorp-nockchain/","line":298},{"title":"https://members.delphidigital.io/feed/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","target":"https://members.delphidigital.io/feed/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","line":299},{"title":"https://podwise.ai/dashboard/episodes/4062914","target":"https://podwise.ai/dashboard/episodes/4062914","line":300},{"title":"https://player.fm/series/the-delphi-podcast/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","target":"https://player.fm/series/the-delphi-podcast/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","line":301},{"title":"https://tracxn.com/d/companies/zorp/\\_\\_VTLVrft6NMSWw6R9NmmNgOwSmC1kn-XXhp2K\\_MTFXxM","target":"https://tracxn.com/d/companies/zorp/__VTLVrft6NMSWw6R9NmmNgOwSmC1kn-XXhp2K_MTFXxM","line":302},{"title":"https://www.zorp.one/","target":"https://www.zorp.one/","line":303},{"title":"https://www.zorp.one/about","target":"https://www.zorp.one/about","line":304},{"title":"https://en.wikipedia.org/wiki/End\\_of\\_the\\_World\\_(Parks\\_and\\_Recreation)","target":"https://en.wikipedia.org/wiki/End_of_the_World_\\(Parks_and_Recreation\\","line":305},{"title":"https://podcasts.apple.com/gr/podcast/the-delphi-podcast/id1438148082?l=el","target":"https://podcasts.apple.com/gr/podcast/the-delphi-podcast/id1438148082?l=el","line":306},{"title":"https://www.chaincatcher.com/en/article/2181453","target":"https://www.chaincatcher.com/en/article/2181453","line":307},{"title":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","target":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","line":308},{"title":"https://cryptorank.io/ico/nockchain","target":"https://cryptorank.io/ico/nockchain","line":309},{"title":"https://cryptorank.io/price/nockchain","target":"https://cryptorank.io/price/nockchain","line":310},{"title":"https://github.com/zorp-corp/nockapp","target":"https://github.com/zorp-corp/nockapp","line":311},{"title":"https://www.everand.com/podcast/864012011/Logan-Allen-Nockchain-s-Global-Competition-for-Useful-ZK-Proof-of-Work-Miners-Kicks-Off","target":"https://www.everand.com/podcast/864012011/Logan-Allen-Nockchain-s-Global-Competition-for-Useful-ZK-Proof-of-Work-Miners-Kicks-Off","line":312},{"title":"https://www.nervos.org/knowledge-base/what\\_are\\_blockchain\\_intents\\_(explainCKBot)","target":"https://www.nervos.org/knowledge-base/what_are_blockchain_intents_\\(explainCKBot\\","line":313},{"title":"https://volet.com/blog/post/understanding-intents-in-blockchain-a-comprehensive-guide-to-intents-01jdpzq69t52gfwxy6z0bx1nmv","target":"https://volet.com/blog/post/understanding-intents-in-blockchain-a-comprehensive-guide-to-intents-01jdpzq69t52gfwxy6z0bx1nmv","line":314},{"title":"https://crypto.com/en/university/intent-centric-design-for-blockchain","target":"https://crypto.com/en/university/intent-centric-design-for-blockchain","line":315},{"title":"https://www.bitget.com/zh-TC/news/detail/12560604756213","target":"https://www.bitget.com/zh-TC/news/detail/12560604756213","line":316},{"title":"https://www.youtube.com/watch?v=G5tE0LFFiTY","target":"https://www.youtube.com/watch?v=G5tE0LFFiTY","line":317},{"title":"https://ethplorer.io/address/******************************************","target":"https://ethplorer.io/address/******************************************","line":318},{"title":"https://phantom.com/tokens/solana/6tCEUogw2uk4VQz5JKb295ixFrj6vAPEQbZqL9VfFXSz","target":"https://phantom.com/tokens/solana/6tCEUogw2uk4VQz5JKb295ixFrj6vAPEQbZqL9VfFXSz","line":319},{"title":"https://www.dextools.io/app/en/solana/pair-explorer/ENDyunqABSwMvfUoRxsNA1N2QLhXeZQTYrnPZUQdnygP","target":"https://www.dextools.io/app/en/solana/pair-explorer/ENDyunqABSwMvfUoRxsNA1N2QLhXeZQTYrnPZUQdnygP","line":320},{"title":"https://web3.bitget.com/en/wiki/nock-wallet","target":"https://web3.bitget.com/en/wiki/nock-wallet","line":321},{"title":"https://m.sosovalue.com/blog/comprehensive-guide-to-bitcoin-mining","target":"https://m.sosovalue.com/blog/comprehensive-guide-to-bitcoin-mining","line":322},{"title":"https://www.cryptominerbros.com/blog/calculate-crypto-mining-profits/","target":"https://www.cryptominerbros.com/blog/calculate-crypto-mining-profits/","line":323},{"title":"https://asicmarketplace.com/blog/how-to-calculate-profit-in-crypto-mining/","target":"https://asicmarketplace.com/blog/how-to-calculate-profit-in-crypto-mining/","line":324},{"title":"https://www.rootdata.com/Projects/detail/Nockpool?k=MTc2Mzg%3D","target":"https://www.rootdata.com/Projects/detail/Nockpool?k=MTc2Mzg%3D","line":325},{"title":"https://en.wikipedia.org/wiki/Mining\\_pool","target":"https://en.wikipedia.org/wiki/Mining_pool","line":326},{"title":"https://podcasts.apple.com/us/podcast/the-delphi-podcast/id1438148082","target":"https://podcasts.apple.com/us/podcast/the-delphi-podcast/id1438148082","line":327},{"title":"https://icodrops.com/nockchain/","target":"https://icodrops.com/nockchain/","line":328},{"title":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA=","target":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","line":329},{"title":"https://github.com/0xmoei/nockchain/security","target":"https://github.com/0xmoei/nockchain/security","line":330},{"title":"https://dysnix.com/blockchain-security-audit","target":"https://dysnix.com/blockchain-security-audit","line":331},{"title":"https://www.lcx.com/how-to-conduct-blockchain-security-audit/","target":"https://www.lcx.com/how-to-conduct-blockchain-security-audit/","line":332},{"title":"https://veridise.com/audits/nft-security/","target":"https://veridise.com/audits/nft-security/","line":333},{"title":"https://immunebytes.com/blog/blockchain-security-audit-the-ultimate-guide/","target":"https://immunebytes.com/blog/blockchain-security-audit-the-ultimate-guide/","line":334},{"title":"https://hacken.io/services/blockchain-security/blockchain-protocol-security/","target":"https://hacken.io/services/blockchain-security/blockchain-protocol-security/","line":335},{"title":"https://quantstamp.com/audits","target":"https://quantstamp.com/audits","line":336},{"title":"https://www.reddit.com/r/CryptoCurrency/comments/1lhoxfc/outside\\_of\\_the\\_crypto\\_community\\_crypto\\_is\\_still/","target":"https://www.reddit.com/r/CryptoCurrency/comments/1lhoxfc/outside_of_the_crypto_community_crypto_is_still/","line":337},{"title":"https://www.reddit.com/r/CryptoMarkets/comments/1l5jqcu/which\\_crypto\\_community\\_still\\_feels\\_genuinely/","target":"https://www.reddit.com/r/CryptoMarkets/comments/1l5jqcu/which_crypto_community_still_feels_genuinely/","line":338},{"title":"https://www.reddit.com/r/CryptoMarkets/comments/1jakyzw/why\\_community\\_sentiment\\_of\\_every\\_crypto\\_on/","target":"https://www.reddit.com/r/CryptoMarkets/comments/1jakyzw/why_community_sentiment_of_every_crypto_on/","line":339},{"title":"https://www.everand.com/podcast/593966517/Launching-the-Mars-Review-of-Books-with-Noah-Kumin-Noah-Kumin-is-the-founder-and-editor-of-the-Mars-Review-of-Books","target":"https://www.everand.com/podcast/593966517/Launching-the-Mars-Review-of-Books-with-Noah-Kumin-Noah-Kumin-is-the-founder-and-editor-of-the-Mars-Review-of-Books","line":340},{"title":"https://podcasts.apple.com/bt/podcast/urbit-nockchain-and-the-current-state/id1195362330?i=1000709342627","target":"https://podcasts.apple.com/bt/podcast/urbit-nockchain-and-the-current-state/id1195362330?i=1000709342627","line":341},{"title":"https://www.nansen.ai/post/best-telegram-channels-for-crypto-chats","target":"https://www.nansen.ai/post/best-telegram-channels-for-crypto-chats","line":342},{"title":"https://ryanschultz.com/2018/07/17/the-telegram-discussion-groups-for-the-various-blockchain-based-vr-projects-have-been-endlessly-entertaining/","target":"https://ryanschultz.com/2018/07/17/the-telegram-discussion-groups-for-the-various-blockchain-based-vr-projects-have-been-endlessly-entertaining/","line":343},{"title":"https://www.reddit.com/r/CryptoCurrency/comments/lz1f54/beware\\_of\\_fake\\_pump\\_coins\\_groups\\_on\\_telegram/","target":"https://www.reddit.com/r/CryptoCurrency/comments/lz1f54/beware_of_fake_pump_coins_groups_on_telegram/","line":344},{"title":"https://asicmarketplace.com/blog/what-is-aleo/","target":"https://asicmarketplace.com/blog/what-is-aleo/","line":345},{"title":"https://risczero.com/blog/zkvm","target":"https://risczero.com/blog/zkvm","line":346},{"title":"https://www.rootdata.com/Projects/detail/RISC%20Zero?k=MjgwMw%3D%3D","target":"https://www.rootdata.com/Projects/detail/RISC%20Zero?k=MjgwMw%3D%3D","line":347},{"title":"https://en.foresightnews.pro/an-overview-of-privacy-solutions-based-on-zero-knowlege-cryptography/","target":"https://en.foresightnews.pro/an-overview-of-privacy-solutions-based-on-zero-knowlege-cryptography/","line":348},{"title":"https://equilibrium.co/writing/privacy-blockchains-and-aleo-deep-dive","target":"https://equilibrium.co/writing/privacy-blockchains-and-aleo-deep-dive","line":349},{"title":"https://www.blockchaincoinvestors.com/blog/risczero-eliminating-tradeoffs-and-unleashing-the-power-of-blockchain-technology-254ec","target":"https://www.blockchaincoinvestors.com/blog/risczero-eliminating-tradeoffs-and-unleashing-the-power-of-blockchain-technology-254ec","line":350},{"title":"https://podcasts.musixmatch.com/podcast/the-delphi-podcast-01hpa65hx48s2p8649mhbr8ke5/episode/logan-allen-nockchains-global-competition-for-useful-01jvmp9agdsppxbp1n6gwtz1ck","target":"https://podcasts.musixmatch.com/podcast/the-delphi-podcast-01hpa65hx48s2p8649mhbr8ke5/episode/logan-allen-nockchains-global-competition-for-useful-01jvmp9agdsppxbp1n6gwtz1ck","line":351}],"class_name":"SmartBlock","last_embed":{"hash":"rr8a7o","at":1751815145143}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**执行摘要**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07144068,-0.01453429,-0.05253021,-0.03482685,0.04207098,0.02607654,0.00165429,0.00620205,0.01304986,-0.02107042,0.06117417,-0.06973218,0.04761615,0.03039759,0.03249493,-0.02611499,0.00813607,-0.0394865,-0.02218417,0.02463541,0.10720453,-0.0682743,0.04306901,-0.00333717,0.06776948,0.02673242,0.02006178,-0.0351002,-0.03016516,-0.17619424,0.00396529,-0.0521158,-0.00937112,0.02064644,0.01696864,-0.04369959,-0.00058726,0.06771255,-0.05284191,0.0060156,0.00737035,0.0036683,-0.00255347,0.00059881,0.05340882,-0.07021954,0.02201595,-0.0603637,0.01185673,-0.05754526,-0.02503952,-0.08198426,-0.00605234,0.03457696,0.00061036,0.02552846,0.00150963,0.04867888,0.01729448,0.04421839,0.06562719,0.0608193,-0.19443907,0.02745931,0.09321838,-0.00869091,-0.0292551,0.01869515,0.04947364,0.07846358,0.01676333,0.0161204,-0.00043218,0.07028094,0.02722966,0.00327353,0.03009443,0.01868003,-0.01909204,-0.03739068,-0.06435642,0.09611264,0.01496518,-0.00349723,-0.02397089,-0.02735947,0.00250196,-0.06155589,-0.05301354,0.01818918,-0.01150118,0.01033773,0.02603346,-0.00354438,-0.00769905,-0.0195473,-0.00157942,0.00644881,-0.06706084,0.12099551,-0.04495468,0.04277899,0.00318351,-0.04487344,0.05546409,0.01061622,0.00586466,-0.05410906,0.02232128,0.04471764,-0.00569825,0.02133996,0.05887752,-0.03245172,0.02180952,0.02111649,-0.00707712,-0.0089333,-0.00516015,0.01376774,-0.02505624,0.05814533,0.03950526,-0.03873833,-0.03630539,-0.0250126,0.00594756,0.04462269,0.046425,0.02151031,0.02728337,0.03838552,-0.07130893,-0.01030694,-0.06547537,-0.02077694,-0.01937437,0.004342,-0.03932048,-0.03048871,-0.03195307,-0.07384609,-0.01521446,-0.12685303,-0.07774412,0.08083528,-0.00636457,0.00171483,0.00314616,-0.04220273,0.024023,0.07505349,-0.04870094,-0.07809522,-0.03553902,0.00598485,0.0368771,0.12479752,-0.0416093,-0.01667154,-0.01758987,-0.04353777,-0.03569735,0.12176913,0.02288016,-0.11432344,0.05477995,0.02687468,-0.01390119,-0.06863306,0.01220968,0.01985345,-0.0610574,0.0117395,0.07699755,-0.04474629,-0.02602934,-0.01622946,0.01508446,0.04802664,0.00205628,-0.02090825,-0.0910068,-0.02528618,-0.02443954,-0.0212039,-0.02946554,0.03952671,0.02513934,0.0540946,-0.07720898,0.048332,-0.00470953,0.0283578,-0.02987069,-0.00705777,-0.03496747,-0.02290411,0.018677,-0.04258571,0.13989584,0.02197295,0.01815508,0.00717536,-0.05898092,0.02283661,-0.02608055,-0.05678944,0.00494424,0.02189006,-0.01885136,0.0260712,-0.0414118,0.04060069,-0.01652178,0.0004986,0.02329485,0.03362571,-0.01344269,0.07452433,0.01565617,0.02188704,-0.10128528,-0.17516215,-0.05030426,0.04697629,-0.0110622,0.02734938,0.01474971,0.03268377,0.03187018,0.05072685,0.08463351,0.09216146,0.06844703,-0.05495462,0.00650927,-0.03289264,0.04697566,0.05931244,-0.0100509,-0.02084503,0.03228866,-0.04115874,0.03399159,0.00471126,-0.04225222,0.04471985,-0.05921869,0.10441034,-0.03247123,-0.00128471,-0.00111536,0.0696971,0.00344371,-0.04756284,-0.16945969,0.02031092,0.07696384,-0.01985831,-0.01288165,-0.05133683,-0.02690436,-0.02731462,0.01744931,0.0566981,-0.10168621,0.00947406,-0.06117296,-0.05835167,-0.03176493,0.00354171,0.01707232,0.02893112,0.03003225,0.02393795,0.10808496,-0.02893925,-0.01195474,0.02761237,-0.0479726,-0.0127122,0.0381077,-0.01379876,-0.01456085,-0.02929613,0.01018567,0.02040079,-0.01569878,-0.01287336,0.02983905,0.01730758,-0.01838404,-0.03868446,0.14419353,0.02860305,0.00472559,0.04414613,0.00988646,-0.04240876,-0.0853262,0.03717814,-0.00184993,0.02379395,0.00792657,0.02730992,0.05047829,-0.00983212,0.04261117,0.02775108,-0.01508645,0.02066371,0.00595489,-0.01289484,-0.03838899,-0.04585718,-0.03378773,0.07245848,0.01675192,-0.2967712,-0.02280578,-0.02810897,-0.0191802,0.07254776,0.03767337,0.06618926,0.01180253,-0.05679799,0.01673515,0.01840566,0.04582468,-0.00891963,-0.06279162,0.00535493,-0.00342445,0.05519057,-0.00285026,0.02175323,0.05810473,-0.00853139,0.07158933,0.22921494,-0.03440693,0.05738069,0.03219713,-0.05146515,0.03977391,0.03429259,0.00748763,-0.04985248,-0.00645647,0.04131501,-0.02783318,0.01992874,0.02563996,-0.00969702,-0.00745114,-0.00960648,-0.00088554,-0.02850207,-0.01996623,-0.03466879,0.04237564,0.12608826,-0.00917745,-0.06534396,-0.07213589,0.01003085,0.05112816,-0.04618865,-0.01951834,0.00756673,0.04017812,-0.0147895,0.06918107,0.03770166,-0.05249578,-0.0788865,-0.0077517,-0.05114377,-0.0045648,-0.04581144,0.06449658,0.01952835],"last_embed":{"hash":"as5obu","tokens":500}}},"text":null,"length":0,"last_read":{"hash":"as5obu","at":1751815145187},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**执行摘要**","lines":[5,67],"size":3095,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"as5obu","at":1751815145187}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**执行摘要**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06910042,-0.01089802,-0.05189871,-0.03730057,0.04000563,0.0245658,0.00160549,0.00894583,0.01185131,-0.02177701,0.06324791,-0.07060753,0.04649935,0.03037453,0.03376886,-0.02903131,0.00830691,-0.03769417,-0.02229054,0.02565135,0.10494591,-0.06939601,0.04374035,-0.00468822,0.07008104,0.02585411,0.01926984,-0.03366875,-0.03042822,-0.17807929,0.0012787,-0.05206804,-0.00706878,0.0211099,0.01472833,-0.04474901,0.00152437,0.06885006,-0.05390803,0.00522911,0.00992873,0.00240141,-0.00392228,0.00101609,0.05299611,-0.0729066,0.02213037,-0.06338406,0.01169189,-0.05565272,-0.02295713,-0.08153824,-0.00804727,0.03258013,0.00138355,0.02693635,0.0005345,0.04681012,0.01574268,0.04292032,0.06074737,0.05906754,-0.19645862,0.02396513,0.09467078,-0.00847273,-0.03135985,0.01964958,0.04888386,0.07874946,0.01608166,0.01510721,-0.00003001,0.0698043,0.02799938,0.00334893,0.0298674,0.01849205,-0.0182598,-0.04072789,-0.0634045,0.09729085,0.01333682,-0.00411442,-0.02238085,-0.02467215,0.00337605,-0.0629435,-0.05207026,0.01850902,-0.0130328,0.01112247,0.02955257,-0.00454765,-0.01154343,-0.01812626,-0.0031786,0.00625179,-0.06821097,0.12187267,-0.04491867,0.04326146,0.00526393,-0.04506413,0.05372922,0.00902021,0.00482329,-0.05608892,0.02229503,0.04544261,-0.00661923,0.02248089,0.06031466,-0.03004476,0.02500758,0.0194232,-0.0060538,-0.00956326,-0.0059257,0.01208935,-0.02363179,0.05878599,0.03987791,-0.03775091,-0.03517426,-0.02471298,0.00559603,0.04507446,0.04610463,0.01452068,0.0246712,0.03718911,-0.07185571,-0.0107739,-0.06451733,-0.02022127,-0.01902635,0.00565081,-0.04003258,-0.03327889,-0.03007208,-0.07472253,-0.0132954,-0.12584405,-0.07586909,0.08116957,-0.00972397,0.00054757,0.00420784,-0.04087149,0.02589177,0.07540323,-0.04661156,-0.07690196,-0.03639034,0.00407584,0.03490704,0.12201709,-0.04489372,-0.01417756,-0.02032025,-0.04420976,-0.03468012,0.1228122,0.02161062,-0.11351413,0.0532764,0.02665618,-0.01647356,-0.06915728,0.01407414,0.02054703,-0.06345522,0.01171029,0.07907557,-0.04288547,-0.02663066,-0.01515321,0.01876477,0.04793745,0.00132978,-0.0182717,-0.09177499,-0.0265481,-0.02319778,-0.01714084,-0.02985806,0.03774,0.0261635,0.05380945,-0.0767685,0.04981577,-0.00543436,0.02394923,-0.03214378,-0.00633752,-0.03360616,-0.023953,0.02159198,-0.04569124,0.13684627,0.02165966,0.01737569,0.00768519,-0.05612591,0.02304996,-0.02944859,-0.05852363,0.00842845,0.02237571,-0.02084848,0.022951,-0.04138079,0.03952482,-0.01572986,0.00283798,0.02355452,0.03275545,-0.01591378,0.07606577,0.01511997,0.02356182,-0.10219996,-0.17421806,-0.04863225,0.04914616,-0.01267012,0.02465763,0.01425761,0.03535155,0.03179567,0.0522095,0.08401622,0.09375948,0.06388013,-0.05442081,0.00602392,-0.03119317,0.04876433,0.06159125,-0.01038459,-0.02042134,0.03192165,-0.03910907,0.03497789,0.00446626,-0.04205927,0.04259333,-0.06292389,0.10540754,-0.03249076,0.0023457,-0.00228523,0.07016804,0.00525637,-0.04889211,-0.16496469,0.02120003,0.07694547,-0.0211767,-0.01302934,-0.0520313,-0.02564888,-0.02539252,0.01882788,0.05693818,-0.10088618,0.00937481,-0.06334061,-0.05781966,-0.02744786,0.0028284,0.01718733,0.02930605,0.0301752,0.02466642,0.10964686,-0.02882711,-0.01296213,0.02667825,-0.04716357,-0.01535657,0.04114639,-0.01077085,-0.01349333,-0.03023967,0.00466922,0.02164241,-0.01568351,-0.01207543,0.03034604,0.01733134,-0.01840987,-0.03611876,0.14148484,0.02963508,0.00406564,0.04681978,0.01250251,-0.04146305,-0.08685009,0.03741743,-0.00156118,0.02312683,0.0085972,0.02733855,0.05020301,-0.00676795,0.04032417,0.02669196,-0.01429552,0.02261744,0.00626229,-0.01205784,-0.03835429,-0.04405285,-0.03056208,0.07281967,0.0149373,-0.29791981,-0.02345821,-0.02907207,-0.0179543,0.0745491,0.03905366,0.06423895,0.01261002,-0.05714254,0.01993617,0.01605261,0.04324316,-0.01086112,-0.06170462,0.00325452,-0.00227824,0.05495685,-0.00179052,0.0222546,0.05366724,-0.00654826,0.07069965,0.23062117,-0.03462991,0.0558982,0.03020573,-0.05109988,0.04097166,0.03454965,0.00538285,-0.04827491,-0.00892916,0.0428215,-0.02401823,0.01844631,0.02787342,-0.00977975,-0.00831612,-0.00912084,0.00063453,-0.03042165,-0.02175405,-0.03494836,0.04266798,0.12751715,-0.00951959,-0.06634419,-0.06992791,0.00793362,0.05435746,-0.04562793,-0.01890071,0.01060272,0.04083901,-0.01493493,0.06906381,0.03807244,-0.0525854,-0.07849911,-0.00852566,-0.05183809,-0.00302266,-0.0470022,0.06531529,0.02091604],"last_embed":{"hash":"kzn3cv","tokens":501}}},"text":null,"length":0,"last_read":{"hash":"kzn3cv","at":1751815145211},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**执行摘要**#{1}","lines":[7,67],"size":3081,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"kzn3cv","at":1751815145211}},
"smart_blocks:web3/summary/research/Nockchain.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05547833,-0.0130042,-0.06920844,-0.04439311,-0.00054602,0.02068866,0.00899136,-0.01318252,-0.00351831,-0.0367277,0.03864959,-0.05394988,0.02795121,0.03019988,0.02173388,-0.00775754,0.01960313,-0.02753491,-0.02630915,0.01348738,0.10642985,-0.06487469,0.02467914,-0.01585773,0.07671854,0.04227228,0.0048431,-0.02764907,-0.03345343,-0.16767758,-0.00222177,-0.06250431,0.009276,0.01540569,0.01566127,-0.03360435,-0.01586455,0.07496478,-0.0842558,-0.01001288,0.01587463,0.01958894,0.00755616,0.01410097,0.05191971,-0.08281866,0.01970913,-0.05865231,-0.01651253,-0.0736677,-0.0308998,-0.07594469,-0.01892782,0.03548964,-0.00435636,0.02474559,-0.00159032,0.07186548,0.01705122,0.04523277,0.05193163,0.04852841,-0.16578239,0.0117434,0.09216589,0.01080026,-0.02578418,0.00534458,0.08189866,0.07589506,0.02158054,0.0020054,-0.00209094,0.06160207,0.03374861,0.00975771,0.02877223,-0.00834273,-0.03098092,-0.04539711,-0.048594,0.06416934,0.03953599,0.01072761,-0.0345678,0.00375096,-0.00951362,-0.04250321,-0.04101034,0.01271382,0.00230148,0.00799081,0.03826457,-0.00853748,-0.02891181,-0.0203559,-0.02289895,0.02330655,-0.05929114,0.12455195,-0.03689011,0.03848347,-0.01777652,-0.04130639,0.03971891,-0.00772785,-0.01494619,-0.06358027,0.01536457,0.0254036,0.00972106,0.02786341,0.07221368,-0.03768353,0.02317558,0.01015888,0.01475859,0.00189889,-0.01631578,0.01674136,-0.00784131,0.07443767,0.03709926,-0.03466297,-0.03832145,-0.00916251,0.01200383,0.05179295,0.03862009,0.04023938,0.01616701,0.03187373,-0.05877091,-0.00800606,-0.03806581,-0.03107381,-0.04130183,0.03338794,-0.04687199,-0.03422769,-0.022372,-0.05885494,-0.00822882,-0.12370514,-0.06131749,0.07791439,-0.00408832,0.02291014,-0.0094961,-0.0441654,0.02183839,0.07335963,-0.04505486,-0.05459655,-0.02007869,-0.00592974,0.01740667,0.09710449,-0.05627033,-0.00232371,-0.03601508,-0.02819837,-0.02779998,0.1354849,0.00577615,-0.11413019,0.06206753,0.04374866,-0.01662618,-0.06054633,0.01062786,0.02621384,-0.06882276,-0.01459212,0.05958303,-0.03204514,-0.03881346,-0.01535449,0.02122205,0.05154798,-0.00179539,-0.0205082,-0.09392951,-0.0182222,-0.02377811,-0.02210445,-0.01079472,0.01787147,0.0323362,0.03868216,-0.10827434,0.02237582,0.01553894,0.03115187,-0.06119777,-0.01665712,-0.02660493,-0.02340454,0.01033521,-0.06087247,0.14880091,0.03173417,0.00872988,0.02310796,-0.04766986,0.02099222,-0.02822253,-0.07370521,0.0082279,0.04815787,-0.03482267,0.04003706,-0.03255089,0.04485936,-0.01375172,0.01919766,0.01012471,0.02692432,-0.00695281,0.06478196,0.02329085,-0.0138594,-0.09693694,-0.19217761,-0.0494698,0.0398557,-0.00930866,0.01932643,-0.00279881,0.02200076,0.01031981,0.0510156,0.07321488,0.09666672,0.05939106,-0.07399132,0.0162209,-0.01530922,0.06000446,0.03963583,-0.02200318,-0.00935474,0.03689162,-0.03478532,0.02688562,0.01431313,-0.06578149,0.04168919,-0.05537682,0.10634062,-0.04047436,0.01378156,0.01095594,0.07038359,-0.00122144,-0.03741901,-0.1433693,0.01626906,0.07399196,-0.02806305,-0.00548924,-0.03747872,-0.0349563,-0.00031473,0.01143242,0.03821614,-0.10141007,0.01894508,-0.0584132,-0.04770511,-0.02346124,0.00299975,0.04216059,0.0069015,0.05747296,0.02945193,0.10603057,-0.02681305,-0.01261668,0.04497033,-0.05219871,-0.02371839,0.04216488,-0.0175534,-0.01165659,-0.04113972,0.00456308,0.01505829,-0.00167648,-0.00682342,0.02800296,0.03476261,-0.02638428,-0.04397236,0.16685799,0.0184883,-0.03579642,0.04091553,0.02874971,-0.05422431,-0.10217901,0.05338997,0.00246701,0.03433535,0.01944596,0.03140329,0.05172007,-0.01101637,0.05696733,0.01313598,-0.01183932,0.00738167,0.01219159,-0.00897511,-0.02263714,-0.03147519,-0.05270752,0.08869871,0.00531075,-0.29512551,-0.02075642,-0.02245384,-0.00649413,0.06476416,0.02207425,0.0584309,0.01144891,-0.08464628,0.00494489,-0.01140244,0.04683239,0.01765381,-0.05848531,0.01439751,0.00978085,0.05917814,-0.00858142,0.02930888,0.04357615,0.00942073,0.0670664,0.24337289,-0.02410712,0.04917786,0.02309867,-0.05334222,0.05900268,0.05747791,0.02698457,-0.02769102,-0.01120784,0.03154197,-0.03393809,0.02802799,0.01672621,-0.01399146,-0.0053101,-0.00600651,-0.0025034,-0.02148989,-0.02130395,-0.01048145,0.02274525,0.13423617,-0.02587893,-0.05153482,-0.06135256,0.0087287,0.04919835,-0.06241941,-0.02344739,0.01447559,0.03252146,-0.02233423,0.06161583,0.03675256,-0.04460413,-0.07518984,-0.00802572,-0.03056492,-0.01765238,-0.02132658,0.08054671,0.03155242],"last_embed":{"hash":"126582n","tokens":456}}},"text":null,"length":0,"last_read":{"hash":"126582n","at":1751815145247},"key":"web3/summary/research/Nockchain.md#---frontmatter---","lines":[15,66],"size":2410,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"126582n","at":1751815145247}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08709133,-0.01965413,-0.06799097,-0.03957671,-0.02484253,0.0194037,-0.00186074,0.00589913,-0.0217153,-0.03505587,0.05813977,-0.0538892,0.01637697,0.02428815,0.01737779,-0.01624872,0.00707393,-0.03142258,-0.04274512,0.01489672,0.10104646,-0.05681219,0.03822121,-0.01786481,0.08932173,0.0593308,0.02940645,0.00160142,0.00255757,-0.18419805,-0.00488469,-0.07281978,-0.00596963,0.03368478,0.02387297,-0.06267516,-0.01749728,0.07189815,-0.06944902,0.00925224,0.00879683,0.02404149,-0.01026236,-0.00439095,0.04580834,-0.08301961,0.02587969,-0.07912996,-0.01871592,-0.06177261,-0.0217367,-0.07163707,-0.01741491,0.05210003,-0.00520245,0.00458986,0.02645628,0.06802104,0.01424274,0.02514292,0.031501,0.05671641,-0.17911588,0.02014164,0.0972739,0.0096743,-0.02070399,0.00286738,0.05508738,0.06428717,0.0246738,-0.00316136,-0.00041201,0.05346507,0.02821475,0.01441556,0.02816429,-0.01078726,-0.05663287,-0.05534426,-0.05933148,0.06217311,0.04374481,0.02515025,-0.02678938,0.00715248,0.00389019,-0.0449944,-0.00498235,0.02096077,0.00229083,0.00663173,0.03316323,-0.00425137,-0.03941234,0.01843488,-0.01326846,0.030046,-0.0658369,0.11825109,-0.04770859,0.05130902,-0.02135079,-0.0281429,0.03181601,-0.02734044,-0.01596074,-0.04618918,0.00102002,0.01486878,-0.00121674,0.02306359,0.06179877,-0.01656309,0.01526488,0.00158778,0.02804959,0.00592801,-0.00906242,0.01563985,0.0169292,0.06073713,0.02076222,-0.02609625,-0.05073402,-0.01642827,0.01140325,0.05560157,0.05537745,0.03734132,0.02778613,0.03273817,-0.02303293,0.00819931,-0.03940936,-0.02730765,-0.01557412,0.04950574,-0.03152123,-0.05116609,0.00348333,-0.06568304,-0.01940668,-0.10674999,-0.07662315,0.06980249,-0.01841356,0.02971849,0.02011457,-0.05239096,0.02383086,0.06764299,-0.04061832,-0.05539136,-0.00966645,-0.00185978,0.044335,0.09369382,-0.07564768,0.01390348,-0.03324573,-0.01448722,-0.05159129,0.12276711,0.0158196,-0.10694697,0.06247395,0.03278003,-0.00827623,-0.09459978,0.01401569,0.00754398,-0.05819708,-0.01338513,0.07129738,-0.04477384,-0.05731979,0.00300453,0.02517517,0.05453463,0.01068838,-0.02257672,-0.11180002,0.00202228,-0.00474765,-0.02855184,-0.01414395,0.03506761,0.02430317,0.04143858,-0.0583378,0.03779951,-0.00844708,0.01866018,-0.04564392,-0.03178183,-0.03336481,-0.01756699,0.02023096,-0.0765742,0.12503181,0.03203384,0.00901178,0.00223488,-0.04964282,-0.01886437,-0.02055236,-0.04573633,0.01103936,0.05059252,-0.0250318,0.03724378,-0.05210186,0.03742534,0.01080341,0.03756296,-0.00420433,0.02522937,-0.00566961,0.04711673,0.01562284,-0.00671995,-0.08139432,-0.17201395,-0.05147093,0.03083534,0.00423613,0.03288892,0.00341236,0.02924486,0.00504105,0.03362816,0.0517822,0.08895257,0.04141431,-0.06333669,0.021392,-0.02870017,0.03980267,0.04358207,-0.02099715,-0.0026306,0.04433591,-0.03748193,0.02482462,0.01400116,-0.04692487,0.07154357,-0.08430524,0.09124563,-0.03806853,0.00608234,0.0171482,0.08324232,0.00260412,-0.03062602,-0.16534248,0.03745542,0.08904313,-0.0510019,-0.01085908,-0.0365179,-0.02133481,0.01912751,0.02513546,0.02237561,-0.1180753,0.03994425,-0.03978958,-0.06591093,0.00396689,-0.0129804,0.03380775,0.00065591,0.07177974,0.02952524,0.10963516,-0.02465012,-0.02518225,0.0304086,-0.01366708,-0.03140705,0.04621274,-0.01054644,-0.01177562,-0.05440958,0.02663491,0.03866711,0.0170187,-0.00802756,0.02446532,0.03997584,-0.03030041,-0.04077015,0.15128992,0.0228781,-0.04102286,0.04990039,0.04044382,-0.05762138,-0.09553544,0.05610085,0.00476166,0.03043224,0.00581019,0.03940599,0.05978174,0.01572133,0.05251139,0.02694399,-0.00156312,0.01698777,-0.01916366,-0.01477806,-0.05061514,-0.02216577,-0.03104431,0.09246131,0.01542798,-0.30625156,-0.01356246,-0.04113798,-0.0054722,0.01268384,0.02378387,0.03664931,0.02703914,-0.09924199,0.01609237,-0.02911803,0.04009061,0.00400324,-0.03729861,0.01747212,0.01388113,0.05356726,-0.02738805,0.03132197,0.03385077,-0.00908849,0.08134516,0.23090304,-0.00705115,0.06048784,0.00410273,-0.02790106,0.0525147,0.04919922,0.02283456,-0.03417827,-0.0088454,0.02287247,-0.02635415,0.00382945,0.04242685,-0.02801705,-0.00626018,-0.01805608,-0.00460782,-0.024419,-0.03212535,-0.03585545,0.03874116,0.12345037,-0.0293974,-0.04990608,-0.0734555,0.00411537,0.03120235,-0.04728515,-0.0299574,0.02885594,0.01932536,-0.01809464,0.05409331,0.04225229,-0.04246584,-0.09060848,-0.00979349,-0.02786316,0.00401801,-0.02786743,0.07642762,-0.00137636],"last_embed":{"hash":"3ld2vn","tokens":456}}},"text":null,"length":0,"last_read":{"hash":"3ld2vn","at":1751815145270},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**","lines":[68,99],"size":1927,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"3ld2vn","at":1751815145270}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.1. 共识引擎：zkPoW \\- 有用工作量证明**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08969864,-0.02110359,-0.06895867,-0.03714885,-0.02302686,0.01974316,-0.00223064,0.00565459,-0.01882638,-0.03646501,0.05727305,-0.05258308,0.01772876,0.02486556,0.01803084,-0.0155666,0.0041916,-0.03053737,-0.04617118,0.0136099,0.10561325,-0.05664517,0.03695974,-0.01540279,0.08961503,0.05824944,0.02845854,0.0000416,0.00579594,-0.18331335,-0.00752169,-0.07256668,-0.00444662,0.03590044,0.02808225,-0.06157068,-0.01699867,0.07060049,-0.07063536,0.00813611,0.0075749,0.02343469,-0.00801308,-0.00473911,0.04283856,-0.08146077,0.0258588,-0.07746795,-0.01832612,-0.06348639,-0.02313914,-0.07045646,-0.0182013,0.05317742,-0.00566325,0.00272089,0.02880294,0.06980145,0.01419309,0.02546075,0.030564,0.05869615,-0.18028276,0.02054077,0.0984797,0.00929006,-0.01960923,0.00183974,0.05117165,0.06503131,0.02251993,-0.00315917,-0.00214137,0.05406117,0.02836928,0.01500905,0.02545733,-0.01365206,-0.05560032,-0.05358502,-0.05972891,0.05991117,0.0460657,0.02991617,-0.02930912,0.00874249,0.00549088,-0.04190757,-0.00244105,0.01960649,-0.00006206,0.00521794,0.02933829,-0.00127979,-0.03732537,0.01795276,-0.01154649,0.03098903,-0.06414614,0.11912299,-0.04902573,0.05227628,-0.02171435,-0.02764837,0.02949496,-0.02585338,-0.01783386,-0.04560423,0.00039778,0.01396889,-0.00229321,0.02416957,0.05930161,-0.0210165,0.01341797,0.0031332,0.02833121,0.00434975,-0.00943221,0.01522902,0.01815834,0.06248083,0.02258605,-0.02530987,-0.05126931,-0.01790137,0.01175616,0.05505838,0.05697478,0.0374722,0.02797651,0.03275222,-0.02383951,0.00993667,-0.0387245,-0.02477113,-0.0136994,0.04978072,-0.03133776,-0.05104004,0.00601081,-0.06575888,-0.01954866,-0.10563219,-0.07556695,0.07060947,-0.01971857,0.02822775,0.01704499,-0.05397355,0.02350714,0.07064554,-0.03965524,-0.0543513,-0.0078191,-0.00239186,0.04477121,0.09595545,-0.07389571,0.0139554,-0.03365951,-0.01325952,-0.05146193,0.12396054,0.01748979,-0.10475224,0.06089069,0.03483783,-0.00816805,-0.09313029,0.01410685,0.00710136,-0.05932551,-0.01391906,0.07258966,-0.04195914,-0.05637798,0.00599007,0.02485667,0.05720555,0.01167157,-0.02626565,-0.11228471,0.00025365,-0.00475539,-0.02728393,-0.01518906,0.03527781,0.02520642,0.04009446,-0.05886513,0.03800925,-0.01138403,0.01834841,-0.04569307,-0.03335901,-0.03208173,-0.01741618,0.01939947,-0.07547209,0.1268539,0.03335075,0.00941407,0.00361134,-0.04697669,-0.02000395,-0.020067,-0.04463629,0.00933537,0.04924272,-0.02464345,0.03743051,-0.05314286,0.03882517,0.01075397,0.03683199,-0.0019481,0.02508066,-0.00493952,0.04759814,0.01694647,-0.00576554,-0.08075052,-0.1719698,-0.05401854,0.02780319,0.00486875,0.03047834,0.00544207,0.0280043,0.00566559,0.03298541,0.05042513,0.08898835,0.04349227,-0.06620808,0.0199823,-0.03106281,0.03739727,0.04212702,-0.01972409,-0.00652846,0.04360217,-0.03486024,0.02584615,0.01342219,-0.04379843,0.07157619,-0.08406084,0.09213374,-0.03895511,0.00608917,0.01846984,0.08185431,0.00218739,-0.03199299,-0.16491604,0.03777869,0.08834348,-0.05572446,-0.0144039,-0.0327629,-0.02334392,0.02016543,0.02746461,0.02059071,-0.11923582,0.03845001,-0.0425369,-0.06631621,0.00324414,-0.01145102,0.0354372,-0.00022725,0.07046437,0.02937259,0.10944951,-0.02459916,-0.02534617,0.02967634,-0.01145589,-0.03202071,0.04213504,-0.00917479,-0.01288721,-0.05303108,0.02845869,0.0371093,0.0142142,-0.00933141,0.02353371,0.03931521,-0.02995458,-0.04053865,0.15066779,0.02482722,-0.04035121,0.04969723,0.0396166,-0.05878482,-0.09551491,0.05572227,0.00225953,0.02840716,0.00572495,0.04012944,0.06098964,0.01646012,0.05443232,0.02654253,-0.00532694,0.01613969,-0.02022216,-0.01653591,-0.05017438,-0.02220231,-0.03255321,0.09273459,0.0163237,-0.30712059,-0.01578957,-0.04216632,-0.0057937,0.01213748,0.01941814,0.03689782,0.02326284,-0.09820468,0.01606903,-0.0324665,0.04093454,0.00150902,-0.03619432,0.01793743,0.01593019,0.05071477,-0.02817188,0.03147813,0.0329642,-0.01180602,0.08022588,0.23034412,-0.00508433,0.06069032,0.00458913,-0.02375033,0.05151171,0.04773092,0.02454975,-0.03531621,-0.01204167,0.02695172,-0.02771595,0.00228992,0.04341676,-0.02955191,-0.00495549,-0.0177115,-0.00557973,-0.02277914,-0.02995871,-0.03612641,0.03750742,0.12270142,-0.0254291,-0.04827993,-0.07464411,0.00555456,0.03163824,-0.04544673,-0.03008959,0.02933989,0.02002004,-0.01647024,0.05372463,0.04378006,-0.04209545,-0.09061152,-0.0094316,-0.02766861,0.00800932,-0.02901399,0.07769126,0.00221748],"last_embed":{"hash":"16aj6q6","tokens":457}}},"text":null,"length":0,"last_read":{"hash":"16aj6q6","at":1751815145290},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.1. 共识引擎：zkPoW \\- 有用工作量证明**","lines":[70,75],"size":668,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"16aj6q6","at":1751815145290}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.1. 共识引擎：zkPoW \\- 有用工作量证明**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08891364,-0.02042776,-0.06671795,-0.03942938,-0.02309584,0.018687,-0.00441367,0.00762563,-0.01890212,-0.03541436,0.05839385,-0.05215248,0.0158658,0.02290983,0.01827118,-0.01644221,0.0064512,-0.03270507,-0.04674906,0.01524605,0.10419491,-0.05802346,0.03675181,-0.01509583,0.09171655,0.05800036,0.02707241,0.00015854,0.0065073,-0.183044,-0.00525473,-0.07289091,-0.00443551,0.0340805,0.02905308,-0.06156132,-0.01618304,0.07030561,-0.07126109,0.007462,0.00754421,0.02271727,-0.00933219,-0.00445603,0.04443718,-0.08352289,0.02621388,-0.07841861,-0.01713671,-0.06260259,-0.02101801,-0.0724591,-0.01827027,0.05259895,-0.00318034,0.00124741,0.02779501,0.06678078,0.01432868,0.02509808,0.03056502,0.05873257,-0.18132389,0.0203372,0.09954119,0.00980055,-0.01943691,0.00283043,0.05292137,0.06748172,0.02424385,-0.0034111,-0.00145235,0.05301524,0.03009519,0.01551955,0.02573222,-0.01377669,-0.05438028,-0.0534919,-0.05993674,0.06130517,0.04658061,0.03050611,-0.02917713,0.00771057,0.00542817,-0.04261141,-0.00185878,0.01885329,-0.00085366,0.00495783,0.02930559,-0.00254844,-0.03763936,0.01924944,-0.01156683,0.02805581,-0.0618322,0.11711295,-0.04949877,0.05298968,-0.02043084,-0.02683521,0.02962006,-0.02549899,-0.01790778,-0.04573415,0.00082392,0.01483804,-0.00177937,0.02506464,0.0603758,-0.01993691,0.01291595,0.00037628,0.02656381,0.00460769,-0.00985283,0.0179286,0.0189945,0.06075315,0.02123677,-0.02552367,-0.05188653,-0.01725829,0.0124343,0.05584935,0.0563768,0.03618876,0.02761371,0.0323194,-0.02344516,0.01001988,-0.03833775,-0.02637174,-0.01077758,0.04940665,-0.03212886,-0.05283382,0.00562067,-0.06452949,-0.01931171,-0.10529378,-0.07586183,0.07187551,-0.02114565,0.02993855,0.01771481,-0.05241507,0.02349932,0.06963823,-0.04055776,-0.05299011,-0.00847759,-0.00459224,0.04649293,0.09544707,-0.07651427,0.01348727,-0.03446604,-0.01443264,-0.04985166,0.12172671,0.01823822,-0.10516351,0.06042471,0.03277249,-0.00804944,-0.09424537,0.01420495,0.0080495,-0.05961573,-0.01412039,0.07398341,-0.03894477,-0.0560862,0.00461831,0.0252422,0.05709766,0.00972739,-0.02475068,-0.11163776,0.00048797,-0.00410283,-0.02722253,-0.01357767,0.03469022,0.02422053,0.04272066,-0.06022929,0.03751842,-0.01185724,0.01756766,-0.04748919,-0.03416586,-0.03224066,-0.01804295,0.02066378,-0.07587505,0.12640831,0.0327576,0.01198224,0.00228204,-0.04612541,-0.01828523,-0.02106535,-0.04608784,0.00977294,0.04854925,-0.02416618,0.03519681,-0.05378068,0.03656074,0.0116141,0.0365871,-0.00353028,0.02450942,-0.00478222,0.04915047,0.01397451,-0.0065178,-0.07883863,-0.17269076,-0.0546883,0.02870155,0.00448225,0.02917867,0.00398753,0.02924457,0.00494992,0.03477126,0.05179325,0.09136176,0.04112894,-0.06408454,0.01989152,-0.02863903,0.03807072,0.04153191,-0.01912446,-0.00567443,0.04300028,-0.0360966,0.02528082,0.01520637,-0.04747802,0.07091878,-0.08410808,0.09232702,-0.03823843,0.00493902,0.01651286,0.08186249,0.00260531,-0.03234012,-0.16614671,0.03803434,0.08610964,-0.0527005,-0.01553953,-0.03300497,-0.02342912,0.0167382,0.02707631,0.02104686,-0.11910811,0.03778587,-0.04218531,-0.06579953,0.00440313,-0.01281064,0.03624982,-0.00029511,0.07141411,0.02973152,0.10846407,-0.02489014,-0.02703463,0.03057535,-0.01165169,-0.0322212,0.0441796,-0.00977251,-0.01327062,-0.05600669,0.02804914,0.0365903,0.01482693,-0.00822185,0.02271197,0.03944205,-0.02865043,-0.03900028,0.14919788,0.02236573,-0.04064674,0.04920558,0.04112701,-0.05755194,-0.09401041,0.05721913,0.00453466,0.02747139,0.00536584,0.04006168,0.0599811,0.01583867,0.05342015,0.02830833,-0.00478317,0.01715283,-0.01925354,-0.01728812,-0.0505043,-0.02235251,-0.03023859,0.08889154,0.01797844,-0.30696389,-0.01767788,-0.03998089,-0.00627707,0.01259909,0.01991371,0.03816618,0.02551005,-0.09827479,0.01742166,-0.03322995,0.0412913,0.00175106,-0.03446783,0.01774146,0.01393154,0.05025284,-0.02718008,0.03186895,0.03231904,-0.01061749,0.08128431,0.23219866,-0.00646944,0.06133503,0.00496222,-0.02313081,0.05157272,0.04757373,0.02422222,-0.03439184,-0.01227199,0.0271058,-0.02983679,0.00147196,0.04319954,-0.02922703,-0.00485467,-0.01618056,-0.0030451,-0.02283203,-0.03171184,-0.03390636,0.03613389,0.12558042,-0.02521971,-0.0472031,-0.07490033,0.0046702,0.03269701,-0.04848517,-0.02935787,0.02882579,0.01942613,-0.01604456,0.05493818,0.04126106,-0.04331945,-0.09127947,-0.00891185,-0.02826761,0.00786847,-0.02675866,0.07611267,0.00205044],"last_embed":{"hash":"13grky3","tokens":458}}},"text":null,"length":0,"last_read":{"hash":"13grky3","at":1751815145320},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.1. 共识引擎：zkPoW \\- 有用工作量证明**#{1}","lines":[72,75],"size":631,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"13grky3","at":1751815145320}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.2. 虚拟机：基于 Nock ISA 的新型 ZKVM**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05100835,-0.01436012,-0.05805362,-0.03139577,0.01853312,0.02468013,-0.02293445,0.00509532,-0.00464153,-0.02008441,0.07186174,-0.07718118,0.01659739,0.0185954,0.03333869,-0.01750431,0.03462249,-0.05944829,-0.02913586,0.02644543,0.0598595,-0.06358785,0.03618117,-0.01798582,0.06600803,0.04290391,0.02739609,-0.00067723,-0.03638991,-0.18962702,-0.0178923,-0.06957618,0.0051546,0.02554226,0.00235264,-0.05580431,-0.02973763,0.0609302,-0.0545871,0.00643879,0.01719052,0.00510094,0.00243936,0.01550441,0.03228485,-0.09103794,0.04214296,-0.06711371,-0.00983491,-0.0738365,-0.04215205,-0.08630591,-0.02505912,0.03574323,-0.00969741,0.01290228,0.00784758,0.05319485,0.02453622,0.02240707,0.06399681,0.04591804,-0.1925098,0.01975261,0.08666585,0.00804937,-0.04088134,0.00391705,0.06481921,0.08686046,0.00418952,0.02125492,-0.01169758,0.05123479,0.03041667,-0.00634749,0.01567026,0.00271591,-0.0287833,-0.06270743,-0.05861156,0.07821047,0.02335841,-0.02156488,-0.00943597,-0.00657071,-0.00695606,-0.0555205,-0.02235077,0.01868665,-0.00119202,0.0086836,0.02342822,0.00213552,-0.02305521,-0.00471752,-0.00373557,0.00985625,-0.05148349,0.12533695,-0.04275074,0.04066302,-0.01456837,-0.05507873,0.04524164,0.00554128,-0.02791557,-0.06268957,0.02850826,0.02469237,0.02859155,0.02433095,0.08611435,-0.03316737,0.02266126,0.01703439,0.00894148,-0.00896108,-0.01563911,0.01623726,-0.00546643,0.04858298,0.05393144,-0.02296192,-0.02624189,-0.03410392,0.01073512,0.04296891,0.05351844,0.01940218,0.03166469,0.04493664,-0.06032009,-0.01947238,-0.0217225,-0.01940014,-0.02156635,0.01478844,-0.04829326,-0.04706176,-0.02736444,-0.0791861,-0.01011394,-0.11054119,-0.07243887,0.1091089,0.00497644,-0.00085064,0.01611007,-0.05663163,0.03942548,0.07708605,-0.0309489,-0.08047642,-0.01519896,-0.00055311,0.02779481,0.11914536,-0.08560723,0.01455542,-0.02665749,-0.03174308,-0.01932044,0.1446774,0.01076121,-0.09350996,0.05375765,0.03557543,0.00466037,-0.0738097,0.01939721,0.02456132,-0.04564876,-0.01110708,0.08846461,-0.03657693,-0.02269865,-0.01866496,0.01191695,0.04069405,0.00957867,-0.03705663,-0.0956703,-0.00564461,-0.02189656,-0.01801164,-0.04075604,0.01137561,0.00682539,0.03641896,-0.07466725,0.05579816,0.00869375,0.01776006,-0.01827502,0.01301583,-0.01993244,-0.01523289,0.0256694,-0.06425162,0.1379167,0.02132695,-0.00512787,0.01586264,-0.06922128,-0.00674789,-0.02817995,-0.07071589,0.02539437,0.06549688,-0.02743128,0.0328732,-0.04898142,0.02027687,-0.02008281,0.01001683,0.01535484,0.03473644,-0.01834231,0.05254565,0.03376173,0.00836711,-0.08736761,-0.17427459,-0.03402584,0.04472531,-0.02207869,0.01281372,0.02556191,0.05090292,0.04130562,0.05488548,0.05483953,0.08591579,0.06304077,-0.06134376,-0.00218868,-0.01385591,0.05786908,0.04119932,-0.01646092,-0.00672339,0.03814826,-0.06537098,0.03722442,0.02820065,-0.05668271,0.06568482,-0.06888241,0.07990759,-0.03101158,0.01372406,-0.00081553,0.08940527,0.02694138,-0.03350045,-0.15366612,0.02576432,0.05163363,-0.02854855,0.00242036,-0.06212965,-0.02854307,0.00045518,0.00139959,0.05885758,-0.09452081,0.02655939,-0.05808616,-0.04117974,-0.01736295,-0.00194018,0.022825,0.00237111,0.04158667,0.03800113,0.10938728,-0.0143465,-0.0161682,0.03087013,-0.04498506,-0.00121987,0.04527181,-0.01289074,0.00475366,-0.04445822,0.00069355,0.03993682,-0.02522079,0.00676222,0.03674301,0.02530613,-0.0412566,-0.03759065,0.14152269,0.02519223,-0.01202957,0.04165567,0.03670534,-0.04233061,-0.07410983,0.03348346,0.00330487,0.03132575,0.00538146,0.02701359,0.05354637,-0.00098775,0.04426244,-0.011212,0.01050087,0.006015,0.00750285,-0.00446108,-0.04053772,-0.03897692,-0.03879691,0.08376087,0.00964435,-0.28653711,-0.00254464,-0.03435395,0.00212975,0.05984988,0.02740253,0.03909189,0.01037633,-0.08568262,0.0134463,-0.01097127,0.04697328,-0.00492124,-0.0478474,-0.01127874,0.00215645,0.06540194,-0.01050188,0.02902765,0.02805187,0.00168838,0.08040252,0.23339194,-0.01503908,0.03686156,0.04100037,-0.05207424,0.06101964,0.04873071,0.01050472,-0.03831036,-0.02580814,0.03035176,-0.02023591,0.0430973,0.03841632,-0.00638677,-0.01358149,-0.00175926,0.01447961,-0.03026022,-0.01505219,-0.02809548,0.03758339,0.12802626,-0.02894832,-0.06545803,-0.08057296,0.00938157,0.04443039,-0.05970466,-0.03793608,0.01501163,0.03787525,0.00283785,0.06293685,0.04126467,-0.04599506,-0.06713188,-0.01077624,-0.0308699,-0.03142405,-0.05069507,0.07167875,0.00422324],"last_embed":{"hash":"11na867","tokens":300}}},"text":null,"length":0,"last_read":{"hash":"11na867","at":1751815145353},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.2. 虚拟机：基于 Nock ISA 的新型 ZKVM**","lines":[76,79],"size":284,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"11na867","at":1751815145353}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.2. 虚拟机：基于 Nock ISA 的新型 ZKVM**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0501915,-0.01230249,-0.05745986,-0.0286349,0.01724328,0.02429053,-0.01970452,0.00669405,-0.00458171,-0.01838558,0.07070021,-0.07743807,0.01571104,0.01895984,0.03357505,-0.0188445,0.03495929,-0.05847377,-0.02763668,0.02721337,0.06037374,-0.06097271,0.03769659,-0.01927959,0.06811044,0.0422599,0.02741935,0.00044628,-0.03589584,-0.1896269,-0.0198556,-0.06870548,0.00511147,0.02612752,0.00196662,-0.05668285,-0.02710404,0.06268217,-0.05491395,0.00536646,0.01729307,0.00473208,0.00289134,0.01504399,0.03290752,-0.09478571,0.04228223,-0.06626179,-0.009622,-0.07540115,-0.04155155,-0.08690917,-0.02463253,0.03491554,-0.00821925,0.01411174,0.00623902,0.05281781,0.02492756,0.02198839,0.06376963,0.04458635,-0.19439529,0.01939724,0.08774133,0.00597547,-0.04064701,0.00333363,0.06567048,0.08757645,0.00442122,0.02105843,-0.01420766,0.04960821,0.03067038,-0.00691218,0.01596403,0.00262031,-0.02549727,-0.06326152,-0.05874349,0.07709969,0.02408749,-0.02219649,-0.00755238,-0.00768477,-0.00602135,-0.05629874,-0.02014077,0.0170722,0.00142467,0.00776322,0.02398364,0.00332102,-0.02223554,-0.00542276,-0.00449022,0.00900589,-0.04791252,0.12530966,-0.04078092,0.04123164,-0.01410598,-0.05546562,0.04566019,0.00685762,-0.02650519,-0.06495094,0.02884658,0.02439447,0.02930987,0.02428327,0.08419627,-0.03174702,0.02301175,0.01568601,0.00934538,-0.00775299,-0.01527982,0.01808457,-0.00562088,0.04806216,0.05394006,-0.02247069,-0.0276723,-0.0342703,0.0095966,0.0416283,0.05146907,0.01563675,0.03109612,0.04302017,-0.06163213,-0.01949296,-0.0219596,-0.01875298,-0.0194671,0.01645159,-0.0491543,-0.04610451,-0.02689799,-0.07813398,-0.00927713,-0.11054451,-0.07386994,0.11035527,0.00543827,-0.0011496,0.01631254,-0.0577865,0.03873197,0.07644274,-0.03126168,-0.0815931,-0.01660073,-0.00075847,0.02760727,0.11891453,-0.08561986,0.01335382,-0.02629859,-0.0318844,-0.01799852,0.14266562,0.00869999,-0.09201124,0.05456845,0.03499383,0.00484052,-0.07312018,0.02135141,0.02493209,-0.04701962,-0.01143215,0.08858876,-0.03649769,-0.02213485,-0.01879745,0.01272336,0.04065229,0.00769214,-0.03698588,-0.09455992,-0.00622112,-0.02104096,-0.01628913,-0.04153147,0.01141853,0.00557792,0.03665835,-0.07435948,0.05609126,0.00834098,0.01768175,-0.01887506,0.01240875,-0.01974563,-0.01566274,0.02437274,-0.0636767,0.13885109,0.02053731,-0.00630159,0.01402946,-0.06814409,-0.00630497,-0.02891978,-0.07223667,0.02694725,0.06371326,-0.02594455,0.03264432,-0.04966553,0.01916174,-0.02014076,0.01078107,0.014854,0.03422432,-0.01883167,0.05337471,0.0344483,0.01039578,-0.08780211,-0.17518716,-0.03496458,0.04415701,-0.02336103,0.01190914,0.0275954,0.05254339,0.04130014,0.0546031,0.05486822,0.08466958,0.06300095,-0.06151692,-0.00300424,-0.01373326,0.05984382,0.04227935,-0.01590375,-0.00760157,0.03670606,-0.0653435,0.03703309,0.03038576,-0.05842723,0.0647595,-0.0676312,0.07972883,-0.03358759,0.01454773,-0.00141438,0.08842948,0.02612196,-0.03402848,-0.15239714,0.0233827,0.04782416,-0.02950937,0.00193644,-0.06363877,-0.02818308,-0.00043234,0.00271169,0.05931603,-0.09473829,0.02808703,-0.05720598,-0.04103662,-0.01668887,-0.00187718,0.02326412,0.00272962,0.04402803,0.03745957,0.1086908,-0.01398947,-0.01542654,0.03106341,-0.0460491,-0.00243052,0.04470953,-0.01455455,0.00552785,-0.04463391,-0.0036182,0.04142714,-0.02449575,0.00623755,0.03508469,0.02410685,-0.04040949,-0.03898878,0.14154807,0.02529342,-0.01233556,0.03946181,0.03529719,-0.04033528,-0.076059,0.03421366,0.00289412,0.02989765,0.00256104,0.02606767,0.05513537,0.00035794,0.0428974,-0.0106318,0.00919907,0.00488139,0.00791285,-0.00431943,-0.04247477,-0.03731513,-0.03865501,0.0814632,0.01261079,-0.28536245,-0.00245761,-0.03607236,0.0018089,0.0589731,0.02853902,0.03969629,0.01024173,-0.08520046,0.0136549,-0.01048771,0.0490823,-0.00556488,-0.04625821,-0.01384807,0.00163882,0.06789151,-0.01110976,0.02851089,0.02857796,0.00375779,0.08046551,0.23326936,-0.01273458,0.03948929,0.04172819,-0.0521909,0.05870695,0.04790847,0.00877904,-0.03688768,-0.0270127,0.03083256,-0.02189056,0.04516122,0.0406764,-0.00627742,-0.0128111,-0.00140061,0.0159292,-0.03124798,-0.01526655,-0.02835601,0.03962175,0.13270363,-0.03052258,-0.06397367,-0.07997726,0.00861389,0.04481116,-0.06064063,-0.03589493,0.01388574,0.04061288,0.00321765,0.06120122,0.04310418,-0.04750471,-0.06868067,-0.01206547,-0.03119924,-0.02910519,-0.05008677,0.06897447,0.00482631],"last_embed":{"hash":"1byzbip","tokens":297}}},"text":null,"length":0,"last_read":{"hash":"1byzbip","at":1751815145370},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.2. 虚拟机：基于 Nock ISA 的新型 ZKVM**#{1}","lines":[78,79],"size":244,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1byzbip","at":1751815145370}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.3. 应用层：NockApp 框架与意图模型**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08307084,-0.0282035,-0.04715607,-0.04207458,0.01908115,0.01091568,0.00186023,-0.00118635,-0.0088538,-0.03794442,0.05339045,-0.07339198,0.03991584,0.02776035,0.02810802,-0.01459013,0.00960151,-0.02915131,-0.03588545,0.01025123,0.09544948,-0.04629353,0.0369912,-0.02603282,0.07670072,0.03780157,-0.00090928,-0.02640451,-0.00784937,-0.18366182,-0.0156697,-0.06847551,0.01854983,0.01496482,0.00582083,-0.01604049,-0.01449624,0.06188578,-0.06263545,0.00728219,0.02065108,0.01798602,0.01068102,0.03636461,0.04361746,-0.08404331,0.02968472,-0.08250802,0.00097894,-0.05498444,-0.03230895,-0.08943003,-0.02725221,0.01789873,0.02588413,0.05239822,0.01578436,0.06695297,0.06507697,0.05429604,0.07401038,0.06045367,-0.18805124,0.02694022,0.11689024,0.01738984,-0.03111991,0.01249395,0.0483977,0.07232469,0.05226977,0.02295945,0.01773586,0.07034118,0.02651732,0.01363337,0.02699772,-0.01557518,-0.03354226,-0.05604354,-0.04615279,0.08650776,0.04474628,0.01220929,-0.04096634,-0.00718855,-0.01036483,-0.05500576,-0.03547904,-0.00839472,0.0140136,0.02422684,0.06734012,0.00763199,-0.04057102,-0.02497744,-0.01742691,0.00376573,-0.08397313,0.10919685,-0.01833533,0.02859924,-0.03367436,-0.05359232,0.06174859,0.00339962,-0.03348195,-0.0750644,-0.00167355,0.01719672,0.03334105,0.03786734,0.04026845,-0.05800113,0.02758586,0.00655913,0.00523744,-0.02132724,-0.02363019,0.02469842,0.00260669,0.05513061,0.03854855,-0.00684545,-0.03632868,-0.01001314,0.02650869,0.03408531,0.03695307,0.03759852,0.03206411,0.03984059,-0.07565881,-0.01721795,-0.03138857,-0.00725024,-0.03711629,0.00958911,-0.04271484,-0.04510361,-0.03943823,-0.08464751,-0.02154561,-0.10302168,-0.05203864,0.07404279,-0.002027,0.00270259,-0.02423778,-0.02174231,0.00497542,0.05872558,-0.04894077,-0.03760466,-0.01362874,-0.01960667,0.04141996,0.07933053,-0.05305353,-0.01712357,-0.01811626,-0.03419123,-0.02513202,0.13532494,0.02803261,-0.12684694,0.07239688,0.05605471,-0.02480255,-0.0671255,0.01098305,0.03727904,-0.06119453,0.00462565,0.0831371,-0.00961593,-0.03630336,-0.01204564,0.01408424,0.04753279,0.01281437,-0.01797378,-0.07659968,-0.02028866,-0.02790683,-0.02698164,-0.01083286,0.00944073,0.03724583,0.03522248,-0.09890837,0.04166455,0.01188901,0.03322707,-0.03489636,-0.01671565,-0.02329436,-0.0353412,0.02033274,-0.03607576,0.12255334,0.04515865,-0.00444458,0.01146389,-0.0332298,0.02770978,-0.08079842,-0.07553414,-0.00814467,0.03686481,-0.03763324,0.03576873,-0.03951328,0.03809188,-0.01133555,0.01449889,0.01805002,0.03472904,-0.02311507,0.08391467,0.04019079,-0.01999332,-0.07676081,-0.19620354,-0.01852739,0.03764804,-0.04443019,0.02200969,-0.0238252,0.05613074,0.00877729,0.058877,0.06990268,0.07835785,0.0685906,-0.04014007,-0.0082145,-0.01133256,0.04984312,0.03397254,-0.01687252,-0.00864054,0.0535642,-0.0023571,0.02922563,0.00999605,-0.06875537,0.01246781,-0.05408904,0.1119587,-0.02742318,0.02755753,-0.00187149,0.06135229,0.01344618,-0.02671856,-0.12681895,0.01333273,0.03995323,-0.01217384,-0.00758848,-0.04928657,-0.02804478,-0.00620337,0.0430485,0.05347229,-0.11060432,0.03532858,-0.07492797,-0.05014783,-0.02228848,0.02656852,0.02225623,0.00532503,0.06567534,0.01365325,0.11900885,-0.01416492,-0.00767859,0.028736,-0.04668828,0.00162497,0.05684189,-0.00629573,-0.00622724,-0.04036142,-0.00261046,0.01077207,0.00221391,-0.00491157,0.01267273,0.02636431,-0.03557576,-0.03040313,0.14391451,0.03846555,-0.00868519,0.00214507,0.02460433,-0.04004091,-0.09487171,0.04496507,0.00587815,0.00619611,0.00684523,0.03995512,0.02014138,-0.00692305,0.06756801,0.00146521,-0.00834032,0.00323992,0.00881255,-0.00828952,-0.0334536,-0.04828372,-0.05299732,0.05669254,0.01236928,-0.30065876,-0.02641927,0.0174088,0.00519633,0.06924966,-0.00024693,0.05828457,0.0159599,-0.06257737,0.0251027,-0.02518496,0.05228078,-0.01707085,-0.07924097,0.00438449,0.00712634,0.06612465,0.00871215,0.00292529,0.00906945,-0.00015684,0.08209284,0.20011154,-0.04951759,0.06922209,0.03545255,-0.05821864,0.02492733,0.07057765,0.0250562,-0.05494618,-0.01579777,0.03546406,-0.04099474,0.01759562,0.0405616,-0.01633303,-0.02163208,-0.01113027,0.00748141,-0.04405113,-0.01665965,-0.03770358,0.03606565,0.13050118,-0.01647894,-0.05890351,-0.06644789,0.01090795,0.05136218,-0.04023141,-0.00100883,0.00171068,0.02622865,0.0071678,0.02743252,0.03421061,-0.01452173,-0.07737806,-0.00497477,-0.02174843,-0.01416261,-0.04826524,0.05782308,0.03540496],"last_embed":{"hash":"6q17ve","tokens":441}}},"text":null,"length":0,"last_read":{"hash":"6q17ve","at":1751815145390},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.3. 应用层：NockApp 框架与意图模型**","lines":[80,89],"size":636,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"6q17ve","at":1751815145390}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.3. 应用层：NockApp 框架与意图模型**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08280402,-0.02093893,-0.04590422,-0.04793783,0.01981337,0.00962657,0.00179538,0.00035518,-0.00683196,-0.04053392,0.05319289,-0.07276091,0.0420423,0.03069755,0.02498088,-0.01515133,0.00931404,-0.02898456,-0.03970984,0.01304838,0.09808239,-0.04980826,0.03871553,-0.0209817,0.07653595,0.04028937,0.00010564,-0.02449988,-0.00853972,-0.18416668,-0.01882384,-0.06811571,0.0231725,0.01731363,0.00220885,-0.01363814,-0.01529272,0.06112361,-0.06018382,0.0051447,0.02267708,0.02220902,0.00795982,0.03913556,0.04562382,-0.08810847,0.03177079,-0.08243209,0.00479301,-0.05565985,-0.03147522,-0.08955587,-0.02582921,0.01569857,0.02258912,0.05443174,0.01455717,0.06995685,0.06242726,0.05469617,0.06956466,0.06188029,-0.18868279,0.02808947,0.11652761,0.01619091,-0.02867083,0.00681644,0.04791949,0.07214111,0.05518266,0.01895,0.01887915,0.07142121,0.02622213,0.01237136,0.02755436,-0.01943139,-0.03310673,-0.05507905,-0.04576954,0.08835194,0.04194387,0.01716835,-0.03747153,-0.00590664,-0.00920659,-0.05386572,-0.03535035,-0.00157878,0.01317747,0.02490723,0.06826504,0.009634,-0.04900359,-0.02500689,-0.01819212,0.00465289,-0.0817192,0.10872943,-0.01567274,0.02735736,-0.03347876,-0.05211344,0.06463509,0.00672209,-0.0363549,-0.07591917,-0.00192601,0.01913742,0.03386747,0.03791462,0.03876919,-0.05578934,0.02636535,0.01101542,0.0002692,-0.02521031,-0.02716247,0.01809031,0.00514996,0.05363591,0.03642799,-0.01029469,-0.03258967,-0.01003583,0.02669888,0.03274766,0.03243002,0.03279788,0.0306498,0.0409676,-0.0788325,-0.02127352,-0.03199193,-0.0044471,-0.03844216,0.00966059,-0.03870812,-0.0504619,-0.03752176,-0.08549584,-0.01738674,-0.09999429,-0.05169576,0.07127409,-0.0057916,0.00490613,-0.02706348,-0.0181726,0.00560363,0.06360627,-0.04719504,-0.03216162,-0.01349678,-0.02691873,0.04242392,0.07435911,-0.05380372,-0.01835776,-0.02071179,-0.03845855,-0.02295392,0.13907266,0.02587748,-0.12557615,0.07315298,0.06107309,-0.02723526,-0.06977145,0.01133952,0.03673484,-0.06311569,0.00370402,0.08450678,-0.00380739,-0.039258,-0.00992206,0.01214702,0.04544085,0.0166034,-0.0159589,-0.0767853,-0.02098908,-0.02666598,-0.02626286,-0.0078056,0.00870845,0.03505081,0.03107365,-0.09904478,0.0413775,0.00710964,0.03261819,-0.03465202,-0.01626616,-0.02367677,-0.03818715,0.02005205,-0.03522331,0.11933722,0.04444284,-0.00566957,0.01360119,-0.03031045,0.0325104,-0.0829545,-0.07506436,-0.01237644,0.03514351,-0.04242645,0.03824875,-0.03483643,0.0419117,-0.00922656,0.01464429,0.02109368,0.03717874,-0.02578207,0.08121438,0.03924767,-0.02137257,-0.07146838,-0.19860859,-0.01127051,0.03828665,-0.04436934,0.02303633,-0.01828415,0.05676145,0.00977777,0.0576643,0.06170138,0.07496819,0.06605996,-0.04444554,-0.01101439,-0.01145023,0.05212145,0.03341535,-0.01614439,-0.0120829,0.05291825,0.00336068,0.02892705,0.01182722,-0.06972833,0.01154443,-0.05299953,0.11193183,-0.02756976,0.02759599,0.00285474,0.06161332,0.01290542,-0.02679418,-0.1235734,0.01233319,0.03663606,-0.012857,-0.00793597,-0.0473598,-0.03025552,-0.00463826,0.04243666,0.05638207,-0.11209174,0.03839202,-0.07780042,-0.05191438,-0.02081798,0.03033579,0.02613093,0.00867997,0.06255373,0.01674426,0.12071394,-0.01625956,-0.01100225,0.02462218,-0.04133675,-0.00551425,0.05624996,-0.008345,-0.00653919,-0.04025111,-0.0054983,0.00701802,0.00403153,-0.00270201,0.01542112,0.02639683,-0.03856021,-0.0289052,0.13703255,0.03673098,-0.00655486,0.00342153,0.02657006,-0.03653369,-0.09352706,0.04624192,0.00653121,0.00223119,0.00937213,0.03936309,0.01745504,-0.00452048,0.06215692,0.00340826,-0.01200315,0.00754989,0.00588148,-0.01217762,-0.03770693,-0.04998149,-0.04823788,0.0532199,0.01370997,-0.30216801,-0.03016556,0.02153157,0.00989021,0.06902114,0.00183147,0.05713189,0.01565714,-0.06086625,0.0244608,-0.02801945,0.05411391,-0.01985661,-0.08037347,0.00422047,0.00409421,0.06843346,0.00891715,0.00801321,0.00493393,0.00219335,0.07992426,0.19796166,-0.04865701,0.06720223,0.0315758,-0.05349198,0.02338789,0.0713656,0.02781571,-0.05560667,-0.0145375,0.03894502,-0.04021376,0.01831715,0.04130851,-0.01273121,-0.02131255,-0.0138232,0.01040746,-0.04218033,-0.01987189,-0.04132379,0.03586407,0.13218088,-0.0161711,-0.05647714,-0.06510143,0.01257582,0.05124443,-0.04415773,-0.00159439,0.00352388,0.02614408,0.00512399,0.02614431,0.03529648,-0.01373932,-0.07866137,-0.00555718,-0.02498349,-0.01154193,-0.0510207,0.05882194,0.03790614],"last_embed":{"hash":"11oxra3","tokens":440}}},"text":null,"length":0,"last_read":{"hash":"11oxra3","at":1751815145419},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.3. 应用层：NockApp 框架与意图模型**#{1}","lines":[82,89],"size":601,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"11oxra3","at":1751815145419}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.4. 开发者体验：Jock 编程语言**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05910669,0.02156641,-0.04966589,-0.06571659,0.07028214,0.02017045,-0.00325985,0.00482686,-0.01766544,-0.0222779,0.05012751,-0.11597867,0.04236239,-0.00002636,0.02360199,-0.03579518,0.01052348,-0.01597071,-0.02671046,0.04332015,0.07573175,-0.06150113,0.01358141,-0.02477461,0.10101151,0.00193776,0.00482732,-0.01778362,-0.04196554,-0.17328668,0.00311034,-0.05982406,0.03596497,0.03749706,-0.00133225,-0.04903636,0.00169874,0.06074994,-0.05106711,0.03861165,-0.00042483,-0.02116495,0.01583632,-0.00031705,0.03502528,-0.05286205,0.03528971,-0.07760112,0.04962724,-0.02631235,-0.0449309,-0.04604562,-0.00169115,0.00812431,0.04623203,0.03587692,0.00854593,0.05179859,0.03052275,0.03818133,0.04875255,0.07164903,-0.20032579,0.03218525,0.09334116,0.00810773,-0.03841457,0.01706039,0.07428084,0.09012772,0.00899874,0.01175846,0.00948742,0.05083291,0.03507613,0.0216002,-0.00518093,0.01494674,-0.01521803,-0.04842724,-0.09747013,0.08325967,-0.00876997,-0.02476451,-0.06568535,0.00024739,-0.02066799,-0.05573626,-0.05169405,0.03478422,-0.0186575,0.01263991,0.00909365,0.01877251,-0.05410801,-0.01316569,-0.03862645,-0.00838453,-0.08926294,0.1275665,-0.04149888,0.02114568,-0.00973677,-0.05394493,0.0404729,-0.00292345,-0.00008902,-0.05178339,-0.0060014,0.02242628,0.01862632,0.04345463,0.049128,-0.03128923,0.03479069,0.02517213,-0.01671859,-0.00500177,-0.03145975,0.02051502,-0.00100073,0.08332683,0.03035315,-0.02228757,-0.03607281,-0.01302685,0.00678017,0.05267427,0.05567631,0.0132742,-0.0020385,0.07069355,-0.05970611,-0.0273721,-0.03307192,-0.00342208,-0.01561952,0.0120359,-0.02747783,-0.00022377,-0.01077903,-0.0686696,-0.02323548,-0.1127784,-0.0592691,0.02822664,0.00832147,0.00190516,0.02434036,-0.02913468,0.00453708,0.06201622,-0.04116665,-0.06029246,-0.02089554,-0.01802969,0.03407137,0.1071351,-0.05740218,0.00161363,-0.06432678,-0.04281254,-0.03196321,0.135085,0.00052029,-0.10579766,0.02519359,0.02703143,-0.02245713,-0.08357586,0.00669006,0.03538078,-0.03147029,0.01285976,0.09177087,-0.01726603,-0.00438657,-0.03448081,0.01874542,0.04608243,0.03086345,-0.02717625,-0.06973889,-0.0203466,-0.01817499,-0.01877002,-0.02120727,0.04093898,0.05232338,0.05850572,-0.06966188,0.0698358,-0.00309806,0.00067604,-0.03674828,-0.00036302,-0.00842429,-0.01239725,0.05763017,-0.04256761,0.08673324,0.01386066,0.01026008,0.02044964,-0.10372822,0.0345425,-0.05797299,-0.0692491,0.03817067,0.00024682,-0.03644767,0.01724142,-0.03709153,0.03208093,-0.00831502,0.01769546,0.05702423,0.04861155,-0.02620913,0.06328875,0.00008092,0.01480811,-0.09597576,-0.18951689,-0.03582601,0.07933527,-0.02441311,0.02488733,-0.00582331,0.02892946,0.03122827,0.05880439,0.06602027,0.10751824,0.05251034,-0.06585871,-0.02705456,-0.0477642,0.06431634,0.03336687,-0.04174905,-0.01483309,0.04365707,-0.02540954,0.06047127,-0.02696759,-0.0330867,0.05361424,-0.08760235,0.10238489,-0.00021101,0.04845509,-0.03832149,0.08134044,0.0324666,-0.01447829,-0.12666927,0.0233187,0.06131077,-0.01760136,-0.02554045,-0.03683436,-0.01576244,-0.02361264,0.02740771,0.03729051,-0.0991047,0.00732428,-0.06609006,-0.05567959,-0.04910461,-0.00566911,0.02536478,0.02881241,0.0361783,0.0576335,0.10640453,-0.02176736,-0.03742009,0.0082202,-0.06400966,0.00371825,0.06509583,0.01074422,0.00832026,-0.02562411,-0.02385115,0.0357582,-0.01495078,-0.01842521,0.02097363,0.00876969,-0.03617414,-0.01709083,0.09274115,0.03057189,0.00304892,0.03017677,0.01481565,-0.05421134,-0.07640691,0.04511253,-0.00024886,0.04685955,0.02023813,0.0285903,0.04112875,-0.0026351,0.02052127,0.00187759,0.01676511,0.04322789,0.01560814,-0.02146702,-0.01969519,-0.03638362,-0.03533665,0.06710319,0.01833811,-0.29748929,-0.00620737,0.02453043,0.00674658,0.06496285,0.03196554,0.05899003,0.00866212,-0.07391667,0.0451862,0.0250008,0.06389775,-0.00889379,-0.05533475,-0.00895313,-0.00461061,0.03984204,-0.01369792,0.02378226,0.01126261,-0.00626191,0.06038595,0.20422402,-0.05411244,0.07782448,0.05040208,-0.05662555,0.02898285,-0.00348407,0.01255031,-0.03118597,-0.00169957,0.03701207,-0.01800678,-0.00740552,0.01007184,0.00144518,0.01028055,-0.01597808,-0.00313161,-0.05257781,-0.02110252,-0.04251584,0.03318843,0.1189836,0.00587861,-0.07332493,-0.06081835,-0.00356612,0.04814881,-0.06167679,-0.02377618,0.02892471,0.04136611,-0.03409116,0.05888601,0.03340809,-0.04534549,-0.05982928,0.00221441,-0.04487944,-0.00340812,-0.04923697,0.07674859,0.02595243],"last_embed":{"hash":"1ads4ky","tokens":283}}},"text":null,"length":0,"last_read":{"hash":"1ads4ky","at":1751815145439},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.4. 开发者体验：Jock 编程语言**","lines":[90,99],"size":298,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1ads4ky","at":1751815145439}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.4. 开发者体验：Jock 编程语言**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05909635,0.02394655,-0.05111001,-0.06779612,0.06932374,0.02086807,-0.00812909,0.00364073,-0.0219845,-0.02302509,0.05026811,-0.11817933,0.04066622,0.00044546,0.02490918,-0.03552461,0.01339729,-0.01456951,-0.02681457,0.04366334,0.07206941,-0.06048371,0.01309551,-0.02510339,0.10034213,0.00020994,0.00054755,-0.01454872,-0.03884092,-0.17535087,0.00194953,-0.05863875,0.0343258,0.04057779,-0.00213256,-0.04861245,0.00302417,0.06407482,-0.05125022,0.04012184,-0.00067423,-0.02068902,0.01814647,-0.0032291,0.03533913,-0.05234456,0.03692324,-0.07941579,0.0456937,-0.02532071,-0.04218699,-0.04739286,-0.0045301,0.00741543,0.04928631,0.03560018,0.00702174,0.04798759,0.03068605,0.03677394,0.05091494,0.07026168,-0.20061305,0.02741769,0.09452993,0.01051768,-0.03924577,0.01779616,0.07124957,0.08803331,0.00940342,0.01017479,0.00918939,0.04934966,0.03455835,0.02312963,-0.00766774,0.01565043,-0.01652369,-0.04830885,-0.09523743,0.08512775,-0.0058683,-0.02480799,-0.06395423,0.00402503,-0.02124874,-0.05590335,-0.05007183,0.0345186,-0.01610996,0.01315042,0.01100812,0.0203494,-0.05202681,-0.01224832,-0.03686241,-0.00897243,-0.08424188,0.12768821,-0.0425216,0.02392522,-0.00706627,-0.05524199,0.03597753,-0.00295383,-0.00231801,-0.04964568,-0.00705363,0.01893968,0.02197546,0.04563186,0.04798248,-0.03293223,0.03451781,0.0233868,-0.01979249,-0.00719078,-0.03206723,0.0252366,0.00222758,0.08083986,0.03189389,-0.02268221,-0.03691464,-0.01034367,0.00985077,0.05181871,0.05689037,0.01182758,0.00106286,0.06907801,-0.05862327,-0.02473607,-0.03063289,-0.00489534,-0.01673055,0.01346841,-0.02556299,-0.00573035,-0.01054622,-0.07028833,-0.0216644,-0.11313542,-0.05975612,0.02817044,0.00749807,0.00255216,0.02456891,-0.02937228,0.00332601,0.0612919,-0.03959284,-0.06018884,-0.02155355,-0.0207819,0.03329203,0.10366679,-0.06149656,0.00291624,-0.06127731,-0.04236967,-0.03141137,0.13243422,-0.00028586,-0.10300938,0.02842539,0.03188361,-0.02175613,-0.08380734,0.00752192,0.03741532,-0.0335436,0.01180179,0.09447389,-0.01920219,-0.00817904,-0.03399027,0.02000343,0.04711448,0.03013419,-0.02573809,-0.06982521,-0.01863915,-0.01813719,-0.02150288,-0.02244573,0.04042247,0.05181146,0.06056321,-0.07191245,0.06742515,-0.00309808,0.00294309,-0.03745562,0.00027275,-0.00823952,-0.01247357,0.05894022,-0.04240101,0.08543301,0.01538056,0.00770525,0.0200892,-0.09997843,0.03228487,-0.05986603,-0.07337996,0.03856079,0.00188977,-0.03906597,0.01748339,-0.03642437,0.03442106,-0.01032307,0.01889389,0.05726347,0.05067919,-0.02817787,0.06405852,-0.00113385,0.01132839,-0.09783363,-0.18797895,-0.03761252,0.07802282,-0.02715134,0.02117123,-0.00717874,0.02885162,0.03141655,0.0575804,0.06627043,0.10748647,0.05188417,-0.06134697,-0.0306727,-0.04756685,0.06877095,0.03207919,-0.03819685,-0.01263789,0.04513566,-0.02552701,0.06007382,-0.02247048,-0.03818613,0.05191474,-0.08721133,0.102729,-0.00088735,0.05164973,-0.03844229,0.08233395,0.03186323,-0.01490428,-0.12526296,0.02471351,0.0611407,-0.01757428,-0.02683632,-0.03883601,-0.01655223,-0.02200479,0.02736663,0.03377684,-0.09955768,0.01424361,-0.0646525,-0.05693229,-0.04754954,-0.00298155,0.02282233,0.02854288,0.04064983,0.0586015,0.11072107,-0.02211501,-0.03632636,0.00930235,-0.0618089,0.00099059,0.0665891,0.01183369,0.00869906,-0.02906254,-0.0238637,0.03466883,-0.01287487,-0.02016997,0.01767443,0.00827831,-0.03591831,-0.01807793,0.09123802,0.0343651,-0.00024656,0.02695838,0.01506423,-0.05676476,-0.07898483,0.04452366,0.00010682,0.04419906,0.01550527,0.02947079,0.04186492,-0.00221509,0.02292673,-0.00127743,0.01417239,0.04326685,0.01676525,-0.02040176,-0.01686402,-0.03514653,-0.03544271,0.06411468,0.0176154,-0.29635119,-0.00704819,0.02231304,0.00538926,0.06275805,0.02756332,0.06077677,0.01338422,-0.07437018,0.04543141,0.02395825,0.06482925,-0.01046732,-0.05619501,-0.01054222,-0.00285322,0.04013836,-0.01182189,0.02572552,0.01006971,-0.0040186,0.06271203,0.20499592,-0.05126398,0.07971348,0.04875839,-0.05538684,0.0297487,-0.00121195,0.01350848,-0.03268266,-0.0015209,0.03716357,-0.01734992,-0.00656423,0.01482181,0.00080495,0.00563446,-0.01594264,0.00008886,-0.05397312,-0.02375305,-0.04400534,0.03608691,0.12237195,0.00181855,-0.07282628,-0.0628759,-0.00532976,0.04817492,-0.06270063,-0.02294057,0.02958525,0.03772055,-0.03555583,0.0598735,0.03331512,-0.04538071,-0.05971937,0.00346556,-0.04699562,-0.00436869,-0.04662629,0.07758909,0.02784899],"last_embed":{"hash":"10sx3sr","tokens":280}}},"text":null,"length":0,"last_read":{"hash":"10sx3sr","at":1751815145458},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第二部分：技术深度剖析 \\- Nockchain 的架构**#**2.4. 开发者体验：Jock 编程语言**#{1}","lines":[92,99],"size":267,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"10sx3sr","at":1751815145458}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0639324,-0.01161074,-0.05813746,-0.02699715,0.03067146,0.01417401,-0.0043324,-0.01176211,0.01630339,-0.03902994,0.05770558,-0.05819638,0.03143686,0.02879441,0.03717311,-0.01897901,0.02968356,-0.05038165,-0.04204566,0.02159096,0.09561332,-0.06353274,0.03477458,-0.00843653,0.07729594,0.0473743,0.00473354,-0.0324579,-0.03514326,-0.17425862,0.00973733,-0.06016875,0.01645168,0.02934405,0.0234927,-0.04141976,-0.01057741,0.08133041,-0.05759803,-0.02070235,0.01590431,0.00325025,-0.0027157,0.02544285,0.03707032,-0.08120366,0.02694807,-0.08027183,0.00056209,-0.05284763,-0.02912843,-0.09002873,-0.0198805,0.03007523,0.02416363,0.02683383,-0.01269632,0.05372217,0.02529574,0.02417319,0.04799721,0.03687223,-0.19053665,0.02231625,0.09984585,0.01265474,-0.01755201,0.01871068,0.08404581,0.05828082,0.02054161,0.0087894,-0.00192982,0.04617685,0.01908101,0.01253244,0.01591598,0.00870439,-0.0375431,-0.05093573,-0.0599929,0.078088,0.01938193,0.02466819,-0.02166021,-0.00030461,0.01786372,-0.04407365,-0.04842015,0.01141361,0.01049954,0.0137656,0.0490827,-0.00682728,-0.02722924,0.00280963,0.00320132,-0.00300371,-0.06369568,0.11279808,-0.02049785,0.04685194,-0.02184463,-0.05789815,0.03644316,0.01454611,-0.01300906,-0.05463363,-0.00923855,0.01948993,0.01433926,0.01168524,0.09563178,-0.04774126,0.02208706,0.01398244,-0.00967335,0.00028688,-0.01903382,0.02407238,-0.00408047,0.04493682,0.02939673,-0.03309277,-0.03372219,-0.02359101,0.00071003,0.04633712,0.06511154,0.04029074,0.0123616,0.04566281,-0.0597375,-0.01551521,-0.05056282,-0.00543891,-0.03199774,0.03220545,-0.02302867,-0.01734513,-0.03299716,-0.05763172,-0.02818878,-0.11365109,-0.08418965,0.07331206,0.0056611,0.00322932,0.00728546,-0.05017111,0.01772648,0.0654888,-0.02035677,-0.06448147,-0.01942275,0.00151165,0.03381557,0.16452794,-0.07045966,-0.00889443,-0.0345372,-0.02983404,-0.04469354,0.11319485,0.02230102,-0.11465856,0.05061776,0.02710236,-0.02244409,-0.07963599,0.0123475,0.01371504,-0.05775454,-0.00542586,0.08015937,-0.0267082,-0.02581571,-0.00786365,0.01664982,0.04573214,0.01370705,-0.03816313,-0.08631363,-0.01831858,-0.0311635,-0.02903607,-0.02826355,0.03045479,0.01902073,0.05123027,-0.11181829,0.04607667,0.00957636,0.04657691,-0.03256593,-0.02780713,-0.01976446,-0.01330824,0.01542047,-0.06405916,0.14360045,0.02373188,0.00584705,0.01228743,-0.06844915,0.05000006,-0.00775476,-0.06441006,0.00941864,0.03171622,-0.01480947,0.03655979,-0.05095745,0.04518782,-0.00532487,0.01455517,-0.00752199,0.03143706,-0.0064218,0.07320954,0.02240991,-0.00399892,-0.10689814,-0.17532031,-0.03843626,0.04426017,-0.01181058,0.03478161,-0.00207612,0.02147028,0.04787538,0.04365427,0.09460986,0.08543765,0.05472656,-0.05635948,0.02960549,-0.02534238,0.04357238,0.04194602,-0.01839789,0.00542654,0.03538289,-0.03298292,0.0304904,0.01683839,-0.03331745,0.05431569,-0.06313355,0.09234621,-0.03577021,0.02044199,-0.01149064,0.09733943,0.00234353,-0.0604661,-0.1365556,0.04044441,0.05970376,-0.02588982,-0.0311301,-0.05609261,-0.0257443,0.00825383,0.0139643,0.04131514,-0.09435327,0.0123483,-0.05310163,-0.04143398,-0.01413178,-0.02749226,0.00574498,0.00372129,0.0358438,0.00729079,0.10828301,-0.02864083,-0.01502807,0.02338814,-0.02431613,-0.01650782,0.05152652,0.00030378,-0.01224238,-0.03705219,0.00724095,0.01916179,0.00145757,0.00273299,0.02149526,0.03287895,-0.02339199,-0.02816628,0.14776762,0.01152807,0.00118199,0.02855615,0.02475926,-0.02713715,-0.10021027,0.04591112,0.00343029,0.05074788,0.022307,0.02123146,0.06003599,-0.02057449,0.0381527,0.0234287,-0.02503579,0.0116613,-0.004112,-0.01412745,-0.04578223,-0.04769286,-0.03002026,0.07294168,0.01103672,-0.30401149,-0.01905101,-0.02669991,0.00438893,0.06674054,0.02730204,0.05119484,0.00909482,-0.08477081,0.01657734,-0.01606013,0.05652954,0.00518026,-0.07194883,-0.00129206,-0.01880372,0.06408188,-0.01459143,0.02158731,0.04566876,0.01167725,0.06732707,0.21070485,-0.0055305,0.03699869,0.00965272,-0.06547294,0.04945525,0.0396742,0.02267808,-0.04073803,-0.03055651,0.03355812,-0.05069436,0.02426077,0.03596406,-0.0168732,-0.0016797,-0.00342023,0.01368583,-0.04762932,-0.01745983,-0.03027638,0.02278711,0.10614879,-0.02615116,-0.04796744,-0.08072268,0.00125807,0.06338339,-0.05019484,-0.00545989,0.01362156,0.0435942,-0.01212475,0.0827643,0.03147997,-0.0414538,-0.07746354,-0.01175336,-0.02480822,-0.01606428,-0.03995831,0.07527773,0.02333005],"last_embed":{"hash":"tkk6lx","tokens":463}}},"text":null,"length":0,"last_read":{"hash":"tkk6lx","at":1751815145475},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**","lines":[100,148],"size":1912,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"tkk6lx","at":1751815145475}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.1. “公平启动”原则**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06132179,-0.00959769,-0.05776058,-0.02826476,0.03273719,0.01599112,-0.00948887,-0.0099197,0.01960559,-0.03693572,0.05610121,-0.05718385,0.03079473,0.02401762,0.04028847,-0.01387415,0.03182349,-0.05759672,-0.03838449,0.02329947,0.08681689,-0.06502035,0.03267729,-0.01174369,0.08222502,0.04180462,0.00196622,-0.03064957,-0.03410728,-0.17252107,0.00962307,-0.06636894,0.01938825,0.02532618,0.02607677,-0.03638447,-0.00919908,0.07904916,-0.05728537,-0.02310064,0.01562713,-0.0000851,0.00101523,0.0324478,0.03227859,-0.0791939,0.03142523,-0.08395292,0.00052392,-0.05428785,-0.02200176,-0.08713926,-0.02120974,0.02759232,0.01283169,0.02606966,-0.00564794,0.05118666,0.02296223,0.02436438,0.04695895,0.03828581,-0.18843158,0.02128826,0.09535746,0.01250859,-0.01770284,0.0152349,0.08362547,0.06186052,0.02621868,0.01398128,-0.0041634,0.04246487,0.01986953,0.01886161,0.01245364,0.0047492,-0.03790987,-0.05123036,-0.06092511,0.07136134,0.01811237,0.02701501,-0.02360856,0.00124672,0.02141293,-0.04099506,-0.0387246,0.01306119,0.00737534,0.01017721,0.05485146,-0.00769366,-0.02773658,0.00678935,-0.00127618,-0.0060853,-0.05902144,0.11389844,-0.02099052,0.05145115,-0.02271103,-0.0549009,0.04116361,0.02059075,-0.00737883,-0.04739457,-0.00426619,0.02164054,0.0204953,0.01230358,0.09357467,-0.04568429,0.02377005,0.0176999,-0.00943505,0.00400838,-0.02131422,0.02264698,-0.00107666,0.04741913,0.03894289,-0.0301441,-0.03681965,-0.02649052,0.00347788,0.04766176,0.06054124,0.04081775,0.01081865,0.04560172,-0.06633212,-0.01873306,-0.04549591,-0.00220015,-0.02898201,0.02935022,-0.01923242,-0.01125728,-0.0304266,-0.05671823,-0.0283125,-0.11485739,-0.08558697,0.07075279,0.00942719,0.00885753,0.00427298,-0.05108461,0.01367262,0.06736901,-0.01687156,-0.05949982,-0.01875509,-0.00505398,0.03763668,0.17146099,-0.06874686,-0.00530201,-0.03845317,-0.03166589,-0.04326233,0.11190181,0.0256431,-0.11623595,0.05259817,0.03652131,-0.01703016,-0.08404581,0.01269349,0.01779567,-0.05673366,-0.01073808,0.07700355,-0.02590134,-0.02531307,-0.00513757,0.00953251,0.04384359,0.01053697,-0.04690277,-0.08760262,-0.01547041,-0.03253789,-0.03048302,-0.03240039,0.02784558,0.02368315,0.05262925,-0.1105569,0.04788814,0.0041071,0.04101911,-0.03167754,-0.0294392,-0.01926233,-0.0166406,0.01793357,-0.06490419,0.13248406,0.02911529,0.00848094,0.01656433,-0.06777085,0.04873124,-0.00529302,-0.06371483,0.00982543,0.02849259,-0.01923919,0.04103606,-0.055953,0.04810384,-0.00768339,0.01198376,0.00192657,0.0289431,-0.00282841,0.07058719,0.02444046,0.00029338,-0.10470194,-0.18031119,-0.04217856,0.03804764,-0.0158611,0.03537646,-0.00179108,0.02422026,0.04899063,0.05025932,0.08453295,0.09209055,0.05576083,-0.05336108,0.02489744,-0.0221863,0.04278718,0.0352338,-0.01421341,0.00313512,0.03096927,-0.03931931,0.02987797,0.01452944,-0.03098368,0.06081161,-0.06200917,0.0941449,-0.03246951,0.02702636,-0.01368245,0.0916867,0.00410655,-0.057658,-0.14637648,0.04001337,0.05871868,-0.02394614,-0.04089404,-0.06356789,-0.03174307,0.00649009,0.01129882,0.03583704,-0.09848914,0.01054076,-0.04987066,-0.04229403,-0.01317701,-0.02672531,0.00812091,0.0079789,0.03278439,0.00638096,0.11155615,-0.02409605,-0.01313106,0.01928878,-0.02279036,-0.01262841,0.04910393,-0.00093224,-0.01035247,-0.0403538,0.00873697,0.01940425,-0.00336146,0.00088561,0.01682368,0.03546492,-0.02056837,-0.03290834,0.14641826,0.00377156,-0.0060462,0.02426492,0.02275766,-0.03035324,-0.10359809,0.05007477,0.00458245,0.05743583,0.0176973,0.02478823,0.05656116,-0.02260502,0.04636599,0.02141129,-0.02932973,0.01093937,-0.00479429,-0.01576313,-0.04811877,-0.04732494,-0.04351251,0.0741074,0.00763796,-0.30058408,-0.02279557,-0.02521924,0.00671672,0.06818539,0.02437271,0.05137567,0.00605646,-0.08343454,0.01947856,-0.01765479,0.06112196,0.00267702,-0.06675185,0.00159988,-0.01744279,0.06567459,-0.01401769,0.01931071,0.03933048,0.01243451,0.06357255,0.21095483,-0.00429715,0.03567491,0.01191449,-0.05883777,0.04817618,0.0341609,0.02596644,-0.04009817,-0.03364659,0.03072567,-0.0523698,0.02819607,0.03907274,-0.00985298,-0.00399746,0.00034327,0.01773168,-0.04566844,-0.01885047,-0.02947199,0.02002164,0.10778064,-0.01945882,-0.0435183,-0.08513664,0.00077714,0.06458829,-0.05256512,-0.00614149,0.00848498,0.04057378,-0.0156658,0.08663667,0.03391068,-0.04318693,-0.0812544,-0.01137938,-0.02304824,-0.01533177,-0.0407983,0.06940697,0.02331418],"last_embed":{"hash":"lkd203","tokens":341}}},"text":null,"length":0,"last_read":{"hash":"lkd203","at":1751815145510},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.1. “公平启动”原则**","lines":[102,109],"size":324,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"lkd203","at":1751815145510}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.1. “公平启动”原则**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06248596,-0.01184919,-0.05664499,-0.0270365,0.03080128,0.01849698,-0.00546997,-0.01186025,0.02328453,-0.03617833,0.05648831,-0.05643987,0.03121583,0.02195539,0.04079274,-0.01498283,0.03364605,-0.0583768,-0.03818399,0.02529388,0.08857985,-0.06440972,0.03262951,-0.00979311,0.08198164,0.04188652,0.00073152,-0.0324988,-0.03356043,-0.17122629,0.0075456,-0.06520356,0.02037853,0.02326911,0.02622128,-0.0391195,-0.00802995,0.07726943,-0.05724671,-0.02427007,0.0166947,-0.00159948,0.00219878,0.03254535,0.03215628,-0.07934922,0.03288344,-0.08291051,0.00154202,-0.05398565,-0.01945016,-0.08619466,-0.02224287,0.02725906,0.01551304,0.02449368,-0.00659434,0.05120999,0.02468078,0.02480555,0.04772888,0.03643313,-0.18958089,0.02285483,0.09299938,0.01246729,-0.01809712,0.01518202,0.08213796,0.06132839,0.02843032,0.01450404,-0.00601229,0.04373059,0.02233966,0.01762953,0.01111012,0.00537404,-0.03850794,-0.052843,-0.06147138,0.0734769,0.01909488,0.02523003,-0.02609058,0.00348697,0.02212866,-0.04076077,-0.03658649,0.01115412,0.00821111,0.00881026,0.05509819,-0.00868906,-0.02575966,0.00504546,-0.00141054,-0.0075443,-0.05740735,0.11321703,-0.0214067,0.05037651,-0.0215962,-0.05468356,0.04047779,0.01901904,-0.00591809,-0.04711321,-0.00368166,0.02344075,0.01935915,0.01139777,0.09277662,-0.04757201,0.02203741,0.01931059,-0.0067418,0.00178019,-0.01984069,0.02106989,-0.00002024,0.04980959,0.03475855,-0.02900226,-0.03835313,-0.02671037,0.00468312,0.0457587,0.05992395,0.03983982,0.009492,0.04331928,-0.0682896,-0.02101862,-0.04298489,-0.00517745,-0.02965522,0.03153634,-0.01594389,-0.01415958,-0.03117938,-0.05357126,-0.02850838,-0.11382889,-0.08491717,0.07092335,0.00409611,0.00969597,0.00559744,-0.05188505,0.01751298,0.06585166,-0.01888221,-0.06091869,-0.01838941,-0.00536584,0.03761219,0.16982298,-0.07149214,-0.0037984,-0.03803808,-0.03073432,-0.04430266,0.11349308,0.02278231,-0.11404472,0.05245332,0.03526915,-0.01650311,-0.08155558,0.01381078,0.01588626,-0.05704509,-0.01107159,0.07801853,-0.02877606,-0.02704501,-0.00446534,0.00924554,0.04440143,0.0106141,-0.04596695,-0.08770881,-0.01522689,-0.03106546,-0.02944973,-0.0321264,0.02868716,0.02461179,0.05370856,-0.10969663,0.04981323,0.00395003,0.04089436,-0.03325333,-0.02816335,-0.02006072,-0.01771541,0.01659396,-0.06220766,0.13153094,0.02431087,0.00785148,0.01575033,-0.06628072,0.04809243,-0.00374643,-0.06393014,0.01036811,0.02810569,-0.02191744,0.04235577,-0.05940386,0.04709821,-0.00522312,0.01206599,0.00065037,0.03057066,-0.00053585,0.06943654,0.02521343,0.00230935,-0.10615864,-0.18101138,-0.04029205,0.03498263,-0.01379781,0.03389472,0.00093719,0.02508033,0.04872688,0.05105139,0.08236393,0.09297145,0.05727762,-0.05747752,0.02741717,-0.02189015,0.04519487,0.03815863,-0.01393327,0.00640719,0.02980516,-0.03523703,0.03217692,0.01511878,-0.03059747,0.05947272,-0.06098787,0.09302223,-0.03202255,0.02723034,-0.01394471,0.09351589,0.00243072,-0.05870359,-0.14644971,0.03821528,0.05812768,-0.02768639,-0.04283175,-0.06593735,-0.02988963,0.00746952,0.00996634,0.03467363,-0.09679037,0.01110001,-0.05258283,-0.03972678,-0.01258421,-0.02431484,0.00846365,0.0061949,0.03114748,0.00585215,0.11315651,-0.02427877,-0.01317929,0.01835303,-0.02171084,-0.01245138,0.04958698,0.00129983,-0.01173527,-0.04077206,0.00867314,0.02182043,-0.00032083,0.00093083,0.01526106,0.03608472,-0.01832466,-0.03537363,0.14717548,0.00665248,-0.00777713,0.02450619,0.02502965,-0.0291389,-0.10395735,0.04960863,0.0054335,0.05824037,0.01965203,0.02457575,0.05493305,-0.02249285,0.04346526,0.02053934,-0.03061968,0.01101617,-0.00590669,-0.01645632,-0.04867857,-0.04862097,-0.04118963,0.07386722,0.00838915,-0.30147323,-0.02205627,-0.02210999,0.00731988,0.06733567,0.02550725,0.05272766,0.00303069,-0.08697594,0.02028605,-0.01774827,0.0626753,0.00201928,-0.06761315,-0.00088069,-0.01916896,0.06730943,-0.01258009,0.01938439,0.04065485,0.01355226,0.06231092,0.21298824,-0.00279797,0.03306545,0.01119687,-0.05921945,0.04490514,0.03192965,0.0240112,-0.04337863,-0.03265823,0.03295167,-0.0524923,0.0258306,0.0406443,-0.0112327,-0.00343946,0.00035506,0.01660349,-0.04399645,-0.01951119,-0.03203781,0.02083975,0.1073574,-0.01753063,-0.043033,-0.08103541,0.00110239,0.06449156,-0.05293117,-0.00487957,0.00736899,0.0399402,-0.01471698,0.08800203,0.03103321,-0.04396094,-0.08011625,-0.01323578,-0.02464427,-0.01403893,-0.04266389,0.07052971,0.02464957],"last_embed":{"hash":"gko6qt","tokens":338}}},"text":null,"length":0,"last_read":{"hash":"gko6qt","at":1751815145534},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.1. “公平启动”原则**#{1}","lines":[104,109],"size":300,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"gko6qt","at":1751815145534}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.2. 代币供应、发行与效用**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07460245,-0.04139676,-0.06428824,-0.01124758,0.03525186,0.01493273,0.00971389,0.00261909,-0.01040838,-0.03616412,0.07010112,-0.06016703,0.03162886,0.04186616,0.03530708,-0.01898878,0.01264281,-0.04378073,-0.03897258,0.02303353,0.10505149,-0.06010635,0.02053961,-0.01475031,0.07599457,0.04217577,0.00067615,-0.04169625,-0.03045098,-0.1846485,0.00613931,-0.05339283,0.00129391,0.01510912,0.02347994,-0.03823464,-0.00755781,0.05810536,-0.06367176,-0.00073616,0.00866052,0.0165264,0.00825108,0.00760087,0.05007309,-0.08380313,0.02309269,-0.0648544,0.00706191,-0.06280975,-0.04360751,-0.09386119,-0.03768043,0.03954631,0.02888373,0.03343637,-0.00534581,0.0660571,0.03382546,0.0350342,0.0744791,0.05032006,-0.19107223,0.03347559,0.10237556,0.01598341,-0.03095688,0.03238844,0.05933121,0.09937442,0.03858421,0.01833597,0.00061314,0.06361346,0.02929538,0.02102462,0.03978308,-0.00289375,-0.0181503,-0.06136226,-0.06142225,0.07067763,0.01632053,-0.00337135,-0.03736632,-0.01658316,0.0054049,-0.0554111,-0.06438916,-0.00173595,0.01642544,0.00225718,0.04466349,-0.00647729,-0.03791174,-0.01884609,0.00123113,0.01378737,-0.05950595,0.1163448,-0.01430615,0.03866981,-0.00543042,-0.06417122,0.03643897,-0.00722361,-0.02688964,-0.06594542,0.00657716,0.0178592,0.0118807,0.04218353,0.07448148,-0.03818852,0.02128454,0.00338403,0.00943119,-0.0137111,-0.02037791,0.01825749,-0.010795,0.05444518,0.03349265,-0.01521498,-0.03851391,-0.03643417,0.01503363,0.04289939,0.06513958,0.03679349,0.0400648,0.02674609,-0.0668094,-0.00554777,-0.04519538,-0.03676782,-0.00503071,0.02853508,-0.04916126,-0.04495632,-0.03857348,-0.06127042,-0.04710738,-0.11267268,-0.07280777,0.08964637,0.0082652,-0.00556795,-0.00053184,-0.03015582,0.02997362,0.06226013,-0.03730609,-0.08970308,-0.01206902,0.02652055,0.04570656,0.12226988,-0.07418506,-0.02442281,-0.01342637,-0.0221344,-0.04100982,0.12530638,0.02745372,-0.10247712,0.05452127,0.02499064,-0.01655717,-0.07308847,0.02253461,0.0316525,-0.06330822,0.0163189,0.07583704,-0.02464826,-0.03292987,0.00297002,0.02187452,0.04553607,0.00720122,-0.02758475,-0.07971276,-0.03629419,-0.0144632,-0.02275541,-0.02502545,0.02772845,0.03000809,0.05043298,-0.09875765,0.047606,0.00469698,0.0453996,-0.03937562,-0.01056606,-0.02356235,-0.0085685,0.00270667,-0.04259774,0.15531936,0.01644334,-0.00742146,0.01661621,-0.03914548,0.0321234,-0.02475833,-0.06035836,0.00464295,0.04289224,-0.01683616,0.02392553,-0.05337943,0.03106408,-0.01198115,0.02519025,0.01252105,0.04362742,-0.02005632,0.07210477,0.03514727,-0.0198003,-0.08224358,-0.16899177,-0.03006941,0.05582665,-0.0161247,0.02317297,-0.00646129,0.04088903,0.04089603,0.04162063,0.08773892,0.06522527,0.06299737,-0.04820596,0.01169722,-0.02781327,0.05272687,0.04125475,-0.02476448,0.01322226,0.04016891,-0.02163171,0.03021038,0.00300939,-0.06566005,0.04038938,-0.0652938,0.09698032,-0.03552557,0.02358442,-0.01414306,0.07128395,0.00709982,-0.05391184,-0.11804314,0.01813084,0.04652918,-0.02219576,-0.02193399,-0.04315708,-0.03826205,-0.00206524,0.0208579,0.04074055,-0.08521401,0.01958637,-0.06439181,-0.04521848,-0.02520075,-0.00564876,0.00584997,-0.00087661,0.04157952,0.0000888,0.10332137,-0.03765626,-0.01156758,0.03537751,-0.04128168,0.00272582,0.05246451,-0.01124803,-0.00087345,-0.04331053,0.00071277,0.0227914,-0.00920458,-0.00574349,0.01143935,0.01204739,-0.04750393,-0.01685008,0.1287839,0.04430323,0.00677931,0.02362531,0.02660943,-0.03017327,-0.09306487,0.02563261,-0.00134649,0.03063856,0.00544588,0.03160682,0.04671577,-0.0029632,0.05528173,0.01252559,-0.00985521,0.01348555,0.01196038,-0.00994789,-0.03740628,-0.05621656,-0.03993251,0.06135616,0.01696147,-0.29655829,-0.00791859,-0.01453756,-0.0010515,0.07053012,0.03457014,0.06001301,0.0200239,-0.06673908,-0.00409554,0.00283076,0.06065331,-0.00155306,-0.07014816,-0.00746165,-0.01386357,0.0539027,-0.00846994,0.01542642,0.04585242,-0.01048154,0.07769927,0.21006729,-0.03384001,0.05973712,0.01935502,-0.07855201,0.0420422,0.06072595,0.01575494,-0.04025097,-0.01578679,0.03748718,-0.05198368,0.01988201,0.02633648,-0.04366941,-0.00259363,-0.01667299,0.01426197,-0.04763531,-0.01361785,-0.02807134,0.03349729,0.12376077,-0.01248067,-0.06419377,-0.07558169,0.0169224,0.07641875,-0.03518084,0.00648984,0.02268248,0.04904554,-0.0061613,0.06012316,0.03272792,-0.041527,-0.08627525,-0.02225734,-0.0304131,-0.01611076,-0.06105667,0.05647691,0.02967497],"last_embed":{"hash":"167tpf0","tokens":269}}},"text":null,"length":0,"last_read":{"hash":"167tpf0","at":1751815145558},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.2. 代币供应、发行与效用**","lines":[110,116],"size":268,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"167tpf0","at":1751815145558}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.3. 市场分析与普遍存在的代币混淆**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06845108,-0.02650465,-0.07866789,-0.01333996,0.0371903,0.02851046,-0.00849325,0.00386359,-0.00119232,-0.03615786,0.05040109,-0.04864521,0.03525428,0.03177212,0.01903223,-0.03073458,0.04019298,-0.05087744,-0.04596996,0.03184304,0.13303487,-0.06275086,0.02385337,-0.01417703,0.0788331,0.05816862,0.00388663,-0.036966,-0.03743355,-0.18338019,0.00322656,-0.02732227,0.02418365,0.02414602,0.01351859,-0.02524029,-0.01440569,0.05775773,-0.05250449,-0.0036861,-0.000815,-0.00275873,-0.00598845,0.01812441,0.05870195,-0.09929287,0.01820558,-0.07431015,0.01570932,-0.05679893,-0.03974798,-0.08544617,-0.02795508,0.03784716,0.00046463,0.02277285,-0.00775887,0.06361996,0.0368145,0.04435007,0.07711928,0.0644507,-0.1943716,0.03463718,0.09079535,0.02760996,-0.0127063,0.00036567,0.06623472,0.07258529,0.03744914,0.00928498,-0.00901374,0.05971633,0.06278671,0.00414465,0.01730652,0.01639655,-0.02168893,-0.04737772,-0.03953974,0.07816389,0.00301779,0.01103473,-0.02292729,-0.00982922,0.00091716,-0.04529503,-0.04798176,0.00839765,0.00322967,0.01897524,0.03322413,-0.02305059,-0.0308211,-0.02391464,-0.01315324,0.00887606,-0.06053576,0.11672117,-0.03042846,0.04850368,-0.02225528,-0.04855179,0.03973731,-0.00166829,-0.02091242,-0.06627572,0.03874256,0.04008008,0.02040381,0.02212223,0.06672865,-0.05650014,0.02837855,0.01052351,-0.00588052,-0.01915054,-0.03534968,0.00648524,-0.02665477,0.03724976,0.038139,-0.02405877,-0.04001975,-0.02825534,0.00923203,0.03810251,0.06116128,0.05298873,0.03143482,0.04493755,-0.06428387,-0.03382427,-0.04498244,-0.0263443,-0.02329656,0.0348505,-0.03751572,-0.03598451,-0.03554502,-0.07914589,-0.01819882,-0.09110191,-0.0857776,0.02895687,0.00432247,-0.01030379,0.00515867,-0.0362875,0.0132366,0.07049371,-0.03363807,-0.06712183,-0.01951185,-0.00282198,0.0537155,0.12870675,-0.06191814,-0.0154043,-0.01296379,-0.03164848,-0.03140056,0.13581356,0.01830154,-0.11140447,0.06160867,0.03161451,-0.0162821,-0.07475168,0.02701894,-0.01566543,-0.0503582,-0.02673313,0.05578363,-0.01558416,-0.03506099,0.0031712,0.01434561,0.05134737,-0.0085506,-0.03341928,-0.07970554,-0.01283617,0.00345842,-0.0281768,-0.02519793,0.0330864,0.02531981,0.03048681,-0.10629237,0.04920331,-0.00499579,0.03124234,-0.01618087,-0.01030199,-0.03907568,-0.01865917,0.00387787,-0.03979538,0.12034285,0.02902246,0.02391367,0.01411241,-0.05748003,0.03773598,-0.02933123,-0.06602986,-0.00086889,0.03830278,-0.02806596,0.04397497,-0.04972459,0.03533457,-0.01086121,0.01753118,0.01400466,0.03361534,0.0013719,0.05199843,0.01864003,-0.02698888,-0.08864003,-0.17474464,-0.03707029,0.05829054,-0.03538182,0.02828829,-0.01168067,0.05336256,0.041032,0.03379858,0.09624995,0.07700074,0.04741677,-0.05465837,0.01345023,-0.02781581,0.0591961,0.06278139,-0.02061373,0.0003903,0.03877373,-0.02471226,0.02172551,0.02321102,-0.06247151,0.02277432,-0.06634623,0.10951257,-0.05256189,0.01913191,0.01870419,0.07087465,-0.00383617,-0.04351959,-0.12998357,0.02622602,0.05298066,-0.0139247,-0.01281357,-0.06184284,-0.01741084,0.00411612,0.00985996,0.04212816,-0.09913381,0.01832764,-0.0696096,-0.04406772,-0.03462033,0.002978,0.02797536,0.00701616,0.01922073,0.01603604,0.09367505,-0.02014654,-0.02146268,0.0187754,-0.06803607,-0.01109065,0.05272551,-0.0110438,-0.00181808,-0.0306556,-0.01370391,0.0268752,-0.01204953,-0.00957801,0.02501508,0.03088471,-0.03743235,-0.02558392,0.15611491,0.02817874,0.01543124,0.03553601,0.03280321,-0.01760379,-0.09121391,0.02836648,0.03058619,0.03770657,0.01603466,0.03480014,0.06101753,-0.00107659,0.05572275,0.02284917,-0.0248674,0.01049102,-0.0132161,-0.01613832,-0.05543234,-0.06053567,-0.02152136,0.07802685,0.01730273,-0.28838068,-0.02359209,-0.01352725,0.00127231,0.06463233,0.03932961,0.07583293,0.03061551,-0.07420492,0.00499189,-0.00555824,0.07598605,-0.01510233,-0.05321575,0.00961958,-0.01422305,0.05154218,-0.02075814,0.01258148,0.01693249,0.0005155,0.06882119,0.21076526,-0.03243521,0.02121577,0.01094751,-0.06923079,0.07213572,0.02957552,0.00736727,-0.03933197,-0.00003774,0.0249638,-0.05462593,0.02916033,0.05473198,-0.01986753,-0.02739985,0.00777947,0.0288951,-0.01378835,-0.02603793,-0.01352983,0.04753773,0.11921198,-0.03177027,-0.06088595,-0.06486787,0.03663209,0.0558034,-0.04863318,-0.00665858,0.01022908,0.03291019,-0.03342839,0.06812251,0.02661479,-0.0221827,-0.08002129,-0.0395136,-0.03311503,-0.00698594,-0.03145429,0.07416876,0.01923111],"last_embed":{"hash":"g1efas","tokens":453}}},"text":null,"length":0,"last_read":{"hash":"g1efas","at":1751815145632},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.3. 市场分析与普遍存在的代币混淆**","lines":[117,124],"size":493,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"g1efas","at":1751815145632}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.3. 市场分析与普遍存在的代币混淆**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06842839,-0.02529967,-0.07692394,-0.01548736,0.03496763,0.02771536,-0.00653753,0.00520159,-0.00344496,-0.03793386,0.05111161,-0.04717987,0.03598778,0.03052996,0.01706478,-0.0333527,0.04176839,-0.05304064,-0.04512936,0.03370857,0.13264237,-0.06165353,0.02223098,-0.01373127,0.07859597,0.05902918,0.00727132,-0.03419699,-0.03880246,-0.18363735,0.00208808,-0.03019515,0.02537608,0.02139606,0.01456686,-0.02453998,-0.01266152,0.06106544,-0.05201866,-0.00548884,0.00133915,-0.00183762,-0.00397979,0.01869354,0.05991786,-0.10140914,0.01942864,-0.07475984,0.01921691,-0.057035,-0.03874879,-0.08702102,-0.02821975,0.039275,0.00148924,0.02411286,-0.01004598,0.06205351,0.03613695,0.04462889,0.07882152,0.06431683,-0.19535178,0.03431164,0.08831617,0.02946531,-0.01246325,-0.00050181,0.06428454,0.07374831,0.04125357,0.00665744,-0.00821206,0.05862603,0.06366009,0.00655728,0.01589715,0.01564572,-0.02110211,-0.04779023,-0.04090734,0.07788214,0.001191,0.01074803,-0.02491293,-0.00942169,0.0009058,-0.04684061,-0.05035412,0.0089411,0.00398122,0.02016143,0.03334472,-0.02247954,-0.03049717,-0.02558756,-0.01377286,0.01129335,-0.06286396,0.1165452,-0.02958822,0.04945141,-0.02311628,-0.04571646,0.03897225,-0.00179439,-0.01895055,-0.06619625,0.03640653,0.0409373,0.0236007,0.01955939,0.06791975,-0.05550446,0.02613264,0.01182336,-0.00458227,-0.0193343,-0.03464168,0.00719419,-0.02543161,0.03734463,0.03607656,-0.02406849,-0.04089573,-0.02772776,0.01057344,0.04010537,0.05958148,0.05260339,0.0298885,0.04646924,-0.06575785,-0.03671798,-0.04509438,-0.02697461,-0.02555088,0.03545246,-0.03915801,-0.0363798,-0.03349103,-0.08158144,-0.01763494,-0.09254672,-0.08636777,0.02983614,0.00103431,-0.00918547,0.00430418,-0.03358458,0.01161405,0.06810684,-0.03840628,-0.0663593,-0.02128576,-0.00568474,0.05319798,0.12631766,-0.06274546,-0.01442407,-0.0148645,-0.03246713,-0.02824582,0.13771836,0.01891831,-0.11004662,0.06015804,0.03101512,-0.01757167,-0.07523897,0.02863941,-0.01696579,-0.04811252,-0.02843932,0.05663934,-0.01384016,-0.03605295,0.00400672,0.01402073,0.05173993,-0.00821733,-0.03378369,-0.07774627,-0.01759812,0.00606266,-0.02801337,-0.02383464,0.03464907,0.02492432,0.03249985,-0.10364304,0.04959525,-0.00565776,0.032252,-0.01983028,-0.01373768,-0.04134673,-0.01875984,0.00401713,-0.03982973,0.1178168,0.02631579,0.02567937,0.01561045,-0.05933985,0.03330317,-0.0299633,-0.0669504,0.0025632,0.03717018,-0.02787957,0.04414975,-0.0501721,0.03778763,-0.00912757,0.01999724,0.01520573,0.02973402,0.00167325,0.05103651,0.01769621,-0.02657837,-0.08735514,-0.17275813,-0.03493507,0.05927417,-0.03268204,0.02820642,-0.01364064,0.05290866,0.04079173,0.03171561,0.09470144,0.07804731,0.0474609,-0.0550148,0.01600396,-0.02701553,0.06181847,0.06244147,-0.02157374,0.00363689,0.03828918,-0.02432069,0.01960218,0.02234388,-0.06317592,0.02530173,-0.06869148,0.11063921,-0.05186597,0.02179535,0.01927501,0.07025495,-0.00321054,-0.04137946,-0.12794372,0.02487374,0.05400676,-0.01258061,-0.01360685,-0.06048319,-0.0170775,0.00194883,0.0093325,0.04088927,-0.0975566,0.01967683,-0.06980661,-0.04533564,-0.03220956,0.003096,0.02841607,0.00679878,0.01968777,0.01614892,0.09338155,-0.02119113,-0.02330956,0.01899265,-0.07170366,-0.01107262,0.05382307,-0.01092036,-0.00241407,-0.03267888,-0.0162969,0.02958415,-0.01088403,-0.01310255,0.02440059,0.03272079,-0.03715115,-0.02231463,0.15489645,0.02736089,0.01208956,0.03204861,0.03429218,-0.01712143,-0.09140459,0.03014219,0.0283685,0.03868052,0.01402225,0.03181079,0.05930516,-0.00440643,0.05636402,0.02089277,-0.02344125,0.011777,-0.01303183,-0.01520176,-0.0558484,-0.05876489,-0.02012065,0.07758443,0.02106304,-0.28688017,-0.02410992,-0.01479004,0.00169677,0.06300906,0.04224624,0.0739888,0.03419784,-0.07205112,0.00784319,-0.00356757,0.07739032,-0.01250484,-0.05174038,0.00859949,-0.0163825,0.05001599,-0.02083079,0.01420431,0.01549175,0.00435272,0.06899648,0.20945717,-0.03410186,0.02166799,0.01284743,-0.07231617,0.07303087,0.02809341,0.00814771,-0.03853718,0.00149318,0.02212695,-0.05545311,0.02953518,0.05804449,-0.01807013,-0.02717436,0.00490382,0.02914913,-0.01290079,-0.0252655,-0.0112102,0.0471991,0.12018281,-0.0318124,-0.06070023,-0.06369318,0.03701297,0.05671193,-0.05185429,-0.00479167,0.01129082,0.03199269,-0.03366386,0.06820226,0.02631893,-0.02363783,-0.08186801,-0.04229398,-0.03498571,-0.00720718,-0.03231889,0.07265098,0.02001749],"last_embed":{"hash":"1o1qkpl","tokens":450}}},"text":null,"length":0,"last_read":{"hash":"1o1qkpl","at":1751815145664},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.3. 市场分析与普遍存在的代币混淆**#{1}","lines":[119,124],"size":463,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1o1qkpl","at":1751815145664}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.4. 挖矿经济学**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06853133,0.00021803,-0.06325749,-0.00686391,0.01780931,0.02131324,-0.01831074,0.01245522,0.02256626,-0.02259116,0.08302876,-0.08352758,0.02522255,0.02665554,0.0243081,-0.0220425,0.00922501,-0.04184866,-0.03643245,0.02604146,0.07018386,-0.08488674,0.03366182,-0.0400081,0.05671969,0.0414755,0.00184911,-0.03070003,-0.0318508,-0.18388206,0.01543596,-0.04354903,-0.00224452,0.01723026,0.00998261,-0.04663158,-0.00017442,0.07553131,-0.07013448,-0.00098093,0.00470768,0.02882503,-0.00916115,-0.00839131,0.06632148,-0.08568323,0.01885287,-0.07014185,-0.00210764,-0.07110165,-0.02860091,-0.08327519,-0.00863091,0.03813153,-0.00421317,0.02904218,0.00965511,0.04078582,0.02677206,0.00877862,0.05484488,0.04734469,-0.1863817,0.02115746,0.11977684,0.00353017,-0.02259702,0.00510371,0.05604324,0.07662329,0.02110703,0.01500375,0.0229309,0.05389639,0.04229519,0.00502245,0.03129535,0.01220511,-0.01329825,-0.05676239,-0.03373962,0.07357331,0.00550702,-0.01177783,-0.02385464,-0.00720059,0.003176,-0.0482319,-0.05638006,-0.00200413,-0.00692268,0.01230342,0.02111851,0.0046709,-0.01843494,0.01142798,-0.00607316,0.01137914,-0.06642963,0.11177705,-0.00493469,0.03939913,-0.01175201,-0.04343028,0.02489285,-0.01243184,-0.01908491,-0.05373146,0.0130907,0.0170467,0.01474056,0.02184198,0.0730566,-0.02093855,0.05778539,0.00165146,-0.01875132,-0.04078783,-0.01024146,0.03108338,-0.01620954,0.05414665,0.00814203,-0.03070742,-0.03130102,-0.03875266,-0.01056923,0.05331257,0.04090452,0.02955477,0.0248043,0.03481627,-0.06348222,0.01228564,-0.02483511,-0.020352,-0.00965719,0.02077856,-0.03307651,-0.02794027,-0.03017278,-0.05404742,-0.00832781,-0.12340304,-0.05192284,0.0859507,-0.00044658,0.01892073,0.01700223,-0.04676154,0.02211011,0.07195244,-0.02705574,-0.05940668,-0.01951353,0.01640009,0.03572937,0.11164832,-0.07442727,-0.01757483,-0.0351719,-0.02315203,-0.04552376,0.13908496,0.04542451,-0.09771306,0.01759505,0.02341051,-0.01723915,-0.06986595,0.01581855,0.02573187,-0.06829199,-0.00508379,0.07735632,-0.03219094,-0.04657709,0.00483082,0.02161158,0.03918512,0.0185881,-0.01617362,-0.08168907,-0.05715355,-0.0321172,-0.0239166,-0.02002161,0.01742223,0.02000397,0.05096381,-0.11922688,0.03693148,-0.0039961,0.02862035,-0.02639438,0.00228138,-0.02413694,-0.02505418,0.01619606,-0.04156252,0.13342761,0.03865155,-0.00758736,0.02355422,-0.05710804,0.02612156,-0.04045246,-0.05832646,-0.01029583,0.03690997,-0.01989039,0.0031048,-0.04902915,0.05058071,-0.02004499,0.01723136,0.00175152,0.03510159,-0.02282342,0.05621598,0.03912493,-0.0055666,-0.1009549,-0.17828849,-0.04412252,0.05279298,-0.02441414,0.03109078,0.0094309,0.04425724,0.03059077,0.05740197,0.07838393,0.07936631,0.06782948,-0.05034452,-0.01349223,-0.02954149,0.05874491,0.05881819,-0.01174485,-0.01319329,0.01350393,-0.04130014,0.04738796,0.02496729,-0.05396648,0.04245439,-0.06427163,0.09334257,-0.04751635,0.02150431,-0.00807491,0.07248994,0.01165651,-0.05346059,-0.13158751,0.03611695,0.07165875,0.0058437,-0.01212378,-0.05797486,-0.02905691,-0.00487322,0.01613812,0.03873623,-0.09052344,0.01573422,-0.0585618,-0.04239918,-0.016524,-0.0067576,0.04288153,0.0069529,0.02479063,0.01252279,0.07735042,-0.00463018,-0.01230287,0.02487946,-0.01669344,-0.01834469,0.05287943,0.00060948,-0.01316025,-0.04176144,0.01500504,0.02150676,-0.0166881,-0.02564422,0.03921283,0.01725475,-0.01288209,-0.01101227,0.12791637,0.01819597,0.00460492,0.05524891,0.02499188,-0.01503854,-0.10118459,0.04639077,0.00139413,0.02015327,0.00031935,0.02879127,0.05975161,-0.00724909,0.06853034,-0.00141735,-0.02303458,-0.00699746,-0.00080792,-0.03275581,-0.04148922,-0.05019724,-0.02562307,0.08274044,0.02060639,-0.3134127,-0.02038269,-0.02999924,0.01574482,0.06803797,0.02239026,0.07402582,0.02055105,-0.07898403,0.02205366,-0.01117667,0.06672651,-0.00109578,-0.04469125,-0.01278098,-0.01371676,0.08441328,-0.02195683,0.02249291,0.05687353,-0.01136014,0.07157452,0.22489426,-0.03969979,0.06136023,0.00615056,-0.05698122,0.05750217,0.05245506,0.03543322,-0.04676293,-0.01843978,0.02851895,-0.05095153,0.03284396,0.0517234,-0.00967271,0.01407263,0.00135763,0.03390254,-0.02445984,-0.02136389,-0.03492586,0.02264813,0.11196551,-0.01981158,-0.06177578,-0.09774607,0.011741,0.06889455,-0.0307818,-0.0327032,0.03173281,0.03549696,-0.01230398,0.06819101,0.03007783,-0.03764139,-0.07482395,-0.00191536,-0.02457071,-0.03006559,-0.04651931,0.08013138,0.01929762],"last_embed":{"hash":"eslj4o","tokens":464}}},"text":null,"length":0,"last_read":{"hash":"eslj4o","at":1751815145693},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**3.4. 挖矿经济学**","lines":[125,132],"size":470,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"eslj4o","at":1751815145693}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**表 2: $NOCK 代币经济学与分配模型**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04945128,-0.02706837,-0.06024696,-0.02383411,0.03247237,0.00775577,-0.00322393,-0.00036853,-0.00465664,-0.02795606,0.07179218,-0.07042128,0.03801575,0.02955464,0.03170548,-0.01187458,0.00676445,-0.03158915,-0.02789834,0.01328343,0.10834907,-0.06891634,0.03006254,-0.01809811,0.07462116,0.03888529,-0.0015529,-0.0328356,-0.02776507,-0.18367904,0.0135825,-0.03737259,0.02506445,0.01894528,0.02608147,-0.039467,-0.0276806,0.05450841,-0.05263964,-0.000164,0.01459741,0.00997563,-0.01153176,0.01589495,0.04493845,-0.07841559,0.03223374,-0.07213431,0.0028883,-0.05079337,-0.03711498,-0.08622101,-0.01695476,0.05051804,0.02427981,0.0433189,-0.00373233,0.04789359,0.02808111,0.04426654,0.0798599,0.04464343,-0.2037511,0.01167476,0.09240293,0.02275671,-0.01405152,0.0311049,0.05655728,0.07590224,0.02051654,0.02567677,0.0078997,0.0581058,0.02923417,0.03238152,0.02437657,0.00904798,-0.02148799,-0.08123602,-0.05679575,0.08110939,0.00399211,-0.00438609,-0.02330575,-0.02252407,0.00717925,-0.05129168,-0.05683713,0.00640632,0.00583961,0.00645179,0.03213597,-0.00315101,-0.04199771,-0.00435499,-0.0067702,0.01038734,-0.05094744,0.10933509,-0.0204278,0.06570032,0.0135864,-0.07658414,0.03189255,0.00471413,-0.03825103,-0.05438103,0.01504334,0.02824422,-0.001257,0.023592,0.08371958,-0.05019584,0.01973236,-0.00019047,0.00680041,-0.01228051,0.00191312,0.00895841,-0.00422833,0.03549244,0.03520307,-0.02786828,-0.04012115,-0.03448876,0.00299619,0.05560764,0.07192792,0.02663144,0.03322916,0.04203227,-0.07585263,-0.00963533,-0.05343202,-0.02618474,-0.02822893,0.0304115,-0.03205264,-0.02645891,-0.03628807,-0.05105385,-0.02839921,-0.11620853,-0.06804608,0.07557627,0.00570188,-0.00498969,0.01277153,-0.03851267,0.0118762,0.06082792,-0.01994313,-0.07509825,-0.00373186,0.00741545,0.02842203,0.1259075,-0.06336568,-0.02846304,-0.03579614,-0.01424779,-0.04763995,0.14336632,0.04231679,-0.09257492,0.04340141,0.01909754,-0.0167173,-0.08757266,0.0194571,0.02708866,-0.05994239,0.01204982,0.09000643,-0.02481841,-0.02635039,-0.01410274,0.01605603,0.0431445,0.01581933,-0.02810182,-0.07811569,-0.02550481,-0.01568168,-0.02163086,-0.03948827,0.03485317,0.01108541,0.0548708,-0.09489729,0.043457,0.00229924,0.02696145,-0.03544706,-0.01220541,-0.02266163,-0.01414997,0.02095947,-0.05262611,0.15374359,0.02540616,-0.00224512,-0.00479816,-0.04770439,0.03160956,-0.01358031,-0.04519669,-0.0109177,0.03194438,0.00426759,0.03256201,-0.03868821,0.04714926,-0.02191908,0.01183709,0.00645568,0.04241053,-0.01295961,0.07057515,0.00888134,-0.01334558,-0.09327692,-0.18860026,-0.03502291,0.05560901,-0.02312689,0.01982342,0.00929596,0.04980548,0.01817512,0.05304372,0.08358375,0.07581534,0.04848275,-0.04391485,0.02541957,-0.01830618,0.05663911,0.05045417,-0.01883306,-0.0104637,0.03543811,-0.03258418,0.02588546,0.01729563,-0.05865284,0.05444192,-0.06514561,0.09492093,-0.03533388,0.0034292,0.00466197,0.08521421,0.01324943,-0.05596703,-0.11435661,0.02822692,0.06333476,-0.01838513,-0.00738417,-0.06265899,-0.03647446,0.0018618,0.01051253,0.04692775,-0.076432,0.00460725,-0.05633117,-0.05384389,-0.01100336,-0.00653915,0.01832517,-0.01148794,0.03355329,0.0334461,0.1033739,-0.02153878,-0.00926907,0.02859573,-0.01965836,-0.02363559,0.03548463,-0.00148959,-0.01493357,-0.04090149,-0.00253224,0.0206489,-0.019347,0.00078169,0.02143769,0.01580937,-0.02769586,-0.03274442,0.11518048,0.03502603,0.00970194,0.03020201,0.02909318,-0.03157315,-0.07655014,0.04620822,0.01127378,0.03257471,0.00647508,0.02220616,0.06462192,-0.00779474,0.063403,0.02946267,-0.02031493,0.01484613,0.01021513,-0.03658518,-0.03515248,-0.04619234,-0.02552655,0.06985632,0.01390461,-0.31397459,-0.01652453,-0.01585646,-0.00191878,0.0674898,0.04361242,0.04166486,0.00098452,-0.08161344,0.00475717,0.00213746,0.05750944,0.00242061,-0.0816471,-0.01026848,-0.02521574,0.07673255,-0.02653521,0.03660862,0.03847337,-0.01654157,0.07946488,0.20936899,-0.00887172,0.03529442,-0.01023381,-0.0799945,0.03749781,0.05444934,0.02081043,-0.02109355,-0.02844837,0.04359235,-0.04833675,0.01839958,0.04536287,-0.03343118,-0.00605865,-0.01550565,0.0225433,-0.05150861,-0.01377738,-0.04609706,0.04230063,0.11901238,-0.01591811,-0.05817951,-0.09111588,0.00376602,0.05810169,-0.03241349,-0.01471574,0.01695139,0.04021743,-0.0255326,0.0820762,0.03480735,-0.0359841,-0.06946126,-0.02477044,-0.02060672,-0.02924656,-0.06113391,0.05302463,0.0213948],"last_embed":{"hash":"1t0d75l","tokens":283}}},"text":null,"length":0,"last_read":{"hash":"1t0d75l","at":1751815145710},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**表 2: $NOCK 代币经济学与分配模型**","lines":[133,148],"size":312,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1t0d75l","at":1751815145710}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**表 2: $NOCK 代币经济学与分配模型**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04721552,-0.023147,-0.06148074,-0.02372374,0.0332411,0.0063915,-0.00448785,-0.00161898,-0.00897428,-0.03189455,0.0727827,-0.07019542,0.03673771,0.0272498,0.02890942,-0.00539811,0.00671172,-0.03149819,-0.02808265,0.0156545,0.10744792,-0.06662859,0.02915406,-0.01818001,0.07519006,0.03846014,-0.00349196,-0.02988192,-0.02587548,-0.18469431,0.01706758,-0.03696613,0.02423829,0.01748859,0.0239241,-0.04121245,-0.02651671,0.05734599,-0.05555451,-0.00015932,0.01648585,0.01246772,-0.01004542,0.01656553,0.04531372,-0.08212544,0.03433092,-0.07336748,0.00604246,-0.05066035,-0.03487772,-0.08719683,-0.01508985,0.04747109,0.02881986,0.04224504,-0.00572053,0.04729199,0.02950611,0.04667022,0.08074496,0.04359213,-0.20567948,0.00935284,0.09443668,0.02223229,-0.01320776,0.02695182,0.05378116,0.07498872,0.02031426,0.02695804,0.00888694,0.05527692,0.02758119,0.03231095,0.02507051,0.00962355,-0.02117346,-0.08290258,-0.05804593,0.0826076,0.00126462,-0.00567644,-0.02288495,-0.02033245,0.0071412,-0.04965492,-0.05412579,0.00565074,0.00759947,0.00524422,0.03409951,-0.00327034,-0.03852643,-0.00336415,-0.00634504,0.00946293,-0.04864701,0.1067827,-0.01830913,0.06569885,0.01126274,-0.07545117,0.03104815,0.00430108,-0.04116567,-0.05498797,0.01971659,0.02722648,0.00396576,0.02219692,0.08472325,-0.05071125,0.02142154,-0.00233016,0.00796682,-0.01194184,0.00288847,0.0118258,-0.00755488,0.03593723,0.03456099,-0.03205478,-0.03993163,-0.03202339,0.00725551,0.05574583,0.07236738,0.02369523,0.03244465,0.04236756,-0.07557858,-0.0074124,-0.05477239,-0.02343194,-0.029724,0.03128327,-0.03128553,-0.02650434,-0.03501198,-0.04824523,-0.03114839,-0.11818436,-0.06369713,0.07319818,0.00456747,-0.00475489,0.0154981,-0.04345286,0.00703621,0.06111856,-0.01692417,-0.07350849,0.00000409,0.00967746,0.02582741,0.12567134,-0.06376223,-0.02624526,-0.03739837,-0.01325751,-0.04539595,0.14231229,0.04217884,-0.09070013,0.04115418,0.0167405,-0.0149651,-0.08826812,0.0209379,0.03076633,-0.05683742,0.00866273,0.08705726,-0.02383616,-0.0314054,-0.01339077,0.01477787,0.04201994,0.01699977,-0.02603321,-0.07286617,-0.02563109,-0.01806492,-0.02468793,-0.03697896,0.03572137,0.01116553,0.05199323,-0.09881074,0.04706364,0.00238537,0.02714242,-0.03511437,-0.01475161,-0.02133457,-0.01209129,0.02124798,-0.05491701,0.15270476,0.02571813,-0.00367614,-0.0030647,-0.05076021,0.03142855,-0.01647622,-0.04739333,-0.01186406,0.0304731,0.00574639,0.02833727,-0.04114909,0.04944078,-0.02069063,0.01458705,0.00823128,0.04179117,-0.01306505,0.07135241,0.01088005,-0.0139965,-0.09808336,-0.18901142,-0.03510395,0.05323123,-0.0217599,0.01588837,0.01105731,0.04987438,0.01944833,0.0544236,0.08322303,0.07673801,0.04577724,-0.04239396,0.02390586,-0.02017951,0.05705994,0.05112502,-0.01737205,-0.01274425,0.0345307,-0.03062955,0.02404209,0.02170733,-0.05912282,0.05298928,-0.06641392,0.0943824,-0.03545982,0.00153738,0.00129444,0.08837412,0.01457201,-0.05333551,-0.11465574,0.02393654,0.06131244,-0.01774909,-0.00902469,-0.06246334,-0.03622631,0.00088745,0.01144491,0.04632077,-0.07885379,0.00688701,-0.0544291,-0.05743846,-0.01504327,-0.00621195,0.01394512,-0.01031115,0.03791374,0.03535946,0.09871006,-0.02124539,-0.01311891,0.0238154,-0.01893014,-0.0284732,0.03328709,-0.00347837,-0.0211565,-0.04260257,-0.00802082,0.02061921,-0.01995235,0.00204271,0.02014317,0.01787907,-0.02692818,-0.02979363,0.11656056,0.03338999,0.0069,0.0283451,0.02841266,-0.02987657,-0.07566198,0.0491486,0.01582113,0.03408641,0.00419258,0.02502866,0.06902523,-0.00567462,0.06485511,0.03029948,-0.02044893,0.01555588,0.00983566,-0.03908266,-0.03708127,-0.04547749,-0.02231108,0.07090572,0.01221783,-0.31131047,-0.01414367,-0.01751336,-0.00148408,0.0663618,0.03952709,0.04532823,0.00293081,-0.08046676,0.00593988,0.00448994,0.05861641,0.00280283,-0.08035502,-0.01540794,-0.02501431,0.07907807,-0.0256694,0.03829481,0.0352409,-0.01259809,0.08478598,0.20921262,-0.00618449,0.03779817,-0.0062656,-0.08218636,0.03696727,0.05159679,0.0197774,-0.01859928,-0.03130648,0.04209239,-0.05164042,0.01853172,0.04756824,-0.03440057,-0.00181799,-0.01348931,0.02364882,-0.05244567,-0.01888679,-0.04630724,0.04240314,0.11850867,-0.01903242,-0.05671006,-0.09062634,0.00252794,0.06003822,-0.0341717,-0.01046155,0.01703793,0.0438393,-0.02380397,0.08298601,0.03420606,-0.03621303,-0.06672186,-0.02834521,-0.02135919,-0.02955106,-0.05712152,0.04988611,0.01824254],"last_embed":{"hash":"ryy7oi","tokens":280}}},"text":null,"length":0,"last_read":{"hash":"ryy7oi","at":1751815145727},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第三部分：$NOCK 代币经济学 \\- 一场激进的公平分配实验**#**表 2: $NOCK 代币经济学与分配模型**#{1}","lines":[135,148],"size":280,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"ryy7oi","at":1751815145727}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0827116,-0.0519418,-0.03707242,-0.00551944,0.02354524,-0.01162454,-0.01347595,0.0223786,-0.03359152,-0.01803131,0.07034618,-0.04614827,0.03463665,0.01693395,0.02946238,-0.00732401,0.01979424,-0.05070063,-0.02933117,0.01515302,0.08392861,-0.08645133,0.05299154,-0.06987558,0.09384867,0.02263487,0.0445292,-0.0474299,-0.03503896,-0.1873647,0.00417129,-0.04054799,0.01125206,0.02622111,0.05411395,-0.04418172,-0.02575171,0.08847081,-0.05730817,-0.00143351,0.01614832,-0.01099642,0.00526431,0.04298851,0.03518495,-0.08211153,0.00657266,-0.05769207,-0.02770185,-0.04546047,0.02071228,-0.0827377,-0.04182289,0.03484056,0.00980661,0.03643502,0.00437553,0.04296085,0.0058192,0.00965393,0.07278287,0.03559311,-0.21890014,0.02704042,0.09200596,0.03433421,-0.08026645,0.00706328,0.05738201,0.07732508,0.02892827,0.03637863,0.01959695,0.04269794,0.00050343,0.01711626,0.06009986,-0.04078767,-0.0208934,-0.05477047,-0.03533217,0.06796521,-0.01725485,-0.0134706,-0.01928113,-0.02434163,0.01367873,-0.07341557,-0.0241745,-0.0093774,0.00928832,-0.01206037,0.07105862,0.00150038,0.00021948,-0.0133764,0.04254041,0.00837032,-0.11466178,0.09514475,-0.0068851,0.03316281,-0.02527686,-0.07311971,0.05809335,-0.03446039,-0.0093324,-0.06034348,0.02615084,0.01667609,-0.00677712,0.03451569,0.05957931,-0.03135001,0.01910302,0.00513685,0.03247016,0.00411582,-0.02008079,0.01396289,0.00115415,0.05021869,0.03357348,-0.00780888,-0.05624912,-0.04737866,0.01366761,0.0589846,0.06384765,0.02284656,0.04010038,0.00910547,-0.07878761,0.01706229,-0.02293887,-0.01747304,0.00270225,0.00321107,-0.0867582,-0.04899136,-0.07514419,-0.05601383,-0.03136722,-0.1155243,-0.05987063,0.09172714,0.01906421,-0.02374143,0.00172703,-0.01985316,0.03402346,0.03728127,-0.02196288,-0.07166366,-0.00406787,0.03502517,0.05228481,0.11014159,-0.0531825,0.01489794,0.00200219,-0.02089815,-0.03282218,0.11776874,0.05817264,-0.14716925,0.05418698,0.03604008,-0.00697153,-0.03658443,0.02153712,0.01992207,-0.05441588,0.01439785,0.06204187,-0.06886589,-0.02633568,-0.00698241,0.02362121,0.05715943,-0.01378553,-0.00322617,-0.08188351,-0.02446358,-0.00705629,-0.01389042,-0.00985637,0.01228253,0.03010588,-0.00177187,-0.07337467,0.05596384,-0.01556376,0.01130196,-0.04230126,-0.04541377,-0.02442434,-0.00928516,0.02223115,-0.04654848,0.12422282,0.02442296,0.00648084,0.01245827,-0.02479153,0.03722686,-0.04384449,-0.04524785,0.00577254,0.04378438,-0.04223204,-0.0029772,-0.05210797,0.00762474,0.00953981,0.02441566,0.02859674,0.0166189,-0.01073127,0.09540108,0.00260365,0.01992967,-0.07136032,-0.18772614,-0.05519659,0.02792705,-0.00852156,0.01560912,-0.00608616,0.04762248,0.02499018,0.07799902,0.0911679,0.06974175,0.0556507,-0.03202089,0.03341842,-0.01894266,0.02514831,0.04349647,-0.00400136,0.02850959,0.03738786,-0.04065666,0.04595546,0.0146199,-0.05664034,0.00960515,-0.07928592,0.10317133,-0.00132166,-0.00270683,-0.00743598,0.05684079,-0.00432849,-0.01947064,-0.12331536,0.01884113,0.03029016,0.01085972,-0.04270145,-0.02934878,-0.02405123,-0.00668482,0.05007333,0.04864215,-0.1015415,0.03553859,-0.07925663,-0.04424399,-0.00398588,-0.01941972,0.02407346,0.00904122,0.04929112,-0.00144937,0.07801592,-0.02628267,-0.02148257,0.04116953,-0.04598463,0.05342279,0.02855711,0.00066769,-0.01493186,-0.02612092,-0.017233,0.0371746,-0.02422197,0.00333208,0.03581474,0.00859992,-0.04597009,-0.02000047,0.13808541,0.04314138,0.01774549,0.05211681,-0.00071471,-0.00758966,-0.09293086,0.02714202,-0.00164734,0.01974057,-0.01296662,0.04914939,0.0536805,0.03199268,0.06593116,0.02395059,-0.00555842,-0.00360777,0.00325355,0.03788587,-0.04565862,-0.06635091,-0.03635559,0.04777652,0.00521194,-0.28679487,0.00363211,-0.03313109,-0.01555326,0.03906192,0.01837438,0.07394891,0.02092479,-0.03338489,0.02362398,-0.02416078,0.02980274,-0.0069611,-0.07625835,0.01140652,0.00811262,0.05448361,0.01845576,0.00835141,0.04668016,0.0106812,0.07354068,0.21231157,-0.03170325,0.03884698,0.06066194,-0.05304067,0.04776303,0.03086145,-0.00517185,-0.03697834,-0.01742102,0.0356327,-0.03998061,0.02983209,0.05539656,-0.02330538,-0.03571567,-0.02062048,0.01383859,-0.03186892,-0.02258896,-0.01772358,0.05198861,0.11675627,-0.05216393,-0.09148476,-0.05040786,-0.00496519,0.04532896,-0.01117106,-0.0237821,0.00018919,0.02213031,0.0005182,0.04541668,0.05341979,-0.02641186,-0.03384719,-0.03715142,-0.004504,-0.00921347,-0.0607702,0.04773218,0.02870999],"last_embed":{"hash":"3x9mct","tokens":390}}},"text":null,"length":0,"last_read":{"hash":"3x9mct","at":1751815145740},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**","lines":[149,172],"size":1186,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"3x9mct","at":1751815145740}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.1. 创始人简介：Logan Allen**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09266479,-0.04759226,-0.04574134,0.00015279,0.01862922,0.00280809,-0.01130297,0.02471602,-0.03411999,-0.01399264,0.0715092,-0.05582622,0.03175236,0.00662756,0.03263634,-0.01090604,0.03540192,-0.04731601,-0.04435207,0.00284845,0.07272756,-0.08944064,0.05501181,-0.06429433,0.09748706,0.03560379,0.04328592,-0.03836252,-0.02691459,-0.18587624,0.00744321,-0.04765604,0.00582438,0.02092815,0.06308541,-0.03101081,-0.02724208,0.08017984,-0.05927634,0.00316093,0.02240987,-0.00353094,0.00859482,0.03593734,0.04356613,-0.08888379,0.02414745,-0.05923365,-0.02017422,-0.04846293,0.01521946,-0.06985919,-0.04173253,0.03688509,-0.00827624,0.03708072,0.01596633,0.05949744,0.01845315,0.01096186,0.07912554,0.0449177,-0.21166639,0.02432358,0.09428824,0.02954704,-0.08114183,0.00958665,0.06487676,0.08300992,0.03117062,0.02887636,0.01651544,0.04479576,-0.00367588,0.01306039,0.05644772,-0.04509922,-0.02168882,-0.05281682,-0.043192,0.0693628,0.00149619,-0.01847303,-0.02670481,-0.03085468,0.02236442,-0.06655062,-0.03100265,-0.02012408,0.01120795,-0.01647304,0.07235583,-0.00339906,0.00735895,-0.02009612,0.01946757,-0.00458535,-0.11347201,0.10663536,-0.00708216,0.02739207,-0.02720592,-0.0652665,0.06180088,-0.03888739,-0.0203027,-0.06823885,0.02117216,0.00774847,0.00366839,0.03620955,0.04888031,-0.02542588,0.03001348,-0.00758017,0.03870093,0.00836392,-0.0132019,0.02398739,-0.00150742,0.03807676,0.04241636,0.00942377,-0.05587372,-0.06100496,0.01497169,0.05079301,0.06377273,0.02505442,0.05624516,-0.01048323,-0.08370322,0.00175518,-0.02877679,-0.01881099,0.00331259,0.01262358,-0.07990236,-0.04879038,-0.05102972,-0.06626375,-0.03878206,-0.11138938,-0.06053838,0.10355284,-0.00371551,-0.01661461,-0.00421253,-0.00087835,0.03684317,0.02146827,-0.03846424,-0.073744,-0.01044028,0.03282125,0.05649003,0.09952147,-0.04853946,0.01142115,0.00528117,-0.02529706,-0.0431448,0.11515635,0.05880784,-0.13888218,0.06594112,0.03465031,-0.00533347,-0.03667443,0.03419819,0.02060079,-0.05137344,0.01041434,0.07031856,-0.06420998,-0.01556123,0.00341548,0.02041339,0.05221695,-0.01435649,-0.00674556,-0.08679455,-0.01874839,-0.00220951,-0.0244805,-0.01323613,0.01156223,0.03231107,0.01480436,-0.06838866,0.05446782,-0.01726921,0.01022627,-0.04550233,-0.03747171,-0.02609905,-0.00146863,-0.00149099,-0.04403755,0.12314874,0.02204239,0.00185868,0.0227179,-0.00757811,0.03672681,-0.05550366,-0.0401946,0.00075069,0.03993346,-0.04314302,0.00340638,-0.05746652,0.01441092,-0.01350543,0.02132642,0.02461528,0.01839356,-0.00761945,0.07945596,0.00196807,0.01034473,-0.05706458,-0.20334285,-0.06173498,0.02184647,-0.01304285,0.0181938,-0.0057873,0.04666654,0.02106915,0.07388055,0.07999302,0.07092898,0.06323626,-0.02626084,0.0216241,-0.0215392,0.04016627,0.02703703,-0.00595027,0.02895225,0.03236049,-0.0494238,0.03602898,0.00927667,-0.05677776,0.00929391,-0.08091879,0.10673586,0.00781539,-0.00255088,0.00453647,0.05163716,0.00012372,-0.01364248,-0.12606081,0.01142801,0.02848576,-0.0018272,-0.0365122,-0.03240023,-0.03088462,-0.00581675,0.04154365,0.05203574,-0.09966768,0.03655234,-0.07155488,-0.03957743,-0.00980902,0.00155505,0.02163951,0.02111649,0.04674144,-0.00524599,0.08033555,-0.01179235,-0.01825524,0.04812895,-0.03935637,0.05083264,0.03628363,0.00457845,-0.01230901,-0.03447833,-0.00598331,0.02950977,-0.02042471,-0.01168045,0.024615,0.01015129,-0.03694455,-0.02943743,0.13742238,0.04034879,0.01469593,0.0557525,0.01269213,-0.00087089,-0.08974609,0.0207627,-0.01680643,0.01471696,-0.01956555,0.04881847,0.04643647,0.03284984,0.06603331,0.01615144,-0.00240078,-0.00226105,0.01876504,0.03681386,-0.0460179,-0.07208091,-0.02350814,0.05665262,-0.00744064,-0.28383166,0.00358395,-0.02912254,-0.00469037,0.04451913,0.01792361,0.07631189,0.0078549,-0.04422271,0.02270346,-0.02319686,0.03883067,-0.02070259,-0.06960437,0.00923913,0.01627183,0.06297009,0.01848301,0.0125768,0.03854675,0.0159808,0.0793055,0.21582942,-0.04070374,0.04647468,0.06807619,-0.05883108,0.03473696,0.0451803,-0.00282973,-0.03722172,-0.02217342,0.02270395,-0.03701587,0.03481498,0.04033213,-0.02185519,-0.02930617,-0.0271807,0.01302269,-0.03597547,-0.02141036,-0.02557316,0.05156286,0.12086073,-0.04472126,-0.08597716,-0.04938223,-0.00869557,0.03990377,-0.00997394,-0.01933639,-0.00215695,0.02126961,0.01018341,0.04809303,0.05293474,-0.01959462,-0.0325772,-0.03274171,-0.00788013,-0.01577333,-0.04835651,0.04832033,0.03783747],"last_embed":{"hash":"ouk2r1","tokens":281}}},"text":null,"length":0,"last_read":{"hash":"ouk2r1","at":1751815145765},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.1. 创始人简介：Logan Allen**","lines":[151,154],"size":300,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"ouk2r1","at":1751815145765}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.1. 创始人简介：Logan Allen**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09015753,-0.04697592,-0.0505432,0.00335542,0.01531952,0.00447577,-0.01143136,0.02429101,-0.03554162,-0.01446206,0.06984305,-0.05321689,0.03090093,0.00698675,0.03237849,-0.01350701,0.03808752,-0.04609524,-0.04308614,0.00191597,0.07265228,-0.08850977,0.05521164,-0.06228198,0.10012663,0.03629493,0.04456398,-0.03890005,-0.0286995,-0.18936531,0.00705864,-0.05040308,0.0074204,0.0251674,0.0617445,-0.02887567,-0.02781848,0.0795664,-0.06068812,0.0042536,0.02508413,0.00115513,0.00985853,0.03895443,0.04082343,-0.08806396,0.02774685,-0.05754369,-0.02098475,-0.04897133,0.01450859,-0.07147825,-0.04420584,0.03536556,-0.00942356,0.03425177,0.01625043,0.05972699,0.01809255,0.01040984,0.07682206,0.04293681,-0.21200064,0.02391986,0.09554457,0.02668896,-0.08504141,0.00872527,0.06774573,0.08169561,0.03115348,0.02676681,0.01659591,0.04372239,-0.00663558,0.014145,0.060328,-0.04826904,-0.0203772,-0.05383646,-0.04034234,0.07017615,0.00263298,-0.02095815,-0.0234009,-0.02938081,0.02434443,-0.06773639,-0.02957939,-0.02179324,0.01535567,-0.0178286,0.07600465,0.00113824,0.00462067,-0.02143647,0.01969048,-0.00359204,-0.11447844,0.10530666,-0.0058405,0.02726711,-0.02828358,-0.06494474,0.06145721,-0.03698949,-0.01946563,-0.06915436,0.02033355,0.00551063,0.00398567,0.03909233,0.04895359,-0.02434416,0.03244813,-0.00735265,0.04026696,0.00892043,-0.01352403,0.02727029,-0.00100179,0.03842528,0.04006101,0.00914983,-0.05665363,-0.05899873,0.01452828,0.05115251,0.06251971,0.02317702,0.05348654,-0.01452909,-0.08382978,0.00097304,-0.02841358,-0.01740558,0.00240261,0.01440152,-0.07994236,-0.05130063,-0.05261382,-0.06609037,-0.03949919,-0.11183343,-0.06215292,0.10320232,-0.00163077,-0.01887178,-0.00613689,-0.00194654,0.03702849,0.02179221,-0.04082178,-0.07520597,-0.00623359,0.03261621,0.05311799,0.0989158,-0.05091295,0.01228052,0.00440699,-0.02539636,-0.04078015,0.11275818,0.05472048,-0.13902225,0.06498796,0.03521572,-0.00667758,-0.032791,0.03522951,0.02423031,-0.05402209,0.01313463,0.07131703,-0.06691033,-0.02288338,0.00299015,0.02246181,0.05173189,-0.01322992,-0.00682025,-0.08482209,-0.01977346,-0.00279574,-0.02209293,-0.01372778,0.01340158,0.03384402,0.01274685,-0.06586099,0.05593751,-0.01391694,0.01345312,-0.0475172,-0.03743739,-0.02456969,0.00071453,-0.00172461,-0.04245003,0.12109651,0.02185331,-0.00027678,0.02183741,-0.00626515,0.03548697,-0.05636331,-0.04216263,0.00415435,0.04110009,-0.04638376,0.00026742,-0.05818807,0.01157457,-0.01243065,0.02411858,0.02286159,0.0157581,-0.00984574,0.07728393,0.00433806,0.01378902,-0.05776988,-0.20293666,-0.06090616,0.02337155,-0.0129223,0.01787373,-0.00553924,0.04888637,0.02239946,0.07290211,0.08220469,0.068441,0.06114556,-0.02665354,0.02070997,-0.01686758,0.04420003,0.02474953,-0.0045014,0.02672531,0.03324898,-0.04955699,0.03500964,0.0081601,-0.05768574,0.01324791,-0.08046646,0.10549198,0.00850383,-0.00043093,0.00461191,0.05345541,-0.00239087,-0.01644426,-0.1231333,0.01204849,0.02680194,-0.00102074,-0.03763959,-0.03163264,-0.03195522,-0.00637157,0.04154358,0.04994843,-0.09896488,0.03816854,-0.06977557,-0.03777905,-0.01174031,0.00270038,0.02380073,0.02140943,0.0468316,-0.00930571,0.08277398,-0.01242445,-0.02178805,0.04702079,-0.03689063,0.04945928,0.03514687,0.0057131,-0.01297345,-0.03580694,-0.00993561,0.02838753,-0.01870005,-0.01409903,0.026888,0.01105854,-0.0362476,-0.02846339,0.13612676,0.04153064,0.01535305,0.05646763,0.01302815,-0.00169097,-0.09018323,0.02182144,-0.0179648,0.01522577,-0.01660877,0.05118234,0.04717121,0.03542968,0.06384178,0.01241382,-0.00358133,-0.00154485,0.02023033,0.04062457,-0.04699175,-0.07085777,-0.01999106,0.05570463,-0.00965866,-0.28118023,0.00323326,-0.03129342,-0.00489198,0.04008365,0.01917857,0.07750259,0.01035376,-0.04310444,0.02351126,-0.02208568,0.03819032,-0.02209264,-0.06919117,0.00691079,0.01510178,0.0620854,0.01954361,0.01168603,0.04160863,0.0152077,0.08044866,0.21507323,-0.03711707,0.05074305,0.0692106,-0.05828938,0.03567228,0.04049736,-0.00411853,-0.03620355,-0.02175782,0.02233317,-0.03537968,0.03200027,0.03934911,-0.02266493,-0.02635754,-0.02780412,0.01376722,-0.03844379,-0.01956961,-0.02869962,0.05003788,0.12240616,-0.04397893,-0.08511111,-0.04688325,-0.01188803,0.03869046,-0.01024747,-0.01837114,-0.00201955,0.02044408,0.00907898,0.04682495,0.05393165,-0.01943692,-0.03394267,-0.03596153,-0.00916706,-0.01561724,-0.05192319,0.04635759,0.03907942],"last_embed":{"hash":"1nz1r7d","tokens":278}}},"text":null,"length":0,"last_read":{"hash":"1nz1r7d","at":1751815145782},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.1. 创始人简介：Logan Allen**#{1}","lines":[153,154],"size":267,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1nz1r7d","at":1751815145782}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.2. 投资者分析与“公平启动”的困境**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05278054,-0.01482682,-0.04606858,-0.04340322,0.0402299,-0.01263062,-0.00669208,0.00656563,-0.00145692,-0.0241861,0.07959961,-0.03937596,0.04556201,0.03555397,0.02435802,-0.01102294,-0.00866825,-0.04957223,-0.02249788,0.03716679,0.10680553,-0.07926244,0.02761677,-0.02819205,0.07413208,0.01421779,0.01518226,-0.03865721,-0.03875548,-0.180149,0.00964925,-0.02890832,0.02004372,0.03167107,0.01380429,-0.06156104,-0.00025212,0.08225396,-0.05191478,-0.00646578,-0.00768811,-0.00329592,-0.00839223,0.02377994,0.03061569,-0.08053308,0.00369873,-0.06102433,-0.00272975,-0.06465359,0.00070173,-0.09663591,-0.0177913,0.03000355,0.01387605,0.02064975,-0.01195188,0.02279958,-0.00138456,0.03112873,0.04980636,0.02027912,-0.21197671,0.01858524,0.07531594,0.01525197,-0.01901935,0.0174091,0.02146295,0.06940313,0.01871374,0.0216316,0.0192579,0.04141807,0.02624262,0.00115265,0.04383289,0.0051586,-0.02863407,-0.07473063,-0.0184387,0.08506888,-0.017597,0.0078826,-0.00378395,0.00520919,0.00107959,-0.05647185,-0.02636486,0.02537232,-0.01072347,-0.0003136,0.0520103,-0.01098346,-0.03640692,0.00612039,0.022738,0.02271951,-0.07885119,0.09568218,-0.02407616,0.04704321,-0.00836401,-0.07946817,0.03672872,0.00392091,-0.00809156,-0.03887739,0.04017737,0.04083405,-0.01963089,-0.00394706,0.07470232,-0.03898345,0.01204562,0.02275331,-0.01667356,-0.0073272,-0.03248225,0.00129547,-0.00427389,0.0644233,0.02099812,-0.03964709,-0.04086885,-0.01957573,0.013727,0.06665947,0.04852001,0.02621086,0.0245846,0.06074947,-0.06260584,0.02334261,-0.01572439,-0.0274559,-0.01078885,0.01047057,-0.04446655,-0.05195512,-0.05813022,-0.04711917,-0.01489274,-0.11805792,-0.05403152,0.06263658,0.02634967,-0.00586549,0.01157728,-0.04282953,0.01372703,0.06750569,-0.00592823,-0.07329095,-0.02489413,-0.00401312,0.02282817,0.13182151,-0.08162274,0.00236366,-0.01624538,-0.02847828,-0.02919596,0.14127946,0.03311677,-0.13664298,0.02748282,0.03016381,-0.01707815,-0.07170066,-0.00780842,0.01853956,-0.05790554,-0.00492261,0.07811938,-0.03636098,-0.04799859,0.0015004,0.01610066,0.05078415,-0.00364697,-0.00324123,-0.07968567,-0.02453898,-0.03502678,-0.00183988,-0.00052802,0.01929753,0.00814193,0.01237233,-0.09520952,0.03432732,-0.02579919,0.02496365,-0.03789058,-0.00686057,-0.02831512,-0.01562162,0.06313765,-0.06016938,0.11898511,0.01789789,0.00908818,0.01535411,-0.07176821,0.02659526,-0.01273815,-0.06626955,-0.0038869,0.0419863,-0.01693816,0.01623262,-0.03420577,0.02637305,0.02424492,0.02259231,0.00684519,0.03709439,-0.01748074,0.0780466,0.01561425,0.03392943,-0.09103921,-0.17815389,-0.03355103,0.05145455,-0.02243195,0.03187566,0.00906087,0.04900424,0.04699472,0.0695938,0.07140419,0.08737951,0.04818779,-0.04602987,0.02462835,-0.02048443,0.04077155,0.08225103,-0.00752751,-0.00985589,0.02628959,-0.02155433,0.05242653,0.02478212,-0.02970625,0.05864723,-0.06003094,0.08419939,-0.03004335,0.01504586,-0.00518263,0.07979897,0.00917439,-0.04569545,-0.13966671,0.04053326,0.04239773,-0.00440021,-0.02563663,-0.05384945,-0.01209262,0.00459221,0.03542288,0.03918552,-0.09426889,0.0105705,-0.08048865,-0.04847854,0.01498554,-0.03879806,0.0188639,-0.00615376,0.04053443,0.01541845,0.11784071,-0.01456796,-0.02748571,0.01027931,-0.04814951,-0.00543684,0.03265093,-0.00330661,-0.02316407,-0.0267324,-0.03042726,0.03608941,-0.0216845,0.03749793,0.0468029,0.03581643,-0.06144954,-0.03522288,0.1280755,0.03036448,0.00179673,0.05073248,0.0144445,-0.04074022,-0.10345768,0.05351577,0.02669073,0.04099986,0.02396447,0.04288941,0.05948142,0.01187407,0.04885831,0.04078483,-0.01835146,0.0163089,-0.02764292,-0.0194749,-0.04641231,-0.05427457,-0.04418285,0.06547596,0.02416234,-0.31510958,-0.01571532,-0.0279496,-0.01829999,0.05799042,0.03748101,0.04383101,0.02730247,-0.04815255,0.02921121,-0.01601958,0.02689983,0.01456025,-0.05946574,0.00870259,-0.01535878,0.02648084,-0.01247455,0.01922788,0.05472708,-0.00443296,0.07123706,0.22071366,0.01536471,0.01518036,-0.00030184,-0.0428772,0.05879475,0.0223019,0.00410072,-0.03752439,-0.03121393,0.04853683,-0.03794308,0.02860285,0.06089094,-0.02326534,-0.01030576,-0.00396514,0.01231357,-0.03761547,-0.03033078,-0.0184794,0.04052379,0.12022336,-0.02338223,-0.06040481,-0.04988584,0.00156589,0.06785714,-0.04304265,-0.02556279,0.01770711,0.03191188,-0.01311263,0.07669438,0.03889307,-0.05065729,-0.05994767,-0.02977817,-0.01865945,-0.0071472,-0.07892288,0.07061849,0.00147412],"last_embed":{"hash":"12jlqem","tokens":429}}},"text":null,"length":0,"last_read":{"hash":"12jlqem","at":1751815145800},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.2. 投资者分析与“公平启动”的困境**","lines":[155,166],"size":662,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"12jlqem","at":1751815145800}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.2. 投资者分析与“公平启动”的困境**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05049774,-0.01510558,-0.04260394,-0.04162775,0.0371296,-0.01710903,-0.00946574,0.00743554,-0.00397786,-0.02501612,0.08071392,-0.03604265,0.04364245,0.03491106,0.02482278,-0.01098473,-0.00801382,-0.05073066,-0.02195311,0.040047,0.11075144,-0.08205106,0.0276842,-0.03191248,0.07367115,0.01510591,0.01872853,-0.03746453,-0.04065206,-0.18251434,0.0123478,-0.03038794,0.01677722,0.03262629,0.01094372,-0.06016794,0.00399776,0.08169828,-0.04960188,-0.0084015,-0.01012116,-0.00011968,-0.00994965,0.02530759,0.0284708,-0.08105416,0.00301014,-0.06002025,-0.00372604,-0.06459706,0.00220794,-0.09645868,-0.01686099,0.0279223,0.01209638,0.01790185,-0.01554427,0.02173928,-0.00008245,0.0300607,0.04873145,0.01755941,-0.2115768,0.01882955,0.06931723,0.01538684,-0.01904654,0.01818737,0.0193778,0.06759119,0.01973738,0.0189853,0.01570089,0.04079965,0.02259449,-0.00217556,0.04183729,0.00398023,-0.02778021,-0.06978407,-0.01568661,0.08424051,-0.01758043,0.00935634,0.00253927,0.00655031,0.00373698,-0.0570271,-0.02349421,0.0264291,-0.01353995,0.00230959,0.05387035,-0.01081472,-0.0371415,0.00694774,0.02725586,0.02687367,-0.0798431,0.09083935,-0.02491708,0.0482302,-0.01512879,-0.07750897,0.03478855,0.00479086,-0.00342108,-0.03828574,0.03859724,0.04000435,-0.02415709,-0.00403973,0.07588638,-0.03493566,0.01448432,0.0235471,-0.01655467,-0.00383544,-0.03826252,0.00043851,-0.00611668,0.06416232,0.02295695,-0.0404223,-0.03850171,-0.01670182,0.0126995,0.06737453,0.04700348,0.02546879,0.01956649,0.05688943,-0.05887328,0.02315077,-0.01003179,-0.02267944,-0.0080961,0.00745384,-0.04425093,-0.05102145,-0.06307983,-0.05224995,-0.01506016,-0.11500476,-0.05735621,0.06315743,0.02687459,-0.00975082,0.01175059,-0.04215841,0.01417849,0.06843571,-0.00754166,-0.0703599,-0.02340249,-0.00246532,0.02432572,0.13133019,-0.08290278,0.00529136,-0.01800686,-0.03045206,-0.02964677,0.1417681,0.03117583,-0.13658893,0.02508774,0.03152981,-0.01736525,-0.0700243,-0.00963759,0.01668716,-0.05859849,-0.00755096,0.07603917,-0.04036661,-0.04670424,0.0009349,0.01403081,0.05430175,-0.00552728,0.00132275,-0.08044903,-0.02683376,-0.03681508,0.00019873,-0.00179935,0.01755158,0.00482376,0.0079966,-0.09569673,0.03834178,-0.02628317,0.02371713,-0.03795692,-0.0123151,-0.02638796,-0.01552926,0.06199016,-0.05857383,0.11974335,0.01876844,0.01048329,0.01818705,-0.06835306,0.02494803,-0.01046317,-0.06609907,-0.00582575,0.04135705,-0.01585393,0.01462384,-0.03068938,0.02843154,0.02503778,0.02384616,0.00553123,0.03594793,-0.01907239,0.07772454,0.0160239,0.03686434,-0.09250531,-0.17688467,-0.02857816,0.04942658,-0.02383146,0.03356757,0.00896717,0.05203156,0.04949799,0.07116196,0.07168698,0.0915641,0.04564576,-0.04485955,0.02381722,-0.02487886,0.04205906,0.08513246,-0.00664974,-0.01070866,0.02500084,-0.02497864,0.05246599,0.03120189,-0.02797804,0.05505731,-0.06116456,0.08225232,-0.02893923,0.0144689,-0.00729666,0.0846066,0.00811309,-0.04460645,-0.13629013,0.03974698,0.04090579,-0.0001872,-0.03018511,-0.05239718,-0.01304206,0.00523628,0.03965103,0.04036329,-0.09722093,0.01146662,-0.07735861,-0.04871069,0.01927564,-0.04023625,0.02373561,-0.00499143,0.04104046,0.0122219,0.11751913,-0.02050799,-0.02863722,0.01247023,-0.05022436,0.00111851,0.03132446,-0.00702907,-0.02324529,-0.02951623,-0.0310949,0.03921548,-0.02035552,0.0381702,0.04503177,0.03495827,-0.06451578,-0.03414829,0.12608841,0.02885753,0.00231264,0.05263504,0.01570693,-0.04067909,-0.10356096,0.05398685,0.02506367,0.03886174,0.02378266,0.04057675,0.05837183,0.01070218,0.04960594,0.04237586,-0.02104011,0.01553702,-0.02842495,-0.0161369,-0.044846,-0.05408043,-0.04068518,0.06242494,0.02479567,-0.31536517,-0.01283363,-0.0288046,-0.02422155,0.05606452,0.03798706,0.04538184,0.03211293,-0.04637552,0.0257901,-0.01594424,0.02695327,0.01681183,-0.05921248,0.01054026,-0.01517112,0.02319954,-0.00913507,0.01680222,0.05388339,-0.00294314,0.07409318,0.22433536,0.01945286,0.01411908,0.00160355,-0.03626756,0.06359519,0.02364379,0.00117205,-0.03758342,-0.03307131,0.05123155,-0.03355273,0.02984619,0.06269843,-0.02242497,-0.01132389,-0.00184107,0.01226607,-0.03856898,-0.03187593,-0.0184852,0.04054519,0.11890868,-0.02552451,-0.06128187,-0.04877453,0.00215437,0.06601938,-0.04468453,-0.02469239,0.01627358,0.03391895,-0.01733899,0.07504646,0.03601484,-0.05187583,-0.0622704,-0.03199553,-0.0188497,-0.00344321,-0.07743751,0.06815837,0.000579],"last_embed":{"hash":"w4wlr7","tokens":429}}},"text":null,"length":0,"last_read":{"hash":"w4wlr7","at":1751815145828},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第四部分：架构师与支持者 \\- 团队、组织与融资**#**4.2. 投资者分析与“公平启动”的困境**#{1}","lines":[157,166],"size":631,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"w4wlr7","at":1751815145828}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07705241,-0.03715951,-0.05944347,-0.02729181,0.02722749,0.01754628,0.00279326,-0.00552969,0.01107117,-0.04398034,0.05361633,-0.06928622,0.04367634,0.01417196,0.03040328,-0.00470393,0.02858751,-0.04998461,-0.02205169,0.02857264,0.09170181,-0.05900252,0.02337331,-0.01136365,0.07697242,0.03515067,0.00905559,-0.03274602,-0.04037035,-0.17179818,0.01629281,-0.06810768,-0.00257319,0.00924908,0.03025442,-0.03564278,-0.01603648,0.05809459,-0.04483854,-0.00392859,0.01028649,0.02463888,0.0047761,-0.01834944,0.04294067,-0.07128337,0.02872609,-0.066179,-0.00098707,-0.06297935,-0.02605188,-0.07219343,-0.00828294,0.04605182,0.01754165,0.01813353,0.00031663,0.06302018,0.02007103,0.03046485,0.0802547,0.05218888,-0.18080007,0.01624449,0.09025252,0.02947566,-0.016845,0.00633937,0.06684445,0.07110629,0.01030133,0.00526517,-0.02509218,0.06637467,0.03248347,0.00615603,0.01121859,-0.00027316,-0.04631738,-0.05389042,-0.05523675,0.07500634,0.02440679,0.02321291,-0.04260469,-0.00791747,0.00059995,-0.05152768,-0.0396137,0.00778215,-0.00279407,0.00589143,0.04977943,-0.00082846,-0.0292333,-0.01394421,-0.00690288,0.02536197,-0.07474327,0.12044619,-0.03806897,0.04260527,-0.01930666,-0.04389514,0.04651142,-0.00042483,-0.00007759,-0.06899912,0.01300831,0.02179533,0.02215656,0.02362116,0.06788942,-0.04641295,0.03039226,0.01700552,0.01121671,-0.00398727,-0.01973764,0.01556,0.00134278,0.05580208,0.04940856,-0.03183243,-0.03220762,-0.01353243,0.0017934,0.03589665,0.05423415,0.04688021,0.02246467,0.0327675,-0.05001213,-0.01708801,-0.03021773,-0.02925103,-0.03611913,0.01192978,-0.03325567,-0.05403914,-0.02284078,-0.05452748,-0.01320901,-0.11107293,-0.0733266,0.06546186,-0.01312812,0.01342454,-0.0037055,-0.05060301,0.01578548,0.08108017,-0.03339835,-0.06443083,-0.02847825,0.00777977,0.0316399,0.1094123,-0.07750534,-0.02440538,-0.01165491,-0.02800099,-0.046206,0.14164695,0.03201244,-0.09987041,0.05744956,0.03577001,-0.01213083,-0.08537267,0.01750314,0.01515819,-0.06793977,0.00800866,0.0563992,-0.02951512,-0.02992938,-0.01996244,0.00574931,0.03626612,0.0031374,-0.03564499,-0.09643622,-0.00245357,-0.02637573,-0.0195604,-0.01886574,0.0480942,0.01341338,0.05168251,-0.10383157,0.03187341,0.01721121,0.03857964,-0.04698207,-0.02007653,-0.03119636,-0.02120938,-0.00004135,-0.05208459,0.1416012,0.03554149,0.00296912,0.00822133,-0.07408891,0.00719683,-0.01847961,-0.05937208,0.01284415,0.04920597,-0.01655016,0.04474532,-0.05300063,0.04321943,0.00234461,0.01526799,0.02330976,0.05098051,-0.01046469,0.06034989,0.02276805,-0.00304785,-0.09190207,-0.18273902,-0.04547019,0.0253695,-0.02315779,0.05234622,-0.0120225,0.02195876,0.02917318,0.04393977,0.06163862,0.08087956,0.06802942,-0.07036342,0.01309905,-0.02864127,0.05866214,0.04990386,-0.01769006,-0.01005502,0.04221512,-0.03543121,0.0354787,0.01020443,-0.05413312,0.04261949,-0.06603194,0.10893675,-0.03481228,0.03638999,-0.00645046,0.05137982,-0.00042197,-0.03744449,-0.1622362,0.03612035,0.05750577,-0.04320241,-0.00408515,-0.058284,-0.03873627,-0.00165198,0.00575275,0.04356708,-0.10968879,0.03070747,-0.07756647,-0.06147323,-0.00803259,-0.01654299,0.01709905,0.0129996,0.0449898,0.01244196,0.11342536,-0.02231149,-0.01130495,0.03112757,-0.02481842,-0.00816882,0.06463605,-0.01505199,-0.00941442,-0.04001898,-0.00182237,0.01459018,-0.02342868,-0.0155844,0.03838796,0.02925725,-0.02746819,-0.03605681,0.15883498,0.01459248,-0.01849341,0.02563739,0.0358019,-0.04000108,-0.09442008,0.02929603,-0.01120055,0.03456135,0.01585568,0.02916938,0.05235771,-0.01285909,0.05162516,0.018394,0.01416449,0.02176766,0.02734917,-0.00862967,-0.05196617,-0.04310926,-0.06249729,0.0731998,0.02342308,-0.28694129,-0.01385434,-0.01941868,0.00350512,0.0651358,0.02010144,0.0653359,0.01775854,-0.06360434,0.02225194,-0.00034216,0.05428126,0.00411482,-0.0560642,0.00099613,-0.00973486,0.04256955,-0.00855937,0.01386195,0.05085062,-0.01011636,0.07223728,0.22026074,-0.01267666,0.0449322,0.02202484,-0.04035475,0.0523438,0.06860606,0.0265626,-0.04600747,-0.01565913,0.04249166,-0.03752352,0.02915835,0.04281266,-0.00089981,-0.00485747,0.00114365,0.01416067,-0.02447882,-0.0288813,-0.00317633,0.03672645,0.12922926,-0.03565442,-0.04893266,-0.0851868,0.0141592,0.05863443,-0.04882049,-0.01851465,0.0210267,0.03012213,-0.00975373,0.05874081,0.04086145,-0.03571568,-0.08559468,0.00030269,-0.034887,-0.02038662,-0.04157881,0.06299046,0.01594768],"last_embed":{"hash":"1wx2vgz","tokens":463}}},"text":null,"length":0,"last_read":{"hash":"1wx2vgz","at":1751815145855},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**","lines":[173,195],"size":974,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1wx2vgz","at":1751815145855}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.1. “实验性与未经审计”的现实**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07478993,-0.035726,-0.05765124,-0.0247663,0.02789046,0.01886518,-0.0021317,-0.00959947,0.0109363,-0.04321693,0.05137895,-0.06768445,0.04521259,0.01265607,0.03161218,-0.00411889,0.0292499,-0.05188458,-0.02154376,0.02675488,0.09370252,-0.05742239,0.02184644,-0.01184518,0.07499631,0.03423792,0.00835949,-0.03085848,-0.0392097,-0.17171755,0.0147793,-0.0679086,-0.00356157,0.01228572,0.02968987,-0.03592053,-0.01528112,0.05709702,-0.04513065,-0.0044533,0.01144104,0.02525055,0.00832127,-0.01746588,0.04145986,-0.06954944,0.02626504,-0.06894355,-0.00190772,-0.06130562,-0.02808199,-0.07445141,-0.00967374,0.04568325,0.01521387,0.01656685,0.00041431,0.06156947,0.01774867,0.02673908,0.08237045,0.05113481,-0.1808596,0.01580602,0.0884907,0.02608451,-0.0176658,0.00889172,0.06541644,0.07059607,0.00899173,0.00515813,-0.02339404,0.06528938,0.03207461,0.00653639,0.01234585,-0.00167189,-0.04750491,-0.05406337,-0.05438506,0.07684831,0.02233907,0.02384057,-0.04250621,-0.0061923,0.0015887,-0.05176056,-0.04046264,0.00941449,-0.00198335,0.00299303,0.04696057,-0.00093127,-0.02893278,-0.01280204,-0.00683606,0.02568354,-0.07472483,0.11911783,-0.03812571,0.04310175,-0.01721454,-0.04771582,0.04475523,0.002617,-0.00279223,-0.06821563,0.01284223,0.02231264,0.02057302,0.02119623,0.06691034,-0.04862921,0.02716264,0.0173685,0.01331521,-0.00572384,-0.02175561,0.01351645,0.00106968,0.05417659,0.04845833,-0.03626663,-0.03126594,-0.0122205,0.00438869,0.03556862,0.05539717,0.04400969,0.02168909,0.03369236,-0.05090287,-0.01709374,-0.03131076,-0.03226614,-0.03985281,0.01229103,-0.03406498,-0.05744807,-0.0201541,-0.05205345,-0.01422682,-0.11413413,-0.07394042,0.06612062,-0.01296484,0.01207686,-0.00324824,-0.0509549,0.01791003,0.07772035,-0.03396873,-0.06255998,-0.03038341,0.00794146,0.03169395,0.11216954,-0.0766381,-0.0229948,-0.0132576,-0.02790029,-0.04521128,0.14128974,0.03249141,-0.10219204,0.05618232,0.03731224,-0.01123496,-0.08184433,0.01782219,0.01594612,-0.06681471,0.00756554,0.0565615,-0.02862473,-0.02841987,-0.01661777,0.0059971,0.03838113,0.005766,-0.03701562,-0.09792092,-0.00368397,-0.02856455,-0.02143668,-0.01893012,0.04712569,0.01235708,0.0505348,-0.10272391,0.03393818,0.01917042,0.03838976,-0.04429529,-0.01939863,-0.03036069,-0.0250636,0.00124141,-0.05344477,0.14225461,0.03596934,0.00277838,0.00629774,-0.07414435,0.00620648,-0.01835873,-0.06054482,0.01330382,0.05009889,-0.01404177,0.04576863,-0.05251385,0.04573758,0.00268201,0.0152217,0.02297044,0.05164356,-0.00860073,0.06380037,0.02401373,-0.00057548,-0.09417396,-0.18371297,-0.04649149,0.02313327,-0.02372319,0.05150988,-0.01202586,0.02251306,0.02910157,0.0421094,0.05981454,0.08170715,0.06947652,-0.07099256,0.01445013,-0.02971605,0.05901907,0.04840036,-0.01729266,-0.00923533,0.04343051,-0.03222148,0.03584273,0.01519641,-0.05541566,0.04006331,-0.06488793,0.10736403,-0.03329687,0.03595916,-0.00660392,0.05392017,-0.00272394,-0.03667734,-0.16103391,0.03641883,0.05618063,-0.04336022,-0.00675039,-0.0595037,-0.03606052,0.00318482,0.00652223,0.04508028,-0.10799434,0.03184364,-0.07962393,-0.05981936,-0.01055367,-0.01461945,0.01748866,0.01439897,0.03877869,0.01203564,0.11338848,-0.02233006,-0.01253157,0.03059682,-0.02467115,-0.00798609,0.06466704,-0.01281029,-0.00975696,-0.03641798,-0.00040258,0.01381208,-0.02095103,-0.01842209,0.04183742,0.02826259,-0.02544433,-0.03659421,0.1608872,0.01407731,-0.01844878,0.02533832,0.0373033,-0.04193009,-0.09549059,0.03186011,-0.00937314,0.03282201,0.01813236,0.03049419,0.05074385,-0.01425255,0.05314952,0.01816812,0.01079924,0.02423625,0.02448496,-0.00728045,-0.04964613,-0.04270259,-0.05883912,0.07296114,0.02341492,-0.28826264,-0.01514726,-0.02283879,0.00194504,0.06549648,0.01835658,0.06620793,0.02025741,-0.06148677,0.02322322,0.00211194,0.0532201,0.00411553,-0.05615308,-0.00008535,-0.01012359,0.04245568,-0.00772267,0.0149959,0.05088234,-0.00928901,0.07219771,0.22083412,-0.01033127,0.04596933,0.01874841,-0.04158195,0.05215085,0.06669719,0.0252585,-0.0464889,-0.01777153,0.04432403,-0.03674315,0.03132578,0.04711784,-0.00102373,-0.00564462,-0.00078406,0.01427062,-0.02489375,-0.02621672,-0.00291805,0.03765646,0.12977731,-0.03540868,-0.04698468,-0.08302038,0.01276625,0.05624528,-0.05147672,-0.01777038,0.02127112,0.02927508,-0.00648587,0.0609801,0.0429134,-0.03561619,-0.08486392,-0.00057098,-0.03302397,-0.02222817,-0.04123047,0.06597587,0.01663281],"last_embed":{"hash":"12hbk9p","tokens":497}}},"text":null,"length":0,"last_read":{"hash":"12hbk9p","at":1751815145884},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.1. “实验性与未经审计”的现实**","lines":[175,184],"size":508,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"12hbk9p","at":1751815145884}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.1. “实验性与未经审计”的现实**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07402132,-0.03428284,-0.05702884,-0.02710058,0.0280593,0.01718942,-0.00013089,-0.00608299,0.01176088,-0.0440857,0.05068695,-0.06547307,0.04362023,0.01259481,0.0282672,-0.00659627,0.02923752,-0.05079168,-0.0240967,0.02992335,0.09401479,-0.06013761,0.0238301,-0.00974613,0.077284,0.03471091,0.00961133,-0.03302753,-0.03848714,-0.1708069,0.01471436,-0.06752647,-0.00331098,0.01555814,0.03005267,-0.03582015,-0.01546167,0.05564979,-0.04947181,-0.0039741,0.01421821,0.02592015,0.00810559,-0.01826276,0.04240165,-0.07027512,0.02957338,-0.07077241,-0.00003109,-0.06121299,-0.02785241,-0.07576853,-0.01094257,0.04346989,0.01559231,0.01714421,-0.00088781,0.06387461,0.02061464,0.02774554,0.08333855,0.05437835,-0.18175949,0.0133858,0.09210061,0.0225077,-0.01744649,0.00817158,0.06266537,0.07403797,0.01358808,0.00572239,-0.0201222,0.06375713,0.03028653,0.0056184,0.01297818,-0.00228989,-0.04635471,-0.0542536,-0.0533109,0.07843952,0.02441951,0.02118288,-0.04275696,-0.00588607,-0.00084926,-0.05404314,-0.04269056,0.01006192,-0.0015221,0.00880649,0.05158532,-0.00033749,-0.02600603,-0.01310768,-0.00663138,0.0263096,-0.07520854,0.12101416,-0.03818204,0.0438635,-0.01588363,-0.04413339,0.04525464,0.00349706,-0.0036465,-0.07038255,0.01356874,0.02415948,0.02058452,0.01912977,0.06794779,-0.04976563,0.03059161,0.01496621,0.010569,-0.00547522,-0.02096652,0.01531367,-0.0014832,0.05403782,0.04756418,-0.03699457,-0.03116535,-0.01205577,0.00224957,0.03497934,0.05324103,0.04475606,0.01904451,0.03296762,-0.05233624,-0.01649787,-0.03007422,-0.03048441,-0.03637122,0.01258616,-0.04030302,-0.05727002,-0.01931731,-0.05547238,-0.01507054,-0.11235602,-0.07376795,0.06414674,-0.01357518,0.01499714,-0.00646392,-0.04834181,0.01739231,0.0777359,-0.03396894,-0.06265196,-0.03010227,0.00702882,0.03233542,0.11078211,-0.07771236,-0.02180895,-0.01285606,-0.02920034,-0.04291226,0.13711485,0.0302752,-0.10211597,0.05535485,0.03734158,-0.01254036,-0.07993145,0.0174175,0.01698983,-0.0669513,0.0085764,0.05796354,-0.02444277,-0.02821729,-0.01828113,0.00881282,0.04030058,0.00353113,-0.035023,-0.09524313,-0.00920451,-0.02828907,-0.0197525,-0.01825568,0.04607547,0.01345537,0.05229102,-0.10263044,0.03424163,0.0160562,0.0361812,-0.0449355,-0.02020273,-0.03034455,-0.02498236,0.00366936,-0.05311343,0.14116888,0.03364591,0.00566069,0.00645891,-0.0741557,0.00970372,-0.02298295,-0.06014699,0.01152623,0.04938338,-0.01594602,0.04422658,-0.05207533,0.04897547,0.00305833,0.0152432,0.02440775,0.05236617,-0.01106532,0.06181294,0.02533695,-0.00183627,-0.09556589,-0.18075892,-0.04170743,0.02562431,-0.02634486,0.05339127,-0.0114221,0.0243212,0.0268354,0.04203109,0.05860176,0.08353411,0.06844632,-0.06814701,0.01466469,-0.03096309,0.0639597,0.04972273,-0.0180227,-0.00976261,0.04108025,-0.03037056,0.03633912,0.01522179,-0.05821869,0.04104874,-0.0613656,0.10563745,-0.03337113,0.03702796,-0.01013149,0.05310363,-0.00244238,-0.0393146,-0.16177708,0.03673923,0.05702438,-0.04117309,-0.00833206,-0.05662668,-0.03540707,-0.00053265,0.00556351,0.04562393,-0.11107407,0.03178416,-0.07979478,-0.06249459,-0.00852912,-0.01550598,0.01663088,0.0128396,0.03878542,0.01097921,0.11434496,-0.02155425,-0.01275799,0.03069563,-0.02668296,-0.00783086,0.06588877,-0.01477029,-0.00944882,-0.03855875,-0.0039462,0.01379897,-0.02314245,-0.0180933,0.03987187,0.02956875,-0.0285523,-0.03351537,0.15718025,0.01226922,-0.01398541,0.0231488,0.03699529,-0.04251971,-0.09266999,0.02990538,-0.00918655,0.03006207,0.015786,0.03158692,0.05097495,-0.01606268,0.05389239,0.02045846,0.00940362,0.02200907,0.02241176,-0.00312241,-0.0503985,-0.04101336,-0.05668585,0.07351641,0.02439279,-0.28821787,-0.01345356,-0.02191534,0.00302504,0.06621695,0.02099024,0.06585365,0.02578998,-0.06010459,0.02138623,0.00018762,0.05136673,0.00123874,-0.05499594,-0.00047552,-0.01097729,0.04248806,-0.00942983,0.01515648,0.04775719,-0.00668549,0.07725395,0.21983257,-0.01082675,0.04569914,0.01892586,-0.04347096,0.05286353,0.06565522,0.02651631,-0.04706654,-0.01932625,0.04093082,-0.03878425,0.03416469,0.04601355,-0.0021842,-0.00689943,0.00011433,0.01184172,-0.02473015,-0.02914025,-0.00025142,0.03850579,0.1306407,-0.03734315,-0.04641106,-0.08310711,0.01559961,0.05879064,-0.05236144,-0.01700954,0.02461719,0.02974435,-0.00754737,0.05809034,0.04093058,-0.03811974,-0.08861063,-0.00088535,-0.03373289,-0.02208097,-0.04059456,0.06463005,0.01588824],"last_embed":{"hash":"1vghwzy","tokens":494}}},"text":null,"length":0,"last_read":{"hash":"1vghwzy","at":1751815145917},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.1. “实验性与未经审计”的现实**#{1}","lines":[177,184],"size":479,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1vghwzy","at":1751815145917}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.2. 潜在攻击向量**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06080697,-0.01093105,-0.07500467,-0.05011301,-0.00469361,0.01900665,-0.01372463,0.02631272,0.00494836,-0.03086121,0.08106766,-0.06574667,0.00916871,0.0265158,0.0169555,-0.02926772,0.02413647,-0.0483615,-0.0167166,0.02712944,0.068561,-0.06284083,0.05445311,-0.03172907,0.06813361,0.05038092,0.02756209,0.00093861,-0.04272908,-0.18794249,-0.00634339,-0.05759354,0.00250653,0.01345003,0.00106091,-0.05157617,-0.0211033,0.05537358,-0.05684209,-0.00435834,0.00917795,0.01658138,-0.00997986,-0.00821928,0.05455542,-0.10663992,0.03486189,-0.09059596,-0.01162107,-0.05036459,-0.0160043,-0.08845914,-0.00429212,0.02820397,-0.00163559,0.00043375,0.0028972,0.06150879,0.02731604,0.03674389,0.040298,0.03941988,-0.18429723,0.02242014,0.09211172,0.00654518,-0.02993404,0.01941535,0.06255866,0.07470987,0.01969994,0.03600367,-0.00876529,0.04862694,0.02649453,-0.00389924,0.01854456,-0.01302113,-0.02401351,-0.07645798,-0.03424333,0.06631938,0.00995203,0.0004702,-0.00382963,-0.01142723,-0.00583042,-0.03507347,-0.02307892,0.00932113,0.00736903,0.00794975,0.02939109,0.00056577,-0.01346448,0.0150216,0.00267237,-0.00192567,-0.08677825,0.10946155,-0.03618941,0.04817155,-0.00797247,-0.03527581,0.04648106,-0.00183625,-0.01597822,-0.08125543,0.0224108,0.00731699,0.02741022,0.01870342,0.08485269,-0.03865902,0.03025371,-0.0001072,0.01307455,-0.00678523,-0.02192768,0.02859261,0.00168789,0.03480567,0.04403012,-0.01115619,-0.01840335,-0.02742304,-0.02207697,0.04471267,0.05969496,0.01028012,0.02340893,0.02340979,-0.05617503,-0.00440695,-0.04239246,-0.01662938,-0.02111798,0.01144079,-0.03550646,-0.06035118,-0.02857869,-0.06205594,0.02296253,-0.11184511,-0.07979629,0.0839797,-0.00572585,0.00807809,0.01143605,-0.04564794,0.01969628,0.07625477,-0.03627082,-0.0651472,-0.01797247,-0.01354393,0.02129829,0.11212838,-0.08147301,-0.00054491,-0.01442001,-0.00449176,-0.03022179,0.15528826,0.02091605,-0.09597121,0.05803075,0.01940597,-0.0208311,-0.06623554,0.00229673,0.03615466,-0.04830394,-0.00067919,0.08560808,-0.01825788,-0.02841022,-0.02808873,0.01460413,0.06920348,0.01335811,-0.01489855,-0.08706144,-0.01648921,-0.01116232,-0.01850095,-0.04107639,0.01060133,0.02165833,0.03615545,-0.09964525,0.03202934,0.00774732,0.00441867,-0.03639033,-0.00226642,-0.03819198,-0.01266559,0.02928387,-0.0623578,0.14719237,0.03921357,-0.00611535,0.01617453,-0.05270184,0.01089054,-0.04768974,-0.05296794,-0.00591976,0.03795575,-0.00069281,0.01990951,-0.03426204,0.02840754,-0.00760141,0.01830404,0.01101402,0.03783716,-0.00115011,0.0672345,0.03138293,0.00612829,-0.08798975,-0.18546419,-0.04438262,0.02256423,-0.0300163,0.01527547,0.01380788,0.06038254,0.0022977,0.06068748,0.0582213,0.09496471,0.0561647,-0.06816887,-0.0081582,-0.02202513,0.07363325,0.05990712,-0.01392333,-0.00866357,0.03309055,-0.05167536,0.01960922,0.03836778,-0.03839023,0.06112364,-0.06473681,0.08746347,-0.03395022,-0.00292244,-0.00035158,0.08120926,0.02982492,-0.0366071,-0.1427104,0.0188219,0.05683552,-0.01158536,0.00685577,-0.05098931,-0.01158207,-0.01843777,0.01392315,0.05712445,-0.11232682,0.01929178,-0.07186466,-0.05298039,-0.01352426,0.00773995,0.01619429,0.00044644,0.04541502,0.04248092,0.1041416,0.01374852,-0.03830473,0.02338032,-0.0117606,-0.0178979,0.042266,-0.03954852,-0.01514923,-0.06288292,-0.01492637,0.03482962,0.00280707,0.00509391,0.03457565,0.02311602,-0.02802118,-0.02950904,0.14059158,0.02611578,-0.02549945,0.03616014,0.02902411,-0.00975071,-0.07668642,0.02565737,-0.00509395,0.03101606,0.00703624,0.0273319,0.05032764,-0.0153346,0.04671798,0.00949487,0.00892683,0.00209779,-0.00075939,-0.01365624,-0.04406849,-0.03710938,-0.03146272,0.08390865,0.0177446,-0.29517987,-0.00983127,-0.03896656,-0.00939213,0.06870434,0.04186412,0.07449503,0.01714406,-0.07668193,0.03361723,-0.02995452,0.05706513,0.0027269,-0.05370073,-0.00069497,-0.00410741,0.04778475,-0.01098282,0.01059697,0.03552666,-0.00334414,0.08079139,0.22216882,-0.02500504,0.03882391,0.01599322,-0.02912398,0.06213661,0.05657706,0.0305108,-0.04780775,-0.02164256,0.04290534,-0.04407967,0.023908,0.04277121,-0.00380409,-0.03082208,0.01391365,0.02010124,-0.01477442,-0.03280695,-0.01866852,0.04242664,0.13125059,-0.01818068,-0.04765873,-0.07277459,0.02806381,0.05969915,-0.0774622,-0.04513609,0.01458249,0.05182973,0.00445395,0.08232629,0.02092244,-0.04325039,-0.08740111,0.00132638,-0.02218356,-0.014725,-0.03878611,0.06011293,0.00491512],"last_embed":{"hash":"1dzuo5x","tokens":411}}},"text":null,"length":0,"last_read":{"hash":"1dzuo5x","at":1751815145951},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第五部分：安全状况与风险评估**#**5.2. 潜在攻击向量**","lines":[185,195],"size":441,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1dzuo5x","at":1751815145951}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05625542,-0.01872133,-0.06979655,-0.04462358,0.02613778,-0.00982008,-0.02466056,0.01711178,-0.00505853,-0.04220209,0.07014323,-0.06319901,0.0259517,0.02564001,0.03260984,-0.02405925,0.0263717,-0.05090562,-0.02882439,0.02078589,0.07155523,-0.08210383,0.04518978,-0.03210397,0.07170862,0.03755619,0.0053608,-0.01670753,-0.04968511,-0.17879701,0.01093075,-0.06830731,0.00916598,0.02465592,-0.00086702,-0.01636065,0.00445111,0.06285544,-0.0374658,-0.00515815,0.00495035,0.00746697,0.00085253,0.02184774,0.04756079,-0.09525786,0.02817972,-0.05343541,-0.01883463,-0.03798776,-0.02360682,-0.06353649,-0.0037421,0.02808256,-0.01031098,0.02628134,0.02019433,0.06976201,0.02071683,0.04226308,0.05966388,0.03700088,-0.18779035,0.02919962,0.09606542,0.00108235,-0.04435002,0.02051767,0.05358063,0.07649312,0.01755291,0.01033425,0.00417017,0.05307269,0.01277495,-0.00229512,0.03494312,0.00565867,-0.01611469,-0.07011172,-0.03752574,0.08772483,0.00919008,0.00306385,-0.0094793,-0.01782232,0.02175942,-0.03851256,-0.03881415,0.01272333,-0.01227723,0.00986924,0.07068762,-0.01768571,-0.05130773,0.00578865,-0.02127315,0.02676991,-0.08039008,0.11142614,-0.04129842,0.03654312,-0.00713592,-0.0588246,0.04195701,-0.01356167,-0.00193575,-0.07074659,0.01860176,0.03471321,-0.01608795,0.01874609,0.0650382,-0.0227588,0.03691872,-0.00217745,0.00548529,-0.01171925,-0.03654797,0.03007271,-0.0108096,0.06316732,0.0632428,-0.01676127,-0.0256988,-0.01378519,0.00555233,0.03747222,0.05944698,0.02343242,0.01828285,0.04985325,-0.04668436,0.00501422,-0.0425007,-0.01232874,-0.02442953,0.00661765,-0.05159204,-0.04114036,-0.04156422,-0.09021857,-0.01153117,-0.10553773,-0.08356728,0.07460235,0.0012937,0.00487446,0.00453378,-0.03469904,0.0198793,0.07573564,-0.04872808,-0.06526186,-0.01220341,-0.00760437,0.03850593,0.09441549,-0.0509297,0.00895297,-0.01695138,-0.03140095,-0.04249603,0.14366667,0.02202981,-0.12895112,0.04302939,0.04275478,-0.00661277,-0.06792959,0.00899523,0.02202693,-0.04712631,-0.01952267,0.07156413,-0.04920445,-0.03341967,-0.0119754,0.03705003,0.06235848,0.0022736,-0.00378303,-0.07780254,-0.01695019,-0.02314375,0.00018171,-0.02515604,0.02049831,0.02915906,0.04609426,-0.09729966,0.04738548,-0.00358881,0.01324126,-0.03707881,-0.02448415,-0.02444459,-0.00633941,0.01792219,-0.05712877,0.13293548,0.0299158,0.00194289,0.02039929,-0.05061682,-0.00257581,-0.01512274,-0.04351391,-0.00649802,0.04477385,-0.02458017,0.03743657,-0.02597092,0.04525883,-0.00461848,0.03388559,0.03001556,0.01631921,-0.01878142,0.07080474,0.01186305,0.0161372,-0.10132368,-0.17400581,-0.03496458,0.05892047,-0.03540669,0.00846256,0.00674834,0.03245578,0.02012363,0.06014764,0.08865361,0.10727213,0.05127351,-0.04939979,-0.00935397,-0.02038214,0.06210464,0.05063965,-0.01659085,-0.0029491,0.0205312,-0.05685633,0.01916801,0.02317607,-0.04752491,0.05738871,-0.07144991,0.08526462,-0.01077439,0.01890748,0.00597038,0.09068481,0.0115337,-0.0347547,-0.14881784,0.02660286,0.07134233,-0.00314177,-0.03689227,-0.04836755,-0.02719713,-0.01537372,0.0256533,0.05707452,-0.10646968,0.03044249,-0.08110017,-0.05220556,-0.03166831,-0.01796792,0.02015123,0.01717314,0.04296984,0.00525552,0.12651199,-0.02196718,-0.0149256,0.03532957,-0.04521586,-0.01147926,0.04458926,-0.00421105,0.00342855,-0.03510253,-0.00178963,0.02803509,-0.00694148,-0.01058256,0.02119726,0.02884166,-0.03926265,-0.03049224,0.13451068,0.01987073,0.01685764,0.03528451,0.02332548,-0.04628883,-0.09959467,0.05317828,0.00136385,0.01535909,0.00709146,0.03105081,0.04005711,0.00393507,0.05766031,0.01265861,-0.03497582,0.02666757,0.00146248,-0.01450976,-0.02251727,-0.03952299,-0.02575467,0.07195961,0.01470302,-0.30334073,-0.02170495,-0.02949646,-0.01913638,0.05190651,0.05607513,0.07927305,0.01805009,-0.07507447,0.02698474,0.01354681,0.06363791,-0.0058445,-0.04663214,0.01693823,0.01701179,0.04660393,0.00987632,0.00345605,0.03302011,0.0032771,0.07282619,0.21672481,-0.0355492,0.04154313,0.03342367,-0.0293692,0.04399028,0.03362326,0.00788494,-0.04831386,-0.02295911,0.0344845,-0.03699389,0.01341709,0.02785606,0.00471835,-0.02305616,-0.01620673,0.00814664,-0.03503936,-0.02889098,-0.01172687,0.05390099,0.10725692,-0.02447846,-0.06824127,-0.06065441,-0.00415453,0.05770725,-0.05712619,-0.03463837,0.00411734,0.05435438,-0.02270553,0.06522316,0.03836985,-0.05916528,-0.0872352,-0.02101567,-0.02961578,-0.03592264,-0.04477604,0.07224211,0.02299858],"last_embed":{"hash":"1o9gzdq","tokens":447}}},"text":null,"length":0,"last_read":{"hash":"1o9gzdq","at":1751815145976},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**","lines":[196,216],"size":859,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1o9gzdq","at":1751815145976}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.1. 官方渠道与开发者资源**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05314122,-0.02467749,-0.07357615,-0.03319847,0.02331737,-0.005997,-0.0223329,0.01873554,-0.00548854,-0.04258915,0.0773316,-0.05496707,0.01981917,0.02903593,0.03613866,-0.01929387,0.0207567,-0.05368385,-0.02790967,0.01694227,0.07414779,-0.08094954,0.042316,-0.02153033,0.07133108,0.04452006,-0.00255883,-0.01704616,-0.04720364,-0.17903388,0.01160714,-0.06577706,0.00563953,0.02083607,0.00264099,-0.01582682,-0.00132204,0.05841676,-0.03959832,-0.01204854,0.00369014,0.0082255,0.00383269,0.02679074,0.04569583,-0.09617518,0.03017045,-0.04882174,-0.01857044,-0.04605762,-0.01566614,-0.06600826,-0.00641462,0.02829231,-0.0101814,0.03117559,0.02661002,0.06826905,0.02664288,0.04084014,0.06332153,0.03647669,-0.18328243,0.02642962,0.10002636,0.00577701,-0.04304388,0.02379603,0.04658931,0.08003183,0.02132946,0.0106725,-0.00088975,0.05413406,0.00965371,-0.00168074,0.03741457,0.00114772,-0.01838403,-0.0656685,-0.03651549,0.0829355,0.00787744,0.00523037,-0.0060197,-0.01519044,0.02800197,-0.03012922,-0.03012455,0.00956571,-0.00966859,0.01261941,0.076384,-0.01761012,-0.050185,0.00600979,-0.01504137,0.02485849,-0.07280162,0.11262254,-0.03389107,0.04046236,-0.00317349,-0.05135786,0.04520274,-0.00650729,-0.00315591,-0.06435386,0.01978525,0.02967373,-0.00612934,0.01649845,0.06465516,-0.02450827,0.03292225,-0.00860712,0.00890757,-0.01046042,-0.03848,0.03102583,-0.01072782,0.06100695,0.06654067,-0.01174242,-0.02526765,-0.01806464,0.00783391,0.03892693,0.05752705,0.02891826,0.02067854,0.05114786,-0.04915412,0.00733333,-0.04027431,-0.00865893,-0.02075657,0.00634336,-0.05280519,-0.04256162,-0.0430567,-0.08983392,-0.01686993,-0.10445525,-0.0801912,0.08086528,0.0052971,0.01167853,-0.00387306,-0.03535042,0.02001603,0.07355744,-0.04893421,-0.06658139,-0.0151252,-0.00497174,0.03798828,0.09270129,-0.04903281,0.00632633,-0.01441603,-0.03187903,-0.04205088,0.14560954,0.02782685,-0.12958911,0.04176876,0.04244537,-0.00223991,-0.0697635,0.00700049,0.02688815,-0.05060543,-0.02664925,0.06788043,-0.05025261,-0.04392834,-0.01038804,0.04110778,0.06355403,0.00078958,-0.00378741,-0.075732,-0.02031377,-0.02719558,-0.0001201,-0.02783173,0.01935442,0.02204519,0.04886212,-0.10267793,0.03859504,-0.00410047,0.02079018,-0.04036205,-0.02597756,-0.01896947,-0.00467074,0.00837902,-0.06062218,0.13738227,0.03502152,0.00139008,0.02609325,-0.04666098,-0.00873211,-0.00957428,-0.03750155,-0.008899,0.04952304,-0.02234318,0.04143617,-0.02466354,0.04736586,-0.00773781,0.03716409,0.03019845,0.01321252,-0.0139578,0.07115657,0.02187039,0.01193818,-0.10198957,-0.16723908,-0.03340475,0.05262409,-0.0367171,0.01083874,0.00003968,0.03595017,0.02191104,0.05643689,0.08586253,0.1092958,0.0525247,-0.05424469,-0.00710686,-0.02468193,0.06430282,0.04511252,-0.016292,-0.0034841,0.0197134,-0.05998028,0.01075423,0.01925936,-0.05136089,0.05724293,-0.07249915,0.08509016,-0.01071635,0.0187846,0.00719591,0.09145838,0.00864202,-0.04241288,-0.15421599,0.02508266,0.07054392,-0.00386787,-0.04316493,-0.0481317,-0.03408229,-0.01050553,0.01823868,0.05199141,-0.10561924,0.03583577,-0.07829656,-0.05188777,-0.02306201,-0.00914729,0.01394896,0.01207025,0.04518316,-0.00058475,0.13058616,-0.02114821,-0.01297821,0.03284842,-0.0406704,-0.01488296,0.04523537,-0.0047905,0.00661053,-0.03631833,0.00696451,0.03312842,-0.00887487,-0.01428647,0.01650408,0.03400168,-0.04418421,-0.02428881,0.13840744,0.01241983,0.01387159,0.02426871,0.0291633,-0.04796604,-0.10594052,0.05121839,0.0022437,0.00855873,-0.0026069,0.02932729,0.04181015,0.00624368,0.06395051,0.0079994,-0.04183825,0.01915236,0.0025962,-0.00981641,-0.02204225,-0.0473573,-0.03044917,0.07460485,0.01240714,-0.29821366,-0.02519189,-0.02803494,-0.02533813,0.04792042,0.05580635,0.0791083,0.01847818,-0.07637714,0.02592854,0.0127641,0.06143748,0.00094152,-0.04523764,0.01774959,0.02088028,0.04725494,0.01098039,0.00164288,0.03319304,0.0007122,0.07813334,0.21280293,-0.02422619,0.03852272,0.02664726,-0.03293394,0.03981122,0.04011776,0.01103108,-0.05684852,-0.02700757,0.0291086,-0.04704085,0.01406747,0.02817059,0.00433614,-0.02501896,-0.0178012,0.01282609,-0.04048153,-0.02709274,-0.00341615,0.04993194,0.09694458,-0.03180072,-0.06242273,-0.06779087,0.00068416,0.05734985,-0.06958735,-0.03243276,-0.00078814,0.05301606,-0.01705535,0.06691828,0.03777391,-0.05994599,-0.09631114,-0.01868932,-0.02545224,-0.03747919,-0.04389104,0.06655002,0.02155533],"last_embed":{"hash":"1kg0rs8","tokens":312}}},"text":null,"length":0,"last_read":{"hash":"1kg0rs8","at":1751815146003},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.1. 官方渠道与开发者资源**","lines":[198,204],"size":365,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1kg0rs8","at":1751815146003}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.2. 开发者采纳的障碍**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06578868,0.00471622,-0.04217592,-0.05876863,0.06503838,0.02740991,-0.00604685,0.01815799,0.00326239,-0.02781844,0.04673776,-0.0992506,0.04431484,0.0192682,0.0116174,-0.03685229,0.02414954,-0.04478082,-0.04516877,0.03341765,0.07923885,-0.05989963,0.02419992,-0.02119243,0.08058549,0.00313487,0.01597348,-0.0320661,-0.03884804,-0.18159373,0.00514956,-0.05654893,0.02891283,0.0348753,0.00084019,-0.04702368,0.01372735,0.06554622,-0.04149854,0.0276509,0.00350457,-0.00793368,0.0179963,0.00637492,0.04118145,-0.06423608,0.04320177,-0.08259296,0.02193171,-0.03530695,-0.03782214,-0.07091576,0.0010652,0.02154203,0.02565885,0.02751049,0.00117859,0.03866773,0.02347194,0.04741159,0.06863089,0.06584025,-0.19909918,0.02528978,0.08126314,-0.00085394,-0.04151327,0.02011504,0.06839044,0.0890415,0.01373731,0.01062087,0.00776388,0.05367315,0.0423829,0.01397962,0.01355801,0.01479685,-0.0053059,-0.05227137,-0.07338217,0.08441933,0.00340498,-0.01186773,-0.05978445,-0.01076119,-0.01426698,-0.06339535,-0.05585094,0.03096116,-0.0040622,0.01344557,0.01296036,0.02014241,-0.04085788,-0.02054969,-0.03431443,-0.00461376,-0.07865908,0.12675679,-0.05335224,0.02984427,0.00539446,-0.04868311,0.03411118,-0.01017354,-0.00743638,-0.04956953,0.00415844,0.032449,0.01361201,0.04386643,0.0459467,-0.02359621,0.04370295,0.01510913,-0.00638349,-0.00361261,-0.02240305,0.02657602,-0.01060612,0.06748899,0.02059434,-0.02780721,-0.04792554,-0.01771934,0.00419275,0.05734357,0.0409214,0.01397478,0.00393877,0.05279225,-0.06922143,-0.02726028,-0.04856948,-0.02099661,-0.01030442,0.01426241,-0.0319672,-0.00184554,-0.00788628,-0.07710329,-0.0248479,-0.12089068,-0.06365289,0.0645619,0.00119018,0.00075909,0.02432736,-0.01963868,0.02079632,0.07246155,-0.05297318,-0.06300852,-0.02565156,-0.01465594,0.0372752,0.1173134,-0.06496644,0.000919,-0.04751695,-0.02971043,-0.0205565,0.128328,0.00432424,-0.10502023,0.04387201,0.03811631,-0.01809989,-0.08352183,0.0129505,0.02533663,-0.04493721,0.01434576,0.08497918,-0.01851291,-0.00434019,-0.03060868,0.01923952,0.04239833,0.0272771,-0.03457442,-0.07942885,-0.02798668,-0.0309567,-0.0248123,-0.02403225,0.03280956,0.04225561,0.06687441,-0.06890942,0.07018485,-0.00467387,0.00138557,-0.03233976,-0.00144389,-0.03291536,-0.02614457,0.04704595,-0.03694615,0.10791722,0.01092535,0.01180414,0.00894934,-0.06782517,0.03486159,-0.03891864,-0.07234205,0.02628681,0.0066813,-0.0355242,0.02041834,-0.0384708,0.05021746,-0.02043742,0.02149081,0.03371044,0.0386082,-0.03944728,0.05468468,0.01693835,0.01738013,-0.08231653,-0.19523127,-0.03606327,0.0800086,-0.02157303,0.01482957,0.00761765,0.03379492,0.03310395,0.07828217,0.09071414,0.09471712,0.06542116,-0.05915263,-0.01448392,-0.03056835,0.06220641,0.03866808,-0.02984936,-0.00774356,0.04016658,-0.02435681,0.0559246,-0.00842593,-0.04268979,0.05591014,-0.06779415,0.0985733,-0.00652486,0.03295207,-0.03489745,0.07143451,0.02683294,-0.02686441,-0.13641596,0.03121576,0.05997043,-0.02524976,-0.03419546,-0.05376838,-0.02744146,-0.01508551,0.01315116,0.03935247,-0.09759843,0.00592031,-0.06718096,-0.05061134,-0.059119,-0.01404265,0.02710937,0.01175149,0.02895873,0.04850135,0.10530635,-0.02095065,-0.02569012,0.02356145,-0.04328787,-0.01119465,0.05182847,-0.00252461,0.00080033,-0.03579912,-0.01339799,0.01695116,-0.01250594,-0.00382779,0.02464322,0.00809214,-0.03289601,-0.03419752,0.10327616,0.03350283,0.00020854,0.03229104,0.01036597,-0.03102208,-0.09013253,0.03938175,0.00521985,0.05167792,0.02222009,0.04645533,0.04873644,-0.01706399,0.02560601,0.01554529,0.00110932,0.03202277,0.00418754,-0.01782893,-0.02261783,-0.03202584,-0.03631563,0.06268571,0.02096204,-0.2994222,-0.01413803,-0.00267518,0.00872712,0.07340194,0.0466461,0.05439057,0.04089346,-0.07570206,0.02655123,0.02208629,0.06009326,-0.00710705,-0.06553748,-0.00999191,-0.01311432,0.05007637,-0.01302742,0.02077521,0.03381264,0.00748044,0.05084884,0.21417838,-0.04667452,0.06268957,0.03175468,-0.0439204,0.03114097,0.0008012,0.01091937,-0.03418873,-0.00624091,0.03758575,-0.02284651,0.0244543,0.00999172,-0.00910478,-0.00142185,-0.01887548,0.00926228,-0.04657608,-0.03028472,-0.04389428,0.03371359,0.12832817,0.01805006,-0.07322314,-0.05980392,-0.00789836,0.04669535,-0.05002062,-0.03816766,0.03111807,0.05208496,-0.02375483,0.0723456,0.04513677,-0.05240516,-0.06727124,-0.00302402,-0.03396508,-0.01821935,-0.05760181,0.06785922,0.02644966],"last_embed":{"hash":"2uxzkp","tokens":242}}},"text":null,"length":0,"last_read":{"hash":"2uxzkp","at":1751815146015},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.2. 开发者采纳的障碍**","lines":[205,208],"size":206,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"2uxzkp","at":1751815146015}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.3. 社区情绪与目标受众**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07653893,-0.0305382,-0.04019454,-0.01668516,0.04388727,0.02512481,-0.00277946,0.00842019,0.03320372,-0.02361622,0.07049959,-0.07443754,0.05142569,0.01757408,0.02820539,-0.01681676,0.0070536,-0.04370973,-0.02655888,0.00076871,0.0905369,-0.07280451,0.02487168,0.00051541,0.05484599,0.03396209,-0.0020283,-0.03381238,-0.03896517,-0.17027743,0.00179797,-0.03988541,0.0253745,0.01317318,0.03757534,-0.02934023,0.00097632,0.06452139,-0.04729899,-0.01056411,0.00212826,0.01646825,0.01845774,0.02175428,0.04996091,-0.05528476,0.02043961,-0.0533355,-0.00734674,-0.06861246,-0.03172167,-0.07754738,-0.00099211,0.03346559,-0.00176832,0.02715784,0.02234781,0.05411996,0.04438458,0.02387794,0.07939621,0.04727569,-0.1974414,0.02773816,0.1042468,-0.00228917,-0.02922684,0.00973117,0.05386639,0.0764717,0.02056492,0.02080585,-0.00378342,0.05599315,0.0117451,0.01314634,0.00902733,0.01428558,-0.01057634,-0.03775363,-0.0561505,0.08535605,0.01420388,0.00255691,-0.03468811,-0.00787379,-0.00058789,-0.02702794,-0.04530983,0.01848559,-0.00333042,0.02079317,0.03200169,0.00695572,-0.02077295,-0.01402416,-0.00815033,-0.00434456,-0.06832204,0.12432288,-0.04628262,0.03476735,0.00077277,-0.04182496,0.04172719,0.01057281,-0.00749716,-0.05515278,0.01097,0.02155367,0.00221338,0.02087271,0.06708751,-0.05521268,0.05263705,-0.00062411,0.00452132,-0.00589568,-0.03718923,0.01066057,-0.00840631,0.0549229,0.03350417,-0.02737758,-0.02022184,-0.02552264,0.02072719,0.0468589,0.06031452,0.04100953,0.01013692,0.05947153,-0.06373651,0.00366207,-0.04827658,-0.00113298,0.00105207,-0.01335236,-0.02003508,-0.04273521,-0.02547145,-0.0629762,-0.01438444,-0.10254297,-0.05372224,0.10251064,-0.00379647,0.03915054,-0.02600294,-0.04164162,0.01991885,0.0916777,-0.03026639,-0.07343259,-0.03142606,0.0040324,0.03173843,0.12726644,-0.06799448,-0.02882062,-0.01997974,-0.02685846,-0.03670231,0.09988425,0.04995097,-0.12396475,0.0435362,0.04329278,-0.00339447,-0.06352839,0.0031349,0.01818644,-0.06583922,-0.00028325,0.07292877,-0.03690275,-0.02416383,-0.00713214,0.02296456,0.05841357,0.02269668,-0.03557752,-0.065534,-0.03743581,-0.03209414,-0.02822631,-0.02352279,0.03371895,0.03588034,0.05636535,-0.1140388,0.05172492,0.01884449,0.0439898,-0.03643063,-0.00697597,-0.0326853,-0.00956531,0.00665112,-0.05219236,0.15192886,0.02855244,-0.00523365,0.01687583,-0.05791966,0.03040986,-0.01087388,-0.04527951,0.00696789,0.03385219,-0.02899409,0.02669794,-0.03807668,0.05605344,-0.03097603,0.00138527,0.02347906,0.03540422,-0.02290766,0.05614416,0.02479433,0.0199529,-0.10498461,-0.19090796,-0.03569242,0.04571169,-0.02428076,0.01472957,-0.00562882,0.02361533,0.0294751,0.04365359,0.08339221,0.09540895,0.05844128,-0.06307835,0.00295675,-0.02683755,0.04285876,0.03912706,-0.01499477,-0.01630946,0.02062727,-0.04613409,0.02957026,-0.00548936,-0.05388932,0.05532859,-0.04165153,0.09953137,-0.04002817,0.02748042,-0.00727526,0.07745782,0.00904602,-0.05616267,-0.15723513,0.03008506,0.05331326,-0.00905736,-0.03570195,-0.05769879,-0.03869462,-0.02441346,0.00276563,0.03541949,-0.10688365,0.00487571,-0.06794894,-0.05087543,-0.03752413,0.01321532,0.00656841,-0.00474521,0.04280727,0.02323748,0.11809082,-0.01279684,-0.00351742,0.01163729,-0.01755627,-0.02154432,0.05801268,0.00566874,-0.00074902,-0.04792475,0.02917616,0.02250386,-0.01079605,-0.02478486,0.03939493,0.01038037,-0.00098002,-0.03064888,0.13839133,0.02773784,0.01261489,0.02411735,0.01393381,-0.03260878,-0.09658167,0.039225,-0.01232692,0.01636654,-0.0174801,0.03145741,0.05179985,-0.02117315,0.05522089,0.03221278,-0.034706,0.02624466,0.01425571,-0.01110771,-0.03911131,-0.05957337,-0.05498805,0.08313693,0.00662067,-0.30099162,-0.01690395,-0.00822146,-0.0239291,0.0700791,0.03539856,0.06114474,0.0190858,-0.07269683,0.02787348,0.00143533,0.06552909,0.00019226,-0.06856854,0.01536088,-0.01205553,0.05318515,-0.00407978,0.02042339,0.04297159,-0.01644845,0.06653797,0.20992249,-0.02341234,0.03842944,0.01579962,-0.04620859,0.02710354,0.02985565,0.02986481,-0.0474276,-0.0236629,0.0259793,-0.05069144,0.02039356,0.03314762,-0.01360724,0.00005599,-0.01058854,0.02440494,-0.0424246,-0.02067689,-0.03758336,0.02379847,0.10804475,-0.01240133,-0.0684895,-0.08033857,0.01113552,0.04592042,-0.04713319,-0.02517855,0.0121065,0.05387585,0.00751056,0.07122869,0.03917572,-0.05814518,-0.09582973,-0.00684268,-0.02462063,-0.0256293,-0.04584013,0.05796384,0.02323447],"last_embed":{"hash":"1qpqvea","tokens":269}}},"text":null,"length":0,"last_read":{"hash":"1qpqvea","at":1751815146028},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.3. 社区情绪与目标受众**","lines":[209,216],"size":262,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1qpqvea","at":1751815146028}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.3. 社区情绪与目标受众**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07474706,-0.02635544,-0.04750785,-0.01807044,0.03975831,0.02184268,-0.00176673,0.00966298,0.03378631,-0.021116,0.06922576,-0.07268086,0.04972186,0.01497475,0.02976728,-0.01948024,0.01274723,-0.04345207,-0.02374061,0.00316113,0.08938358,-0.07393487,0.02644665,0.00036755,0.06215648,0.03754033,-0.00099598,-0.03312561,-0.04329607,-0.16766931,0.00442325,-0.04058669,0.02536734,0.01459111,0.03718254,-0.02799856,0.00288463,0.06535225,-0.04734427,-0.01172686,0.00224049,0.0160673,0.01992483,0.02174725,0.05051893,-0.05847133,0.02107069,-0.05438725,-0.00672641,-0.06633838,-0.03096018,-0.08052157,-0.00101828,0.03570788,0.00220343,0.0305321,0.0184706,0.05360488,0.04525524,0.02086873,0.07475258,0.04636808,-0.19684145,0.0269223,0.10495741,-0.00066977,-0.02829243,0.00985505,0.05475155,0.07354648,0.02458128,0.02111303,0.00216119,0.05348257,0.01148888,0.01376149,0.00856978,0.01360081,-0.01066921,-0.03851862,-0.05500105,0.0834773,0.01597745,0.00381633,-0.03274525,-0.00906712,-0.00021359,-0.0281946,-0.04653688,0.01712123,-0.00558664,0.02266474,0.0328886,0.0076756,-0.01696787,-0.01163799,-0.00841008,-0.00349314,-0.07129016,0.12351602,-0.04327346,0.03550762,-0.00106073,-0.04092645,0.04034256,0.01277112,-0.00709677,-0.0581573,0.00834934,0.01966656,0.00358096,0.02117904,0.07391747,-0.05472363,0.05392528,-0.00234441,0.00330297,-0.00864623,-0.03657806,0.01521758,-0.01123124,0.05785052,0.0320204,-0.0269309,-0.02258518,-0.02712674,0.01814296,0.04972853,0.05974835,0.04212327,0.00926808,0.06059813,-0.0649093,0.00512539,-0.04494732,0.00055632,0.00002855,-0.01187865,-0.02433105,-0.04455065,-0.02370161,-0.06005437,-0.0136795,-0.10484288,-0.05685037,0.09851719,-0.00120708,0.04222746,-0.02301836,-0.04424555,0.01637554,0.08910716,-0.0308232,-0.07204231,-0.03250252,0.00369946,0.03015948,0.12716807,-0.07139427,-0.02380546,-0.01889799,-0.02565821,-0.03522506,0.09939805,0.04885047,-0.12342981,0.04342357,0.0391066,-0.00400159,-0.06339373,0.00330511,0.01624459,-0.06563696,-0.00009261,0.07456505,-0.03463425,-0.02126343,-0.00571053,0.01955769,0.05656816,0.02055171,-0.03756959,-0.06537888,-0.04006459,-0.02882822,-0.02741577,-0.0193696,0.03115236,0.03636815,0.05701175,-0.11772865,0.05152896,0.01649039,0.04492725,-0.03752676,-0.01133787,-0.03213229,-0.01017567,0.00549593,-0.05570029,0.14935189,0.02936929,-0.00451466,0.01792659,-0.05978405,0.02956251,-0.01212419,-0.04868533,0.00938363,0.03244418,-0.02797148,0.02553501,-0.04067222,0.05566199,-0.03051496,0.00599842,0.02457569,0.0378262,-0.02153411,0.0527623,0.02275393,0.01842447,-0.10868728,-0.19033006,-0.03423996,0.0474895,-0.02120386,0.01833945,-0.00823697,0.025491,0.02963015,0.04041415,0.08182177,0.09814878,0.05634638,-0.06325515,0.00072944,-0.02286561,0.04843037,0.03757484,-0.01448397,-0.01240608,0.02055766,-0.04764185,0.02955731,-0.00535053,-0.05304073,0.05853724,-0.04068827,0.09973462,-0.04257387,0.02591029,-0.00914503,0.07984478,0.01220622,-0.05618311,-0.15538374,0.029906,0.05335965,-0.00615075,-0.03108734,-0.0556247,-0.03649679,-0.02543011,0.00298125,0.03600002,-0.10694172,0.00715662,-0.06855715,-0.04982154,-0.03786554,0.01059831,0.00456076,-0.0054867,0.04536649,0.02273359,0.11657586,-0.01106519,-0.01137516,0.00949686,-0.02085731,-0.02080161,0.06152499,0.00369021,-0.00308394,-0.05197858,0.02462757,0.02297059,-0.00839198,-0.02427164,0.03780276,0.01111116,-0.0024118,-0.02374894,0.14065962,0.02713177,0.01370385,0.02342078,0.01658652,-0.02952908,-0.09544349,0.04110574,-0.00559638,0.01812589,-0.01786316,0.02706023,0.05374823,-0.0226969,0.05364238,0.0340769,-0.0312246,0.02343681,0.01183068,-0.01088359,-0.03826184,-0.0607743,-0.04923674,0.07990696,0.00553283,-0.30239537,-0.01494451,-0.01075438,-0.02255279,0.06783022,0.03694267,0.06210441,0.02281092,-0.07717984,0.03349541,-0.00129931,0.06629089,0.00327752,-0.06859105,0.01266739,-0.01547946,0.0523341,-0.00580026,0.01656316,0.03880616,-0.01111245,0.06749721,0.20822443,-0.02570273,0.03348115,0.01784948,-0.05209252,0.03064034,0.02844898,0.02843504,-0.05130738,-0.0262977,0.02278393,-0.05051306,0.02055953,0.03480096,-0.01374438,-0.00259703,-0.00791218,0.0246725,-0.04338188,-0.02231826,-0.03428634,0.02313628,0.10840033,-0.01433287,-0.07151425,-0.07966202,0.01457265,0.04524118,-0.04928435,-0.02285387,0.0143459,0.05455158,0.00653909,0.07084782,0.03527698,-0.05691296,-0.09616037,-0.00642648,-0.02515592,-0.02450022,-0.04570428,0.05746887,0.02028128],"last_embed":{"hash":"1x7w9al","tokens":266}}},"text":null,"length":0,"last_read":{"hash":"1x7w9al","at":1751815146045},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第六部分：生态系统与社区分析**#**6.3. 社区情绪与目标受众**#{1}","lines":[211,216],"size":237,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1x7w9al","at":1751815146045}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05607769,-0.04240542,-0.06847262,-0.04593009,0.02155207,0.00522812,0.00500454,0.01673675,-0.00089559,-0.02315908,0.07651655,-0.07108384,0.03668224,0.02277789,0.02329377,-0.04119741,0.0290953,-0.02790927,-0.0182917,0.03170722,0.09698492,-0.11283717,0.01434669,-0.06051743,0.0863867,0.04063943,0.03459359,-0.01271827,-0.04043003,-0.21417798,0.00910995,-0.06055122,0.01940715,0.02105273,-0.02522445,-0.04808654,-0.01507829,0.0901858,-0.04688428,0.00735829,0.01365509,-0.0083657,0.00676295,0.00890425,0.0108452,-0.08767065,0.0157322,-0.06902557,0.01546512,-0.04082469,-0.05395497,-0.04532972,-0.02087888,0.02018937,0.00695636,0.04167271,0.01725864,0.04038119,0.04341415,0.04181652,0.03428106,0.03381227,-0.20456719,0.01295524,0.07056454,0.02524345,-0.00972956,0.02857735,0.06403177,0.07892878,0.00348688,0.0026794,0.00142465,0.03548542,0.01216122,0.01048923,0.0064672,-0.00110171,-0.04142217,-0.06705153,-0.07263215,0.07770374,-0.00013704,-0.01705873,-0.02539439,-0.01063971,0.00425144,-0.02949153,-0.03419688,0.00855658,-0.03893375,0.02549644,0.04504532,0.00710778,-0.03262862,-0.0133853,-0.00244683,0.01462542,-0.07915122,0.10112948,-0.01167485,0.05467873,-0.03445917,-0.05799206,0.05424299,0.00936825,-0.00326979,-0.06558812,-0.00826962,0.02950028,0.01666379,0.00574515,0.06726942,-0.06100153,0.010637,0.02797633,0.00704951,-0.0085723,0.0091107,0.0225075,0.00989,0.04350752,0.04050355,-0.02506155,-0.03850271,-0.02766022,0.0053648,0.0665004,0.06468315,0.02905375,0.03070202,0.05040661,-0.05683778,-0.02109952,-0.06256616,-0.01417029,-0.05853884,0.01411474,-0.01901471,-0.01734767,-0.03474239,-0.05944549,0.01370105,-0.09244023,-0.07711695,0.08847153,-0.0145984,-0.01641654,0.02230084,-0.05180661,0.02768115,0.05439248,-0.01114495,-0.06178133,-0.01327129,-0.03765587,0.00943896,0.12262761,-0.08192429,-0.01271724,-0.04657754,-0.02754441,-0.03863394,0.15413283,0.0282795,-0.09121431,0.05448554,0.02583077,-0.02215741,-0.07391553,0.01844173,-0.01311466,-0.03319469,0.00738034,0.08129007,-0.0391782,-0.03049914,-0.03890933,-0.00049768,0.04675192,0.00930616,-0.02048197,-0.06795084,-0.00385014,0.00845716,-0.01751583,-0.03466831,-0.00257371,0.01845437,0.02740498,-0.05903872,0.05326996,0.0237337,0.03291633,-0.01976318,-0.00868436,-0.0431892,-0.01244813,0.01933398,-0.03886769,0.13501443,0.0325586,0.02617445,0.00214058,-0.06826988,0.02011104,-0.04281475,-0.04728019,-0.00488628,0.06982253,-0.00976754,0.02049019,-0.03617487,0.0211215,0.00087887,0.0065373,0.0316226,0.02261757,-0.02877712,0.06153265,0.01231184,0.01049819,-0.09911038,-0.18346708,-0.03037823,0.03422621,-0.02564434,0.03425102,-0.00274338,0.04033682,0.0184589,0.04038958,0.08084039,0.07919921,0.04591664,-0.04901055,0.02374393,-0.01606099,0.0463564,0.0517084,-0.02445555,0.02101193,0.04145669,-0.0432276,0.02333736,0.01157451,-0.02495831,0.05056845,-0.07809675,0.09236748,-0.02350162,0.05192539,-0.01266159,0.07515436,0.019258,-0.02920152,-0.09665316,0.03836637,0.06213801,-0.01212728,-0.02486161,-0.05256768,-0.0191572,-0.00499049,0.0162051,0.03660379,-0.09453724,0.03061115,-0.04565777,-0.04615473,-0.00148156,-0.03734183,0.01442986,0.00893872,0.05451239,0.03257941,0.10169768,0.00797654,-0.01689527,0.00030081,-0.07157923,-0.00638416,0.058587,0.00265769,-0.01042858,-0.03398634,-0.01783131,0.05296062,0.00224699,-0.01039191,0.03777434,0.03696515,-0.03438841,-0.02587217,0.13011494,0.01645162,-0.01321834,0.04935863,0.04211173,-0.02121224,-0.07908398,0.01916929,0.0174245,0.03163202,0.01980897,-0.00446469,0.03240223,0.00899982,0.05679256,0.04228749,0.01733001,0.01095907,0.01011515,-0.02678959,-0.02721225,-0.04213975,-0.01577543,0.08849888,-0.01814465,-0.31996769,-0.00848104,-0.02546106,-0.02156427,0.05919632,0.043125,0.01862785,-0.01957903,-0.10236447,0.04690782,0.00495387,0.06069979,0.0094237,-0.04683286,-0.00102323,-0.01363679,0.06231155,-0.01685274,0.03592244,0.03006322,0.00933442,0.08929435,0.20556161,-0.02534177,0.03566374,0.03383613,-0.04247067,0.05884364,0.0253284,0.01478497,-0.01940203,-0.00026862,0.04121632,-0.03074796,-0.00971847,0.07426763,-0.02608873,0.01637774,-0.00758837,0.03776771,-0.01969179,-0.0172851,-0.03624278,0.05964328,0.10416637,-0.01542581,-0.04839421,-0.07746677,0.03699852,0.05038435,-0.0786523,-0.00521043,-0.01775543,0.03462161,0.00190057,0.05805672,0.00815218,-0.05086816,-0.07806352,-0.03070351,-0.00358455,0.00274319,-0.05961204,0.07438683,0.01030959],"last_embed":{"hash":"123mjhy","tokens":412}}},"text":null,"length":0,"last_read":{"hash":"123mjhy","at":1751815146061},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**","lines":[217,246],"size":1474,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"123mjhy","at":1751815146061}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**表 3: 竞争分析：Nockchain vs. Aleo vs. RISC Zero**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05886314,-0.0385394,-0.05923421,-0.03232072,0.03279315,0.01182481,0.00437286,0.0234411,0.00019543,-0.02063017,0.07583512,-0.08068238,0.03629465,0.02334024,0.02472587,-0.02661221,0.02297109,-0.01933389,-0.02389172,0.03597215,0.09558658,-0.10987189,0.01902546,-0.07095433,0.08488975,0.0363846,0.02697564,-0.0174956,-0.03814094,-0.21769196,0.00702645,-0.04807764,0.02180029,0.01711475,-0.0250761,-0.05071183,-0.01734445,0.08928031,-0.04325904,0.00133448,0.00730739,-0.00775047,0.01217195,0.00607672,0.01047148,-0.08558868,0.02301742,-0.06500728,0.01440308,-0.03782213,-0.05121617,-0.05220173,-0.02291752,0.02609177,0.01088129,0.0432375,0.01382766,0.03613802,0.04472624,0.04792575,0.03268385,0.04201026,-0.21057732,0.01078182,0.08080341,0.02317794,-0.01455929,0.04119994,0.04571308,0.07342005,0.00269498,0.00306402,-0.00331483,0.04634766,0.00250839,0.01591712,0.01831923,-0.00668703,-0.03531043,-0.06906117,-0.06778903,0.07610654,-0.00449671,-0.0131496,-0.02405273,-0.01281257,0.00004549,-0.03202004,-0.04292875,0.00523764,-0.03908612,0.03449843,0.05249951,-0.00031168,-0.02899254,0.00610719,-0.00446909,0.01432184,-0.08057009,0.10313682,-0.00722208,0.05501277,-0.03092769,-0.06330902,0.05010995,0.01979677,-0.0009379,-0.06506494,-0.01073961,0.03909521,0.01407637,0.0085424,0.06408998,-0.0521023,0.01037909,0.02763246,0.00921894,-0.00956836,0.01290293,0.02159588,0.00943868,0.0382,0.05048668,-0.02481155,-0.03532944,-0.02899894,-0.00310579,0.06989427,0.0492854,0.01971081,0.03762255,0.04195097,-0.05766987,-0.01169102,-0.07135258,-0.02004864,-0.061,0.00600876,-0.01952749,-0.02250788,-0.02792823,-0.06425463,0.02122463,-0.09394524,-0.07100574,0.08949419,-0.02196979,-0.01249038,0.0256434,-0.04690605,0.03203497,0.05228619,-0.00243827,-0.06615833,-0.006848,-0.03318273,0.00788678,0.11773586,-0.07602705,-0.02051893,-0.04605996,-0.02811516,-0.04186545,0.15526968,0.03903929,-0.08605459,0.05354944,0.02888048,-0.02318246,-0.07090157,0.02513973,-0.01658645,-0.03248209,0.00474714,0.08895501,-0.03766005,-0.02430643,-0.04336336,0.00591458,0.03822241,0.0086029,-0.02493538,-0.0592686,0.00160621,0.00372347,-0.02103896,-0.04118868,-0.0040497,0.01911798,0.0266444,-0.06029716,0.05147835,0.02328365,0.0336422,-0.01395412,-0.0085414,-0.03821367,-0.0174361,0.01811915,-0.03832583,0.13166091,0.03719687,0.03021413,-0.00539107,-0.06170098,0.02510948,-0.04367589,-0.04601963,-0.00592988,0.06146451,-0.0108989,0.01275781,-0.03228587,0.02425132,-0.00494208,0.00640354,0.03125479,0.02633818,-0.03462168,0.06611884,0.00115016,0.01032284,-0.09310491,-0.19383319,-0.04200781,0.01429541,-0.0284388,0.03885963,-0.0034251,0.04139737,0.01392509,0.03957168,0.07830002,0.08121623,0.04264682,-0.04071886,0.02085229,-0.0126865,0.04425186,0.04849849,-0.01708835,0.00852173,0.02767067,-0.04283312,0.02427734,0.01457198,-0.03202636,0.05192728,-0.06747823,0.09216949,-0.01550274,0.05146707,-0.01152381,0.07645471,0.01312567,-0.02814843,-0.10593588,0.03528231,0.05976337,-0.01418545,-0.02945951,-0.04728339,-0.02103774,-0.00493165,0.02529231,0.0499148,-0.08293234,0.03256327,-0.04911306,-0.04296236,-0.00442263,-0.03369756,0.0153285,0.00118124,0.05979405,0.01954967,0.09398328,0.00604658,-0.00805137,-0.00510012,-0.06751098,-0.00584115,0.05587692,0.00447902,-0.01264449,-0.03742511,-0.0145369,0.03834162,0.01037376,0.00672528,0.03469123,0.02789538,-0.02576003,-0.01440995,0.12432254,0.00983669,-0.01600761,0.05343233,0.04519108,-0.02378546,-0.08591703,0.02012722,0.01244019,0.02472852,0.01934197,-0.00600651,0.03528976,0.01239273,0.04819382,0.04911723,0.00709077,-0.00195435,-0.00285229,-0.02675173,-0.0201082,-0.0396805,-0.01439959,0.0863983,-0.0267135,-0.33158582,-0.00979858,-0.02722458,-0.02654158,0.06506365,0.0402002,0.01440986,-0.0199174,-0.1024145,0.04060308,0.0073205,0.05693329,0.0061334,-0.0401905,0.00292423,-0.00585971,0.07356117,-0.01916954,0.0365916,0.02875409,0.00842606,0.09214033,0.20723666,-0.03501501,0.03575956,0.02936899,-0.03277801,0.06117597,0.01862584,0.01624517,-0.01213721,-0.00448257,0.04197511,-0.0367164,-0.0085535,0.06661278,-0.03346999,0.01941708,-0.01558824,0.03769685,-0.02628567,-0.01541631,-0.03640001,0.06568164,0.10623494,-0.00454413,-0.04522796,-0.08019652,0.03376881,0.05874126,-0.07253616,-0.00461258,-0.01457745,0.03140358,0.01505136,0.05277963,0.01455278,-0.06091977,-0.0775386,-0.02019427,-0.0028975,0.00700428,-0.0541232,0.07189338,0.01376178],"last_embed":{"hash":"9tk8o5","tokens":414}}},"text":null,"length":0,"last_read":{"hash":"9tk8o5","at":1751815146079},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**表 3: 竞争分析：Nockchain vs. Aleo vs. RISC Zero**","lines":[221,234],"size":546,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"9tk8o5","at":1751815146079}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**表 3: 竞争分析：Nockchain vs. Aleo vs. RISC Zero**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05744874,-0.0383356,-0.05750882,-0.03225435,0.03267952,0.0116839,0.00444843,0.02236251,0.00090259,-0.01962723,0.0767465,-0.07717878,0.03712743,0.02313431,0.02399039,-0.02736791,0.0249259,-0.02058172,-0.02364326,0.03654021,0.09613524,-0.10938814,0.01855396,-0.06879937,0.08491984,0.03664666,0.02795004,-0.01942593,-0.03920003,-0.21830775,0.00752582,-0.04811273,0.02164271,0.01806779,-0.0245109,-0.04975013,-0.01490323,0.09115586,-0.04527439,-0.00074298,0.0073989,-0.00821358,0.01516684,0.00486246,0.00940982,-0.08709048,0.02294567,-0.06840892,0.01567556,-0.04142334,-0.05059548,-0.0558627,-0.02227994,0.02482718,0.01124646,0.04287577,0.01331066,0.03132815,0.04400442,0.04714992,0.03366664,0.04442635,-0.21221477,0.01104835,0.07834428,0.02322193,-0.01321359,0.04349718,0.04694437,0.07399395,0.00231981,0.00200974,-0.0033029,0.04564806,0.00487158,0.01788731,0.01796522,-0.00731014,-0.03582653,-0.06926563,-0.06889389,0.07629051,-0.00472982,-0.01321564,-0.02503517,-0.01101053,-0.00052302,-0.03217759,-0.04333876,0.0057563,-0.039281,0.03387288,0.05188692,0.00037748,-0.02924398,0.0035181,-0.0048359,0.01405802,-0.0800826,0.10301599,-0.00666042,0.05596002,-0.03082974,-0.06140348,0.04777087,0.02240144,-0.0006392,-0.06503883,-0.01076323,0.03936991,0.01464708,0.00884727,0.06198993,-0.0521267,0.01116611,0.0309745,0.00783111,-0.00769564,0.01190914,0.01995992,0.00940907,0.03845065,0.04992568,-0.02869033,-0.03571717,-0.02957668,-0.00442814,0.06991679,0.04483527,0.01694755,0.03908812,0.04331934,-0.05945508,-0.0120985,-0.07157406,-0.01858471,-0.0596464,0.00172299,-0.02097151,-0.02341917,-0.02721587,-0.06488042,0.01906933,-0.09343987,-0.07121725,0.09217069,-0.02429289,-0.01155259,0.02324845,-0.04508773,0.0343689,0.05552502,-0.00216608,-0.06532591,-0.00828968,-0.03469165,0.00675082,0.12034281,-0.0758262,-0.02136868,-0.04625775,-0.02948835,-0.04081197,0.15534936,0.038042,-0.08661541,0.05446586,0.02897035,-0.02202095,-0.06964035,0.02833031,-0.01794877,-0.03242159,0.00534885,0.08959799,-0.03471702,-0.0227664,-0.04464335,0.0076015,0.03611713,0.00963333,-0.02565133,-0.05818085,0.00115522,0.00351724,-0.01962038,-0.04265132,-0.0015466,0.01895772,0.02534349,-0.06056867,0.05385884,0.02258117,0.03433857,-0.01429402,-0.00884044,-0.03796766,-0.01754984,0.01631681,-0.03756755,0.13033584,0.03532033,0.03329814,-0.00664937,-0.05979446,0.02503563,-0.04226771,-0.04799512,-0.00648412,0.06250917,-0.01046506,0.01165746,-0.0310524,0.02369973,-0.00522252,0.0069112,0.03123171,0.02453115,-0.03614799,0.06543546,0.00444569,0.01223222,-0.09163472,-0.1930512,-0.0412043,0.01480188,-0.02896322,0.04004246,-0.00371294,0.04033096,0.01721289,0.04110113,0.0765086,0.07918217,0.04499519,-0.04183604,0.02204186,-0.01364149,0.04465342,0.0484479,-0.01646024,0.00988882,0.02606991,-0.0395163,0.0243952,0.0154072,-0.03142348,0.05048741,-0.06628165,0.08978723,-0.01698391,0.05027911,-0.0115504,0.0743217,0.01336535,-0.02939727,-0.10672809,0.03546711,0.05977957,-0.01442537,-0.03044317,-0.0474233,-0.01723185,-0.0047873,0.02705831,0.05014986,-0.08137199,0.03309267,-0.04972465,-0.0410693,-0.00469936,-0.03109591,0.0136433,0.00116263,0.05938684,0.01869441,0.09582894,0.00427241,-0.00800659,-0.00633718,-0.0684751,-0.00609383,0.05550056,0.0045132,-0.01399182,-0.03610095,-0.01538866,0.0389138,0.01099659,0.00484011,0.03409563,0.02862677,-0.02544797,-0.01142354,0.12553754,0.01089573,-0.01579508,0.05450696,0.0439211,-0.02304602,-0.08629911,0.01799911,0.01226342,0.02644997,0.01946317,-0.0070572,0.03653384,0.01377572,0.04818608,0.04998718,0.00743848,-0.00340921,-0.00331745,-0.02654943,-0.0220129,-0.0400674,-0.01306693,0.08421696,-0.02665511,-0.33061829,-0.01045135,-0.02934171,-0.02542387,0.06471083,0.04005273,0.01359961,-0.01847904,-0.10080404,0.0403593,0.00783678,0.05592579,0.00484051,-0.04231609,0.00063936,-0.00721718,0.07301773,-0.01924051,0.03898106,0.02807386,0.0090998,0.09064846,0.20654619,-0.03702005,0.035827,0.02923526,-0.03306547,0.05934908,0.01824242,0.01614842,-0.01407675,-0.0030496,0.04059524,-0.03707333,-0.0074925,0.06959052,-0.03490509,0.01907521,-0.0161632,0.03996303,-0.02756638,-0.0157338,-0.0366717,0.06402478,0.10763895,-0.0040705,-0.04247248,-0.08224629,0.03448072,0.05922152,-0.07099909,-0.00263117,-0.01173304,0.03015163,0.01438647,0.05274843,0.01579039,-0.06210363,-0.07873479,-0.0214767,-0.00439981,0.0076134,-0.05339162,0.07200904,0.01561257],"last_embed":{"hash":"19rumnj","tokens":411}}},"text":null,"length":0,"last_read":{"hash":"19rumnj","at":1751815146107},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**表 3: 竞争分析：Nockchain vs. Aleo vs. RISC Zero**#{1}","lines":[223,234],"size":493,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"19rumnj","at":1751815146107}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**7.1. Nockchain vs. Aleo**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0637468,-0.01022801,-0.06977075,-0.02534598,0.00577894,0.00450107,0.01825152,0.01413128,-0.00006883,-0.03332495,0.05741352,-0.06791551,0.01949592,0.02090013,0.02911595,-0.03581262,0.02267696,-0.06418477,-0.04976835,0.03009191,0.09189074,-0.08816113,0.03516886,-0.03353589,0.07126992,0.03150218,0.0206985,0.01776244,-0.03437395,-0.19619928,0.00308454,-0.05366439,0.01341591,-0.00681309,-0.00599141,-0.04926931,0.00859132,0.06174355,-0.04238339,-0.01977956,0.02522982,0.00699642,0.00598003,0.00619842,0.03853691,-0.07778256,0.02617887,-0.09282828,0.00115969,-0.05084328,-0.01687543,-0.07734255,-0.0192454,0.03232608,0.00385928,0.03620521,0.0019332,0.04691166,0.04472395,0.0364106,0.05661173,0.02543093,-0.18805349,0.04490108,0.09056588,0.00684119,-0.0227483,0.03359028,0.04120382,0.07481173,0.02587298,-0.01078517,-0.00799243,0.04605872,0.02113665,0.00508148,0.03559156,-0.00984361,-0.03097414,-0.05630591,-0.06988558,0.07946255,-0.00517526,0.0049166,-0.02580928,-0.02288783,0.00830056,-0.03178885,-0.03520142,-0.01449905,-0.01261324,0.02308483,0.0572601,0.00186193,-0.01389789,0.00706733,-0.02445562,0.00738267,-0.05865198,0.11407638,-0.04440066,0.07402645,-0.02597118,-0.03398767,0.05461904,0.00477704,0.00175355,-0.07852911,0.03086997,0.03283748,0.02664452,0.00156932,0.09236554,-0.03252037,0.01677847,0.01488677,0.00976772,-0.01901554,-0.0070978,0.03643862,-0.01470382,0.05512976,0.04447907,-0.00383952,-0.02994675,-0.01979716,-0.0029532,0.04687737,0.05383474,0.02421939,0.02517007,0.01707313,-0.07019828,-0.02993204,-0.05175622,-0.0198764,-0.03299878,0.01866176,-0.03599207,-0.03194013,-0.01763727,-0.06659928,0.00801794,-0.10255601,-0.08879545,0.09124415,-0.00089758,0.01276859,0.00293279,-0.05334047,0.01315655,0.05618107,-0.03256636,-0.08691099,-0.03032344,0.00458299,0.0138692,0.124768,-0.0918557,0.0018234,-0.0378493,-0.0080055,-0.04061322,0.12410832,0.02215152,-0.08563562,0.05730483,0.01615225,-0.0065921,-0.07239746,0.03750537,0.02335495,-0.03953643,-0.00447589,0.0737299,-0.04340294,-0.03671446,-0.02120154,0.01050647,0.05231414,-0.00221285,-0.01259369,-0.08121597,-0.01730658,0.02419819,-0.0244631,-0.02460937,-0.00510049,0.02309417,0.07561827,-0.06728863,0.04691176,0.00836166,0.04485377,-0.03215308,-0.02179669,-0.0522365,-0.02774273,-0.00482951,-0.04155703,0.12841186,0.05413368,0.01744965,0.0297354,-0.05677277,0.00853591,-0.02462484,-0.05191046,0.02288432,0.05715455,-0.0293964,0.03549087,-0.05035689,0.03698683,0.01098572,-0.00696653,0.01214636,0.02313089,-0.00630149,0.05415337,0.01250475,-0.00560701,-0.06993127,-0.1899305,-0.02522504,0.0207597,-0.03032935,0.0371601,0.00396884,0.04652095,0.03185792,0.03143418,0.08244194,0.06960315,0.05363344,-0.05457128,0.01990844,-0.03180172,0.05273912,0.05683269,-0.03293779,0.00551131,0.03132396,-0.04928852,-0.00306286,0.0267319,-0.05096575,0.05477276,-0.070887,0.08580926,-0.05239425,0.06234198,0.00010646,0.06679151,0.00691002,-0.02806631,-0.14164525,0.02780674,0.05470217,-0.01236546,-0.04466494,-0.0549292,-0.03526691,-0.01368551,0.02235572,0.04415414,-0.09996029,0.02606465,-0.04603492,-0.0487906,0.00285968,-0.03309615,-0.00052147,0.00668307,0.05034469,0.00808363,0.11767649,-0.00957768,-0.01737913,0.01158641,-0.02715138,-0.01458133,0.04794154,-0.01413665,-0.02111745,-0.04456231,-0.00485664,0.0396886,0.02663674,-0.01142844,0.0293115,0.02641194,-0.03300679,-0.02237189,0.14760733,0.00221456,-0.01910571,0.05636393,0.05484224,-0.00611178,-0.09536443,0.03031209,0.01517826,0.02422435,-0.00008383,0.02364557,0.04761495,-0.0143955,0.0754047,0.04066391,-0.01569095,-0.00453763,-0.00920819,-0.00404426,-0.03164551,-0.04770515,-0.04743709,0.07487404,0.00334012,-0.3048372,-0.00604415,-0.00721858,-0.01066804,0.05703096,0.0454172,0.04212141,0.01030905,-0.07397528,0.01293149,-0.00872847,0.07889532,0.0078873,-0.04144939,-0.01729721,-0.00900059,0.05814084,-0.00295092,0.02286859,0.05594267,0.02654168,0.0776931,0.20066549,-0.01417091,0.04055985,0.03931493,-0.05907337,0.04492762,0.01680096,0.02004181,-0.03349246,0.01788794,0.04076862,-0.04408229,0.00613253,0.06964404,-0.02401198,0.01114974,-0.01246579,0.02436102,-0.01950041,-0.0291707,-0.0354488,0.03940609,0.10664314,0.00653413,-0.05391333,-0.07230402,0.01570181,0.03238581,-0.09120338,-0.02857665,-0.00862924,0.0489213,0.0089395,0.07149509,0.02505852,-0.03873736,-0.10165576,-0.01765966,-0.00418568,-0.00040598,-0.05807057,0.08391002,-0.00167109],"last_embed":{"hash":"2fs1i1","tokens":307}}},"text":null,"length":0,"last_read":{"hash":"2fs1i1","at":1751815146135},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**7.1. Nockchain vs. Aleo**","lines":[235,238],"size":334,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"2fs1i1","at":1751815146135}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**7.1. Nockchain vs. Aleo**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06414723,-0.0090349,-0.07196828,-0.02576593,0.00522234,0.0062947,0.01851828,0.01480911,-0.00038926,-0.03303402,0.05712231,-0.06506256,0.01957887,0.02170158,0.0312774,-0.03596887,0.0212079,-0.06343456,-0.05047748,0.03182746,0.09105644,-0.08873629,0.03284412,-0.03273464,0.07000693,0.03134238,0.02227208,0.01655184,-0.03424273,-0.19742782,0.00720659,-0.05459876,0.01248048,-0.00745479,-0.00804669,-0.04626058,0.0098053,0.06184091,-0.04168465,-0.02084136,0.0260109,0.00838938,0.0091893,0.00484439,0.03758515,-0.07813628,0.02647628,-0.09447889,0.00119579,-0.05045535,-0.01614476,-0.07822026,-0.0217219,0.0337566,0.00308685,0.03960592,-0.00079672,0.04902332,0.04477169,0.03977905,0.05468139,0.02557572,-0.18886524,0.04488936,0.08819826,0.00776145,-0.02185268,0.03598157,0.03787156,0.07574103,0.02819899,-0.01124695,-0.0049459,0.04487421,0.02249187,0.00598471,0.03555438,-0.01011861,-0.03103857,-0.0566627,-0.07028061,0.07930343,-0.00458354,0.00226625,-0.02638328,-0.02508706,0.01068834,-0.03404124,-0.03688371,-0.0148081,-0.013363,0.02421205,0.0586864,0.00169347,-0.01228066,0.00529712,-0.02469865,0.00995885,-0.05686317,0.11341587,-0.04364963,0.07553912,-0.02751939,-0.03083985,0.05369254,0.00796154,0.0037143,-0.08096983,0.02955807,0.03328493,0.0270072,0.00434046,0.09255856,-0.03131309,0.01493561,0.0150138,0.00765373,-0.01778126,-0.00720281,0.03708984,-0.01481908,0.05615301,0.04588485,-0.00461826,-0.03169231,-0.0201071,-0.00342253,0.04413489,0.05445075,0.0233738,0.02427502,0.01567492,-0.07053524,-0.03048097,-0.05270202,-0.01992689,-0.03232305,0.01639871,-0.03712137,-0.03212611,-0.01587493,-0.06311962,0.00936108,-0.10281822,-0.092018,0.09169583,0.00028557,0.0141493,0.00349955,-0.05224515,0.01351175,0.05331276,-0.03591744,-0.08352347,-0.03152417,0.00613499,0.01246652,0.1235364,-0.09141021,0.00472036,-0.0402206,-0.00658456,-0.03707936,0.12550975,0.01961766,-0.08499745,0.05663794,0.01614748,-0.00678314,-0.07373295,0.0363603,0.02460052,-0.03866865,-0.00316035,0.07542299,-0.04396069,-0.03806798,-0.02391224,0.01023596,0.05327724,-0.00368888,-0.0145903,-0.08223801,-0.02093029,0.02549021,-0.02403212,-0.02452634,-0.00669248,0.02419693,0.07600823,-0.06494141,0.05044309,0.00984083,0.0478939,-0.03279199,-0.02312545,-0.0546302,-0.03108402,-0.00534674,-0.04133492,0.12598833,0.05590346,0.01893572,0.03010592,-0.0562026,0.0085369,-0.02359831,-0.05187854,0.02347827,0.05596391,-0.02872818,0.03477103,-0.05106637,0.03808048,0.0112284,-0.00543749,0.01411696,0.02352086,-0.00514831,0.05440002,0.01242738,-0.00727115,-0.06805337,-0.18874538,-0.024643,0.01803543,-0.02993659,0.0377846,0.00425908,0.04463984,0.02935341,0.02941751,0.08107351,0.0696068,0.05462059,-0.0539562,0.01978767,-0.03340521,0.0530799,0.05633338,-0.03317017,0.0052686,0.02776419,-0.05115667,-0.00354603,0.02588243,-0.05340997,0.05381152,-0.06909202,0.08632866,-0.05186304,0.06286164,0.00141559,0.06399538,0.0050353,-0.02705775,-0.14179763,0.02715368,0.05353087,-0.01116398,-0.0465454,-0.05402929,-0.03750989,-0.01456626,0.02459105,0.04532615,-0.10152271,0.02441612,-0.04444473,-0.04928201,0.004735,-0.03159397,-0.00098389,0.00732617,0.04779101,0.00474647,0.1185444,-0.0096863,-0.01758705,0.0094213,-0.02696854,-0.01203283,0.04848079,-0.01556907,-0.02134933,-0.04431419,-0.0061509,0.04080157,0.02729173,-0.01359059,0.02781098,0.02606416,-0.03690574,-0.02132889,0.1479429,0.00130649,-0.0195069,0.05639078,0.05386924,-0.00433344,-0.09211362,0.02951789,0.01539919,0.02094469,-0.00169375,0.0210328,0.0495479,-0.01677711,0.07589427,0.04124731,-0.0151,-0.00698172,-0.01087642,-0.00212097,-0.02841256,-0.0466529,-0.04483314,0.07383587,0.00468598,-0.30471784,-0.00320228,-0.0054733,-0.00932568,0.05292783,0.04339581,0.04244161,0.01274767,-0.07153859,0.01154709,-0.00714256,0.08054485,0.01023674,-0.03780242,-0.01966687,-0.01058795,0.05755984,-0.00399945,0.02313277,0.05480371,0.02999358,0.07930097,0.20051628,-0.01509797,0.04120678,0.040867,-0.05782627,0.0464526,0.01686999,0.02034421,-0.03219494,0.01610282,0.04048685,-0.04695476,0.00825045,0.0708833,-0.02315916,0.0123363,-0.01400254,0.02507327,-0.01992806,-0.02766838,-0.03461163,0.03975794,0.10777344,0.00687127,-0.05342882,-0.07017029,0.01732396,0.03203894,-0.09427857,-0.02622516,-0.00956244,0.04917484,0.00789781,0.06739075,0.02560231,-0.03770363,-0.10371906,-0.01830985,-0.00510884,-0.00051375,-0.05795254,0.08150675,-0.00271304],"last_embed":{"hash":"vewrtc","tokens":304}}},"text":null,"length":0,"last_read":{"hash":"vewrtc","at":1751815146156},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**7.1. Nockchain vs. Aleo**#{1}","lines":[237,238],"size":300,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"vewrtc","at":1751815146156}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**7.2. Nockchain vs. RISC Zero**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04770255,-0.03947381,-0.05392851,-0.03022263,0.05116975,0.01512699,0.00905949,0.02580002,0.00745829,-0.00811713,0.06035018,-0.06492641,0.03496027,0.03058705,0.00425916,-0.0227618,0.03825625,-0.00403924,-0.02465341,0.04346842,0.10770969,-0.09575911,0.00390153,-0.05221066,0.08946005,0.02606334,0.02070201,-0.04255425,-0.02812232,-0.20633283,0.01394644,-0.05946542,0.00299611,0.03618519,-0.03427116,-0.05388321,0.01381052,0.06994167,-0.050397,0.00485006,-0.00519443,-0.01185691,0.02173105,-0.00048986,0.01811268,-0.07036787,0.0238718,-0.07437428,0.03167228,-0.058334,-0.06914075,-0.07037411,-0.01937749,0.01072503,-0.0000144,0.05534039,0.00647712,0.01874981,0.04746215,0.05989997,0.06303933,0.04529842,-0.21740489,0.02625831,0.07814825,-0.00768367,-0.01194855,0.03434854,0.05099448,0.06453223,0.02697809,-0.00884583,-0.02580658,0.05278347,0.02454738,-0.00513173,0.02380666,0.01354682,-0.02019792,-0.04671447,-0.04355897,0.05682908,-0.01708312,-0.00296111,-0.03779502,0.03000501,-0.01137151,-0.06254797,-0.03756887,0.02016138,-0.00625135,0.02900613,0.0620013,-0.01391474,-0.0438217,0.00699578,-0.00422125,0.00787486,-0.07390212,0.11040585,0.00156651,0.04478408,-0.04272906,-0.03398111,0.04570185,0.03896025,-0.00041307,-0.07108872,0.01219768,0.02075967,0.02381265,-0.02269942,0.03833802,-0.051482,0.01595061,0.03946247,0.00562542,0.00211471,-0.00136568,-0.00257092,-0.00279816,0.02536763,0.06135673,-0.04592044,-0.02341201,-0.00383849,-0.00413628,0.06761476,0.02635302,0.01837234,0.0360538,0.05675006,-0.04669311,-0.01418854,-0.06752719,-0.01231009,-0.03400424,0.00461318,-0.02099963,-0.02481376,-0.03790933,-0.07141209,-0.01520297,-0.10204019,-0.07455716,0.07323391,-0.02308268,-0.00057963,0.01175577,-0.02454035,0.03327904,0.07637076,-0.03454613,-0.08511232,-0.00661981,-0.04827652,0.01779192,0.11517993,-0.05401484,-0.04453579,-0.03727803,-0.05927425,-0.03082033,0.12685554,0.04846894,-0.09392094,0.0679877,0.05029727,-0.01814462,-0.06522229,0.02711155,-0.02930079,-0.03117475,-0.00198225,0.06678713,-0.02838247,-0.04994331,-0.03902796,-0.00343303,0.02905094,0.01988158,-0.0096872,-0.0291383,0.00614529,0.0017318,-0.0350442,-0.03964102,0.01039631,0.0256838,0.00748416,-0.09434105,0.03238365,0.01269745,0.06326855,-0.0029309,-0.00972793,-0.03106567,-0.01465766,0.0255764,-0.0335241,0.10205432,0.02037866,0.02145847,-0.0007793,-0.04048621,0.05592766,-0.04201778,-0.05617351,-0.00322097,0.05424286,-0.02350497,0.0151867,-0.03587787,0.02827239,-0.01016113,0.02557646,0.04254639,0.0209199,-0.05120979,0.05193642,0.03201998,-0.00132451,-0.098819,-0.19692622,-0.04332766,0.01931475,-0.05354532,0.06786309,-0.0028753,0.04002431,0.03514495,0.03304522,0.09285744,0.07898431,0.04644601,-0.04195116,-0.00583072,-0.02929732,0.03567739,0.05373699,-0.01831236,0.01057258,0.02389484,0.00355874,0.03553185,0.00464501,-0.0301331,0.04055147,-0.06455413,0.10355971,-0.01654841,0.05450698,-0.00537775,0.06334493,0.01531925,-0.03026454,-0.13555411,0.0164998,0.0723514,-0.03361255,-0.03362552,-0.04378416,-0.00941492,0.00621786,0.02768072,0.04304723,-0.08141728,0.03888911,-0.05248207,-0.04661308,-0.0146525,-0.03366261,0.01718355,0.02040161,0.05611005,0.02020346,0.10496863,-0.00295481,-0.01616291,-0.01058712,-0.08405685,0.00830382,0.04641548,-0.01610644,-0.00244801,-0.04242408,-0.01880781,0.01029317,0.00325886,-0.01822233,0.05055014,0.04014549,-0.02194645,-0.00261907,0.13869925,0.00847634,0.01132599,0.03520999,0.039606,-0.04333228,-0.09179024,0.0282854,0.0358747,0.00814163,0.0256387,0.01915708,0.03744431,0.01163643,0.04636376,0.04440739,0.00205353,0.0034638,0.00096834,-0.02048521,-0.03469026,-0.05260545,-0.01673563,0.11436526,-0.03199181,-0.31025806,-0.02568174,-0.03401991,-0.03189061,0.06502191,0.04589682,0.04021092,0.0071511,-0.06442618,0.04138944,-0.00020165,0.04802313,-0.00267005,-0.05992519,0.00802926,-0.01861915,0.05044931,-0.01222136,0.02593412,0.0271664,0.02541482,0.07673045,0.2080722,-0.04311346,0.05506467,0.01657083,-0.02862077,0.05348801,0.01006076,0.02351766,-0.02033698,0.01419011,0.02393728,-0.04162054,-0.00209971,0.05362254,-0.04678461,0.01960362,-0.01423275,0.01045549,-0.02113971,-0.0175173,-0.03784354,0.05707365,0.10679508,-0.02595859,-0.0195645,-0.08812302,0.03636799,0.0549136,-0.05696624,0.02068623,0.00102587,0.02638006,-0.00865433,0.04126467,0.01940871,-0.07253108,-0.07677795,-0.02178162,-0.0161753,0.02514172,-0.03802228,0.06161184,0.04166825],"last_embed":{"hash":"1mfhhhk","tokens":465}}},"text":null,"length":0,"last_read":{"hash":"1mfhhhk","at":1751815146170},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**7.2. Nockchain vs. RISC Zero**","lines":[239,246],"size":508,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1mfhhhk","at":1751815146170}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**7.2. Nockchain vs. RISC Zero**#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04341777,-0.03864046,-0.05388949,-0.02927232,0.05057838,0.01583199,0.00879151,0.02748017,0.00822611,-0.00831454,0.05875542,-0.05871289,0.03133345,0.03031233,0.00367227,-0.02522713,0.04267532,-0.00654741,-0.02236213,0.04757423,0.10955311,-0.09541901,0.00177087,-0.05426993,0.09001907,0.02466966,0.02381733,-0.04416238,-0.02899673,-0.20624602,0.01712305,-0.05949936,0.00111543,0.03557484,-0.03241674,-0.05495061,0.0161684,0.07243843,-0.05102099,0.00473246,-0.00453239,-0.00979438,0.02105787,-0.00369875,0.01882498,-0.07177091,0.02430865,-0.07294694,0.02878683,-0.05797092,-0.06655436,-0.07151271,-0.02133464,0.01357731,-0.00275251,0.05496925,0.00417601,0.01709347,0.04675917,0.06039847,0.06432083,0.04730293,-0.21909013,0.02773108,0.07711539,-0.0084683,-0.0107465,0.03376256,0.04844442,0.06489307,0.02556287,-0.01159974,-0.02700957,0.05308062,0.02471364,-0.00626896,0.02509541,0.01435535,-0.01829206,-0.04762305,-0.04194798,0.05569359,-0.01685682,-0.00588931,-0.03944947,0.02771297,-0.011624,-0.06436634,-0.03921072,0.0206039,-0.00400713,0.03022865,0.06544425,-0.01790745,-0.04374649,0.00593404,-0.00279778,0.00736877,-0.07252469,0.10906453,-0.00146457,0.04663665,-0.04228956,-0.02974065,0.04647709,0.03891133,0.00384692,-0.0701123,0.01211522,0.02253093,0.02337446,-0.02551995,0.03660351,-0.04841552,0.01141426,0.03913268,0.00556987,0.0050241,0.00329573,-0.00726499,-0.0041859,0.02507092,0.06148367,-0.04708252,-0.02389655,-0.00411718,-0.00482594,0.06520756,0.02451416,0.01567366,0.03586208,0.05614664,-0.0471784,-0.01476544,-0.066025,-0.01386852,-0.03433231,0.00426718,-0.02127091,-0.02555976,-0.03716534,-0.07003144,-0.01603436,-0.10136734,-0.07740939,0.07467866,-0.02532957,-0.00158871,0.01414589,-0.02275345,0.03467041,0.07559734,-0.03441223,-0.08621923,-0.00493516,-0.04669784,0.01896244,0.11266021,-0.05396628,-0.04526019,-0.03344679,-0.05921556,-0.03162426,0.1272745,0.04966659,-0.09258439,0.07026717,0.04688448,-0.01636729,-0.06439874,0.02641304,-0.03207348,-0.03169305,-0.00199264,0.0689273,-0.02967015,-0.05235197,-0.04048323,-0.00315951,0.02791872,0.01600272,-0.00993668,-0.02881557,0.00510713,0.00487005,-0.03845913,-0.04177082,0.00832468,0.02521202,0.00691725,-0.09143163,0.03291258,0.01310062,0.06414,-0.00535123,-0.00880171,-0.0302862,-0.01612829,0.02385823,-0.03411528,0.10082228,0.01839272,0.0203507,-0.0015816,-0.03801098,0.05678322,-0.04248023,-0.0522202,-0.00051953,0.05378525,-0.02341217,0.01371433,-0.03419044,0.02408347,-0.0130106,0.02705324,0.04656826,0.020801,-0.05233027,0.04881863,0.03157917,0.00223839,-0.09905174,-0.19610199,-0.04798777,0.01674558,-0.05273459,0.07090919,-0.00350331,0.03818602,0.03598345,0.03030045,0.09258336,0.07783938,0.04433991,-0.04305028,-0.00501662,-0.02831477,0.03707004,0.05310225,-0.0178545,0.01148589,0.01840313,0.00332958,0.03475286,0.00268937,-0.03120369,0.0412367,-0.06006841,0.10450855,-0.01691225,0.05385849,-0.00449365,0.0641917,0.01554193,-0.03244063,-0.13666083,0.01510628,0.07259771,-0.03284635,-0.03498518,-0.0463936,-0.00806769,0.00327173,0.02599343,0.04088032,-0.07891044,0.03763263,-0.05040637,-0.04632911,-0.01204927,-0.03437794,0.01705989,0.01758664,0.05756104,0.01941801,0.10733436,-0.00319922,-0.01717406,-0.01059089,-0.08652903,0.0075875,0.04556666,-0.02018647,-0.00464012,-0.04229682,-0.01781027,0.01071712,0.00439462,-0.01884121,0.04788185,0.04035733,-0.0199727,-0.00374873,0.13894574,0.00340603,0.01405778,0.03777589,0.04036922,-0.03952701,-0.09151664,0.02564738,0.03751526,0.00781971,0.02575263,0.01709889,0.03887614,0.01232424,0.04567569,0.04992002,0.00321329,0.00197019,0.00007045,-0.02164404,-0.03653771,-0.05201975,-0.01386628,0.11505943,-0.0320629,-0.30861151,-0.02559262,-0.0335221,-0.02980877,0.06378853,0.05029951,0.03963052,0.01054021,-0.06323119,0.04087402,0.00292449,0.04547257,-0.00183722,-0.0574725,0.00738842,-0.0184553,0.05151553,-0.01053151,0.02724996,0.02830143,0.02710861,0.07742535,0.20875292,-0.04595511,0.05623292,0.01653366,-0.02749205,0.05593986,0.00706313,0.02151804,-0.01893386,0.01573109,0.02172798,-0.03919843,-0.00256883,0.05550931,-0.04856244,0.02293746,-0.01020533,0.01322014,-0.01848109,-0.01921572,-0.03655767,0.05770988,0.10856424,-0.02791249,-0.01753552,-0.08907387,0.03780605,0.05377053,-0.05769797,0.01981538,0.00082564,0.0268313,-0.00929382,0.04080034,0.02144341,-0.07657445,-0.07576583,-0.02306565,-0.01674596,0.02752952,-0.03611933,0.05975538,0.04002718],"last_embed":{"hash":"e3s0f9","tokens":462}}},"text":null,"length":0,"last_read":{"hash":"e3s0f9","at":1751815146202},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第七部分：ZK 领域的竞争定位**#**7.2. Nockchain vs. RISC Zero**#{1}","lines":[241,246],"size":469,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"e3s0f9","at":1751815146202}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08017188,-0.03044393,-0.0494186,-0.03460811,0.01247282,0.00511727,-0.01076307,0.00256956,-0.00626312,-0.05188571,0.04875749,-0.0605563,0.0793967,0.04411738,0.04119837,-0.01701864,0.01194162,-0.04176119,-0.01786192,0.0209619,0.09077963,-0.05011759,0.00956877,-0.05987085,0.04654075,0.06356976,0.04349371,-0.0432681,-0.06537037,-0.19012266,0.01071217,-0.03687368,0.01441899,0.00706326,-0.01532159,-0.02614736,-0.01399377,0.08530203,-0.07460219,-0.00522572,-0.00995078,-0.00898043,-0.01818117,-0.00854134,0.02337182,-0.09384467,0.00678362,-0.0467683,-0.01965968,-0.05320355,-0.00917431,-0.05476242,0.01458787,0.0331223,0.00639271,0.03453892,-0.0196728,0.04084643,0.00901177,0.03327916,0.05649401,0.03132725,-0.2058327,0.02431356,0.08607803,0.0225823,-0.02661472,0.01727027,0.04705864,0.06687714,0.0079462,0.03079362,0.01112081,0.07716475,0.02927309,0.01630362,0.04125709,-0.0097127,-0.01306687,-0.04136014,0.00097428,0.07710206,-0.01812887,0.04023059,-0.00805179,0.01097049,0.00552242,-0.07826914,-0.01928883,0.00680917,-0.00005543,0.01825678,0.05654763,-0.0269367,-0.05221021,-0.02611574,0.01760816,0.07319938,-0.10492635,0.09454536,-0.0139559,0.04466311,-0.0018045,-0.06795582,0.06313641,-0.00634763,-0.01899085,-0.06463375,0.0310479,0.03685893,0.0018362,-0.00507588,0.02339597,-0.00382805,0.01503238,0.00307202,-0.01611848,-0.01518818,-0.04580049,-0.01356309,0.00831552,0.0531751,0.04732998,-0.02515536,-0.02665939,-0.01085865,0.00537196,0.05959709,0.05183451,0.01824857,0.02749035,0.03979598,-0.04765631,0.01023079,-0.03374286,-0.05727201,-0.0224835,0.0205575,-0.0282171,-0.04768379,-0.03103868,-0.07124034,0.00992037,-0.09821426,-0.07174347,0.07605783,0.00447174,0.00633035,0.0060706,-0.0528728,0.03022814,0.06560203,-0.02612638,-0.07362137,-0.01486009,0.00331953,0.0324093,0.11610678,-0.06205338,-0.03623811,-0.01730514,-0.03847494,-0.04517681,0.15210024,0.03258314,-0.11703447,0.04409323,0.07072061,-0.00999911,-0.06048194,-0.00063114,-0.01110302,-0.04430355,0.00325531,0.06721739,-0.05808722,-0.05198821,0.00032276,-0.01066735,0.04950355,-0.02192597,-0.00170885,-0.07015388,-0.02585268,-0.01064363,-0.03065257,-0.00637744,0.00481362,-0.00411066,0.0473268,-0.11599305,0.02595994,0.0162168,-0.01138193,-0.04318347,0.01719479,-0.03736379,-0.00695564,0.03802051,-0.01673392,0.10357289,0.02880588,-0.01417768,0.00307414,-0.05375235,0.03492199,-0.02005858,-0.04671909,0.01885259,0.02566819,-0.04040834,0.0293063,-0.02541957,0.03931509,-0.00206354,0.00775727,-0.00332276,0.03223858,-0.03477106,0.04819516,0.01441055,0.03395945,-0.08110294,-0.19735885,-0.03395878,0.02092401,-0.05601114,0.0228978,-0.00550476,0.03861366,0.01897781,0.04003487,0.05942729,0.10561947,0.05861287,-0.06197954,0.01221247,-0.00240697,0.03193747,0.06827056,-0.02193309,-0.00432962,0.01732452,-0.02963279,0.06403579,0.02038878,-0.00870184,0.04744607,-0.02664249,0.09003156,-0.03057235,0.00863475,0.0064621,0.0638086,0.01813021,-0.02443991,-0.12013279,0.01149497,0.07712867,-0.01050372,-0.01102196,-0.05293224,-0.00681114,0.01042269,0.03442915,0.04796152,-0.10059707,0.02670656,-0.06274557,-0.0479033,-0.00315461,-0.04343653,0.03799037,0.02230986,0.03693068,0.01598236,0.12147151,0.01550545,0.03096415,0.00310172,-0.03969284,-0.00392741,0.00955506,-0.02173711,0.01015154,-0.01572197,-0.01751208,0.01935445,-0.02803706,0.02637513,0.02543526,0.00400434,-0.05044765,-0.05372369,0.12373216,0.02718817,-0.00642432,0.00802412,0.01989422,-0.0450245,-0.10332429,0.04777599,0.00068451,0.05090215,0.061481,0.05804934,0.04313338,0.01304188,0.04189375,0.0369398,-0.00259064,0.03328048,-0.00854578,-0.02163227,-0.07620382,-0.03799801,-0.00165819,0.08598454,0.02164759,-0.31702325,-0.01462039,-0.02423876,-0.00940126,0.01739901,0.04908191,0.06593497,0.01051129,-0.05350728,0.00866511,-0.0069815,0.04922424,-0.01897063,-0.08843613,0.02051911,0.00189656,0.04387469,0.00352225,0.01370808,0.0261622,-0.04615695,0.05274993,0.23173083,0.00663944,0.04836514,0.02397023,-0.02348727,0.05061514,0.01789251,-0.0050292,-0.01248803,-0.02274014,0.0312714,-0.02166933,0.02758372,0.0617189,-0.01729839,-0.02168962,-0.02212322,0.01988145,-0.00419241,-0.00809711,-0.03181734,0.03539268,0.14317894,-0.04730802,-0.04577975,-0.06117486,0.01136997,0.0479917,-0.04066161,-0.01333676,0.0193171,0.04276963,-0.03777863,0.05929266,0.05873984,-0.03004631,-0.05674157,-0.04731119,-0.00038363,0.02223808,-0.05485834,0.06746301,0.00390319],"last_embed":{"hash":"14lh4z0","tokens":431}}},"text":null,"length":0,"last_read":{"hash":"14lh4z0","at":1751815146235},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**","lines":[247,353],"size":16395,"outlinks":[{"title":"https://github.com/zorp-corp/nockchain","target":"https://github.com/zorp-corp/nockchain","line":47},{"title":"https://github.com/0xmoei/nockchain","target":"https://github.com/0xmoei/nockchain","line":48},{"title":"https://www.nockchain.org/","target":"https://www.nockchain.org/","line":49},{"title":"https://icoanalytics.org/projects/zorp-nockchain/","target":"https://icoanalytics.org/projects/zorp-nockchain/","line":50},{"title":"https://urbit.org/overview/history","target":"https://urbit.org/overview/history","line":51},{"title":"https://github.com/zorp-corp/jock-lang","target":"https://github.com/zorp-corp/jock-lang","line":52},{"title":"https://www.globenewswire.com/news-release/2024/12/30/3002639/0/en/Alpha-Sigma-Capital-Research-Unveils-2025-Crypto-Market-Predictions-AI-Agents-Staking-ETFs-and-Decentralized-Infrastructure-Lead-the-Way.html","target":"https://www.globenewswire.com/news-release/2024/12/30/3002639/0/en/Alpha-Sigma-Capital-Research-Unveils-2025-Crypto-Market-Predictions-AI-Agents-Staking-ETFs-and-Decentralized-Infrastructure-Lead-the-Way.html","line":53},{"title":"https://crypto-fundraising.info/projects/zorp-nockchain/","target":"https://crypto-fundraising.info/projects/zorp-nockchain/","line":54},{"title":"https://members.delphidigital.io/feed/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","target":"https://members.delphidigital.io/feed/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","line":55},{"title":"https://podwise.ai/dashboard/episodes/4062914","target":"https://podwise.ai/dashboard/episodes/4062914","line":56},{"title":"https://player.fm/series/the-delphi-podcast/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","target":"https://player.fm/series/the-delphi-podcast/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","line":57},{"title":"https://tracxn.com/d/companies/zorp/\\_\\_VTLVrft6NMSWw6R9NmmNgOwSmC1kn-XXhp2K\\_MTFXxM","target":"https://tracxn.com/d/companies/zorp/__VTLVrft6NMSWw6R9NmmNgOwSmC1kn-XXhp2K_MTFXxM","line":58},{"title":"https://www.zorp.one/","target":"https://www.zorp.one/","line":59},{"title":"https://www.zorp.one/about","target":"https://www.zorp.one/about","line":60},{"title":"https://en.wikipedia.org/wiki/End\\_of\\_the\\_World\\_(Parks\\_and\\_Recreation)","target":"https://en.wikipedia.org/wiki/End_of_the_World_\\(Parks_and_Recreation\\","line":61},{"title":"https://podcasts.apple.com/gr/podcast/the-delphi-podcast/id1438148082?l=el","target":"https://podcasts.apple.com/gr/podcast/the-delphi-podcast/id1438148082?l=el","line":62},{"title":"https://www.chaincatcher.com/en/article/2181453","target":"https://www.chaincatcher.com/en/article/2181453","line":63},{"title":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","target":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","line":64},{"title":"https://cryptorank.io/ico/nockchain","target":"https://cryptorank.io/ico/nockchain","line":65},{"title":"https://cryptorank.io/price/nockchain","target":"https://cryptorank.io/price/nockchain","line":66},{"title":"https://github.com/zorp-corp/nockapp","target":"https://github.com/zorp-corp/nockapp","line":67},{"title":"https://www.everand.com/podcast/864012011/Logan-Allen-Nockchain-s-Global-Competition-for-Useful-ZK-Proof-of-Work-Miners-Kicks-Off","target":"https://www.everand.com/podcast/864012011/Logan-Allen-Nockchain-s-Global-Competition-for-Useful-ZK-Proof-of-Work-Miners-Kicks-Off","line":68},{"title":"https://www.nervos.org/knowledge-base/what\\_are\\_blockchain\\_intents\\_(explainCKBot)","target":"https://www.nervos.org/knowledge-base/what_are_blockchain_intents_\\(explainCKBot\\","line":69},{"title":"https://volet.com/blog/post/understanding-intents-in-blockchain-a-comprehensive-guide-to-intents-01jdpzq69t52gfwxy6z0bx1nmv","target":"https://volet.com/blog/post/understanding-intents-in-blockchain-a-comprehensive-guide-to-intents-01jdpzq69t52gfwxy6z0bx1nmv","line":70},{"title":"https://crypto.com/en/university/intent-centric-design-for-blockchain","target":"https://crypto.com/en/university/intent-centric-design-for-blockchain","line":71},{"title":"https://www.bitget.com/zh-TC/news/detail/12560604756213","target":"https://www.bitget.com/zh-TC/news/detail/12560604756213","line":72},{"title":"https://www.youtube.com/watch?v=G5tE0LFFiTY","target":"https://www.youtube.com/watch?v=G5tE0LFFiTY","line":73},{"title":"https://ethplorer.io/address/******************************************","target":"https://ethplorer.io/address/******************************************","line":74},{"title":"https://phantom.com/tokens/solana/6tCEUogw2uk4VQz5JKb295ixFrj6vAPEQbZqL9VfFXSz","target":"https://phantom.com/tokens/solana/6tCEUogw2uk4VQz5JKb295ixFrj6vAPEQbZqL9VfFXSz","line":75},{"title":"https://www.dextools.io/app/en/solana/pair-explorer/ENDyunqABSwMvfUoRxsNA1N2QLhXeZQTYrnPZUQdnygP","target":"https://www.dextools.io/app/en/solana/pair-explorer/ENDyunqABSwMvfUoRxsNA1N2QLhXeZQTYrnPZUQdnygP","line":76},{"title":"https://web3.bitget.com/en/wiki/nock-wallet","target":"https://web3.bitget.com/en/wiki/nock-wallet","line":77},{"title":"https://m.sosovalue.com/blog/comprehensive-guide-to-bitcoin-mining","target":"https://m.sosovalue.com/blog/comprehensive-guide-to-bitcoin-mining","line":78},{"title":"https://www.cryptominerbros.com/blog/calculate-crypto-mining-profits/","target":"https://www.cryptominerbros.com/blog/calculate-crypto-mining-profits/","line":79},{"title":"https://asicmarketplace.com/blog/how-to-calculate-profit-in-crypto-mining/","target":"https://asicmarketplace.com/blog/how-to-calculate-profit-in-crypto-mining/","line":80},{"title":"https://www.rootdata.com/Projects/detail/Nockpool?k=MTc2Mzg%3D","target":"https://www.rootdata.com/Projects/detail/Nockpool?k=MTc2Mzg%3D","line":81},{"title":"https://en.wikipedia.org/wiki/Mining\\_pool","target":"https://en.wikipedia.org/wiki/Mining_pool","line":82},{"title":"https://podcasts.apple.com/us/podcast/the-delphi-podcast/id1438148082","target":"https://podcasts.apple.com/us/podcast/the-delphi-podcast/id1438148082","line":83},{"title":"https://icodrops.com/nockchain/","target":"https://icodrops.com/nockchain/","line":84},{"title":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA=","target":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","line":85},{"title":"https://github.com/0xmoei/nockchain/security","target":"https://github.com/0xmoei/nockchain/security","line":86},{"title":"https://dysnix.com/blockchain-security-audit","target":"https://dysnix.com/blockchain-security-audit","line":87},{"title":"https://www.lcx.com/how-to-conduct-blockchain-security-audit/","target":"https://www.lcx.com/how-to-conduct-blockchain-security-audit/","line":88},{"title":"https://veridise.com/audits/nft-security/","target":"https://veridise.com/audits/nft-security/","line":89},{"title":"https://immunebytes.com/blog/blockchain-security-audit-the-ultimate-guide/","target":"https://immunebytes.com/blog/blockchain-security-audit-the-ultimate-guide/","line":90},{"title":"https://hacken.io/services/blockchain-security/blockchain-protocol-security/","target":"https://hacken.io/services/blockchain-security/blockchain-protocol-security/","line":91},{"title":"https://quantstamp.com/audits","target":"https://quantstamp.com/audits","line":92},{"title":"https://www.reddit.com/r/CryptoCurrency/comments/1lhoxfc/outside\\_of\\_the\\_crypto\\_community\\_crypto\\_is\\_still/","target":"https://www.reddit.com/r/CryptoCurrency/comments/1lhoxfc/outside_of_the_crypto_community_crypto_is_still/","line":93},{"title":"https://www.reddit.com/r/CryptoMarkets/comments/1l5jqcu/which\\_crypto\\_community\\_still\\_feels\\_genuinely/","target":"https://www.reddit.com/r/CryptoMarkets/comments/1l5jqcu/which_crypto_community_still_feels_genuinely/","line":94},{"title":"https://www.reddit.com/r/CryptoMarkets/comments/1jakyzw/why\\_community\\_sentiment\\_of\\_every\\_crypto\\_on/","target":"https://www.reddit.com/r/CryptoMarkets/comments/1jakyzw/why_community_sentiment_of_every_crypto_on/","line":95},{"title":"https://www.everand.com/podcast/593966517/Launching-the-Mars-Review-of-Books-with-Noah-Kumin-Noah-Kumin-is-the-founder-and-editor-of-the-Mars-Review-of-Books","target":"https://www.everand.com/podcast/593966517/Launching-the-Mars-Review-of-Books-with-Noah-Kumin-Noah-Kumin-is-the-founder-and-editor-of-the-Mars-Review-of-Books","line":96},{"title":"https://podcasts.apple.com/bt/podcast/urbit-nockchain-and-the-current-state/id1195362330?i=1000709342627","target":"https://podcasts.apple.com/bt/podcast/urbit-nockchain-and-the-current-state/id1195362330?i=1000709342627","line":97},{"title":"https://www.nansen.ai/post/best-telegram-channels-for-crypto-chats","target":"https://www.nansen.ai/post/best-telegram-channels-for-crypto-chats","line":98},{"title":"https://ryanschultz.com/2018/07/17/the-telegram-discussion-groups-for-the-various-blockchain-based-vr-projects-have-been-endlessly-entertaining/","target":"https://ryanschultz.com/2018/07/17/the-telegram-discussion-groups-for-the-various-blockchain-based-vr-projects-have-been-endlessly-entertaining/","line":99},{"title":"https://www.reddit.com/r/CryptoCurrency/comments/lz1f54/beware\\_of\\_fake\\_pump\\_coins\\_groups\\_on\\_telegram/","target":"https://www.reddit.com/r/CryptoCurrency/comments/lz1f54/beware_of_fake_pump_coins_groups_on_telegram/","line":100},{"title":"https://asicmarketplace.com/blog/what-is-aleo/","target":"https://asicmarketplace.com/blog/what-is-aleo/","line":101},{"title":"https://risczero.com/blog/zkvm","target":"https://risczero.com/blog/zkvm","line":102},{"title":"https://www.rootdata.com/Projects/detail/RISC%20Zero?k=MjgwMw%3D%3D","target":"https://www.rootdata.com/Projects/detail/RISC%20Zero?k=MjgwMw%3D%3D","line":103},{"title":"https://en.foresightnews.pro/an-overview-of-privacy-solutions-based-on-zero-knowlege-cryptography/","target":"https://en.foresightnews.pro/an-overview-of-privacy-solutions-based-on-zero-knowlege-cryptography/","line":104},{"title":"https://equilibrium.co/writing/privacy-blockchains-and-aleo-deep-dive","target":"https://equilibrium.co/writing/privacy-blockchains-and-aleo-deep-dive","line":105},{"title":"https://www.blockchaincoinvestors.com/blog/risczero-eliminating-tradeoffs-and-unleashing-the-power-of-blockchain-technology-254ec","target":"https://www.blockchaincoinvestors.com/blog/risczero-eliminating-tradeoffs-and-unleashing-the-power-of-blockchain-technology-254ec","line":106},{"title":"https://podcasts.musixmatch.com/podcast/the-delphi-podcast-01hpa65hx48s2p8649mhbr8ke5/episode/logan-allen-nockchains-global-competition-for-useful-01jvmp9agdsppxbp1n6gwtz1ck","target":"https://podcasts.musixmatch.com/podcast/the-delphi-podcast-01hpa65hx48s2p8649mhbr8ke5/episode/logan-allen-nockchains-global-competition-for-useful-01jvmp9agdsppxbp1n6gwtz1ck","line":107}],"class_name":"SmartBlock","last_embed":{"hash":"14lh4z0","at":1751815146235}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.1. 综合分析 (SWOT)**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08678326,-0.02936188,-0.06164599,-0.03578588,0.00975587,0.01281949,-0.01298455,0.00771784,-0.00986022,-0.05215499,0.06122745,-0.06077442,0.07427725,0.04919171,0.04602551,-0.01128501,0.01830926,-0.04382426,-0.02748069,0.02606315,0.0854174,-0.06405041,0.02839589,-0.0641784,0.05765462,0.06752461,0.04643555,-0.04020043,-0.05976517,-0.20044681,0.01501705,-0.0463829,0.01138784,0.01070215,-0.00655245,-0.03034995,-0.00492712,0.08426309,-0.07841707,-0.00268244,-0.00366593,-0.00732705,-0.0179968,-0.00330809,0.0142151,-0.08602899,0.00556102,-0.04460521,-0.01424121,-0.05945783,-0.00689171,-0.05920073,0.01150903,0.03054183,0.00270884,0.03326793,-0.01215366,0.04501115,0.01228289,0.03878341,0.05043619,0.02749515,-0.20101844,0.02137433,0.09719221,0.02109295,-0.03194971,0.0088237,0.04565409,0.06345202,0.00789921,0.02851388,0.01082768,0.07535788,0.02523382,0.01032158,0.04097835,-0.00864391,-0.01183034,-0.04594209,-0.01107796,0.07289834,0.00044763,0.02916981,0.0009577,0.01708272,-0.00121899,-0.07747667,-0.02216215,0.00453511,-0.01167056,0.02670694,0.0583015,-0.03685814,-0.05119145,-0.02820284,0.01288264,0.06213522,-0.10715475,0.09683132,-0.0093064,0.03772768,-0.0058174,-0.06185055,0.05879477,-0.00385711,-0.00991124,-0.0664789,0.02663577,0.03004011,0.00363697,-0.009975,0.02206169,-0.0162473,0.02825198,0.00979895,-0.00819971,-0.00713769,-0.03332423,-0.00979878,-0.0022033,0.05127289,0.05132788,-0.02318783,-0.01098307,-0.01338524,0.00817217,0.06590679,0.04924362,0.01413356,0.02713248,0.04094072,-0.04151774,0.00700545,-0.04480916,-0.04936877,-0.02445137,0.021818,-0.0253334,-0.04829076,-0.03575634,-0.06480984,0.00225802,-0.105091,-0.06754445,0.07736786,-0.00739099,0.00955553,0.00614663,-0.0584102,0.03898843,0.06035512,-0.0220744,-0.08105946,-0.01497864,0.00566692,0.02967648,0.1119949,-0.06644466,-0.03251755,-0.01523124,-0.03990689,-0.0527052,0.15119863,0.02824961,-0.12509388,0.05001008,0.06636913,-0.01713046,-0.06191782,-0.00315766,-0.01701056,-0.04132855,-0.00107214,0.07933008,-0.05689711,-0.04758273,-0.00698804,-0.00907923,0.04521644,-0.01903266,-0.00541559,-0.05732307,-0.0212549,-0.0072586,-0.02973611,-0.00896167,0.00548256,-0.00480176,0.03970797,-0.12009466,0.01813547,0.02629269,-0.00233236,-0.03250545,0.0141559,-0.02781097,0.00009186,0.03285883,-0.02738513,0.11082232,0.03221416,-0.00488901,0.00071605,-0.0431506,0.02569582,-0.02623818,-0.03728595,0.02436535,0.02487441,-0.03892317,0.0267353,-0.02790402,0.03833479,-0.00060941,-0.00292758,-0.00423835,0.03063201,-0.03275931,0.04974845,0.01519243,0.04048157,-0.09445447,-0.19716777,-0.02788871,0.02898383,-0.05639378,0.02616994,-0.00591934,0.0356296,0.01234098,0.04579085,0.05563241,0.10856413,0.0643961,-0.05947462,0.00682515,-0.00623071,0.0311533,0.06007496,-0.02165115,-0.00741399,0.01850889,-0.03228188,0.06004142,0.01175128,-0.01547315,0.0486098,-0.0205879,0.08816934,-0.03402112,0.01157728,0.00041822,0.05988723,0.01853597,-0.02048053,-0.11364716,0.01214042,0.08402361,-0.00544573,-0.00996464,-0.04758225,-0.00247982,0.00731755,0.02667008,0.05268949,-0.0977285,0.02467339,-0.05764567,-0.05348526,-0.01358905,-0.04047079,0.04325476,0.03070523,0.045908,0.01335297,0.12069849,0.01737748,0.02960761,0.008109,-0.03116248,-0.00363177,0.01739483,-0.01500528,-0.00035027,-0.02233768,-0.01257818,0.01750114,-0.02825234,0.03195319,0.02665552,0.00960379,-0.05150769,-0.04918965,0.12794341,0.02756491,-0.0003953,0.00492814,0.02242265,-0.04084128,-0.1043226,0.04004627,-0.00819253,0.04309851,0.05934162,0.04920992,0.04787134,0.00212716,0.04872372,0.03824452,-0.00191473,0.02648853,-0.00774401,-0.01841115,-0.07160924,-0.03064657,-0.00555403,0.08238852,0.01248677,-0.31331816,-0.01622749,-0.02766408,-0.01469624,0.02109096,0.04639656,0.05790157,0.00835371,-0.05915336,0.02406122,-0.00843519,0.04582856,-0.00804963,-0.07894753,0.02806275,-0.00053234,0.04608196,-0.00355571,0.01392223,0.02297444,-0.04271461,0.06318238,0.2397631,-0.00392823,0.04543712,0.03195221,-0.02857435,0.04812944,0.01676651,-0.01125175,-0.01153281,-0.01898898,0.02551848,-0.01552535,0.02099139,0.04909571,-0.01237263,-0.02802406,-0.01989636,0.02026412,-0.01356866,-0.00787456,-0.02148771,0.03794051,0.14238295,-0.04118073,-0.03946258,-0.06059071,0.00684553,0.04665119,-0.04598016,-0.01189819,0.02465269,0.03652124,-0.03047389,0.0579509,0.05813973,-0.03263809,-0.06600399,-0.04731102,-0.00279702,0.02380599,-0.05696999,0.05867617,0.00459427],"last_embed":{"hash":"1p891qe","tokens":464}}},"text":null,"length":0,"last_read":{"hash":"1p891qe","at":1751815146273},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.1. 综合分析 (SWOT)**","lines":[249,270],"size":599,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"1p891qe","at":1751815146273}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.2. 未来路线图与关键里程碑**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07284041,-0.01026805,-0.06557579,-0.04401804,0.01091005,0.02404026,0.00295589,-0.00176064,-0.00975938,-0.03736994,0.07190062,-0.05711799,0.02926824,0.03011298,0.02800146,-0.02239371,0.01465503,-0.05728934,-0.03519274,0.00659078,0.10260165,-0.06054448,0.03760519,-0.0125483,0.0739068,0.04312425,0.00322241,-0.00922465,-0.02856935,-0.18313366,0.00065623,-0.04932764,-0.00216729,0.02016013,0.01716966,-0.02914984,0.00281984,0.07809243,-0.05587272,-0.00342038,0.01189918,0.02187678,-0.00351667,0.02972844,0.05102293,-0.0862712,0.03063281,-0.05269727,-0.01067065,-0.04855973,-0.01360131,-0.08601392,-0.01715118,0.03097964,0.00046309,0.03040135,0.00353234,0.06696263,0.0274257,0.05510558,0.07107874,0.05016278,-0.18083359,0.02684698,0.10432909,0.00073214,-0.0353502,0.0146811,0.05977006,0.07963071,0.02431577,0.01677734,0.00888883,0.06678364,0.02372938,0.00401845,0.02609859,0.00730313,-0.02000875,-0.05735743,-0.04608642,0.07945755,0.036767,0.00867641,-0.01628538,-0.01185027,0.00663468,-0.04274463,-0.04594039,0.0143626,0.00461277,0.01280705,0.04834675,-0.010584,-0.04375586,-0.00668937,-0.01834719,0.00996981,-0.07801462,0.11609163,-0.03729112,0.04543014,-0.00830058,-0.04640368,0.03492794,0.00157388,-0.00969679,-0.06696873,0.01489498,0.02687576,0.00805356,0.02486453,0.05885556,-0.03276793,0.034212,0.00643221,-0.00407874,-0.01180485,-0.02561096,0.03014168,-0.01860936,0.04516014,0.04225824,-0.02333027,-0.03274952,-0.0216458,0.01054742,0.03380976,0.03247291,0.02246703,0.02863753,0.03611979,-0.06325867,-0.00687064,-0.02665624,-0.03328857,-0.03388249,0.02799354,-0.0437818,-0.04023829,-0.02727119,-0.08242632,-0.03120401,-0.1081963,-0.06602024,0.08199185,-0.01178359,0.01007312,-0.01258727,-0.03618687,0.00618313,0.06826122,-0.03843031,-0.05827042,-0.02354357,-0.005249,0.02433724,0.09927989,-0.0649502,-0.00007834,-0.02719355,-0.03359522,-0.03012066,0.14809765,0.02030054,-0.12411842,0.05835831,0.05179896,-0.00898366,-0.06001048,0.01276757,0.02528922,-0.06906945,-0.0118336,0.07309052,-0.034571,-0.03676517,-0.01610686,0.02754564,0.04071753,0.01309563,-0.023406,-0.09713565,-0.04627367,-0.03897393,-0.01926542,-0.02910406,0.02303095,0.02241597,0.04155793,-0.09682721,0.02456271,0.01130188,0.02300246,-0.03477943,-0.01595674,-0.02144434,-0.02725028,0.02354749,-0.06870141,0.14934005,0.03548304,0.00757798,0.02119074,-0.04289773,0.01323076,-0.03793359,-0.06382927,-0.00202826,0.03025135,-0.02805613,0.03398493,-0.02949177,0.05036759,-0.0217184,0.02926539,0.00387129,0.04390524,-0.01598733,0.06575464,0.02444499,0.00095328,-0.09078201,-0.17947872,-0.0250184,0.05313003,-0.02958546,0.03697847,0.01571923,0.04741285,0.02308561,0.05704797,0.06945713,0.08037676,0.05419789,-0.05906041,0.00701525,-0.00949011,0.05817604,0.04740784,-0.01499773,-0.012373,0.03250565,-0.03658373,0.02832361,0.0146029,-0.0664712,0.03945581,-0.05970943,0.09494946,-0.02793555,0.01794086,0.00202748,0.06670111,0.00219252,-0.05335925,-0.14562842,0.01036247,0.05398025,-0.01767535,-0.00845422,-0.05044518,-0.02425871,0.00466585,0.01673296,0.06244906,-0.10889405,0.02895821,-0.06577266,-0.05578786,-0.00688586,0.0109117,0.02081861,0.00400147,0.04796883,0.01249884,0.11919186,-0.01935741,-0.00413806,0.0385363,-0.03134228,-0.02014831,0.04691715,-0.00809726,-0.01356059,-0.01750721,-0.0016307,0.01639282,-0.00810632,0.00868453,0.00640584,0.04303183,-0.03410186,-0.03714233,0.14391428,0.02250984,-0.00985002,0.04166668,0.0345729,-0.05084252,-0.11489493,0.05790544,0.00029197,0.0114881,0.016435,0.0400885,0.05258634,-0.01090251,0.06247554,0.01881762,-0.01618579,0.003188,-0.0128825,-0.00544726,-0.03140471,-0.0533941,-0.02492162,0.05205522,0.00357124,-0.31014824,-0.02172638,-0.01488065,-0.00424965,0.06313805,0.03485797,0.05922197,0.03176621,-0.0690896,0.01527763,-0.01006969,0.04455002,-0.00311397,-0.05009912,0.00770482,0.00750808,0.05645035,0.0026917,0.01945785,0.04111924,0.00137737,0.07471424,0.22412135,-0.03192801,0.05191663,0.02282237,-0.06194232,0.03929352,0.0724326,0.00677674,-0.0500383,-0.01729183,0.02572256,-0.04341085,0.0375404,0.03120712,-0.00814635,-0.0244918,-0.02630806,0.01497072,-0.03063229,-0.02412413,-0.01975203,0.03592649,0.11584887,-0.02481724,-0.0554697,-0.06698212,0.00515695,0.05089508,-0.04361463,-0.01721754,0.0130732,0.0400701,-0.00102069,0.06953978,0.0329712,-0.04259615,-0.07546448,-0.00204671,-0.03597842,-0.02144957,-0.03778369,0.05550254,0.02438323],"last_embed":{"hash":"jep9ez","tokens":312}}},"text":null,"length":0,"last_read":{"hash":"jep9ez","at":1751815146290},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.2. 未来路线图与关键里程碑**","lines":[271,281],"size":300,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"jep9ez","at":1751815146290}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.3. 针对用户的建议**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06451966,-0.01657132,-0.05279567,-0.02852753,0.03020288,0.01970391,-0.00432228,0.00574894,0.00322972,-0.0274056,0.06496982,-0.06091453,0.02225082,0.03119747,0.02947468,-0.02666393,0.01851426,-0.04962661,-0.01689873,0.0278758,0.09302113,-0.07273342,0.04632549,-0.00674791,0.0782196,0.0368786,0.01856631,-0.01861969,-0.03042794,-0.18572791,0.00465761,-0.05069696,-0.01514441,0.01650137,0.01814404,-0.03771452,-0.00260775,0.0679842,-0.05219416,0.00351709,0.00671915,0.01381482,-0.00765112,0.01061468,0.05526016,-0.08161228,0.02821148,-0.05702198,-0.00950595,-0.06403956,-0.02483228,-0.09917759,-0.01368267,0.03372258,0.00058977,0.02316611,-0.00558553,0.04801732,0.02561091,0.0409129,0.06753352,0.04707094,-0.19621548,0.02701098,0.10556083,0.00098208,-0.03547506,0.0255107,0.05243102,0.08110791,0.01973983,0.01033948,-0.00492683,0.05990574,0.0298123,0.00396699,0.03755893,0.01438706,-0.01458461,-0.05768333,-0.05053445,0.08977493,0.01885321,-0.00459113,-0.01896882,-0.02878923,0.00357527,-0.06261043,-0.04836554,0.01299223,-0.00234678,0.01166693,0.04180413,-0.00983786,-0.01832281,-0.00309983,-0.00302137,0.00925263,-0.06014727,0.12044698,-0.04127243,0.05074997,0.0027321,-0.05255592,0.03839172,0.00378768,-0.00838647,-0.06155166,0.02358273,0.03571401,0.00357078,0.01499444,0.06436395,-0.02625182,0.03531718,0.00434822,-0.00014483,-0.01145623,-0.00891082,0.01935333,-0.02257162,0.05223859,0.04246911,-0.02621211,-0.03539633,-0.02775424,0.00242075,0.04478363,0.04906981,0.01871088,0.03468512,0.02947156,-0.06157807,-0.00364013,-0.05056915,-0.0301727,-0.01460303,0.0128507,-0.04634993,-0.03704859,-0.03286347,-0.08419061,-0.0172466,-0.11427129,-0.07845819,0.0941368,-0.00961132,0.00294629,0.00869223,-0.04647003,0.02479865,0.07115988,-0.03992698,-0.07420197,-0.03243446,0.01065292,0.02606467,0.10893473,-0.05922187,-0.01151181,-0.01778126,-0.03134543,-0.03269769,0.1300378,0.02458089,-0.10651869,0.05230642,0.03118116,-0.01112188,-0.07469857,0.01611473,0.02059448,-0.05938289,-0.00345745,0.08073036,-0.03701267,-0.03832548,-0.00745507,0.01937706,0.04091084,0.00773269,-0.01243628,-0.09185893,-0.02239843,-0.03031831,-0.0101831,-0.02937678,0.03235497,0.01478355,0.05251773,-0.082734,0.04320488,0.0083909,0.02424432,-0.0319736,-0.00683377,-0.02469729,-0.02731835,0.01146185,-0.05524899,0.13962568,0.03447036,0.00713776,0.01100525,-0.04947556,0.01300061,-0.02492421,-0.06117761,0.00586577,0.02551459,-0.01942453,0.02540305,-0.0424542,0.03889586,-0.00615167,0.01059033,0.01245685,0.03172307,-0.01519342,0.07292272,0.02322428,0.00538605,-0.09448453,-0.17129411,-0.04207994,0.04534648,-0.02158045,0.02647242,0.02031411,0.04521501,0.03657578,0.05141676,0.08635636,0.08905074,0.05885078,-0.05348099,0.00261156,-0.02324196,0.05303857,0.06151372,-0.0118407,-0.02033718,0.02502369,-0.04626401,0.02372931,0.02560198,-0.05917268,0.04815649,-0.06547767,0.08529354,-0.03413913,0.00200849,-0.00272383,0.07708648,0.01035862,-0.052946,-0.15039754,0.01534144,0.06912921,-0.01657137,-0.02126036,-0.05905772,-0.02582434,-0.0075287,0.01030319,0.05942803,-0.09133716,0.01813108,-0.05494025,-0.06019696,-0.01452845,-0.00734867,0.02044483,0.00758402,0.03847448,0.02017579,0.11110774,-0.02819443,-0.0070121,0.02681313,-0.03617344,-0.01366857,0.04008282,-0.01770564,-0.01620851,-0.03367745,0.00057485,0.02672749,-0.01118971,-0.00137848,0.02849591,0.0224078,-0.02421563,-0.03588384,0.14735413,0.02427983,0.00569521,0.03898374,0.02904737,-0.03169224,-0.09473389,0.04662427,-0.00554832,0.02668774,-0.00080695,0.02983321,0.05823742,0.00224296,0.04972554,0.02261509,-0.0262548,0.00996849,0.00643805,-0.00801358,-0.04257767,-0.04540353,-0.03086078,0.0717045,0.01787732,-0.30244425,-0.01652708,-0.02927646,-0.00514977,0.07716184,0.04364621,0.06119159,0.02511399,-0.05644302,0.01065493,0.00660405,0.04283377,-0.0165283,-0.04860594,-0.00154477,-0.00060533,0.06138986,-0.00397016,0.02292797,0.05221544,0.00101332,0.08420345,0.22623694,-0.02957671,0.05405036,0.02732755,-0.0507779,0.04612407,0.04344432,0.00252872,-0.04757048,-0.0144617,0.03160533,-0.03830168,0.031632,0.04510977,-0.01140337,-0.0143756,-0.01246632,0.01789134,-0.03379676,-0.02670216,-0.0258331,0.04183261,0.13121615,-0.02631176,-0.06152258,-0.08394233,-0.00124352,0.05564901,-0.05195411,-0.02433049,0.01346614,0.04051929,-0.01450192,0.06636995,0.03850583,-0.05093685,-0.07890613,-0.01084774,-0.03760906,-0.01817616,-0.05928357,0.06592104,0.01325374],"last_embed":{"hash":"2782l8","tokens":431}}},"text":null,"length":0,"last_read":{"hash":"2782l8","at":1751815146304},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**8.3. 针对用户的建议**","lines":[282,290],"size":442,"outlinks":[],"class_name":"SmartBlock","last_embed":{"hash":"2782l8","at":1751815146304}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07621755,-0.0386887,-0.05395742,-0.02393359,0.00303804,0.01449185,-0.03382569,0.02078548,-0.01287802,-0.03188117,0.0642691,-0.05307376,0.02065614,0.03596324,0.02422254,-0.0340804,-0.01071222,-0.04482778,-0.00217241,0.00918773,0.12011801,-0.1160295,0.02923223,-0.02748339,0.06944504,0.03780071,0.01184053,-0.01188665,-0.01978902,-0.19623908,0.01765846,-0.03931552,-0.00778511,0.01409546,0.02392122,-0.01848888,-0.01169418,0.05579006,-0.05303919,0.01849479,0.01847203,0.00544065,-0.01105454,0.0168236,0.06877092,-0.09866034,0.00558986,-0.05419335,-0.01527605,-0.02285257,-0.00066854,-0.05195105,-0.01736552,0.02043814,-0.00436894,0.01373142,0.01940935,0.05724367,0.0393982,0.03665942,0.04540057,0.06259976,-0.21294293,0.0241481,0.10604713,0.01417509,-0.05317034,0.01735416,0.04823781,0.07693648,0.01557621,0.02136106,0.01086839,0.04387997,0.01036063,-0.01705565,0.02878818,-0.00577344,-0.01992791,-0.06521676,-0.03112722,0.08651033,0.00588752,0.00410702,0.01284253,-0.02510847,0.023526,-0.02982617,0.00465793,0.00578906,-0.00444487,-0.01219108,0.0642091,0.00643653,-0.05751237,0.02486278,0.0058626,0.02016186,-0.04830075,0.10861455,-0.03486185,0.05815952,0.01671915,-0.04579959,0.03946235,-0.01203837,-0.03177875,-0.04498011,0.03220757,0.03643042,-0.031003,0.02635404,0.06588878,-0.04566489,0.02603086,-0.01279047,-0.00457197,-0.0149678,0.01017391,0.02154319,0.0028569,0.05779606,0.06272017,0.00542075,-0.01420179,-0.02040795,0.00183354,0.04028327,0.04679152,0.02988045,0.04403246,0.01747715,-0.05602631,0.00567227,-0.03006035,-0.0046982,-0.00912968,-0.00569495,-0.03719627,-0.03708195,-0.03447584,-0.08569438,-0.00705013,-0.08069857,-0.07018202,0.10029643,0.01679258,0.00672417,0.0009742,-0.05048334,0.01121124,0.07775922,-0.04371537,-0.06673027,0.00003679,0.02762882,0.04729968,0.09585381,-0.06293726,-0.00604046,-0.02551663,-0.02313619,-0.04523638,0.14736165,0.03246124,-0.1278519,0.03033133,0.03096451,-0.00292253,-0.08696422,0.00532662,0.00566833,-0.04607109,-0.01942228,0.07963422,-0.04229017,-0.05004581,-0.03550213,0.02995782,0.04874475,-0.01655015,-0.00939625,-0.08815291,0.00203704,-0.05350596,-0.01262122,-0.04187445,0.02653554,0.00010084,0.05850779,-0.08756169,0.03561041,-0.01732557,0.01300385,-0.03050442,-0.00876528,-0.03940329,-0.01901225,0.03082361,-0.08875779,0.09002679,0.01834605,0.01727439,-0.00351078,-0.03048484,-0.00103846,-0.02526473,-0.01216124,-0.00387046,0.03454011,-0.01693245,0.04302573,-0.02364663,0.03817253,-0.02346263,0.01523384,0.03224918,0.02631634,-0.01397654,0.04824902,-0.0110626,0.04085247,-0.10475601,-0.17050271,-0.02018294,0.07073068,-0.02694857,0.03584537,0.00020846,0.04517082,0.02414897,0.05077018,0.09993272,0.10360558,0.03063042,-0.05205883,0.02135976,-0.02761736,0.05902085,0.05358882,-0.02284005,-0.02228232,0.03633974,-0.033912,0.01754844,0.0134019,-0.06751198,0.02808907,-0.07379764,0.09402061,-0.00651679,0.03687365,0.0006067,0.09789754,-0.01003005,-0.03543681,-0.14766209,0.03693577,0.06392623,-0.00969157,-0.03018994,-0.04150255,-0.01736165,-0.00921161,0.0169436,0.07332818,-0.11465635,0.00693998,-0.0609578,-0.05483795,-0.00659943,-0.03553117,0.01997228,0.0003451,0.03063097,0.01731064,0.11902416,-0.01454178,-0.00896028,0.01069982,-0.04905776,-0.02360359,0.05037456,0.00089303,0.02132838,-0.03660715,-0.01568598,0.06432047,-0.03210108,0.02242704,0.00132506,0.04100089,-0.03968652,-0.02324629,0.11826975,0.01376242,0.0197169,0.02684608,0.026154,-0.06024783,-0.09991427,0.01213919,0.02139846,0.02068489,0.02378779,0.03560094,0.02690072,0.03283846,0.05245849,0.0406788,-0.04449428,0.02144917,-0.00858646,-0.02732278,-0.03134366,-0.02421319,-0.02449853,0.05807475,0.03032251,-0.30900598,-0.01953913,-0.01628485,-0.01222523,0.04310058,0.0411763,0.06950776,0.00096551,-0.07918879,0.02998655,0.00229006,0.02130403,-0.00915503,-0.03958185,-0.00421,0.01035366,0.06813065,-0.00133603,0.02598034,0.03460425,-0.00414901,0.06575847,0.20377308,-0.01139738,0.01499755,0.01131306,-0.03437554,0.04649552,0.040992,-0.00757638,-0.0533637,-0.0235495,0.05389277,-0.04155232,0.00361093,0.07345622,0.01218934,-0.00277851,-0.00860127,0.01741865,-0.04261328,-0.04892714,0.00407213,0.02520601,0.11546333,-0.01644551,-0.06209258,-0.06686024,-0.01335621,0.06240224,-0.06880064,-0.00470828,0.01252275,0.04448624,-0.03659679,0.04675163,0.03068825,-0.05074554,-0.06433735,-0.00044344,-0.01609466,-0.01873543,-0.07538366,0.06804138,0.00941704],"last_embed":{"hash":"3x8g8l","tokens":479}}},"text":null,"length":0,"last_read":{"hash":"3x8g8l","at":1751815146333},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**","lines":[291,353],"size":15027,"outlinks":[{"title":"https://github.com/zorp-corp/nockchain","target":"https://github.com/zorp-corp/nockchain","line":3},{"title":"https://github.com/0xmoei/nockchain","target":"https://github.com/0xmoei/nockchain","line":4},{"title":"https://www.nockchain.org/","target":"https://www.nockchain.org/","line":5},{"title":"https://icoanalytics.org/projects/zorp-nockchain/","target":"https://icoanalytics.org/projects/zorp-nockchain/","line":6},{"title":"https://urbit.org/overview/history","target":"https://urbit.org/overview/history","line":7},{"title":"https://github.com/zorp-corp/jock-lang","target":"https://github.com/zorp-corp/jock-lang","line":8},{"title":"https://www.globenewswire.com/news-release/2024/12/30/3002639/0/en/Alpha-Sigma-Capital-Research-Unveils-2025-Crypto-Market-Predictions-AI-Agents-Staking-ETFs-and-Decentralized-Infrastructure-Lead-the-Way.html","target":"https://www.globenewswire.com/news-release/2024/12/30/3002639/0/en/Alpha-Sigma-Capital-Research-Unveils-2025-Crypto-Market-Predictions-AI-Agents-Staking-ETFs-and-Decentralized-Infrastructure-Lead-the-Way.html","line":9},{"title":"https://crypto-fundraising.info/projects/zorp-nockchain/","target":"https://crypto-fundraising.info/projects/zorp-nockchain/","line":10},{"title":"https://members.delphidigital.io/feed/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","target":"https://members.delphidigital.io/feed/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","line":11},{"title":"https://podwise.ai/dashboard/episodes/4062914","target":"https://podwise.ai/dashboard/episodes/4062914","line":12},{"title":"https://player.fm/series/the-delphi-podcast/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","target":"https://player.fm/series/the-delphi-podcast/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","line":13},{"title":"https://tracxn.com/d/companies/zorp/\\_\\_VTLVrft6NMSWw6R9NmmNgOwSmC1kn-XXhp2K\\_MTFXxM","target":"https://tracxn.com/d/companies/zorp/__VTLVrft6NMSWw6R9NmmNgOwSmC1kn-XXhp2K_MTFXxM","line":14},{"title":"https://www.zorp.one/","target":"https://www.zorp.one/","line":15},{"title":"https://www.zorp.one/about","target":"https://www.zorp.one/about","line":16},{"title":"https://en.wikipedia.org/wiki/End\\_of\\_the\\_World\\_(Parks\\_and\\_Recreation)","target":"https://en.wikipedia.org/wiki/End_of_the_World_\\(Parks_and_Recreation\\","line":17},{"title":"https://podcasts.apple.com/gr/podcast/the-delphi-podcast/id1438148082?l=el","target":"https://podcasts.apple.com/gr/podcast/the-delphi-podcast/id1438148082?l=el","line":18},{"title":"https://www.chaincatcher.com/en/article/2181453","target":"https://www.chaincatcher.com/en/article/2181453","line":19},{"title":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","target":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","line":20},{"title":"https://cryptorank.io/ico/nockchain","target":"https://cryptorank.io/ico/nockchain","line":21},{"title":"https://cryptorank.io/price/nockchain","target":"https://cryptorank.io/price/nockchain","line":22},{"title":"https://github.com/zorp-corp/nockapp","target":"https://github.com/zorp-corp/nockapp","line":23},{"title":"https://www.everand.com/podcast/864012011/Logan-Allen-Nockchain-s-Global-Competition-for-Useful-ZK-Proof-of-Work-Miners-Kicks-Off","target":"https://www.everand.com/podcast/864012011/Logan-Allen-Nockchain-s-Global-Competition-for-Useful-ZK-Proof-of-Work-Miners-Kicks-Off","line":24},{"title":"https://www.nervos.org/knowledge-base/what\\_are\\_blockchain\\_intents\\_(explainCKBot)","target":"https://www.nervos.org/knowledge-base/what_are_blockchain_intents_\\(explainCKBot\\","line":25},{"title":"https://volet.com/blog/post/understanding-intents-in-blockchain-a-comprehensive-guide-to-intents-01jdpzq69t52gfwxy6z0bx1nmv","target":"https://volet.com/blog/post/understanding-intents-in-blockchain-a-comprehensive-guide-to-intents-01jdpzq69t52gfwxy6z0bx1nmv","line":26},{"title":"https://crypto.com/en/university/intent-centric-design-for-blockchain","target":"https://crypto.com/en/university/intent-centric-design-for-blockchain","line":27},{"title":"https://www.bitget.com/zh-TC/news/detail/12560604756213","target":"https://www.bitget.com/zh-TC/news/detail/12560604756213","line":28},{"title":"https://www.youtube.com/watch?v=G5tE0LFFiTY","target":"https://www.youtube.com/watch?v=G5tE0LFFiTY","line":29},{"title":"https://ethplorer.io/address/******************************************","target":"https://ethplorer.io/address/******************************************","line":30},{"title":"https://phantom.com/tokens/solana/6tCEUogw2uk4VQz5JKb295ixFrj6vAPEQbZqL9VfFXSz","target":"https://phantom.com/tokens/solana/6tCEUogw2uk4VQz5JKb295ixFrj6vAPEQbZqL9VfFXSz","line":31},{"title":"https://www.dextools.io/app/en/solana/pair-explorer/ENDyunqABSwMvfUoRxsNA1N2QLhXeZQTYrnPZUQdnygP","target":"https://www.dextools.io/app/en/solana/pair-explorer/ENDyunqABSwMvfUoRxsNA1N2QLhXeZQTYrnPZUQdnygP","line":32},{"title":"https://web3.bitget.com/en/wiki/nock-wallet","target":"https://web3.bitget.com/en/wiki/nock-wallet","line":33},{"title":"https://m.sosovalue.com/blog/comprehensive-guide-to-bitcoin-mining","target":"https://m.sosovalue.com/blog/comprehensive-guide-to-bitcoin-mining","line":34},{"title":"https://www.cryptominerbros.com/blog/calculate-crypto-mining-profits/","target":"https://www.cryptominerbros.com/blog/calculate-crypto-mining-profits/","line":35},{"title":"https://asicmarketplace.com/blog/how-to-calculate-profit-in-crypto-mining/","target":"https://asicmarketplace.com/blog/how-to-calculate-profit-in-crypto-mining/","line":36},{"title":"https://www.rootdata.com/Projects/detail/Nockpool?k=MTc2Mzg%3D","target":"https://www.rootdata.com/Projects/detail/Nockpool?k=MTc2Mzg%3D","line":37},{"title":"https://en.wikipedia.org/wiki/Mining\\_pool","target":"https://en.wikipedia.org/wiki/Mining_pool","line":38},{"title":"https://podcasts.apple.com/us/podcast/the-delphi-podcast/id1438148082","target":"https://podcasts.apple.com/us/podcast/the-delphi-podcast/id1438148082","line":39},{"title":"https://icodrops.com/nockchain/","target":"https://icodrops.com/nockchain/","line":40},{"title":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA=","target":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","line":41},{"title":"https://github.com/0xmoei/nockchain/security","target":"https://github.com/0xmoei/nockchain/security","line":42},{"title":"https://dysnix.com/blockchain-security-audit","target":"https://dysnix.com/blockchain-security-audit","line":43},{"title":"https://www.lcx.com/how-to-conduct-blockchain-security-audit/","target":"https://www.lcx.com/how-to-conduct-blockchain-security-audit/","line":44},{"title":"https://veridise.com/audits/nft-security/","target":"https://veridise.com/audits/nft-security/","line":45},{"title":"https://immunebytes.com/blog/blockchain-security-audit-the-ultimate-guide/","target":"https://immunebytes.com/blog/blockchain-security-audit-the-ultimate-guide/","line":46},{"title":"https://hacken.io/services/blockchain-security/blockchain-protocol-security/","target":"https://hacken.io/services/blockchain-security/blockchain-protocol-security/","line":47},{"title":"https://quantstamp.com/audits","target":"https://quantstamp.com/audits","line":48},{"title":"https://www.reddit.com/r/CryptoCurrency/comments/1lhoxfc/outside\\_of\\_the\\_crypto\\_community\\_crypto\\_is\\_still/","target":"https://www.reddit.com/r/CryptoCurrency/comments/1lhoxfc/outside_of_the_crypto_community_crypto_is_still/","line":49},{"title":"https://www.reddit.com/r/CryptoMarkets/comments/1l5jqcu/which\\_crypto\\_community\\_still\\_feels\\_genuinely/","target":"https://www.reddit.com/r/CryptoMarkets/comments/1l5jqcu/which_crypto_community_still_feels_genuinely/","line":50},{"title":"https://www.reddit.com/r/CryptoMarkets/comments/1jakyzw/why\\_community\\_sentiment\\_of\\_every\\_crypto\\_on/","target":"https://www.reddit.com/r/CryptoMarkets/comments/1jakyzw/why_community_sentiment_of_every_crypto_on/","line":51},{"title":"https://www.everand.com/podcast/593966517/Launching-the-Mars-Review-of-Books-with-Noah-Kumin-Noah-Kumin-is-the-founder-and-editor-of-the-Mars-Review-of-Books","target":"https://www.everand.com/podcast/593966517/Launching-the-Mars-Review-of-Books-with-Noah-Kumin-Noah-Kumin-is-the-founder-and-editor-of-the-Mars-Review-of-Books","line":52},{"title":"https://podcasts.apple.com/bt/podcast/urbit-nockchain-and-the-current-state/id1195362330?i=1000709342627","target":"https://podcasts.apple.com/bt/podcast/urbit-nockchain-and-the-current-state/id1195362330?i=1000709342627","line":53},{"title":"https://www.nansen.ai/post/best-telegram-channels-for-crypto-chats","target":"https://www.nansen.ai/post/best-telegram-channels-for-crypto-chats","line":54},{"title":"https://ryanschultz.com/2018/07/17/the-telegram-discussion-groups-for-the-various-blockchain-based-vr-projects-have-been-endlessly-entertaining/","target":"https://ryanschultz.com/2018/07/17/the-telegram-discussion-groups-for-the-various-blockchain-based-vr-projects-have-been-endlessly-entertaining/","line":55},{"title":"https://www.reddit.com/r/CryptoCurrency/comments/lz1f54/beware\\_of\\_fake\\_pump\\_coins\\_groups\\_on\\_telegram/","target":"https://www.reddit.com/r/CryptoCurrency/comments/lz1f54/beware_of_fake_pump_coins_groups_on_telegram/","line":56},{"title":"https://asicmarketplace.com/blog/what-is-aleo/","target":"https://asicmarketplace.com/blog/what-is-aleo/","line":57},{"title":"https://risczero.com/blog/zkvm","target":"https://risczero.com/blog/zkvm","line":58},{"title":"https://www.rootdata.com/Projects/detail/RISC%20Zero?k=MjgwMw%3D%3D","target":"https://www.rootdata.com/Projects/detail/RISC%20Zero?k=MjgwMw%3D%3D","line":59},{"title":"https://en.foresightnews.pro/an-overview-of-privacy-solutions-based-on-zero-knowlege-cryptography/","target":"https://en.foresightnews.pro/an-overview-of-privacy-solutions-based-on-zero-knowlege-cryptography/","line":60},{"title":"https://equilibrium.co/writing/privacy-blockchains-and-aleo-deep-dive","target":"https://equilibrium.co/writing/privacy-blockchains-and-aleo-deep-dive","line":61},{"title":"https://www.blockchaincoinvestors.com/blog/risczero-eliminating-tradeoffs-and-unleashing-the-power-of-blockchain-technology-254ec","target":"https://www.blockchaincoinvestors.com/blog/risczero-eliminating-tradeoffs-and-unleashing-the-power-of-blockchain-technology-254ec","line":62},{"title":"https://podcasts.musixmatch.com/podcast/the-delphi-podcast-01hpa65hx48s2p8649mhbr8ke5/episode/logan-allen-nockchains-global-competition-for-useful-01jvmp9agdsppxbp1n6gwtz1ck","target":"https://podcasts.musixmatch.com/podcast/the-delphi-podcast-01hpa65hx48s2p8649mhbr8ke5/episode/logan-allen-nockchains-global-competition-for-useful-01jvmp9agdsppxbp1n6gwtz1ck","line":63}],"class_name":"SmartBlock","last_embed":{"hash":"3x8g8l","at":1751815146333}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03686527,-0.03315094,-0.06103392,-0.00485408,0.01067928,-0.01112594,-0.01531456,0.03354037,-0.02572269,-0.01529348,0.07289642,-0.0785762,0.012274,0.03722343,0.01952809,-0.02355078,0.00606208,-0.03880387,-0.03182688,0.01575623,0.10811143,-0.10869084,0.03323476,-0.06228579,0.06832461,0.0277583,0.00489425,-0.01745674,-0.02761738,-0.17898448,0.02281751,-0.05885151,0.00055114,-0.00176256,0.02576714,-0.02022793,-0.0194561,0.06580129,-0.03598584,0.00363764,0.04536396,0.02053171,-0.05392886,0.02002765,0.06533638,-0.10355968,0.00199142,-0.03536983,-0.03344195,-0.02985937,0.00622224,-0.06353815,-0.00900247,0.03978044,-0.01674217,0.00163893,0.02450279,0.04412668,0.02168616,0.04072732,0.05371002,0.04435859,-0.21879099,0.00844169,0.08744882,0.00507213,-0.05690995,0.00739747,0.03098138,0.06265901,0.03666797,0.0172807,0.01884649,0.0297729,0.00692689,-0.02621673,0.03151015,0.00277474,-0.02295686,-0.04087751,-0.00048576,0.07936075,0.00237143,0.00562116,0.01843357,-0.01330145,0.02624524,-0.01407094,0.01180588,-0.00147901,0.00179454,-0.03499664,0.05229907,0.00826279,-0.03918581,0.04382727,-0.00018196,0.02430681,-0.05316543,0.10210366,-0.01236506,0.05721755,-0.01349221,-0.04646803,0.0079476,-0.02587812,-0.04417038,-0.04567534,0.03243265,0.02592606,-0.01620148,0.02280241,0.05812519,-0.03833842,0.02011471,-0.02185461,0.00626969,-0.00121959,0.0239495,0.02141603,-0.01599886,0.0575516,0.06687905,0.01418819,0.00700904,-0.02010642,-0.00324383,0.07271634,0.01556274,0.02208405,0.04903098,-0.00947871,-0.07905556,0.00632204,-0.01214275,-0.00389561,-0.01185269,0.004394,-0.04052331,-0.02629712,-0.05081744,-0.05716306,0.01864289,-0.10172724,-0.04139899,0.11452635,0.02943674,0.01139972,0.01054821,-0.05593454,-0.00529337,0.06615195,-0.04052655,-0.05839085,0.00787568,0.03126844,0.03146226,0.07478726,-0.07606558,0.00677213,-0.04010916,-0.01204627,-0.0461317,0.15856388,0.03775367,-0.13256018,0.04982906,0.03093357,-0.03144042,-0.08603504,-0.00299305,0.01563394,-0.06701067,-0.00730188,0.08356921,-0.0469951,-0.04288062,-0.04083516,-0.01164206,0.05846694,-0.00986145,-0.01040216,-0.08327781,-0.00683115,-0.04543267,-0.02445746,-0.03651248,0.03208438,-0.00569498,0.05382979,-0.09302637,0.03853782,-0.04377602,0.01819758,-0.03542792,-0.02654486,-0.03837335,0.00430365,0.02366284,-0.05624999,0.12511335,0.00948367,0.01527953,0.02841494,-0.01959005,0.005688,-0.02870987,-0.00720324,-0.02068954,0.03031442,-0.01729349,0.05267336,-0.02468309,0.03682056,-0.04242981,0.00598265,0.02454993,0.02285594,-0.01485949,0.0697544,-0.01762454,0.04495428,-0.08324841,-0.19933496,-0.01486311,0.05885394,-0.03840712,0.05396534,0.01042884,0.05765354,0.00887118,0.05500647,0.08601017,0.09847188,0.03074533,-0.03317573,0.01770385,0.00818723,0.05545392,0.04741853,-0.02578575,-0.01092591,0.02862926,-0.05109056,0.01985111,0.04040261,-0.09369997,0.02343794,-0.07291019,0.10448793,-0.00184782,0.01889717,-0.00414962,0.06793834,-0.02136893,-0.04157977,-0.12971298,0.0345317,0.05084398,0.00248926,-0.00154895,-0.04202948,-0.02762361,-0.01992372,0.02025099,0.07585213,-0.11586928,0.01899682,-0.04891816,-0.03146062,0.05233303,-0.02060265,0.0390593,-0.01405003,0.03240566,0.0286639,0.1065285,0.00444213,-0.02996069,0.01225965,-0.02426046,-0.01727935,0.04118849,0.00269953,-0.00809414,-0.04990005,-0.02409831,0.06100484,-0.04885421,0.02039439,0.01254132,0.03568252,-0.05698054,-0.02069216,0.0903253,0.00418497,-0.00447404,0.07940728,0.03459672,-0.0462194,-0.1019311,0.00800133,0.04315223,0.01991938,0.00066881,0.01736943,0.02921892,0.015669,0.07229237,0.03596596,-0.02237999,0.00524534,-0.02569526,-0.02066715,-0.02627526,-0.05266805,0.00310931,0.06130461,0.02003857,-0.29889968,-0.00788579,-0.01187198,-0.00316871,0.05748233,0.04749534,0.07464381,0.0139079,-0.05808175,0.00698122,-0.01164851,0.01069304,-0.00664066,-0.05189239,-0.01351133,0.02679255,0.06972619,-0.02010651,0.03330133,0.03941736,-0.00017222,0.06785362,0.22785643,0.013066,-0.00075061,0.00903355,-0.05549459,0.04118843,0.05609205,-0.02756457,-0.04162865,-0.03121238,0.05360553,-0.03244543,0.01428697,0.06880756,0.02642064,-0.00203563,-0.01060086,0.03170467,-0.02334944,-0.05966123,-0.01739264,0.03128828,0.09800967,-0.02720189,-0.06602329,-0.05461358,-0.02162983,0.06747168,-0.0556128,-0.02392304,0.00108426,0.01943864,-0.01382828,0.06912697,0.02224698,-0.04816777,-0.05382283,-0.00739657,-0.01760616,-0.00227721,-0.07853463,0.03814244,0.00219538],"last_embed":{"hash":"4e9r35","tokens":156}}},"text":null,"length":0,"last_read":{"hash":"4e9r35","at":1751815146374},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{4}","lines":[296,296],"size":218,"outlinks":[{"title":"https://icoanalytics.org/projects/zorp-nockchain/","target":"https://icoanalytics.org/projects/zorp-nockchain/","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"4e9r35","at":1751815146374}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{7}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01156024,-0.08129716,-0.05948034,0.00951339,0.05057709,0.02552672,-0.02193129,0.03908649,0.00326158,-0.01268263,0.06037598,-0.04887981,0.00642337,0.01046042,0.0413542,-0.01724524,-0.04076582,-0.03659592,-0.02574681,0.01967566,0.11289211,-0.02935304,0.02839941,-0.09197458,0.05695407,0.00325837,0.03515677,-0.06972868,-0.08274744,-0.18429387,-0.01531942,-0.06291365,0.01336978,0.0194532,0.0038008,-0.02835033,-0.00514534,0.07328771,-0.03013861,0.0256898,0.00286973,-0.00149839,0.01583436,0.0171011,0.04316235,-0.07720483,0.00190018,-0.03979434,-0.04230542,-0.0285271,0.00605404,-0.07103009,-0.06013921,0.03140416,0.02425478,0.0141355,-0.02060791,0.02391629,0.03738373,0.05207768,0.06682642,0.02461916,-0.21692172,0.00953018,0.05631068,0.08649563,-0.04557519,-0.00470745,0.03282818,0.05436867,0.04783391,0.0194768,0.01894974,-0.03035939,0.05860046,0.03969783,0.0016758,-0.00672866,0.00575626,-0.05873274,-0.0244961,0.0716432,-0.05841783,-0.00051146,-0.07539146,-0.01185909,0.01595603,-0.0249598,0.02539932,-0.01775327,0.00198561,0.00739358,-0.01243289,0.01169933,-0.04483836,0.00369917,0.01597469,0.02206647,-0.05641818,0.10117664,-0.02276136,0.0847927,-0.00635353,-0.0519842,0.01915196,-0.02431217,-0.0499174,-0.0633176,0.00720139,0.02273492,0.01781068,0.02820804,0.03927359,-0.07519681,-0.00120242,0.00989389,0.03288483,0.02884972,-0.01169095,0.0025295,-0.01908229,0.02092772,0.06132616,-0.00733074,-0.03984935,0.02636124,-0.00696026,0.073861,0.0230795,0.04403485,0.03498682,0.02474099,-0.12108703,-0.01656392,-0.00581004,-0.02386963,-0.03622149,-0.02444004,-0.03218591,-0.0206839,-0.08294654,-0.0116254,-0.01411743,-0.11051873,-0.04273073,0.09444404,0.04442659,0.00300833,0.00020238,-0.04009994,0.0303303,0.07230481,0.00386504,-0.0491286,-0.01842985,-0.02085127,0.02957239,0.09003436,-0.12504843,-0.00937186,-0.05204821,-0.03405542,-0.05880556,0.14525391,0.01239287,-0.0891977,-0.02713723,0.00141517,0.02615512,-0.06913074,0.02404837,0.00751265,-0.05413075,-0.01659492,0.04392061,0.01406354,-0.0643946,-0.05896249,0.01450622,0.02540086,-0.01840585,-0.00901276,-0.05250983,0.02301271,-0.03813999,-0.01616531,-0.01891994,0.00116664,0.01380329,-0.01794809,-0.06510954,0.07557923,-0.02229826,0.0825227,0.00663052,0.02172501,-0.04208633,-0.02812581,0.02429588,-0.05278024,0.04556045,0.03850561,-0.00839432,-0.00510391,-0.03576716,-0.00750696,-0.03711085,-0.04386031,-0.00268946,0.06773191,0.00222611,0.02416488,-0.00808864,0.02883983,-0.04456144,0.02534522,0.02546282,0.02219319,-0.01734078,0.04234587,0.03174664,0.06670529,-0.0741332,-0.18372725,-0.04934072,0.02248629,-0.03003151,0.05664637,0.00004506,0.07147958,0.03830548,0.05911205,0.10022039,0.05067088,0.06238983,-0.03803227,0.04133407,0.02625914,0.02570686,-0.00984795,-0.03140044,0.00412444,0.07301304,-0.01280907,0.02522021,-0.035639,-0.08647203,0.04394246,-0.00261671,0.07362515,-0.02949643,0.0103033,-0.03950733,0.02118288,0.01272644,-0.02073005,-0.10543109,0.05355548,-0.00134131,0.0558062,-0.00164488,-0.07300942,-0.04356967,-0.01560647,0.05238792,0.02123711,-0.08784968,-0.04129197,-0.041158,-0.01621706,0.03037165,-0.01528066,0.04012055,0.03720173,0.02340757,0.03957292,0.06182005,0.02563591,-0.03920625,0.04590262,-0.01076728,0.01652922,0.08614866,0.04629327,0.01733928,-0.02706055,-0.02937696,0.06428438,-0.02307786,-0.00339414,0.01117426,0.01866289,-0.02185774,-0.02739549,0.17257157,0.02128791,0.01607758,0.07155067,-0.01644714,-0.04422833,-0.08227842,-0.010259,0.08115514,0.04930678,-0.03454414,0.07431252,0.06248016,0.03321163,0.09310783,-0.00263293,-0.00292779,0.00220235,0.01227571,-0.00715134,-0.03183576,-0.0679369,-0.06791858,0.04776418,0.01954438,-0.29373601,0.01468487,0.03164336,0.01023228,0.03633006,-0.00075444,0.06734671,0.01948653,-0.05399727,0.03446466,-0.04276613,0.06429112,0.02618291,-0.00601849,-0.03836054,-0.03356684,0.07982214,-0.02276819,0.04938184,0.02882069,-0.01094357,0.05186637,0.2228784,0.00651156,0.01335292,0.03746752,-0.0580183,0.0375828,0.05179419,-0.03292106,0.01966758,-0.00226458,0.02053194,-0.07996464,0.02062513,0.04451073,0.00315852,-0.01656681,-0.01445411,0.00577197,0.02110654,0.01261252,0.02011303,0.03694794,0.10762619,-0.04371981,-0.03307606,-0.05338758,0.00724611,0.04168704,-0.0519871,-0.00499626,-0.01392696,0.01384403,-0.00790955,0.04811582,0.03319088,-0.04562574,-0.05189703,-0.0283154,0.0024365,-0.05086554,-0.08354435,0.00693431,-0.03582333],"last_embed":{"hash":"d5ymcp","tokens":281}}},"text":null,"length":0,"last_read":{"hash":"d5ymcp","at":1751815146382},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{7}","lines":[299,299],"size":602,"outlinks":[{"title":"https://www.globenewswire.com/news-release/2024/12/30/3002639/0/en/Alpha-Sigma-Capital-Research-Unveils-2025-Crypto-Market-Predictions-AI-Agents-Staking-ETFs-and-Decentralized-Infrastructure-Lead-the-Way.html","target":"https://www.globenewswire.com/news-release/2024/12/30/3002639/0/en/Alpha-Sigma-Capital-Research-Unveils-2025-Crypto-Market-Predictions-AI-Agents-Staking-ETFs-and-Decentralized-Infrastructure-Lead-the-Way.html","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"d5ymcp","at":1751815146382}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{9}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07912377,-0.01787034,-0.05986443,0.02325314,0.0114277,-0.00844699,-0.02335445,0.03825026,-0.04946969,-0.02540784,0.03485489,-0.04429542,0.0089825,0.0025866,-0.00056789,-0.01905901,-0.025921,-0.00803807,-0.02262157,0.00412811,0.07164063,-0.05767121,0.02244909,-0.05024403,0.11305375,0.05243108,0.01694596,-0.03194126,-0.01686418,-0.20356089,-0.00513412,-0.06152808,0.00333034,-0.00050034,0.05512827,-0.05486569,-0.00920803,0.07023294,-0.12011928,0.00068431,0.03973363,-0.00012022,0.0108554,-0.02284991,0.05330998,-0.07883412,0.0202881,-0.0530213,-0.06943248,-0.01292061,0.01748668,-0.05591877,-0.02347247,0.02736592,0.0342946,0.02172827,0.01015775,0.0437546,0.04642959,0.01244513,0.06956717,0.0379593,-0.21656458,0.01770494,0.09671625,0.03826474,-0.03750975,0.04338993,0.03787713,0.1037712,0.0402109,0.02079775,0.00710825,0.03583627,0.01041032,0.05254022,0.03112398,-0.05173466,-0.0550282,-0.06620419,-0.06345023,0.04949553,-0.01321363,-0.0026594,-0.06384104,0.00314451,0.03056704,-0.04495427,0.00837363,-0.01508719,0.00405968,0.00413972,0.04701309,0.02477026,-0.02900801,-0.01807435,0.03850433,0.03638208,-0.04568784,0.11371379,-0.03191037,0.05609336,-0.02833572,-0.05535069,0.05608307,-0.02655189,-0.01766543,-0.01857116,0.00445888,0.01405372,0.02324953,0.02293512,0.06130331,-0.03480707,0.00869844,0.00692137,0.06622835,0.00975738,0.01471547,0.01152954,0.0250647,0.07670273,0.00733233,-0.0153216,-0.07143495,-0.01700187,0.03020006,0.04383776,0.06325317,0.00222295,0.06421844,0.01444122,-0.03878875,0.01600189,-0.02484787,-0.00776174,0.016472,0.02288469,-0.05984183,0.02376289,0.00503981,-0.05152487,-0.00245422,-0.11619639,-0.035312,0.06395048,0.00848526,-0.01066354,0.02212012,-0.06532786,0.00447393,0.03418531,-0.02261277,-0.05556371,0.03041673,0.03473688,0.0492987,0.08377535,-0.05635997,-0.00477005,-0.06069798,0.00588519,-0.04734604,0.13517478,0.05377107,-0.10872643,0.03392039,0.03408988,-0.01822247,-0.07822748,0.030911,-0.00523366,-0.07818243,-0.01653364,0.05303175,-0.0676716,-0.05749703,-0.00548103,0.01915141,0.05221564,0.00557602,-0.02360524,-0.08018761,-0.02138566,0.0111622,-0.02399874,-0.0060057,0.00410057,0.04380347,0.03775523,-0.08250157,0.03017876,-0.04337026,0.0123778,-0.03760657,-0.04431685,-0.04971623,-0.04662184,0.02967803,-0.05685425,0.07401385,0.04700497,0.0015765,-0.01852183,-0.04191888,0.0105188,-0.02591445,-0.04909026,0.02111295,0.01402258,-0.04084237,-0.01117782,-0.05924037,0.00714185,0.00925813,0.02171435,0.04594579,0.00417378,-0.03880365,0.02933621,0.02514283,0.00727546,-0.05622973,-0.19059849,-0.07967433,-0.00080782,0.03083253,0.06206272,-0.02154008,0.03694285,0.011472,0.03673558,0.06763772,0.10445391,0.04978255,-0.07081258,0.02988822,0.01001457,0.01641245,-0.0087085,-0.04335611,0.05037098,0.03764204,0.00011986,0.02693851,-0.009553,-0.08034051,0.00888619,-0.04476021,0.12343431,0.02418421,0.016121,-0.01984506,0.02324556,0.02786062,-0.05940628,-0.13828638,0.02210945,0.0712398,-0.00322134,-0.03711437,0.00770944,-0.02994729,-0.01242086,0.05994708,0.01621711,-0.12639125,0.05532278,-0.04953979,-0.05803595,0.04259152,-0.02859718,0.03649449,0.00017792,0.06860281,0.04795494,0.10055579,-0.01120294,-0.02631383,0.01577467,-0.04280152,0.00744642,0.03567947,-0.02898902,0.00112222,-0.0619454,0.01528014,0.06057162,0.01042725,-0.00453112,0.01676833,0.03730202,0.00002267,-0.01898696,0.1066648,0.06422549,-0.02267683,0.0206527,0.02571226,-0.01903543,-0.08300714,0.0235953,0.01658613,0.0441636,-0.02129907,0.04934501,0.06480119,0.02059691,0.09686956,0.01894691,0.01960195,0.02667568,0.01057832,-0.01660814,-0.00979123,-0.06095429,-0.06821413,0.05616383,0.00638808,-0.27999392,0.01663333,-0.02240183,-0.00729708,0.01052173,0.00663129,0.06631745,-0.00848524,-0.07448508,0.00665369,-0.02577801,0.05705436,-0.00184542,-0.02687515,0.00372621,0.00920637,0.0689308,-0.01597158,0.03975277,0.03145286,0.00412849,0.07978723,0.18754239,-0.03922359,0.06259516,0.03887967,-0.0329506,0.02594904,0.05109583,0.00469462,-0.00070035,-0.03012833,0.01980194,-0.04783269,-0.0000809,0.08904346,-0.05573228,-0.01890131,0.00839464,-0.01175276,-0.03467859,-0.03714674,-0.01049021,0.02722676,0.12873612,-0.0528617,-0.06217713,-0.09470543,0.00333515,0.01357084,-0.03308283,-0.03333511,-0.00901636,-0.01472163,-0.0226366,0.03876798,0.06052972,-0.02261791,-0.05873267,-0.01655926,0.00159765,0.02316686,-0.0441889,0.05815377,0.02682698],"last_embed":{"hash":"szmkil","tokens":202}}},"text":null,"length":0,"last_read":{"hash":"szmkil","at":1751815146400},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{9}","lines":[301,301],"size":366,"outlinks":[{"title":"https://members.delphidigital.io/feed/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","target":"https://members.delphidigital.io/feed/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"szmkil","at":1751815146400}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{10}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07970389,-0.04747464,-0.0346971,-0.00634058,0.03205593,-0.00249847,-0.0118158,0.05293499,-0.03419252,-0.01719597,0.04802104,-0.05761759,0.00926435,0.05797379,0.00655377,-0.01866497,0.00458624,-0.00857671,-0.01348029,0.01163546,0.10106906,-0.06070659,0.00573188,-0.05885909,0.06233609,0.04917099,0.04821581,-0.03256339,-0.02035747,-0.22112992,0.02225242,-0.06011205,0.01017936,0.01703265,0.0450127,-0.04684084,-0.0281169,0.08188727,-0.1057899,0.00645618,0.0292267,-0.00721553,0.0067672,-0.03275466,0.02864666,-0.08454057,0.02383659,-0.0232585,-0.04025148,-0.00634491,0.01230301,-0.05969439,-0.02725476,0.02208594,0.04084413,0.00973111,0.01839413,0.03532395,0.04503782,0.02106259,0.07201378,0.0227017,-0.20844884,0.01977978,0.09927317,0.04393397,-0.03571441,0.0108877,0.01939731,0.09137662,0.04555791,0.01940924,0.01742352,0.02616811,0.0083068,0.05729283,0.0220969,-0.04474789,-0.04311691,-0.09313732,-0.08226388,0.03963067,-0.03684877,0.02708038,-0.04434182,-0.00004009,-0.01482391,-0.06057459,-0.00720314,-0.02557912,0.02117511,0.03460906,0.06031183,0.01973959,0.00352874,-0.02249219,0.03396668,0.02031806,-0.063655,0.10750619,-0.01576839,0.05038147,0.00178737,-0.06352478,0.05319076,-0.01467477,-0.02727247,-0.0254905,0.01382859,0.00598446,0.0216387,0.0342994,0.07839492,-0.05494177,0.03013046,0.02333083,0.01883772,0.03864393,0.00553094,0.00343915,0.03997803,0.07964259,0.03212335,-0.04211337,-0.07181637,-0.02034097,0.03898751,0.07387504,0.04417383,-0.00002736,0.05097193,0.01123776,-0.0436255,0.02371993,-0.00106978,-0.04272959,0.0076187,0.01445853,-0.08078456,0.01288403,-0.03952904,-0.05838623,-0.0111055,-0.10694157,-0.03271154,0.0749644,0.01885695,0.00693938,0.00260876,-0.03127314,0.02053021,0.02389159,-0.01255729,-0.05160646,0.01347598,0.02002509,0.07752499,0.10602247,-0.07893289,0.00483801,-0.06439596,-0.01279207,-0.02522934,0.13977951,0.05930177,-0.09879143,0.01646616,0.04192521,0.00124493,-0.05031325,0.01387565,0.00020892,-0.04940011,0.01190875,0.05761409,-0.07844058,-0.05905282,0.00049152,0.00865376,0.05255261,0.00710284,-0.01658127,-0.05907938,-0.00358552,-0.02436747,-0.01889467,-0.00054995,0.01479743,0.03572422,0.03360454,-0.04712925,0.0253951,-0.03468245,0.04012737,-0.02903769,-0.04332325,-0.02615051,-0.03545126,0.01409763,-0.01523358,0.06041135,0.0495017,0.01109909,0.00229472,-0.06082539,-0.00947379,-0.0624589,-0.05368137,-0.01125818,0.03458318,-0.04126777,-0.01839777,-0.05256207,0.04373365,-0.01041372,0.01477943,0.02436907,-0.00246512,-0.02624234,0.02265967,0.02134387,0.03536528,-0.06659602,-0.21758313,-0.05255365,0.02042024,0.02503803,0.0248408,-0.03617974,0.036324,0.01736364,0.05878199,0.06768293,0.07485473,0.02101181,-0.03119034,0.00567729,0.00430146,0.03636387,0.00251369,-0.01097605,0.01690933,0.04115769,-0.00624389,0.02107367,0.01630492,-0.10963666,0.02859876,-0.05329306,0.11394442,0.01500961,0.01256127,-0.00162792,0.02989404,0.02461874,-0.03844042,-0.14797118,0.00680774,0.05886721,0.02087076,-0.05507717,-0.03478119,-0.03457995,-0.05388157,0.0594412,0.02465445,-0.1244893,0.05113205,-0.05680914,-0.07344466,0.00507509,-0.03695777,0.01247994,0.00180762,0.07337213,0.01956289,0.08804622,-0.01762405,-0.03832746,0.02867304,-0.03756358,0.01875017,0.02909596,-0.00982829,-0.0108334,-0.06163866,0.02079907,0.04027418,0.00055109,-0.00118984,0.02033043,0.00988735,-0.02459985,-0.01836378,0.11635847,0.04895008,-0.01347145,0.02996503,0.01418398,-0.01569507,-0.06617465,0.05021092,0.02012068,0.03838423,-0.00842354,0.03551919,0.06867416,0.05617955,0.0983676,0.03312687,0.00681131,0.04513531,0.0139512,0.00487101,-0.01758145,-0.06923763,-0.06305067,0.07930932,0.02241902,-0.27695379,0.01853192,-0.03673885,0.00385655,-0.00306779,0.01056808,0.0430871,0.01442387,-0.06076223,-0.01289568,-0.01161481,0.0443386,-0.00811438,-0.04087765,-0.00052909,0.01904305,0.08432645,0.01174153,0.04346286,0.04503311,-0.0225847,0.07776607,0.19724053,-0.0283792,0.05613436,0.04570489,-0.04349243,0.00552577,0.02492763,0.01119579,-0.0375374,-0.04790697,0.01097368,-0.04832498,0.02254474,0.06840639,-0.05144251,0.00729812,0.01750682,0.0058623,-0.04820295,-0.03142352,-0.01235385,0.04404357,0.10565334,-0.04413231,-0.07888629,-0.06447919,-0.00743648,0.03606891,-0.02152999,-0.03924426,-0.01742918,0.01910646,-0.01900321,0.07291254,0.05162727,-0.01953584,-0.06747182,-0.04632199,-0.02893131,0.00191294,-0.02848239,0.06771778,-0.00695467],"last_embed":{"hash":"3ssjkl","tokens":149}}},"text":null,"length":0,"last_read":{"hash":"3ssjkl","at":1751815146412},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{10}","lines":[302,302],"size":240,"outlinks":[{"title":"https://podwise.ai/dashboard/episodes/4062914","target":"https://podwise.ai/dashboard/episodes/4062914","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"3ssjkl","at":1751815146412}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{11}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08711938,-0.02174752,-0.0401658,0.00313174,0.03863089,-0.02122235,-0.01829039,0.03388981,-0.0544777,-0.0156819,0.02429798,-0.03746779,0.00195053,0.01143262,0.01173467,-0.01806574,-0.02944599,-0.0069627,-0.01544294,0.00633304,0.07212933,-0.05514446,0.01106382,-0.052913,0.08566178,0.04565632,0.02800268,-0.03873718,-0.034441,-0.22681917,-0.00894414,-0.05688995,0.03073322,-0.01013973,0.03877645,-0.06231467,-0.01909106,0.091571,-0.12017415,0.01180426,0.03581424,-0.01221895,-0.00289269,-0.03315382,0.03385105,-0.06709494,0.02537662,-0.03530146,-0.04428319,-0.00214445,0.01466589,-0.04948252,-0.0201306,0.02677801,0.05326688,0.01290448,0.01207771,0.03687568,0.02895981,0.01888353,0.07567481,0.03122043,-0.22768718,0.03199093,0.0841881,0.06484163,-0.03333454,0.05689351,0.02885393,0.0989052,0.04612839,0.03549829,0.02185045,0.02213638,0.02606458,0.05054406,0.02368615,-0.05061248,-0.05050949,-0.08172258,-0.06448666,0.03276104,-0.03119617,-0.00550065,-0.04237866,0.00640907,0.01691612,-0.06350783,0.00014485,-0.02118017,0.00121311,0.00957962,0.06226706,0.03249975,-0.01498235,-0.00913738,0.03097722,0.04785535,-0.06210311,0.11763103,-0.02419453,0.06681747,-0.01983545,-0.04752083,0.05576014,-0.03749028,-0.02048032,-0.02192062,0.01673679,0.03084095,0.02012473,0.0115058,0.07143681,-0.02934263,-0.00247286,0.03540475,0.05788526,0.01882726,0.00568708,-0.01468467,0.04853205,0.08446687,-0.00354884,-0.02946052,-0.06961693,-0.02905385,0.03926121,0.05929639,0.06323591,0.0057283,0.05346328,0.0228055,-0.03184959,0.02825407,0.01017025,-0.0215113,0.00837057,0.00757659,-0.07667305,0.01907515,0.01028768,-0.05842523,-0.00787171,-0.10661303,-0.03166737,0.06757111,0.01592606,-0.02056318,0.01603209,-0.04873163,0.00268953,0.02494256,-0.00428607,-0.04918471,0.02909991,0.0197741,0.07634156,0.08468325,-0.06591022,-0.00375261,-0.07236006,0.00347324,-0.01723315,0.15100478,0.06016311,-0.09640954,0.0203805,0.03913701,0.00076236,-0.0646865,0.01830788,-0.00975345,-0.07696784,-0.00049427,0.05336,-0.07289818,-0.04875537,0.00402811,0.00890431,0.04051214,0.00186007,-0.01758975,-0.06712267,-0.02384897,-0.00686695,-0.04264699,0.00275514,0.00609435,0.03432836,0.04764559,-0.07104789,0.0292577,-0.02676641,0.01656328,-0.06091621,-0.06210632,-0.03446688,-0.05125239,0.03663386,-0.03198561,0.03760045,0.04685193,-0.00936293,-0.00093555,-0.07740708,0.01462066,-0.03434638,-0.05298478,0.04039387,0.02029438,-0.04933484,-0.01188517,-0.05607272,0.01645787,0.02052743,0.02031894,0.03311947,-0.00041589,-0.05038629,0.02800184,-0.00446102,-0.00035554,-0.05302459,-0.20122685,-0.04509027,-0.00811275,0.02151955,0.04144242,-0.03529465,0.05478424,0.01036853,0.05396226,0.06768172,0.08513432,0.03099672,-0.06327275,0.02725961,0.010757,0.00563898,-0.00249637,-0.04571957,0.04061259,0.04804343,0.0178908,0.03414543,0.0032179,-0.08972793,0.00653369,-0.04474963,0.11609772,0.0325706,0.03067448,-0.01737502,0.00556283,0.01606887,-0.03133579,-0.13488847,-0.0000113,0.07181903,0.03810658,-0.04789428,0.0078469,-0.04416281,-0.03636528,0.06853414,-0.00329496,-0.1250086,0.04303761,-0.05567664,-0.07257314,0.0304483,-0.03943144,0.03644364,0.00961963,0.06731774,0.05242159,0.08816485,-0.02659126,-0.01857529,-0.0052312,-0.05757322,0.02456183,0.02848112,-0.02472633,0.01488032,-0.0700477,0.0037354,0.06047254,0.02369485,0.01242606,0.02268664,0.03225833,-0.02319915,-0.01931009,0.08159509,0.06360994,-0.02006737,-0.00547716,0.01688526,-0.00328719,-0.08996373,0.04052673,0.00581338,0.05561531,0.0096717,0.05435832,0.05124369,0.04013266,0.08347014,0.02908014,0.01617283,0.03882329,-0.00698854,-0.02016356,0.00187286,-0.05161101,-0.08132088,0.05455049,0.00551523,-0.26892355,0.00056385,-0.00101065,-0.01008625,-0.00940786,0.00536675,0.05895612,0.0146138,-0.07648196,-0.00687541,-0.01904449,0.05579477,-0.00332316,-0.02240928,0.00928156,0.00014537,0.08041219,0.00270162,0.03979168,0.03214676,-0.01805513,0.06757404,0.17782302,-0.02045693,0.07354109,0.04668055,-0.02237002,0.01971573,0.03594125,0.01722813,-0.01308808,-0.02837194,0.02224782,-0.04754408,0.00511781,0.085695,-0.07103232,-0.00816989,0.01972174,-0.00783511,-0.01755933,-0.03935665,-0.0043245,0.0329224,0.11913562,-0.06322639,-0.07851962,-0.08411566,-0.00105495,0.01422427,-0.03062169,-0.02386868,0.01117491,-0.01308939,-0.01862764,0.06133517,0.05012397,-0.03008314,-0.04049259,-0.01393937,-0.00735067,0.02044419,-0.05135075,0.05524453,0.0330814],"last_embed":{"hash":"1dtd59k","tokens":212}}},"text":null,"length":0,"last_read":{"hash":"1dtd59k","at":1751815146421},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{11}","lines":[303,303],"size":411,"outlinks":[{"title":"https://player.fm/series/the-delphi-podcast/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","target":"https://player.fm/series/the-delphi-podcast/logan-allen-nockchains-global-competition-for-useful-zk-proof-of-work-miners-kicks-off","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1dtd59k","at":1751815146421}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{12}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04945421,-0.02688072,-0.06102703,-0.01139366,0.02639348,0.02855157,-0.01478137,0.02232403,-0.01763088,-0.03989045,0.05766694,-0.07424253,0.03668976,0.03060879,0.01330976,-0.0359615,0.01087233,-0.05575418,0.00190843,0.05672337,0.07718396,-0.08906586,0.05347908,-0.02139376,0.08268487,0.02503087,-0.00067984,-0.0084946,-0.0320687,-0.18829924,0.00112742,-0.06068162,-0.01244287,0.0191052,0.04108949,-0.01799652,-0.02032111,0.09007456,-0.02372444,0.0121356,-0.01207382,0.00488565,-0.01236185,0.02382232,0.06685844,-0.10276163,0.01573356,-0.03693744,-0.01739725,-0.03865319,-0.00908372,-0.08797597,-0.01064287,0.0403588,0.00775784,0.01260039,0.00260258,0.03398816,0.03598503,0.02635958,0.07884539,0.02562286,-0.22744967,0.02829199,0.07864983,0.05712979,-0.03618975,0.03113196,0.03061952,0.09616288,0.02365148,-0.00271388,0.00315042,0.03585552,0.01053577,0.01164401,0.03333871,0.00612839,-0.02328463,-0.04853094,-0.01855463,0.07174167,-0.0019661,-0.029356,-0.00517179,-0.00746715,0.04010068,-0.03181047,-0.01218775,0.0067281,0.01000785,0.02507566,0.05399391,-0.00740713,-0.06407638,-0.00249882,0.02429905,0.02425752,-0.08499009,0.11441358,-0.04080202,0.05606165,0.00834744,-0.06526098,0.05910594,-0.0079629,-0.00767391,-0.02705048,0.02049738,0.04248857,0.00759316,0.01493827,0.05075045,-0.04992573,0.03680919,0.01257545,0.00322168,-0.00869179,-0.02514706,0.00830098,-0.01444141,0.05912992,0.0528619,-0.00118735,-0.05141973,-0.02607581,0.02209471,0.04023283,0.03398567,0.02676062,0.05859063,0.04558082,-0.06469359,0.00168812,-0.03193902,-0.03248433,-0.03713098,0.00767233,-0.06370781,-0.03349969,-0.03957774,-0.07789521,0.00207836,-0.10755707,-0.07514129,0.08109976,-0.00212367,-0.00965901,-0.0013,-0.02869601,0.01212746,0.06205645,0.00125659,-0.06688045,-0.02825964,0.02070758,0.02587172,0.10116707,-0.05989362,0.01045088,-0.01616063,-0.04889445,-0.05869619,0.15601523,0.04157962,-0.1508199,0.01317673,0.05492876,0.01239436,-0.07686256,-0.00606037,0.0008884,-0.05974119,-0.01546206,0.05549285,-0.03411138,-0.06578656,-0.02403971,0.00362652,0.01995949,0.00138778,-0.00029279,-0.07599514,-0.01043457,-0.00225252,0.01063419,-0.01576272,0.01740023,-0.01295331,0.03218287,-0.06974017,0.02749037,-0.00898125,0.02266441,-0.01170976,-0.00110429,-0.02025373,-0.0270722,0.0275461,-0.05472164,0.10076106,0.02556852,0.00211097,0.03115285,-0.05100952,-0.00639046,-0.02893681,-0.04417236,0.03317063,0.02516361,-0.03763214,0.03182583,-0.01745642,0.02635969,-0.03081694,0.04196752,0.02758808,0.03617194,-0.01341038,0.07014015,0.0173449,0.05763547,-0.05914911,-0.18194072,-0.02063945,0.03830592,-0.01311894,0.01329717,0.02130706,0.02777064,0.03641309,0.05324096,0.08527549,0.08013047,0.06410191,-0.04525824,0.01460052,0.00079771,0.05255017,0.03139607,-0.00032228,0.0024318,0.00408732,-0.05278618,0.01214912,-0.04963984,-0.06638284,0.06523997,-0.05181996,0.0980464,-0.00996471,-0.01293504,0.0103424,0.06879644,0.01015162,-0.03574757,-0.11723512,0.04833303,0.04026347,0.00003715,-0.02240023,-0.04341333,-0.04095527,-0.00865645,0.00562687,0.03361867,-0.11090241,0.0098988,-0.06030647,-0.0514028,0.00579831,-0.02578079,0.03042411,0.01444194,0.04499715,0.02031405,0.11720619,0.00923937,-0.04359338,0.01312883,-0.03013675,-0.00422323,0.03235634,-0.02178493,0.00441007,-0.04051467,0.01370542,0.03497304,-0.02684735,0.0030078,0.02670244,0.02789125,-0.04056794,-0.03443843,0.10480722,0.04331465,-0.00248625,0.04813616,0.01797804,-0.059423,-0.0829469,0.03096781,0.01126794,0.00818055,-0.01712555,0.05388955,0.06060259,0.00878408,0.04479127,0.02691636,-0.00892265,0.04973106,-0.00705542,-0.03886526,-0.04331563,-0.04620057,-0.03208624,0.07507113,0.01674498,-0.30594149,-0.02724095,-0.02382192,-0.0267446,0.04797016,0.05218348,0.04821183,0.01013139,-0.05945666,0.01922101,-0.00188228,0.03846157,0.01379735,-0.02645524,0.01503417,0.00609526,0.08494291,-0.02985218,0.02881438,0.05752876,-0.01223331,0.056762,0.22194315,-0.03721116,0.04032581,0.02657328,-0.06640782,0.03664966,0.07224496,-0.00754066,-0.0335198,-0.00854296,0.02922121,-0.04429586,0.00480223,0.04377073,-0.00251832,-0.01404538,-0.02280691,0.02152364,-0.02506161,-0.02657241,-0.01080839,0.04695035,0.13557878,-0.04332381,-0.0561318,-0.08322848,0.00370998,0.05531251,-0.06151437,-0.01922451,-0.01027212,0.0436984,-0.0072966,0.06059603,0.04135038,-0.05723573,-0.05740335,-0.04066722,-0.03526134,-0.01903188,-0.09073807,0.04311503,0.00728212],"last_embed":{"hash":"1ykxskm","tokens":216}}},"text":null,"length":0,"last_read":{"hash":"1ykxskm","at":1751815146430},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{12}","lines":[304,304],"size":275,"outlinks":[{"title":"https://tracxn.com/d/companies/zorp/\\_\\_VTLVrft6NMSWw6R9NmmNgOwSmC1kn-XXhp2K\\_MTFXxM","target":"https://tracxn.com/d/companies/zorp/__VTLVrft6NMSWw6R9NmmNgOwSmC1kn-XXhp2K_MTFXxM","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1ykxskm","at":1751815146430}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{15}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05641728,-0.02960221,-0.00640995,-0.02799153,0.03560472,0.03549189,-0.0090269,0.00278493,0.00564631,-0.02375203,0.06314096,-0.09272552,0.02210728,0.07455683,0.01266352,-0.03643298,0.00426648,-0.00412541,-0.03545967,0.02636405,0.14181463,-0.04827073,0.02046332,-0.01058417,0.0040816,0.0685114,-0.01150672,-0.04155334,-0.06111495,-0.19542882,-0.03041937,-0.06184395,-0.03635636,-0.00004622,-0.00658588,-0.04781327,-0.00635649,0.05152581,-0.07109996,0.04556074,0.03392407,0.00822906,0.02399218,0.02232247,0.04840516,-0.08400298,0.02035361,-0.06283181,-0.01201226,-0.05270935,0.01228098,-0.04154296,-0.02046488,0.03470223,-0.00906863,0.073333,-0.00196577,0.05143954,0.03226464,0.02949294,0.09703536,0.05466553,-0.23749818,0.03497177,0.07846778,0.03709596,-0.01916442,0.0491899,0.04930546,0.06174539,-0.00752751,0.02993001,0.03172561,0.04432542,0.01780244,0.02373327,0.00276536,0.0059785,-0.02769073,-0.05909342,-0.04886051,0.06172227,0.04484196,-0.00599936,-0.02171042,-0.02043437,0.00022523,-0.04071771,-0.06023713,0.02940787,-0.01553703,0.00216724,0.08799063,0.00502863,-0.05551923,-0.02713358,0.00732193,-0.00121551,-0.02437431,0.11244691,-0.08280499,0.05369325,-0.00359867,-0.04514248,0.03820881,-0.03473493,-0.02805083,-0.0425111,0.0199745,-0.00623063,-0.01283787,0.0090485,0.05475095,-0.01066383,0.05005042,0.02592158,0.0156633,0.01131303,-0.02659482,0.02171407,0.00513438,0.04907407,0.03370901,-0.0077017,-0.06148462,-0.03110457,0.03028062,0.02918267,0.081818,0.00355203,0.0263209,0.03657806,-0.07593602,-0.01200772,-0.03370054,-0.01790426,0.01672154,-0.01934715,-0.01524051,-0.03509787,0.01432734,-0.11285871,-0.0219274,-0.09900654,-0.0622676,0.10260609,0.01331731,-0.02100676,-0.00090511,0.0218304,0.02892523,0.07982995,-0.00609417,-0.03755368,-0.0316721,0.03675867,0.06397478,0.09676928,-0.04128793,-0.02420143,-0.04355339,-0.03371707,-0.02184528,0.11959357,0.04654629,-0.09502471,0.0452595,0.03487886,-0.02531468,-0.09511136,-0.01497529,-0.01330574,-0.01893156,0.02149509,0.11256402,-0.01465392,-0.05722537,-0.01877114,0.03672436,0.04850573,0.00572135,-0.01223784,-0.07550057,0.00750546,-0.0330806,-0.00924654,-0.0110808,0.01518127,0.0423993,0.07067745,-0.05035109,-0.01951266,0.01617131,-0.00054105,-0.00807817,-0.02407884,-0.06467483,-0.04251152,0.04961082,-0.07000621,0.08602987,0.03620036,-0.0070034,0.00666864,-0.05372074,0.02906388,-0.01441467,-0.0461975,0.00530635,0.02672125,-0.00740942,-0.00061009,-0.05891937,0.03994004,-0.01544753,0.01323442,0.02032137,0.01168229,-0.03789307,0.04054781,0.01818027,-0.01410535,-0.04842833,-0.18383096,-0.05659392,0.02441878,-0.03845111,0.01115638,-0.02083531,0.04883014,0.00980218,0.03127739,0.0824904,0.0683971,0.00153544,-0.02268498,0.02711227,0.02532422,0.10243388,0.03261829,-0.02645875,0.00766795,0.02009702,-0.03730413,0.02870632,-0.01074202,-0.06824566,0.03314161,-0.09027242,0.10700246,-0.00299971,0.00277194,-0.0122579,0.0587326,0.03012806,-0.04018595,-0.12869982,0.06291164,0.0476491,-0.01278213,-0.03098209,-0.07099241,-0.06849835,0.00349254,0.04338112,0.03411118,-0.0970792,0.01780218,-0.04675502,-0.03384075,-0.02914504,0.02168354,0.01133982,0.01514179,0.01788688,0.04310958,0.08072964,0.00550742,0.00711012,0.0061077,-0.02537613,-0.02393277,0.05007208,-0.04902909,-0.01637803,-0.03385649,0.00433628,0.01312717,-0.01560317,-0.03491012,0.01174358,0.00993256,-0.03350732,-0.04156509,0.11088711,0.03171088,-0.05707096,0.02300567,0.04100037,-0.03500546,-0.06897268,0.01276428,-0.00569825,0.01885779,-0.00583027,0.05081387,0.04343894,0.02116421,0.05908641,-0.01041144,-0.03046977,0.00503687,-0.00401101,-0.01740033,-0.0098911,-0.03660498,-0.06543568,0.09407826,0.03760202,-0.30118966,0.01892079,0.02744996,-0.00697766,0.05790922,0.06231317,0.04423746,0.04795795,-0.01993117,0.0059686,0.02559153,0.05030126,0.00862579,-0.02599234,-0.01112645,-0.02278581,0.06184503,-0.00032121,0.08242556,0.04550362,-0.00332079,0.05312859,0.18014318,-0.00569022,0.04497233,0.02186122,-0.07893161,0.03859622,0.05589842,-0.00032604,-0.07230838,-0.0139293,0.00457078,-0.04261013,0.04044215,0.02989972,0.01886581,0.00335093,-0.01117079,0.00848729,-0.01097056,-0.01240752,-0.00141358,0.01509059,0.12670124,-0.00597799,-0.08637376,-0.08444623,-0.01928799,0.06195512,-0.04062569,-0.02561971,-0.04316449,0.04351978,-0.05579694,0.0668755,0.02998263,-0.05619032,-0.0411744,0.00407166,-0.00712425,-0.03267304,-0.04831916,0.05827789,0.03383582],"last_embed":{"hash":"io4rw0","tokens":163}}},"text":null,"length":0,"last_read":{"hash":"io4rw0","at":1751815146439},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{15}","lines":[307,307],"size":229,"outlinks":[{"title":"https://en.wikipedia.org/wiki/End\\_of\\_the\\_World\\_(Parks\\_and\\_Recreation)","target":"https://en.wikipedia.org/wiki/End_of_the_World_\\(Parks_and_Recreation\\","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"io4rw0","at":1751815146439}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{18}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03945594,-0.03192875,-0.05590846,-0.01457109,0.07003365,0.00831578,-0.05814261,0.03162832,-0.04198967,0.00803611,0.06379722,-0.08352675,0.0279533,0.02896474,0.03068166,-0.02359721,-0.00691556,-0.05346219,-0.03168209,0.02078753,0.1394922,-0.06338933,0.02551512,0.00479945,0.08300453,0.07299375,0.00455043,-0.03038447,-0.02700948,-0.18578708,-0.00519883,-0.02483145,0.0017638,-0.00423097,0.0265592,-0.03423174,-0.02856949,0.06442603,-0.03200987,0.0177866,0.02594648,-0.00890914,0.00235493,-0.00038036,0.07390025,-0.08032481,-0.0016944,-0.04899151,0.0091413,-0.04156049,-0.00274288,-0.08561687,-0.01024183,0.03412531,0.01760832,0.04912924,0.00738823,0.04156007,0.04214378,0.05960812,0.11265254,0.05898727,-0.22427592,0.02936424,0.09622896,0.05260704,-0.01294404,0.00815242,0.05655609,0.07491371,0.01911858,0.03072725,0.00180442,0.04057195,0.03341238,0.00260983,0.03108142,0.00778424,-0.01002935,-0.08964914,-0.00911134,0.08714891,-0.01298259,-0.00957927,-0.02304547,-0.02876556,-0.00880182,-0.02354187,-0.05162224,0.02192303,-0.00212323,-0.00927557,0.05524737,0.00863237,-0.05958339,-0.01301654,-0.01231495,0.02630749,-0.05862682,0.12085106,-0.04157126,0.05316893,0.01588571,-0.02883679,0.03428388,-0.00681933,-0.02310885,-0.04195479,0.04545628,0.03261007,0.00342723,0.01706458,0.02672669,-0.05713808,0.01690685,-0.01650075,-0.03137973,-0.01491586,-0.02658422,0.00550822,-0.01975689,0.0428557,0.04573398,-0.01738179,-0.06649073,-0.02240429,0.02213566,0.04106127,0.0626628,0.05854734,0.06022706,0.04052595,-0.09597556,-0.00723173,-0.03102644,-0.0190919,0.00320543,-0.00583469,-0.04389484,-0.03789531,-0.02887576,-0.10131566,-0.02126838,-0.09336263,-0.09982239,0.08275642,0.00481747,0.01138589,0.01699867,-0.01812848,0.02119277,0.07017819,-0.00334032,-0.06664149,-0.01341677,-0.01080925,0.03634148,0.0849084,-0.06751942,0.00602171,-0.03913813,-0.03327492,-0.04714559,0.15296638,0.02373902,-0.14164911,0.01985622,0.04271036,-0.00223566,-0.07263222,0.0018809,0.00792458,-0.03000137,-0.01837862,0.08024932,-0.02133609,-0.04063712,-0.03494794,0.00122037,0.04971483,0.0029078,-0.01736765,-0.08294436,-0.00158749,-0.02607148,-0.05213519,-0.02635911,0.0308219,-0.01228866,0.04446591,-0.06789054,0.01110075,-0.03695418,0.01705239,-0.00251327,0.00017423,-0.01354778,-0.03391281,-0.00149378,-0.01957763,0.05883399,0.0329499,0.00800033,0.03279451,-0.02945913,0.03371085,-0.04133784,-0.03151142,-0.0127885,0.01819294,-0.04505861,0.02598615,-0.00670545,0.04797017,-0.05223029,0.03245454,0.04622513,0.03192534,-0.00681004,0.07344493,0.00245271,0.00904612,-0.04093578,-0.1945141,-0.04437438,0.04343709,-0.03998051,0.00626271,-0.01199398,0.04970316,0.02315676,0.02459604,0.05754897,0.10095797,0.04132551,-0.02277388,0.01177229,-0.0145678,0.04786751,0.04506531,-0.01021874,0.00485284,0.01056805,-0.02687696,0.02095065,-0.01014288,-0.08423172,0.02729729,-0.05269453,0.09912072,-0.02605871,-0.02368276,0.00647349,0.07209762,-0.0048079,-0.04167559,-0.14686076,0.04316029,0.00872451,0.0031226,-0.00351635,-0.08583595,-0.02995728,0.00799121,0.00473858,0.05356018,-0.09898055,0.02491921,-0.07840463,-0.05634535,0.00049317,0.01338276,0.01710431,-0.0122815,0.03423553,0.02698635,0.10954157,-0.00706675,-0.00138332,0.03395804,-0.01775802,-0.0040318,0.02891052,-0.00825404,0.01395923,-0.0249726,0.00415394,0.03766708,-0.02353254,-0.02539595,-0.01603029,0.03092467,-0.04396013,-0.01263383,0.110187,0.05573934,-0.00222271,0.03504574,0.057164,-0.03370321,-0.0983208,0.02664868,0.00158742,0.02741538,-0.00802306,0.04183429,0.05716554,0.01248306,0.05126397,0.01099186,-0.02325229,0.01819417,-0.01451039,-0.00344562,-0.04332992,-0.03430614,-0.04015162,0.07155181,0.0240965,-0.28814864,-0.00343476,0.01539313,-0.02654618,0.05151239,0.02959529,0.03967742,-0.00064611,-0.06231986,0.02226349,0.00226387,0.0646618,-0.00289082,-0.03616489,0.00320716,-0.00612661,0.08633015,-0.00189463,0.05731413,0.00343583,0.00319759,0.06507553,0.20093352,-0.03330879,0.0046676,0.02648642,-0.08554898,0.03808551,0.06095111,0.0314303,-0.06087752,-0.01218555,0.04141401,-0.03060537,0.02125233,0.05268187,0.0146832,-0.00942301,-0.00986396,0.02244013,-0.04799346,-0.01705251,-0.01225501,0.02503734,0.11241768,-0.02479139,-0.06836484,-0.08042882,-0.00202291,0.03623352,-0.04648247,0.01489311,-0.01944824,0.04062667,-0.02341561,0.0756821,0.05017369,-0.01621856,-0.04805722,-0.03502997,-0.03429838,-0.03528483,-0.05017838,0.08096402,0.02912438],"last_embed":{"hash":"1ww9bbp","tokens":160}}},"text":null,"length":0,"last_read":{"hash":"1ww9bbp","at":1751815146449},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{18}","lines":[310,310],"size":223,"outlinks":[{"title":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","target":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1ww9bbp","at":1751815146449}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{20}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02438065,-0.05363666,-0.06798632,0.01180507,0.05513943,0.0030782,-0.00677425,0.02268084,0.00026506,0.00265178,0.06954185,-0.0906093,0.04984718,0.0297382,0.04741984,-0.0088311,-0.00112718,-0.06185135,-0.03937783,0.04376065,0.13334915,-0.0548659,0.02255415,-0.03946982,0.08269204,0.02860533,0.00017933,0.00955368,-0.03332092,-0.1617032,-0.00606635,-0.05621966,0.00901966,0.00585333,0.00684507,-0.03235244,-0.01434716,0.03082293,-0.01993169,0.01683022,-0.02169711,0.01463736,-0.01598194,-0.0004683,0.03763613,-0.08634366,-0.00220759,-0.04699001,0.00866281,-0.02908263,-0.00858208,-0.05002256,-0.01625858,0.03863695,0.02270001,0.00964889,0.00406792,0.02022231,0.06432487,0.03041153,0.07693464,0.03929701,-0.20890646,0.01776169,0.06910736,0.01943157,0.0006824,0.00844061,0.03229292,0.07615587,0.05532645,0.02284101,0.01512081,0.00751896,0.04180371,-0.02483415,0.02676901,-0.00951976,-0.01537511,-0.06566657,-0.03924682,0.08860445,-0.01774381,-0.0090522,-0.02331116,-0.02105798,0.03671582,-0.02928915,-0.03679316,0.00532677,-0.01851527,0.00304059,0.00813093,0.01364615,-0.0224359,0.02786415,0.02445939,0.03522953,-0.05295185,0.10048258,0.00207653,0.08043732,-0.00228416,-0.07497054,0.0230438,-0.02296858,-0.05891333,-0.04058012,0.00453519,0.04039689,0.00264126,0.02691701,0.05890895,-0.07934269,0.00865151,-0.02991947,-0.01889158,-0.01486738,0.02412765,0.03171069,0.00113241,0.0263832,0.07388958,0.01874342,-0.02774381,-0.00429953,0.02027934,0.06193115,0.01857874,0.05717097,0.05834076,0.00863831,-0.088393,-0.01141718,-0.00226021,-0.02570731,-0.00678868,0.00911088,-0.03553851,-0.02651614,-0.05873046,-0.065329,-0.00869391,-0.10901598,-0.05419079,0.08894004,0.02280582,0.02114533,-0.00720809,-0.03158699,-0.02503375,0.04984102,-0.04907529,-0.08484963,-0.03350454,0.02648789,0.06123297,0.09152551,-0.07651302,-0.02914752,-0.03190174,-0.03795927,-0.06659201,0.13779581,0.01335786,-0.11127687,0.02100221,0.02826351,-0.02688932,-0.05662398,-0.00820967,0.02587637,-0.05914475,-0.01358538,0.1122653,-0.007261,-0.01535544,-0.04433135,0.00251154,0.02599358,0.00028599,-0.04305327,-0.08786494,-0.02144856,-0.04713013,-0.03237617,-0.04167122,0.04452303,0.00113343,0.03939845,-0.05334723,0.00213261,-0.03111858,0.04882645,-0.04240768,0.00887179,-0.05380925,-0.00868091,0.0187937,-0.04009228,0.12904912,0.03379193,0.00573181,0.02659163,-0.03859242,0.03894913,-0.00111843,-0.02604404,0.01252862,0.03750996,0.0138845,0.05629417,-0.03702315,0.01414341,-0.06867567,0.01756314,0.01773914,0.03989856,-0.00151043,0.03384661,0.0069397,-0.00928316,-0.08908036,-0.20147216,-0.03490208,0.04660303,-0.02745765,0.06373143,-0.00752362,0.06559853,0.02644696,0.0765273,0.08269656,0.06390978,0.02738005,-0.03777752,-0.004647,0.00690911,0.05023191,0.03685399,-0.03796959,0.02782667,0.03499093,-0.06516974,0.01696392,-0.02440342,-0.10378712,0.0744945,-0.04148212,0.09111148,-0.04256729,0.01119093,-0.02161936,0.07141862,-0.01441396,-0.03646352,-0.09674447,0.0321965,0.0474033,0.02671018,-0.00600206,-0.09001322,-0.04361703,-0.00299042,0.01183159,0.0717656,-0.10081849,0.00792547,-0.03423508,-0.00961259,0.04778096,-0.02052802,0.01201562,-0.00119069,0.03679884,0.03219379,0.07511064,0.00246395,-0.04342847,0.03441072,-0.05842977,-0.00453267,0.06540173,-0.00375551,-0.00140408,-0.03914831,0.01753643,0.03480823,-0.01712034,0.0217086,0.0149235,-0.00225394,-0.02214587,-0.01817217,0.09735487,0.02152828,-0.00543676,0.05318426,0.04760823,-0.04425651,-0.07149557,0.01459636,0.04997598,0.02078928,0.01197845,0.02239278,0.07142029,-0.01125513,0.0868736,0.03370407,-0.0307427,-0.0060418,-0.00796542,-0.03945282,-0.03537352,-0.04566325,-0.03951083,0.02703035,0.02563533,-0.3004902,-0.02384482,0.0077196,0.00319336,0.06530057,0.02120947,0.04058245,0.04935405,-0.11544699,-0.00029086,-0.02612181,0.06354396,0.01109639,-0.02650132,-0.03730204,-0.01775546,0.05754741,-0.01662099,0.01647658,0.04798315,-0.00782296,0.06634382,0.21514498,-0.00422169,0.0078306,0.03142313,-0.06547184,0.06918939,0.10573798,-0.00567492,-0.04326794,-0.02861425,0.02020435,-0.05441466,0.03633741,0.06297302,0.00236046,-0.00549587,-0.04015511,0.04612771,-0.00438403,-0.00443474,-0.00586671,0.04328101,0.11081102,-0.0223079,-0.07110278,-0.07737073,-0.0109368,0.02309155,-0.05026921,-0.02797313,0.02950023,0.01414173,-0.00754781,0.07554705,0.04627247,-0.02525586,-0.05924201,-0.04040086,-0.0203636,-0.01496519,-0.06564419,0.01456354,0.01447002],"last_embed":{"hash":"3bxftn","tokens":144}}},"text":null,"length":0,"last_read":{"hash":"3bxftn","at":1751815146458},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{20}","lines":[312,312],"size":200,"outlinks":[{"title":"https://cryptorank.io/price/nockchain","target":"https://cryptorank.io/price/nockchain","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"3bxftn","at":1751815146458}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{21}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08585535,-0.01816839,-0.05745041,-0.04681232,-0.00205203,0.00329573,0.00008985,0.01776353,-0.03979749,-0.04815117,0.06898762,-0.04782476,0.03560294,0.03757234,0.01181747,-0.02291836,-0.01210935,-0.00312355,0.03044181,0.01623628,0.07951016,-0.08280899,0.0304054,-0.00692547,0.07574173,0.05185379,0.00302872,-0.00010917,-0.01746617,-0.20136972,0.0239376,-0.0475608,-0.04290104,0.01582148,-0.00789169,0.00973368,0.00709804,0.06496156,-0.05150256,0.01262424,0.0131616,0.04681434,0.0049464,0.0331103,0.06299027,-0.10849992,0.02860219,-0.05088791,-0.01400408,-0.0096966,0.02026021,-0.05962569,-0.02237997,0.00446498,-0.01427485,0.03892441,0.03420094,0.05207054,0.05683389,0.06914248,0.07210243,0.02975318,-0.15457922,0.02492715,0.11265935,0.026273,-0.05479364,-0.00411706,0.05687931,0.07771394,0.02101178,0.01980687,0.01448237,0.09299805,0.00654335,-0.02058326,0.0596306,-0.00300371,-0.02113881,-0.04926177,-0.0326022,0.07678024,0.01232494,-0.00242963,-0.02230565,-0.01235877,0.02499373,-0.05594492,-0.00935196,-0.02301369,0.00522465,0.03596302,0.1235705,-0.01396625,-0.06032018,0.02708018,0.01563287,0.02201695,-0.06228948,0.11421394,-0.01641535,0.04847652,-0.02051171,-0.02247628,0.03409828,-0.01204611,-0.00475802,-0.05295352,-0.00913951,0.02994802,-0.00232254,0.02170958,0.0510559,-0.05072599,0.03631418,-0.03772032,-0.01029964,-0.00054867,-0.01014495,0.03076552,0.03173217,0.06107565,0.06348381,0.01882597,-0.0188288,-0.01483617,0.0038798,0.01971894,0.01819897,-0.00857347,0.04428206,0.04015313,-0.06934711,0.00763772,-0.0027749,0.00342772,-0.0514861,0.01277416,-0.05523446,-0.04392367,-0.06249376,-0.05893379,0.02165966,-0.09128585,-0.04199703,0.07667707,0.00080992,0.02060094,-0.00048616,-0.02217182,0.00979893,0.04403612,-0.02751589,-0.02051339,0.02533463,0.01388358,0.03152475,0.0755915,-0.0374137,0.02102682,-0.05824655,-0.04362056,-0.0312891,0.14954749,0.00518329,-0.13905668,0.03693777,0.031429,-0.03240478,-0.06457391,0.0201014,0.01633351,-0.0584504,-0.02716587,0.07559258,-0.03410832,-0.04922172,-0.02379824,0.03434024,0.0557676,0.00044593,0.02421138,-0.07963695,-0.04698083,-0.02980744,-0.01725336,-0.05426281,-0.01620116,0.01297425,0.0473063,-0.09119622,0.0283725,-0.00199831,0.00709831,-0.01673711,-0.03298429,-0.03727673,-0.03334146,0.02587266,-0.07707307,0.1094977,0.04476143,0.0274045,0.02187626,-0.01647431,0.00230831,-0.08407668,-0.05162814,-0.01229779,0.02047133,-0.0523042,0.02572603,0.01891909,0.06328364,-0.03641882,0.04218116,-0.00971075,0.01696259,-0.00030281,0.05298165,0.03965096,0.04708851,-0.06944743,-0.19389661,-0.00343094,0.02596708,-0.04761215,0.05217336,-0.01192872,0.05010919,0.00914003,0.0511609,0.04398824,0.07090164,0.01382413,-0.020223,0.03384968,-0.01738699,0.05453511,0.03901369,-0.02799174,-0.01147831,0.01148968,-0.03535929,-0.03693283,-0.01483951,-0.08712355,0.03608296,-0.03695547,0.08065047,-0.02146774,0.00227951,-0.00691808,0.06737462,0.00566881,-0.0400258,-0.15245344,-0.00695842,0.05757749,0.00619626,-0.03144633,-0.00238598,-0.0259302,-0.01355452,0.04014139,0.05118907,-0.14083166,0.04130684,-0.05420561,-0.03307437,0.01328476,-0.01392048,0.019021,0.00355419,0.02080107,-0.00112404,0.12232082,-0.02126502,-0.03036713,0.03775883,-0.03147274,-0.01166534,0.04556743,-0.01807697,-0.01488093,-0.05333887,0.00616466,0.06622218,-0.00849863,0.01408942,0.00936736,0.0596156,-0.0745362,-0.02304087,0.11927103,-0.0082134,-0.0099271,0.03141737,0.01292016,-0.04350213,-0.0953273,0.03569369,-0.00181364,0.0055003,0.00440691,0.06597204,0.04224834,-0.00477909,0.06026513,0.0525119,-0.0313428,0.01506273,-0.01034225,0.0060051,-0.02502171,-0.05320026,-0.02167599,0.06586755,-0.00708857,-0.30324826,-0.00989943,-0.01718167,0.00351849,0.02495605,0.05328568,0.05210846,0.00378248,-0.0730248,0.02638554,-0.04758999,0.02414238,0.02611317,-0.03775928,0.03791528,0.02601779,0.06642634,-0.00484428,0.01265433,-0.02364854,0.02174115,0.07318325,0.22315139,-0.05149493,0.04530448,0.03418706,-0.01716975,0.03415053,0.08303517,0.00169682,-0.0398716,-0.00450866,0.04735421,-0.04385773,-0.00243331,0.03923857,-0.00032022,0.00708975,-0.00861622,0.00776804,-0.05364389,-0.06666321,0.00237325,0.04098038,0.11588734,-0.08026347,-0.05597156,-0.06599043,-0.02817771,0.04542795,-0.05581493,-0.04366292,0.007147,-0.00081807,-0.01429796,0.04234629,0.04165295,-0.03505812,-0.07768818,-0.00924835,-0.01419788,-0.00156668,-0.00984089,0.04236992,0.0041104],"last_embed":{"hash":"si5oor","tokens":151}}},"text":null,"length":0,"last_read":{"hash":"si5oor","at":1751815146465},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{21}","lines":[313,313],"size":204,"outlinks":[{"title":"https://github.com/zorp-corp/nockapp","target":"https://github.com/zorp-corp/nockapp","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"si5oor","at":1751815146465}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{22}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07570455,-0.01149974,-0.0669245,0.02709303,0.02794348,0.00274503,-0.02694086,0.03261651,-0.05141677,-0.011741,0.02820371,-0.02905383,-0.00754052,-0.000586,0.00867677,-0.00150985,-0.01733404,-0.01426011,-0.02638748,0.00496866,0.07104201,-0.06189163,0.03364961,-0.04195036,0.11213423,0.04424413,0.01519658,-0.04369374,-0.01104847,-0.20696017,-0.01161853,-0.05835596,0.01303599,-0.0001847,0.05393281,-0.04897312,-0.02264943,0.06987745,-0.10250182,0.00531084,0.04420493,0.0080701,0.01220859,-0.02086747,0.04862717,-0.07104512,0.02411553,-0.03685001,-0.05498928,-0.00879433,0.01183141,-0.05789576,-0.031647,0.0134084,0.03043613,0.02262831,0.02319473,0.05523377,0.04291118,0.0236518,0.07280649,0.03417221,-0.21235551,0.02656698,0.09531888,0.0393729,-0.03635091,0.04797919,0.03939757,0.10014009,0.02783757,0.01605466,-0.00205216,0.03460845,0.01719153,0.05565421,0.02634086,-0.03346884,-0.04382437,-0.0765556,-0.0697884,0.03958874,-0.01660678,-0.01235648,-0.06119513,0.00226631,0.00602923,-0.04159348,0.00571311,-0.00968615,-0.00602365,0.01873737,0.05019783,0.02180628,-0.02850052,-0.02242786,0.03705394,0.03104575,-0.04698019,0.11222041,-0.03995372,0.05957893,-0.02173823,-0.05926552,0.05035999,-0.02410959,-0.01958269,-0.02219676,0.00386592,0.01633774,0.03743492,0.0260402,0.06260385,-0.03785947,0.03175545,0.02011976,0.07798777,0.01311386,0.03161763,0.01385812,0.01086319,0.07752058,0.01365014,-0.02149011,-0.07710497,-0.03340743,0.04606109,0.0322039,0.06262884,-0.00935632,0.0698228,0.00554385,-0.04384106,0.02391616,-0.02352125,-0.01215511,0.02233926,0.03126091,-0.03894717,0.03249564,0.02541011,-0.05346315,-0.01988705,-0.1214504,-0.03726315,0.05486009,0.00496773,-0.01429954,0.01968079,-0.06534614,0.01316051,0.02627021,-0.01552734,-0.04687782,0.02755942,0.036758,0.06429539,0.08437275,-0.05417863,-0.01452174,-0.06732486,0.01531264,-0.06354351,0.13262826,0.06288657,-0.11693668,0.03146014,0.03718635,-0.015417,-0.0806335,0.03708183,-0.01047564,-0.0799243,-0.01967286,0.05852153,-0.06361365,-0.04454827,0.0058834,0.02164511,0.04685905,0.01815826,-0.02513674,-0.06840532,-0.01302076,0.00138537,-0.03234247,0.00220408,0.00860286,0.04721644,0.02917808,-0.06476865,0.02174864,-0.03894973,0.01919402,-0.01983223,-0.04528829,-0.04748343,-0.06772029,0.01634493,-0.05589749,0.07887648,0.03926127,0.00617606,-0.01102024,-0.03532335,0.00862798,-0.03033133,-0.04789215,0.01104735,0.02967936,-0.03163276,-0.0190334,-0.06057797,0.01686472,0.00853343,0.00673709,0.0497994,-0.01073195,-0.04142944,0.02438301,0.01325727,0.00130836,-0.06079419,-0.19371444,-0.08489767,-0.01368972,0.0399445,0.05243175,-0.00833218,0.03363837,0.01879804,0.0455417,0.07720103,0.09861974,0.04200331,-0.05256044,0.02169403,-0.00323914,0.01353744,-0.0168374,-0.0362374,0.03642903,0.03659184,0.00378629,0.02224662,-0.02104026,-0.08572182,0.00362079,-0.05974977,0.12560326,0.02174987,0.00868604,-0.02246897,0.03835871,0.02079327,-0.05242025,-0.13854228,0.02096579,0.08143909,-0.00659225,-0.03434487,0.0029172,-0.03221469,-0.04250073,0.05189858,0.01886136,-0.12998684,0.0311681,-0.0408162,-0.07476033,0.01994559,-0.02545604,0.02791306,0.00714629,0.07695685,0.05639864,0.07538907,-0.00799955,-0.04061115,0.01720112,-0.03163924,0.00390789,0.04663102,-0.02954932,0.00746702,-0.06290822,0.02057208,0.05920193,0.01856977,-0.00693136,0.0159925,0.04044182,0.00298777,-0.01327002,0.10844956,0.06467325,-0.01975478,0.01263033,0.01805045,-0.01107913,-0.08133226,0.01846232,0.00674188,0.02803716,-0.03766786,0.04491879,0.05161234,0.02774019,0.09814359,0.02002249,0.01985684,0.02832733,0.01707873,-0.03472599,-0.00001929,-0.06223949,-0.07036429,0.05784616,0.01204523,-0.2704576,0.00585147,-0.02367049,-0.00139164,0.00229749,0.00403732,0.07284345,-0.016381,-0.06731573,0.01705392,-0.02130977,0.05872092,-0.00103121,-0.01154816,0.01437715,0.01708876,0.0863131,-0.0023793,0.0469886,0.0301438,0.00816604,0.09076461,0.18322192,-0.05138045,0.06899916,0.04010936,-0.03810585,0.00290867,0.04890789,0.00326883,-0.00407685,-0.02868402,0.02584574,-0.05919059,-0.00334572,0.07127044,-0.06824507,-0.00108728,-0.00659771,-0.01030059,-0.04654729,-0.05998397,-0.01857264,0.04711066,0.12835397,-0.0405342,-0.05667596,-0.09928207,-0.00172931,0.02287298,-0.04153966,-0.0284172,-0.0044611,-0.01249849,-0.03021346,0.05143334,0.05590024,-0.01629281,-0.05844742,-0.01549813,-0.00246332,0.01629548,-0.04716302,0.05338457,0.03136558],"last_embed":{"hash":"104hddn","tokens":212}}},"text":null,"length":0,"last_read":{"hash":"104hddn","at":1751815146475},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{22}","lines":[314,314],"size":377,"outlinks":[{"title":"https://www.everand.com/podcast/864012011/Logan-Allen-Nockchain-s-Global-Competition-for-Useful-ZK-Proof-of-Work-Miners-Kicks-Off","target":"https://www.everand.com/podcast/864012011/Logan-Allen-Nockchain-s-Global-Competition-for-Useful-ZK-Proof-of-Work-Miners-Kicks-Off","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"104hddn","at":1751815146475}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{23}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07998984,-0.04657063,-0.03293195,0.01040806,0.01953698,0.03348569,0.01816997,0.02370472,-0.0073521,0.0050479,0.05156917,-0.07088646,0.03200044,0.02988385,0.02789109,-0.02914805,-0.01493849,-0.05985246,-0.00750028,0.04731724,0.13576613,-0.08272695,0.02053854,0.00559859,0.05293967,0.06036117,0.04027224,-0.03127378,-0.03397597,-0.16659933,0.04657443,-0.0511737,-0.00072251,-0.01562711,0.00888915,0.01657508,-0.00686005,0.04305531,-0.04616573,0.03211689,0.0074237,-0.00883514,0.02312752,0.01235159,0.02533028,-0.07701679,0.03072262,-0.02864131,-0.02282767,-0.08593154,-0.02790695,-0.07082254,0.00702498,0.04590871,-0.00210309,0.01179378,0.0159114,0.07666416,0.06154439,0.02824882,0.08258212,0.03358035,-0.20615061,0.02774643,0.09430341,0.02641781,-0.03879903,0.02839849,0.04159708,0.07700886,0.05492734,0.02973453,0.00069601,0.07651125,-0.00876487,0.00392605,0.01398836,0.00503976,-0.00771807,-0.04866683,-0.06871983,0.03247205,0.02476185,-0.01031583,-0.02874246,-0.01333263,-0.01929896,-0.02114867,-0.05623718,0.00095823,-0.01015788,-0.00442994,0.05479593,-0.00567338,-0.01411306,-0.02036463,0.0012128,0.00235071,-0.08438699,0.11492559,-0.02470917,0.02822435,-0.01231746,-0.0234389,0.03722735,-0.02028869,-0.03731025,-0.04453954,0.02078357,0.02337429,-0.00407626,0.02967984,0.07467829,-0.06718653,0.02896827,0.0151131,0.02024293,-0.00462276,0.02923836,-0.00261529,-0.00204644,0.05525547,0.06454942,-0.0315917,-0.03975993,-0.03008517,-0.0090353,0.03145827,0.03520151,0.07949609,0.04130776,0.02297025,-0.06430358,0.04079897,-0.000269,-0.02030843,0.00291106,0.006814,-0.0343011,-0.0460064,-0.01940022,-0.06993442,0.00309456,-0.11577383,-0.0510058,0.09635931,0.03497665,0.02520847,0.00611136,-0.01034215,0.0019922,0.05701099,-0.03399264,-0.07339986,-0.03174371,0.01323776,0.0579582,0.08097931,-0.08989892,-0.00339351,-0.02780907,-0.03537645,-0.06673182,0.19542103,0.04742707,-0.11762766,0.01573967,0.01723708,-0.00328518,-0.07972708,-0.0071768,0.01046612,-0.04481059,0.01372231,0.06841116,-0.01674579,-0.02199457,-0.01990747,-0.00694385,0.01959903,-0.01052454,-0.08337432,-0.05692625,-0.02307883,-0.04668861,-0.02918356,-0.0268773,-0.00330082,0.01265349,0.02790722,-0.09863898,0.03757351,0.0156673,0.05620922,-0.01850321,-0.01980075,-0.00881777,-0.0158989,-0.03952512,-0.03171161,0.07067677,0.05778174,-0.0363993,0.01428609,-0.08786031,0.02163227,-0.00945663,-0.0250009,0.03365647,0.0032713,-0.02251516,0.06390186,-0.02786548,0.03064606,-0.02250559,-0.00429328,0.0266188,0.02340914,-0.03398264,0.02676067,0.00682627,0.0279799,-0.07764441,-0.19328494,-0.03616839,0.03015219,-0.03480184,0.00802845,0.01802656,0.05434158,0.02712261,0.05675058,0.06942034,0.0960307,0.01618694,-0.05685376,-0.01108306,0.02024701,0.05700142,0.03516465,-0.01639406,-0.0026006,0.04793154,-0.01765641,0.01527713,-0.00271364,-0.1034608,0.04235673,-0.04126216,0.09383148,0.01943929,0.01283948,0.00770847,0.06353038,0.03395465,-0.0368445,-0.17010088,0.02694442,0.01419509,0.0267259,0.01062252,-0.02977222,-0.03364429,-0.05046615,0.03342525,0.01691059,-0.07504279,0.009313,-0.04556226,-0.0473412,0.00677033,0.01366813,0.0189676,0.02477976,0.04672551,-0.00298068,0.11607551,-0.00628652,-0.0037354,0.00163338,-0.04645404,0.00115786,0.04148775,-0.01698737,0.02466776,-0.05228074,-0.01860121,0.03742066,-0.0038622,-0.01666428,0.03085399,0.01115259,-0.0212459,-0.03359582,0.12747638,0.05328872,-0.03844929,0.01452914,0.00925513,-0.04630254,-0.09516953,0.03286558,-0.01186394,0.00763753,-0.01093939,0.01454114,0.02169825,0.0112026,0.0466759,0.01521241,-0.04984757,0.01697604,0.01097568,-0.01428704,-0.03021931,-0.07312595,-0.05760383,0.06794205,0.03417647,-0.28796899,-0.00554678,0.01065191,0.0168473,0.05149341,0.02724149,0.07718938,-0.00880332,-0.09221494,0.00810671,-0.00937235,0.04366475,0.00534898,-0.03380716,-0.03011897,0.01422985,0.05510525,0.01169922,-0.00568924,0.02704547,0.00275343,0.08024529,0.19729103,-0.00738368,0.05377965,0.03305443,-0.0429629,0.02352043,0.06411061,0.046618,-0.01027333,-0.00258044,0.01280645,-0.03020018,0.01445657,0.0687518,-0.00387938,0.02495409,-0.00438186,0.0066872,-0.04076708,-0.05286583,-0.00662699,0.03759594,0.13858128,-0.02734668,-0.0927763,-0.09491625,0.01423661,0.0649046,-0.0612557,-0.01728437,0.00342782,0.0405923,-0.0141921,0.06234284,0.00241188,-0.03461424,-0.09891023,-0.00534742,-0.0047436,0.02702778,-0.03519424,0.01971513,-0.00721297],"last_embed":{"hash":"19dt7qo","tokens":174}}},"text":null,"length":0,"last_read":{"hash":"19dt7qo","at":1751815146484},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{23}","lines":[315,315],"size":260,"outlinks":[{"title":"https://www.nervos.org/knowledge-base/what\\_are\\_blockchain\\_intents\\_(explainCKBot)","target":"https://www.nervos.org/knowledge-base/what_are_blockchain_intents_\\(explainCKBot\\","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"19dt7qo","at":1751815146484}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{24}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0738675,-0.05522548,-0.04389771,-0.02652677,-0.01546163,0.03372412,0.04016042,0.04053275,0.0079713,-0.00462748,0.05587121,-0.05815309,0.03054766,0.01968227,0.01356602,-0.05286086,0.00562993,-0.03840946,-0.02559796,0.03678786,0.12815282,-0.07081665,0.03257178,0.00908229,0.04720269,0.05627346,0.03849201,-0.04346988,-0.04502846,-0.19167067,0.03099673,-0.02298202,-0.00159773,-0.02691622,0.00237282,-0.00566993,-0.03851635,0.07422142,-0.05379858,0.03435943,0.03038381,-0.00104932,0.01709841,0.00341697,-0.00324933,-0.11319114,0.02488719,-0.04116592,-0.00748986,-0.07603452,-0.02201877,-0.06656814,-0.00660417,0.0466397,0.00961811,0.01533597,0.02984155,0.0822842,0.05676284,0.02987922,0.10386891,0.04632892,-0.20662713,0.04557041,0.08819528,0.03595028,-0.0385299,0.02568697,0.0799194,0.06529,0.04681782,0.03899804,-0.01454595,0.075803,0.02198222,-0.00825664,0.04429505,0.00747433,-0.01282895,-0.06997425,-0.04729561,0.05460247,0.0164855,-0.0253341,-0.03136012,-0.0116754,-0.01155371,-0.03647539,-0.0219145,-0.00427999,0.00773635,-0.00420213,0.02433999,-0.00070121,-0.01192878,-0.01045051,-0.00122579,-0.02128079,-0.07052644,0.12146826,-0.01086673,0.04686273,-0.00420298,-0.0188267,0.03371075,-0.0030049,-0.02815967,-0.03993464,0.02622824,0.01695829,0.01846306,0.01030899,0.06217085,-0.0544954,0.00619971,0.02303713,0.02568253,-0.01513703,0.06230903,-0.01385124,-0.02113221,0.03968393,0.07571276,-0.0077985,-0.04851493,-0.04199854,-0.00134648,0.03195324,0.03516375,0.06425957,0.06031979,0.04851025,-0.0719469,0.00425651,-0.00321852,-0.00248411,-0.00016525,-0.01400264,-0.0469932,-0.03638065,-0.01671245,-0.0416143,0.01533653,-0.12031018,-0.05122383,0.12692362,0.03005319,0.02708447,0.0032738,-0.01777319,0.00233325,0.06014479,-0.01812477,-0.06042783,-0.01156164,0.00502384,0.0405179,0.094487,-0.09106022,-0.02550961,-0.03458048,-0.0193576,-0.07700723,0.17894022,0.03703364,-0.10134926,0.02493766,0.02208204,-0.00939854,-0.08419608,0.01207217,-0.00070843,-0.05081436,0.02046761,0.06812079,-0.00875609,-0.04092653,-0.02956155,0.00793661,0.01596149,0.02709012,-0.07134234,-0.04539685,-0.03256686,-0.03237531,-0.04111701,-0.03070462,0.00304782,0.01191083,0.05579811,-0.08841708,0.04500118,0.00616217,0.04334468,-0.0195778,-0.02486357,-0.0381993,-0.03657767,-0.03732176,-0.04807797,0.08245759,0.02832386,-0.04150167,0.02927574,-0.06645219,0.02498111,-0.01540489,-0.04244194,0.02678848,-0.0063465,-0.03138467,0.06065597,-0.020626,0.03779854,-0.01054009,0.0033863,0.03938748,0.03504013,-0.01739741,0.04541244,0.00946246,0.01234304,-0.04748958,-0.19650054,-0.07871538,0.04339557,-0.02181216,-0.00219411,0.00530767,0.04497408,0.01213189,0.04270897,0.05385094,0.09263801,0.03489323,-0.04239159,-0.00116526,0.02507098,0.07138918,0.01745947,0.00413205,0.00165876,0.04828643,-0.00836297,0.0169398,-0.03182953,-0.1161571,0.05041452,-0.04935906,0.0907705,-0.0103252,0.00728749,-0.00867648,0.06895729,0.03694857,-0.01379905,-0.18127127,0.01777703,0.01764127,-0.00423783,0.02791672,-0.00066954,-0.0373562,-0.0437165,0.01767479,0.01862709,-0.08187932,0.00631238,-0.06276403,-0.04387778,0.02558057,0.02008969,0.01759885,0.02293194,0.01582032,0.00292798,0.09567056,0.00318781,0.00632594,-0.00195701,-0.03545043,-0.02390734,0.04046949,-0.02865503,-0.00190587,-0.05040703,-0.00477545,0.02888458,-0.01020366,-0.00349623,0.03496082,-0.0060164,-0.02651743,-0.04671163,0.10997785,-0.00378571,-0.03805432,0.00345681,0.01666141,-0.01221231,-0.07253075,0.01950412,-0.00307828,-0.00157468,0.0016125,0.02372443,0.0068073,-0.00518282,0.06370644,0.00284037,-0.04877114,0.02035255,-0.00762087,0.00110184,-0.05906168,-0.05157283,-0.06454476,0.0712499,0.04462787,-0.27758911,-0.01037483,0.00887839,0.00145852,0.04575581,0.03721849,0.09162565,-0.02073061,-0.06664699,0.00689378,-0.026968,0.05157764,-0.00705429,-0.04024888,-0.01430193,0.00968987,0.07604891,0.02423771,-0.00234017,0.03568117,0.00015191,0.07600716,0.20717512,-0.01073196,0.02015496,0.01203461,-0.04343279,0.0177702,0.07145783,0.02954293,-0.01623738,-0.00650985,0.0347907,-0.02181166,0.01731985,0.08253174,-0.01468669,0.02258024,-0.00542276,0.0258022,-0.03178532,-0.04179929,0.01548681,0.02750649,0.13078193,-0.02009306,-0.09209752,-0.0719168,-0.0095946,0.05364286,-0.05728456,0.00572091,-0.02089754,0.03422307,-0.00960687,0.05407357,0.00926099,-0.04828412,-0.09695008,-0.00803195,0.00742043,0.00955765,-0.01591588,0.03719162,0.0109467],"last_embed":{"hash":"1x21ggm","tokens":219}}},"text":null,"length":0,"last_read":{"hash":"1x21ggm","at":1751815146495},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{24}","lines":[316,316],"size":359,"outlinks":[{"title":"https://volet.com/blog/post/understanding-intents-in-blockchain-a-comprehensive-guide-to-intents-01jdpzq69t52gfwxy6z0bx1nmv","target":"https://volet.com/blog/post/understanding-intents-in-blockchain-a-comprehensive-guide-to-intents-01jdpzq69t52gfwxy6z0bx1nmv","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1x21ggm","at":1751815146495}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{25}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06115667,-0.01962604,-0.03988029,-0.01307458,-0.00845152,0.03148807,0.00085037,0.05475454,-0.00831962,0.00798839,0.06187224,-0.04111604,0.04061725,-0.00039237,0.02817212,-0.02728859,0.00759995,-0.04860726,-0.0168221,0.03543832,0.14126638,-0.0659576,0.03056266,-0.01974107,0.05023794,0.06278506,0.01475553,-0.02198585,-0.03708424,-0.18951775,0.03068337,-0.00342698,0.0279521,-0.0278447,0.00582239,-0.02032214,0.00856213,0.08919015,-0.03846933,0.02420254,-0.02242589,0.02214166,-0.01237315,0.01956108,0.0272212,-0.05864039,0.01293998,-0.05621465,-0.01801134,-0.0509752,0.02491732,-0.08295992,-0.02788813,0.05316702,-0.01032616,0.0164182,0.02819456,0.05403674,0.0467305,0.02596784,0.08687612,0.04883842,-0.18606135,0.01786454,0.08237778,0.01738547,-0.02158024,0.00715068,0.04729149,0.0862412,0.04537423,0.00899438,-0.01294498,0.05798259,0.02782735,-0.00416743,0.02372055,-0.0067363,-0.02675088,-0.0595789,-0.06298138,0.04041808,-0.01155619,-0.0012411,-0.06665957,-0.02007376,-0.00269903,-0.03306421,-0.03066301,0.02175779,0.00424359,-0.00833105,0.03498592,-0.01052488,-0.00779344,0.00596803,-0.02807429,-0.01228722,-0.10748174,0.13021843,-0.01668397,0.05318819,0.01348533,-0.04319044,0.06252436,-0.01033512,-0.01028784,-0.04543078,0.0217242,0.01727126,0.0045989,0.03538834,0.03136625,-0.02897814,0.03240166,-0.00152728,-0.01581015,-0.00682302,0.04710384,0.0145259,-0.02794148,0.03959053,0.05132157,-0.01962576,-0.06422523,-0.03885016,-0.00502974,0.04714681,0.02212397,0.06061349,0.06121137,0.03290418,-0.05440363,-0.01678822,-0.03373067,0.00047882,-0.0109891,0.00430482,-0.05562535,-0.03683234,-0.02372583,-0.02054515,0.01704118,-0.1298445,-0.04888872,0.08389055,0.03865391,0.03336979,0.02062252,-0.01148812,0.00392289,0.03156711,-0.02759087,-0.0393136,-0.02830973,0.00418926,0.02332228,0.08857887,-0.05781336,0.00833247,-0.03336037,-0.01066328,-0.06999765,0.18205571,0.0050836,-0.11655855,-0.0004329,0.04216594,0.00202954,-0.07126109,0.00697861,0.02781527,-0.05129751,0.00233878,0.08039426,-0.00671576,-0.05608911,-0.05874995,0.01743252,0.02811598,0.02828878,-0.01642855,-0.05261089,-0.02897906,-0.05207257,-0.03611628,-0.00667286,0.02361611,0.00718569,0.05370083,-0.07327899,0.05892264,0.01329836,0.07162876,-0.03987245,-0.05600078,-0.01478009,-0.04757468,-0.04707192,-0.03433851,0.07316171,0.05567514,-0.03018468,0.00472515,-0.05867673,0.01326056,-0.00109535,-0.0492065,0.02030755,0.02571166,-0.03371736,0.06160054,-0.02438628,0.02586865,-0.01534919,0.00856074,0.01968247,0.02635886,0.00678261,0.02170709,0.00560376,0.03997591,-0.08089983,-0.20514551,-0.06653697,0.01177399,-0.05518269,0.04369004,0.03224652,0.02067695,0.02653004,0.02893309,0.0470949,0.10940913,0.05171893,-0.09723604,0.00703029,-0.00198161,0.05588252,0.04107944,-0.01172676,-0.00270686,0.0141721,-0.01334232,0.02719198,-0.03202171,-0.123468,0.05875581,-0.02844611,0.10113093,0.00143657,-0.02703666,0.00141149,0.0678556,0.01082655,-0.03340068,-0.18403076,0.03905063,0.04822423,0.01075145,0.02025546,-0.02625928,-0.04307731,-0.03257783,0.03776239,0.0290824,-0.08237835,-0.00280554,-0.07032698,-0.06163844,0.00725783,0.00185317,0.0156582,0.02741581,0.03936896,0.0088252,0.11791467,0.00638748,-0.03237467,0.00656489,-0.04573639,0.0208994,0.06975237,-0.01442618,-0.0018132,-0.01128493,-0.01363713,0.00925942,0.00469456,-0.00942804,0.00639521,0.0028409,-0.02606102,-0.01584231,0.14532824,0.01650935,-0.06724846,0.00803445,0.01238689,0.00395047,-0.06379036,0.05658735,0.0073595,0.01596084,-0.01401315,0.02650032,-0.01305653,0.00538561,0.06650595,-0.00082858,-0.05895428,-0.01163606,-0.03612892,-0.02855422,-0.04964716,-0.04594743,-0.03617927,0.03603305,0.0051061,-0.28180557,-0.00436534,-0.00332035,0.00886456,0.04681662,0.05361893,0.07474542,0.00670404,-0.10286602,0.00832841,-0.02423483,0.04569986,0.01159939,-0.016894,-0.00241453,-0.0069925,0.08297988,-0.0230158,0.02568906,0.02095514,0.01493702,0.07906546,0.20758414,-0.01428946,0.0202058,0.03167206,-0.0386871,0.04862932,0.07999773,0.04356962,-0.04056757,0.01590154,0.01884351,-0.04137621,0.00154756,0.07136655,-0.0289876,0.00356296,-0.03226604,0.02591789,-0.0248463,-0.01584309,0.01122998,0.02175514,0.13679339,-0.01703637,-0.06648333,-0.06203511,-0.00409448,0.04527646,-0.05669285,-0.01328193,-0.02445984,0.01999605,-0.00290836,0.05751835,0.00534749,-0.01011376,-0.10061059,0.00714617,-0.00578886,0.01231364,-0.00587358,0.0454625,0.02932777],"last_embed":{"hash":"8pba1h","tokens":163}}},"text":null,"length":0,"last_read":{"hash":"8pba1h","at":1751815146507},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{25}","lines":[317,317],"size":268,"outlinks":[{"title":"https://crypto.com/en/university/intent-centric-design-for-blockchain","target":"https://crypto.com/en/university/intent-centric-design-for-blockchain","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"8pba1h","at":1751815146507}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{27}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06563103,-0.0252733,-0.07622034,0.00661616,0.01628864,-0.00513376,-0.01525983,0.03575516,-0.04833435,-0.02426234,0.05575388,-0.0643052,-0.00525236,0.01830783,0.00935836,0.00028959,0.00038069,-0.02296182,-0.02909734,0.01685837,0.08810009,-0.05265602,0.03730673,-0.04269445,0.10428328,0.04811778,0.01928096,-0.03208368,-0.01785939,-0.20920935,0.00503473,-0.0622407,-0.01042785,0.00858112,0.04676857,-0.05266344,-0.01489347,0.08020739,-0.09182066,0.00050472,0.02486806,-0.00207607,0.0003943,-0.01526081,0.06063636,-0.07564731,0.02767181,-0.05038936,-0.04507464,-0.02064167,0.01047998,-0.05582505,-0.0310882,0.02620129,0.01883628,0.01657359,0.01773275,0.05344389,0.0585354,0.03665252,0.07787559,0.0301488,-0.20637494,0.01022269,0.10710538,0.03828795,-0.03641821,0.02800967,0.03058703,0.10449811,0.03931955,0.01847151,-0.00295866,0.04670468,0.0111224,0.05105823,0.03600204,-0.03029549,-0.04532047,-0.069296,-0.05347638,0.03537159,0.00376742,-0.00170327,-0.05725311,0.0013043,0.00416724,-0.02772181,-0.00459992,-0.00621514,0.00788395,0.01720325,0.05135963,0.00885671,-0.01733533,-0.01430811,0.02606447,0.02319573,-0.04903089,0.10478934,-0.02981764,0.05367426,-0.00626369,-0.06319308,0.0541377,0.00080181,-0.03136929,-0.00659108,0.01653081,0.01354276,0.03828585,0.04678459,0.06831831,-0.03694436,0.03113801,-0.00379521,0.03442843,0.0088958,0.00944117,0.01859729,0.01714635,0.06594601,0.02406687,-0.02322678,-0.06724498,-0.024922,0.02818426,0.04986586,0.05629941,0.00573939,0.05018244,0.01114289,-0.04073419,0.02206695,-0.03695128,-0.01237574,0.00899567,0.02795357,-0.04245613,-0.00043469,-0.01523034,-0.05422806,-0.0236187,-0.12294058,-0.04810607,0.06147155,0.00292797,-0.0198976,0.0294371,-0.05100854,0.00412129,0.03343177,-0.02575452,-0.04324792,0.01597061,0.02213922,0.06053721,0.0860046,-0.0651039,0.01255328,-0.05346568,0.01077813,-0.06066893,0.13186002,0.05878744,-0.12388264,0.02558214,0.03825365,-0.01540028,-0.09585144,0.02227191,0.00137624,-0.07064267,-0.01512569,0.06716681,-0.05719777,-0.03973385,-0.0040564,0.01028286,0.05270893,0.00625353,-0.02727237,-0.06659994,-0.00829076,-0.00457042,-0.02553872,-0.00195579,0.01595152,0.03518564,0.04640016,-0.08351877,0.02394134,-0.04042133,0.01369967,-0.01932644,-0.03761959,-0.05906417,-0.05484244,0.02307498,-0.04229777,0.09253834,0.05490874,0.00790647,0.00469555,-0.04162464,0.00854017,-0.03712208,-0.05733727,-0.00351149,0.03382662,-0.00721387,-0.01934005,-0.05216303,0.01661852,-0.00538036,0.01532345,0.03906808,-0.01402334,-0.02601835,0.0337056,0.01372293,0.00288576,-0.07589384,-0.19512287,-0.08732425,0.00269475,0.02742513,0.05449911,0.00870556,0.02907852,0.01792099,0.05132461,0.07647938,0.0995735,0.05367668,-0.06206735,-0.01359809,0.00044548,0.03383051,-0.00049411,-0.02134059,0.03121978,0.02511857,-0.02685423,0.00594999,-0.01767792,-0.092672,0.02228555,-0.06942242,0.11316559,0.00860331,0.00476271,-0.00911012,0.05539715,0.02043429,-0.04319058,-0.15320611,0.01376376,0.08453526,0.01809597,-0.0407472,-0.00361103,-0.02781975,-0.02902051,0.04135435,0.03454147,-0.12714529,0.04517783,-0.04453143,-0.06372515,0.01487333,-0.03459258,0.01257653,0.00294874,0.06906593,0.02866179,0.0858926,-0.01519599,-0.04294333,0.02896282,-0.03496747,0.00817443,0.04262745,-0.01535814,-0.00816686,-0.05881687,0.02913571,0.05220034,0.00781325,-0.00983245,0.01286221,0.03097441,0.00209013,-0.00503436,0.1127691,0.05495225,-0.02959122,0.03293663,0.03131513,-0.03330914,-0.09117875,0.03428926,0.02482625,0.02805519,-0.01045896,0.03286748,0.06644985,0.02756617,0.10067465,0.00405326,0.0111211,0.01514901,0.00640818,-0.01489187,-0.02018722,-0.06679849,-0.07606898,0.0705862,0.00081706,-0.27370462,0.00462709,-0.02865575,-0.00285471,0.01409586,0.00228683,0.07784054,0.00488904,-0.0760852,0.01699961,-0.01974803,0.0550661,-0.00732499,-0.02127325,0.00415534,0.00881001,0.06701163,0.00041526,0.02814436,0.05257487,0.00429614,0.09525336,0.20231502,-0.05244161,0.06313138,0.03841877,-0.04067972,0.01079911,0.06114693,0.00405688,-0.01415117,-0.02299928,0.02256929,-0.0511662,0.00632128,0.04746296,-0.04498331,0.00285264,-0.00201056,0.00863548,-0.0372568,-0.04757051,-0.0195094,0.04591085,0.1294724,-0.02930382,-0.0626307,-0.09580764,-0.00574345,0.02703529,-0.03772542,-0.03327229,-0.00138682,0.01828982,-0.0253917,0.05106455,0.05179582,-0.01945819,-0.06029832,-0.02770002,-0.00495944,-0.00041071,-0.04324416,0.06163612,0.01655279],"last_embed":{"hash":"1cjpkxs","tokens":150}}},"text":null,"length":0,"last_read":{"hash":"1cjpkxs","at":1751815146518},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{27}","lines":[319,319],"size":205,"outlinks":[{"title":"https://www.youtube.com/watch?v=G5tE0LFFiTY","target":"https://www.youtube.com/watch?v=G5tE0LFFiTY","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1cjpkxs","at":1751815146518}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{28}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06247389,-0.04895245,-0.04458373,-0.00572028,0.00496842,-0.02133328,-0.00701448,0.01597437,-0.00910333,0.00015639,0.0774543,-0.08341602,0.02872126,0.00444977,0.01105264,-0.03586553,-0.0326197,-0.0385887,-0.03149321,0.02846264,0.13140318,-0.06834882,0.02877512,-0.04306882,0.02262582,0.05689148,-0.0111471,-0.03593234,-0.03270902,-0.16648671,0.009494,-0.03995836,0.00636456,-0.00556621,0.02281464,-0.04674732,-0.01669748,0.06311826,-0.05714428,-0.0147731,-0.00035649,0.01698412,-0.01997931,-0.02110229,0.06694502,-0.07470456,0.0237186,-0.03302188,0.00119177,-0.05623884,0.013197,-0.06820556,0.00784889,0.03448547,0.0138368,0.02863723,0.01236537,0.03437382,0.03915507,0.03555549,0.08254427,0.05748423,-0.19908133,0.04705461,0.07423157,0.01626231,-0.03155349,-0.00397324,0.01886232,0.06608072,0.02641758,0.02692443,-0.0256354,0.06157707,0.03073445,0.02842247,0.03127683,0.0213535,-0.00580096,-0.05306614,-0.05843201,0.05751233,-0.01348038,-0.00535558,-0.01211139,-0.03190112,0.01922073,-0.02673812,-0.02106344,0.00062457,0.00297094,-0.00963049,0.04080438,0.00749411,-0.03247127,-0.00306188,0.02739617,0.00148003,-0.08912851,0.11874791,-0.04159586,0.07007232,-0.00777649,-0.05435222,0.0514701,-0.01655526,-0.00930442,-0.03421487,0.02016298,0.01010342,-0.00718436,0.01305742,0.04567983,-0.0422873,0.03397937,-0.00567674,-0.00464859,-0.00047418,0.0145451,-0.00852852,0.00004213,0.0261206,0.02549728,0.01126036,-0.05998692,-0.03350211,-0.00003033,0.04319838,0.04290209,0.07865558,0.04725179,0.01910682,-0.0567693,-0.02361478,-0.02702974,0.0087854,-0.03086539,0.00938912,-0.03561025,-0.01626384,-0.00981924,-0.0706928,-0.04993457,-0.07934305,-0.05197894,0.11271304,0.02478071,0.00110579,-0.02502147,-0.04480402,0.02897623,0.07410701,-0.01331761,-0.06314802,0.00219157,0.0317121,0.04393504,0.09584708,-0.08357957,-0.02051541,-0.00243055,-0.01650686,-0.05129569,0.15851116,0.01916256,-0.14240208,0.00116642,0.03377441,-0.01007266,-0.0656582,0.04237879,0.0163807,-0.04547365,-0.00937548,0.05882081,-0.02632569,0.01144273,-0.0308489,0.01519819,0.04725222,-0.02521385,-0.03799305,-0.05701855,-0.00915619,0.00686778,-0.00338821,-0.04704903,0.05040094,-0.00318792,0.03127031,-0.07584388,0.03696796,-0.01549427,0.04386882,-0.01050471,-0.04097568,-0.04475111,-0.0160563,0.03861994,-0.03050695,0.18059608,0.06527611,-0.01249479,0.0309061,-0.02894191,0.01695634,-0.02857904,-0.05434053,0.00933691,0.03120483,-0.02385155,0.05312865,0.00097139,0.01046242,-0.02666435,0.01599141,0.03100254,0.04208386,0.02717491,0.05844745,-0.00436664,0.01443876,-0.05096141,-0.18341087,-0.03474057,0.01750278,-0.02424331,0.03137684,0.00451745,0.0494167,0.01835243,0.08137309,0.0553549,0.08779124,0.057325,-0.07674643,-0.00199274,-0.02880883,0.0665377,0.04338045,-0.03363609,0.01205653,0.03066326,-0.0515728,0.02030124,-0.033825,-0.0671116,0.05845205,-0.05862594,0.11565286,-0.02604851,-0.00705183,-0.04364829,0.04295294,0.03559712,-0.02978074,-0.1755785,0.02481837,0.05388246,0.01598842,0.00423979,-0.00664351,-0.0441935,-0.0367716,0.01367877,0.00248845,-0.06849078,0.01579784,-0.07677186,-0.05415811,0.00766405,-0.00890785,0.03702902,0.01497297,0.02744871,0.01038134,0.0858309,0.01569846,-0.00136694,0.04160267,-0.03582515,0.01143289,0.0531042,-0.01168711,0.00330224,-0.03233496,0.00395052,0.05002237,-0.01144807,0.00475431,-0.00970579,0.01475908,-0.0407741,-0.02342634,0.12324819,0.03497814,0.00430838,0.05123954,0.01848832,-0.03192927,-0.07337926,0.010186,0.00881942,0.00540879,-0.04390692,0.07222491,0.03819502,-0.02853596,0.0736094,0.02603647,0.03156554,0.00075771,-0.00772721,-0.02846638,-0.02734551,-0.05764916,-0.04218765,0.05934471,0.01778517,-0.29786387,-0.02266547,-0.02848249,0.00669462,0.07188182,0.06540412,0.04543928,0.01677634,-0.0979414,-0.01210081,-0.03497013,0.04140083,-0.01572087,-0.02961299,-0.03640226,0.01098206,0.03597504,-0.03580384,-0.02957182,0.03537707,-0.01891066,0.04171705,0.20107876,-0.01277601,0.01688362,0.03650748,-0.06375082,0.09079652,0.08633517,0.00026952,-0.0406927,-0.00581542,0.03472086,-0.04964062,0.02283317,0.04003702,0.00718353,-0.04137537,0.00830839,0.03252612,-0.00385632,-0.05496625,0.00108799,0.04409521,0.0872605,-0.00942884,-0.085227,-0.05096979,0.0559581,0.04175445,-0.06936722,-0.03169784,0.01111514,0.07590397,-0.00037095,0.08604421,0.02266067,-0.04743252,-0.10616605,-0.00766158,-0.03728392,-0.01964185,-0.05187682,0.04847569,0.0250503],"last_embed":{"hash":"17bivst","tokens":243}}},"text":null,"length":0,"last_read":{"hash":"17bivst","at":1751815146527},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{28}","lines":[320,320],"size":287,"outlinks":[{"title":"https://ethplorer.io/address/******************************************","target":"https://ethplorer.io/address/******************************************","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"17bivst","at":1751815146527}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{29}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04624161,-0.03627829,-0.05539688,0.00638467,0.03219038,0.00131075,0.00532213,0.05935244,-0.00973807,0.00103249,0.05546966,-0.06930983,0.01513971,0.0577823,0.00784613,-0.01753978,0.02432127,-0.06057099,-0.06504628,0.04465911,0.10939457,-0.07121602,0.02998113,-0.02172163,0.07563812,0.03461201,-0.00034002,-0.02502171,-0.05253191,-0.18236053,0.00552853,-0.0331188,0.00739224,0.01012313,0.01659373,-0.04364012,-0.02037073,0.05561801,-0.01044192,-0.01290545,-0.00784047,-0.0173099,-0.04009173,-0.00378452,0.05634297,-0.10379354,0.01355184,-0.06323957,0.01763794,-0.02025868,-0.0345001,-0.08249832,-0.03832012,0.03795104,0.004232,0.00642265,-0.00854472,0.03101882,0.06395527,0.04882462,0.07462292,0.04297224,-0.21834336,0.03886797,0.05553699,0.00971774,-0.02052391,-0.00856228,0.02278028,0.07381921,0.03705988,0.02077161,0.01151437,0.03467046,0.07249692,-0.03537689,0.0274875,0.00094252,-0.01157966,-0.06697256,-0.02079232,0.09401582,-0.01782125,0.00853998,-0.0182845,-0.01504531,0.02511236,-0.03811708,-0.04700679,0.02996647,-0.0026535,0.02892392,-0.00097961,-0.02176193,-0.06308182,-0.0084648,0.01710597,0.02313015,-0.02776292,0.11302905,-0.00309489,0.05891498,-0.03476391,-0.07285622,0.00994455,-0.00229082,-0.03777876,-0.02888959,0.02626817,0.05052442,-0.00992791,0.05300914,0.05630491,-0.05897427,0.00691004,-0.00031607,-0.01676839,-0.03153506,0.00312486,-0.04819863,-0.01408155,0.03312681,0.11349721,-0.00126822,-0.03187463,-0.01752966,0.01195306,0.05673305,0.02465806,0.04331965,0.07117596,0.00154311,-0.06335682,-0.02711705,-0.01556305,-0.01355152,-0.00944587,0.01426503,-0.03507927,0.00205652,-0.06694323,-0.08573924,-0.02074466,-0.06835676,-0.09039479,0.01889069,0.00538206,-0.00902006,0.01154968,-0.00753054,-0.0394442,0.07518354,-0.03099033,-0.04225546,-0.01313071,0.02244873,0.0557985,0.12479092,-0.07800499,-0.0070958,-0.02112948,-0.04922494,-0.05740448,0.14765845,0.03921999,-0.11877245,0.01407389,0.02030465,-0.03145368,-0.07251546,-0.00857894,-0.00473693,-0.04149598,-0.01567233,0.09644838,-0.00811922,-0.02727708,-0.03011961,-0.01090105,0.03927983,-0.02781628,-0.02379025,-0.06423816,0.00945851,-0.00494765,-0.04910068,-0.04412533,0.0347706,0.02388436,0.02600552,-0.06154604,0.03074261,-0.02341791,0.03825068,-0.03102762,0.01372898,-0.06373077,-0.02573434,0.03667323,-0.0422187,0.13131537,-0.00944245,0.02650004,-0.00126394,-0.05121966,0.04811436,-0.01921335,-0.01377057,0.00765036,-0.01155943,-0.00240243,0.05528441,-0.02475725,0.01879814,-0.07686511,0.02823711,0.0095237,0.01457785,-0.01657571,0.04906107,-0.02282542,-0.00926659,-0.07981833,-0.19895732,-0.00157536,0.05748231,-0.00032272,0.02086719,-0.00628277,0.06111314,0.05568454,0.0843406,0.11662215,0.10487046,0.02395181,-0.04604503,-0.01080403,0.00625027,0.04145666,0.01649983,-0.0166501,-0.00839656,0.03159906,-0.03428157,0.01118869,0.00387365,-0.06536905,0.04563346,-0.03424958,0.1341892,-0.02147567,0.01361647,0.001831,0.06689422,-0.03245988,-0.03646584,-0.07962096,0.02931911,0.07597542,0.02071339,0.01345973,-0.07269199,-0.01925627,-0.02493279,0.00060875,0.04653348,-0.11056152,0.00323772,-0.05297169,-0.01548769,0.02457888,0.01112381,0.04189593,-0.02197252,0.02727014,0.01430551,0.05002987,-0.00151583,-0.03631323,0.0184081,-0.07067981,0.01383991,0.05992128,-0.00912226,0.0128883,-0.04189201,-0.02963265,0.0269027,-0.01698215,0.0346836,0.00322867,0.01214997,-0.01525229,-0.01178377,0.11106215,0.0302866,0.00488951,0.07075118,0.02850302,-0.03711141,-0.02907506,0.0054936,0.07419617,0.04146299,-0.00629313,0.00157052,0.0915524,-0.0007597,0.06357117,0.05285864,-0.04077572,0.00723142,-0.0455752,-0.01764294,-0.0430312,-0.05300587,-0.02748879,0.07476785,0.02678918,-0.2899614,-0.01873991,-0.01109372,0.01953356,0.05480543,0.05005854,0.08724747,0.05464401,-0.11956407,-0.01499517,-0.01389659,0.05666022,-0.00124374,-0.05344293,-0.00955198,0.00173947,0.04907262,-0.03116806,0.0306696,0.00286949,-0.01121513,0.04224481,0.21342435,-0.01321229,-0.01717129,-0.00063016,-0.07287826,0.05378154,0.05548276,0.01969703,-0.0213525,0.00453674,0.02682052,-0.0371752,0.02748221,0.01395184,-0.00588897,0.00258083,0.00808961,0.04884537,-0.00765631,-0.03646105,0.00512464,0.03327141,0.11002619,-0.01615259,-0.07345427,-0.04150738,0.01647161,0.03932221,-0.06026291,0.01465075,-0.00205714,0.00604156,-0.03576493,0.08310422,0.00545937,-0.02566912,-0.04822373,-0.02909696,-0.03664407,0.02227675,-0.04515003,0.05426888,0.01473116],"last_embed":{"hash":"wj8no","tokens":196}}},"text":null,"length":0,"last_read":{"hash":"wj8no","at":1751815146542},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{29}","lines":[321,321],"size":242,"outlinks":[{"title":"https://phantom.com/tokens/solana/6tCEUogw2uk4VQz5JKb295ixFrj6vAPEQbZqL9VfFXSz","target":"https://phantom.com/tokens/solana/6tCEUogw2uk4VQz5JKb295ixFrj6vAPEQbZqL9VfFXSz","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"wj8no","at":1751815146542}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{30}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05343208,-0.05077264,-0.05415519,0.01946537,0.06785843,0.00222575,-0.02559167,0.0309164,0.00479255,-0.0150062,0.04868924,-0.06171205,0.04359699,0.06844848,0.01007402,-0.02501725,0.00604881,-0.04954084,-0.04367431,0.07186485,0.13114391,-0.06650634,0.00039663,-0.04882432,0.05922967,0.04377817,0.01266579,0.00569583,-0.0293858,-0.19176553,-0.01942955,-0.04377413,0.01812618,-0.01839791,-0.02620584,-0.01267382,-0.01020012,0.03508034,-0.01156559,0.00973152,-0.01025535,0.01763321,-0.033704,-0.00072618,0.04754572,-0.11361766,-0.0171981,-0.03944827,0.01098472,-0.01894235,-0.00818037,-0.06272552,-0.01136591,0.02445286,0.02717775,0.0476108,-0.02728012,0.01274976,0.05043428,0.05785204,0.06114925,0.04461174,-0.22631407,0.03161051,0.02857325,0.0294279,0.006333,-0.00475321,0.03940298,0.06405322,0.06917328,0.01221235,0.03777309,0.00859267,0.03595177,-0.03097509,0.04659529,0.00572322,0.00821177,-0.0525404,-0.01242831,0.07661974,-0.02135537,0.00413429,-0.0052584,-0.02192946,0.05017005,-0.04304973,-0.03981991,-0.01275886,-0.01372331,0.00128774,0.02196218,0.00598035,-0.04792965,0.02582741,0.02409905,0.01561028,-0.06676806,0.11690719,0.01174305,0.06391509,0.00558038,-0.06193309,0.02363045,-0.05525582,-0.07289087,-0.03664882,0.00458495,0.02219426,-0.00033263,0.025418,0.06443675,-0.04870337,0.04456653,0.0066513,0.01430311,-0.01228053,0.01317293,0.01085281,-0.02522003,0.02393732,0.071208,0.0123262,-0.02962081,-0.00523752,0.04246738,0.07296674,0.01952933,0.04999151,0.05729607,0.01260927,-0.08072209,-0.01903211,-0.0150403,-0.01797782,-0.0039752,0.01264789,-0.0357679,-0.01520513,-0.06751765,-0.0726164,-0.02101376,-0.09947002,-0.06478351,0.05221879,0.03062877,-0.00106552,0.00040926,-0.02500396,-0.03404046,0.07086074,-0.03076324,-0.07270406,-0.01593985,0.01458016,0.04953207,0.07728612,-0.06867198,-0.03571908,-0.03629907,-0.03631547,-0.07265969,0.11789425,0.02794239,-0.1167199,0.03358549,0.02493745,-0.0252098,-0.06166685,0.02342217,0.01307342,-0.03105231,-0.02344783,0.10764334,-0.00795494,-0.01768757,-0.02147141,-0.00502542,0.01401224,-0.00629166,-0.03094013,-0.06253521,-0.02348321,-0.04364216,-0.0287513,-0.00902009,0.02275079,-0.00055201,0.04886036,-0.02887041,-0.0148596,-0.00126719,0.05377999,-0.02385874,0.02524967,-0.0518337,-0.00703332,0.01922297,-0.02928432,0.12594667,0.00279018,0.0064998,0.01378804,-0.02310494,0.05152977,-0.01844016,-0.0196481,-0.01522571,0.01108292,0.03681313,0.03126764,-0.00200937,0.01511849,-0.07782155,0.01793654,0.00720739,0.05279678,-0.01913993,0.07533724,0.00465984,-0.02213145,-0.04972667,-0.20159194,0.01761555,0.06119322,-0.01808292,0.06247578,-0.00396075,0.08483999,0.00543749,0.06338806,0.10983422,0.06078444,0.01706167,-0.02853329,-0.00898582,0.02446692,0.05694194,0.03458736,-0.0250433,0.01759586,0.0284144,-0.05449385,0.01489396,-0.02565092,-0.1048502,0.07000428,-0.03966808,0.10219529,-0.04126894,0.01119767,-0.03509472,0.04633649,0.00207096,-0.02250995,-0.09789258,0.02363305,0.06360913,0.04964951,-0.02187977,-0.06978744,-0.03124459,-0.02255605,0.0101065,0.06335324,-0.11044275,0.03732564,-0.02972685,-0.00220613,0.03031991,-0.01624051,0.04825583,-0.00521475,0.01315582,0.05440679,0.04074671,0.00232349,-0.047467,-0.01255818,-0.08451944,-0.02191503,0.05004914,0.0024313,0.00851727,-0.02851728,-0.01357007,0.04668407,-0.02255027,0.0271301,-0.0208883,0.00422007,-0.01879016,-0.01193751,0.08720359,0.01444557,0.02881324,0.02456306,0.03722875,-0.0079237,-0.09349303,0.00614828,0.04568854,0.014493,0.04849843,0.03502565,0.09185309,0.03153311,0.06315237,0.05593446,-0.03609337,0.01071859,-0.01821065,-0.03551444,-0.06536218,-0.04109664,-0.04018008,0.07201759,0.01882696,-0.30193996,-0.00712434,-0.01523714,-0.00365964,0.05875206,0.01586974,0.04312838,0.04369767,-0.09248313,-0.00257126,-0.02404598,0.08103792,0.004239,-0.03253405,-0.00272732,-0.03224289,0.05548808,-0.02405825,0.00888369,0.00946824,-0.01354358,0.03836374,0.18280858,0.00161366,0.0274507,0.02679481,-0.10876448,0.07781811,0.08521208,0.00908615,-0.04823943,0.000208,0.00098848,-0.06219219,0.03514817,0.08101996,-0.00319378,-0.01877869,-0.02250855,0.05299079,0.01308583,-0.00969368,-0.02992679,0.07571333,0.11892306,-0.02741393,-0.04820579,-0.07109962,0.00615392,0.02421364,-0.061051,-0.01677462,0.02358138,0.0308435,-0.03509738,0.05895354,0.01794327,-0.01915894,-0.07314052,-0.07469493,-0.03810716,-0.02050151,-0.07173843,0.02528818,-0.01441634],"last_embed":{"hash":"1ums59x","tokens":220}}},"text":null,"length":0,"last_read":{"hash":"1ums59x","at":1751815146552},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{30}","lines":[322,322],"size":302,"outlinks":[{"title":"https://www.dextools.io/app/en/solana/pair-explorer/ENDyunqABSwMvfUoRxsNA1N2QLhXeZQTYrnPZUQdnygP","target":"https://www.dextools.io/app/en/solana/pair-explorer/ENDyunqABSwMvfUoRxsNA1N2QLhXeZQTYrnPZUQdnygP","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1ums59x","at":1751815146552}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{32}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06415944,-0.0337393,-0.05754352,0.00298783,0.04052399,-0.01012575,-0.02528148,-0.01582678,-0.03152544,-0.02481098,0.0706294,-0.07219016,0.00512528,0.01943777,0.03166998,0.03616196,0.01338261,-0.01223005,-0.04073016,0.01117564,0.11805076,-0.04453497,0.02881068,-0.04014778,0.09478001,0.02117587,-0.0057192,0.00024679,-0.04973014,-0.18471761,0.00983347,-0.01567074,0.01589197,-0.02008257,-0.00566743,-0.01256486,-0.0337737,0.07387517,-0.04864471,0.02684227,-0.00251608,0.01571405,0.00179321,-0.0237141,0.02661391,-0.07062288,0.03253379,-0.03930861,0.02705965,-0.07141102,-0.02304299,-0.00668653,-0.01943319,0.02429484,0.04118202,0.03282101,-0.01823747,0.0476859,0.00425709,-0.0258374,0.09916379,0.04103876,-0.19161004,0.00509965,0.08062734,0.04740567,-0.01676773,0.01739183,0.02928516,0.07860087,0.07548562,0.04025396,-0.01262641,0.0211341,0.0117023,-0.00285353,0.04051703,0.01875795,-0.02011213,-0.05147837,-0.05504687,0.05947105,-0.01380429,-0.01212406,-0.02580114,-0.03366368,0.03013403,-0.02296332,-0.00740761,-0.00373205,0.02985082,0.02714524,0.02398212,0.02018544,-0.01712882,0.0123761,0.02073937,0.00536252,-0.06189524,0.11389517,0.00888428,0.04909676,-0.01266391,-0.06909339,0.0231108,0.01508302,-0.02422258,-0.02047884,0.02687238,0.00065373,0.02093261,0.01867853,0.09626591,-0.08790796,-0.00759753,-0.00238301,-0.03303121,0.01306423,0.02802664,0.04571418,-0.02210081,0.03870321,0.05683191,-0.01988655,-0.01821745,-0.02510918,0.03726544,0.03873362,0.06095745,0.07108507,0.02963174,0.0453751,-0.0994611,0.02743061,-0.04774995,-0.0567762,-0.03541892,0.00634229,0.00869476,0.00698053,-0.0384502,-0.01819945,-0.00409933,-0.12558158,-0.01771749,0.05467107,0.03591749,0.01970246,-0.01156485,-0.08372049,-0.01751587,0.02927794,-0.0122926,-0.07051831,-0.04636373,0.01497698,0.04930626,0.08884715,-0.10170266,-0.03104584,-0.03379666,-0.02846002,-0.0546338,0.15090762,0.04440333,-0.09300257,-0.00936425,0.02197495,-0.01724801,-0.11206408,0.00262673,0.02762615,-0.05885103,-0.01345935,0.06470285,-0.0149861,-0.0509261,-0.04685976,-0.01743345,0.01965133,0.0126352,-0.04218261,-0.06481597,0.00891179,-0.02432082,-0.04245925,-0.03675587,0.02579591,0.01674072,0.04680404,-0.08661523,0.0192285,-0.04362933,0.01860127,-0.01757091,-0.02253324,-0.04468787,-0.0289557,-0.01769209,-0.00707863,0.09266624,0.05321875,-0.04676773,0.04380931,-0.04456516,0.01792335,-0.04569748,-0.05393071,0.01632309,0.01070354,-0.01167954,0.02908562,-0.03386974,0.04395847,-0.05054827,-0.0086344,0.08803985,0.03842893,-0.06058494,0.05238982,0.04913666,-0.01172053,-0.08481319,-0.19380052,-0.06399545,0.00492486,0.02511543,0.03468237,0.00750666,0.04491205,0.00124813,0.05066404,0.02537903,0.08403061,0.03296985,-0.05537187,0.00833699,0.02464041,0.04194692,0.01392045,-0.07233957,0.0317757,0.05753609,-0.04022,0.0154872,-0.00266687,-0.07661009,0.02943005,-0.04751408,0.12541558,-0.03886272,0.00950877,-0.01642527,0.0135431,0.03485028,-0.03142601,-0.1499496,0.00543632,0.04595178,0.02401688,0.01119843,-0.02732322,-0.02332789,-0.01629733,0.00857761,0.02366966,-0.08221441,0.01470272,-0.03572813,-0.02440214,0.0401644,0.01082462,0.02682224,0.03348834,0.03965787,0.03558937,0.09973779,-0.01698259,-0.05548995,0.0232442,-0.02738044,-0.04385555,0.04178001,-0.02300818,-0.05158146,-0.02954519,0.01638807,0.05773655,-0.03699032,-0.0085707,0.01998563,0.01326927,-0.0251253,-0.02477118,0.11156306,-0.00238878,-0.04630305,0.03077197,0.04405723,-0.03318892,-0.09232218,0.03481674,0.02456834,0.01365954,-0.0098661,0.04718816,0.1032606,-0.00462079,0.09113999,0.01362301,-0.01173719,0.00402486,0.02133627,-0.05365392,-0.03331744,-0.10492475,-0.05344425,0.07577804,0.04231064,-0.26681811,-0.00501471,0.00020113,-0.0041554,0.0637601,0.01392732,0.10269429,0.00257809,-0.03497031,0.04222063,-0.00481981,0.05381471,0.01930066,-0.06828288,-0.03751158,-0.00964628,0.04320555,0.01129789,0.02965626,0.05639586,0.0007262,0.12221503,0.1890967,-0.01036694,0.02713209,0.06503802,-0.07281543,0.0523436,0.06050832,0.01562437,0.01262574,-0.0102702,0.03275476,-0.05647973,0.01816547,0.07699221,-0.01366148,0.02209403,-0.01510882,0.01070574,-0.01300379,0.00985538,-0.00594985,0.0283158,0.10793186,-0.05981497,-0.04184747,-0.08890184,0.00511322,0.01654697,-0.06827382,0.01252198,0.01885815,0.04305908,-0.02225689,0.07900537,0.04016874,-0.01598784,-0.0494803,-0.02396275,-0.02655241,-0.02254385,-0.04586798,0.05262513,-0.00792759],"last_embed":{"hash":"159rp5","tokens":163}}},"text":null,"length":0,"last_read":{"hash":"159rp5","at":1751815146562},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{32}","lines":[324,324],"size":265,"outlinks":[{"title":"https://m.sosovalue.com/blog/comprehensive-guide-to-bitcoin-mining","target":"https://m.sosovalue.com/blog/comprehensive-guide-to-bitcoin-mining","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"159rp5","at":1751815146562}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{33}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03890964,-0.03246034,-0.05948659,-0.00016552,0.0132113,-0.0012198,0.0285647,-0.00610553,-0.00231002,0.00750584,0.08150547,-0.07924984,0.02960578,0.00904937,0.02037055,-0.00800042,-0.0131219,-0.02983537,-0.07136025,0.00925682,0.13664982,-0.05701132,-0.00900319,-0.03692756,0.07147109,0.05107477,-0.04172172,0.01663215,-0.05111768,-0.16888514,-0.00324268,-0.04348964,0.05066558,-0.0264726,0.01110149,-0.00680951,-0.02569096,0.08880485,-0.04168373,0.00813869,0.01640462,0.01175558,0.01781065,0.03338384,0.04077819,-0.08579842,0.01077261,-0.01834442,0.00884925,-0.01852487,0.00723491,-0.05781846,-0.00986314,0.0287408,0.05103801,-0.01235884,-0.00357868,0.02716284,0.02209141,0.00948459,0.09124282,0.03596092,-0.2106436,0.00447344,0.08670747,0.01486296,-0.03539423,-0.00536029,0.03468302,0.1171132,0.06721555,0.01570101,-0.00640024,0.01700067,0.01998586,0.01983356,0.02362499,0.02952191,-0.02554577,-0.06166146,-0.04422706,0.0533455,-0.01374087,0.01994819,0.00871431,-0.01122333,0.02167504,-0.00132072,-0.01371328,-0.00342032,0.02534037,0.02582313,0.01628634,-0.01353937,-0.01239168,-0.01249288,0.00504748,0.0186626,-0.04032253,0.12577017,0.00679406,0.0597643,-0.01014894,-0.08816332,0.0116354,0.00248571,-0.04873816,-0.00985098,0.01824243,0.00262752,-0.00053033,0.04628703,0.06319886,-0.07416148,0.01051729,0.01782502,-0.00554632,0.03280932,0.01086683,0.01709391,0.00674487,0.02771936,0.04589898,0.03135091,-0.0327055,-0.02897566,0.04664307,0.05395509,0.02891632,0.05845748,0.04224571,0.03877939,-0.10154112,-0.00697922,-0.03365748,-0.03674179,-0.02031356,0.006367,-0.02807669,0.00410447,-0.0136234,-0.04754267,-0.01114211,-0.08448692,-0.01708815,0.0717259,-0.00458444,0.00657457,0.01326973,-0.10043715,-0.01078763,0.04283005,-0.0252511,-0.08111008,-0.03359443,-0.01726899,0.06083087,0.06381936,-0.07385582,-0.02792247,-0.06713908,-0.00612791,-0.04534326,0.12755775,0.05659504,-0.0668508,0.01665655,0.03041449,0.00561969,-0.11488409,0.02845772,0.03194479,-0.06646319,-0.02107234,0.09045608,-0.03728743,-0.02118726,-0.03052229,0.01025851,0.05011101,0.02694982,-0.03236792,-0.0774048,0.00140038,-0.04613152,-0.0384201,-0.01547928,0.01897826,-0.0210476,0.06578882,-0.08222865,-0.00376917,-0.03301007,0.02249282,-0.02500926,-0.03832809,-0.05809754,-0.02414497,-0.01182204,-0.03352369,0.11347545,0.02803664,0.00459701,-0.00974003,-0.08403043,0.04203942,-0.01240843,-0.04650187,0.03618896,0.03411358,-0.01447541,0.01825232,-0.02615081,0.00237027,-0.02738582,0.03558012,0.0602985,0.03520842,-0.03591354,0.06320196,0.01646261,-0.02318243,-0.07417197,-0.18739489,-0.08069839,0.04565698,-0.01015268,0.04298791,-0.01382009,0.05423329,0.0039279,0.04679505,0.06043731,0.07151612,0.02334675,-0.0652907,0.00157248,0.01867462,0.07097861,0.01256753,-0.04615281,-0.00416367,0.04164211,-0.02635783,0.00346534,0.01333817,-0.10857873,0.04023588,-0.04959138,0.08762232,-0.00585202,0.04039292,-0.01887969,0.06790181,0.00476579,-0.05500109,-0.08127355,0.01861858,0.01842249,-0.0062248,-0.02033828,-0.0651783,-0.03080171,-0.03657251,0.01219425,0.03552917,-0.06021227,0.00125535,-0.01588435,-0.04277585,0.00304799,0.03026253,0.03306773,0.02604864,0.06604867,0.03991197,0.0829704,-0.00445294,-0.05243165,0.02205692,-0.03023312,-0.04462098,0.06027888,0.01070995,-0.04436348,-0.04598232,0.00239413,0.07838815,-0.02995131,-0.00126965,-0.02012608,0.0453612,-0.00819401,-0.02186571,0.13531968,0.0239491,-0.03633275,0.03966064,0.01299577,-0.03293204,-0.120208,0.02741497,0.05061578,0.02021613,-0.03288923,0.04822673,0.05465868,0.0066289,0.06130948,0.0184961,0.00403505,0.01069879,-0.01509633,-0.03497823,-0.03851171,-0.06704078,-0.03260289,0.02477373,0.04917382,-0.2968989,-0.01699758,-0.02277526,-0.00333004,0.07301433,-0.01285486,0.08449524,0.00630421,-0.0647885,0.0117201,0.0235389,0.0524355,-0.00890663,-0.09151951,-0.01577014,-0.02543987,0.01117583,-0.00753426,0.05291159,0.04231179,0.04936679,0.08554136,0.20828788,0.00084923,0.04467094,0.03049448,-0.0349209,0.03102615,0.09553239,-0.00335965,-0.02271398,-0.02417159,0.03035474,-0.05892781,0.03413874,0.06068034,-0.00119187,0.00196961,-0.00906977,0.01800069,0.01110854,-0.02882625,-0.01654346,0.04242324,0.15602878,-0.05819391,-0.04383741,-0.089579,0.02228439,0.03597059,-0.08282046,0.00874206,0.00833462,0.05584726,-0.01848123,0.07178818,0.0109894,-0.01677447,-0.05325263,-0.03655868,-0.04870116,-0.0158981,-0.0302214,0.0225405,-0.00787227],"last_embed":{"hash":"9acv9p","tokens":152}}},"text":null,"length":0,"last_read":{"hash":"9acv9p","at":1751815146573},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{33}","lines":[325,325],"size":227,"outlinks":[{"title":"https://www.cryptominerbros.com/blog/calculate-crypto-mining-profits/","target":"https://www.cryptominerbros.com/blog/calculate-crypto-mining-profits/","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"9acv9p","at":1751815146573}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{34}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03504057,-0.00470971,-0.05594136,0.01444913,-0.00996951,-0.00304222,0.03230128,0.00691085,0.00448775,0.01261128,0.08743139,-0.08441527,0.04318288,0.01258617,0.00478601,-0.00559962,-0.00457963,-0.02015221,-0.07822482,0.01021662,0.13429615,-0.05400721,-0.01256796,-0.03101128,0.05957457,0.02621533,-0.04336618,0.01292813,-0.05738425,-0.17385033,0.00264148,-0.04029129,0.06072678,-0.02366171,0.00497894,-0.00365906,-0.04582596,0.07401691,-0.04767709,0.02085079,0.02867026,0.02298173,-0.00596318,0.00273326,0.05429911,-0.10102378,0.0239446,-0.03733665,0.01651639,-0.02124103,0.00324646,-0.04829393,-0.00636845,0.0358198,0.04168435,-0.01128104,0.00761542,0.00829736,0.0062891,-0.00500322,0.0944501,0.03930814,-0.21811303,0.01274728,0.09721074,0.00664591,-0.0457595,-0.00820177,0.02037723,0.10474758,0.07640491,0.01553957,-0.00040122,0.02053804,0.02060017,0.02986832,0.02939404,0.03305511,-0.01613553,-0.07310296,-0.03423766,0.05024922,-0.02467877,0.01498179,0.00209291,-0.00751003,0.04331086,0.00283158,0.00824996,-0.03126917,0.03382476,0.02404405,-0.01469741,-0.02570956,-0.00218065,-0.00071883,-0.023055,0.01702793,-0.02862392,0.12291928,0.03086391,0.06696019,-0.03593813,-0.09084225,-0.00487673,-0.01019906,-0.05424771,-0.00031248,0.0291248,-0.01232198,0.00504966,0.06712507,0.05979108,-0.07478885,0.00128687,0.03408948,-0.01039009,0.02755965,0.01914484,-0.00477914,0.03012433,0.01801474,0.01828242,0.03439241,-0.01888527,-0.02750416,0.04967349,0.06026171,0.0283938,0.03569845,0.05942472,0.02823434,-0.09135915,-0.00122524,-0.01283047,-0.02903681,-0.02366854,-0.00169986,-0.01863329,-0.00024192,-0.02371606,-0.04021325,-0.01066669,-0.07318378,0.00076493,0.09332579,0.0108865,0.01010155,-0.01405348,-0.08624659,-0.04781872,0.06514826,-0.01995536,-0.07019198,-0.01054053,0.00261082,0.05675318,0.05286768,-0.07933904,-0.05541792,-0.05718069,-0.02579137,-0.05586631,0.14843616,0.03993175,-0.05662696,-0.00065161,0.02473755,-0.0031001,-0.10118996,0.04130399,0.04422032,-0.056656,-0.01663678,0.09385528,-0.02563363,-0.02848545,-0.02593998,0.00966276,0.01774215,0.0199353,-0.03142131,-0.07865232,-0.02526409,-0.04527225,-0.03748621,-0.0106442,0.02602789,-0.02069251,0.05152347,-0.09767579,-0.01846958,-0.04946179,0.03055659,-0.01621406,-0.00386344,-0.04728369,-0.03722542,-0.01394535,-0.01388226,0.0965058,0.03338545,-0.03625485,0.00773971,-0.06804916,0.04173231,-0.01846017,-0.03947258,0.02446157,0.05136359,-0.05002677,0.01913721,-0.03079394,0.02299395,-0.04106775,0.01877968,0.05638589,0.01960046,-0.03779773,0.06759881,0.0161529,-0.01468813,-0.05748202,-0.18965696,-0.06788256,0.04704589,0.01184333,0.03938168,-0.02605287,0.02973846,0.01351421,0.04708935,0.05678757,0.07678863,0.03760283,-0.04642116,0.02715314,-0.00097133,0.04460543,0.00410031,-0.0493788,0.02822903,0.04699105,-0.02565113,0.00918045,0.05115618,-0.10114993,0.064528,-0.03830864,0.10290916,-0.00787304,0.05625623,-0.02873473,0.0465253,-0.00231331,-0.06882285,-0.09910492,0.02046107,0.03427738,0.0006836,-0.00235688,-0.06894072,-0.0383409,-0.0368361,-0.0040205,0.03109921,-0.06022886,0.00297587,-0.0122331,-0.04715564,0.01399073,0.02271702,0.06070859,0.01998691,0.06247512,0.04141311,0.08797663,-0.01076483,-0.04634736,0.03309384,-0.04707627,-0.0493601,0.0441218,0.01655152,-0.03486621,-0.03264071,0.01211065,0.0722791,-0.05273673,-0.01223724,-0.0151444,0.0357174,-0.0208311,-0.02243987,0.12017555,0.00377056,-0.03881676,0.04056991,0.00675477,-0.04400817,-0.11529494,0.02332635,0.05855804,0.01501092,-0.03542384,0.05172221,0.06868526,0.01967898,0.07783785,0.00260885,-0.0039833,0.00235952,-0.02082544,-0.03637617,-0.03284105,-0.0756769,-0.01844611,0.04724746,0.04123019,-0.29717511,-0.01369916,-0.01769797,0.02455095,0.04774391,-0.02291686,0.08390533,0.00206481,-0.06699923,0.00628611,0.01822724,0.05717907,0.01213713,-0.08721746,-0.02454647,-0.05255816,0.01415858,-0.02509292,0.06131331,0.02613271,0.04303812,0.08560687,0.19621319,0.00000311,0.04716029,0.04721191,-0.04053942,0.03958734,0.05780862,0.02465376,-0.00050034,-0.01449463,0.02664161,-0.02800685,0.06185421,0.05927916,-0.01143016,0.03425364,-0.00733699,0.02877471,0.01236252,-0.01849232,-0.02113556,0.02310348,0.13202609,-0.05904768,-0.03325397,-0.1024528,0.02225618,0.02223455,-0.0769683,-0.00367745,0.04252167,0.03531998,-0.0270685,0.0689214,0.01574967,-0.00688124,-0.04071729,-0.02893024,-0.04241658,-0.03020165,-0.02848243,0.02295859,-0.00920956],"last_embed":{"hash":"wzieac","tokens":159}}},"text":null,"length":0,"last_read":{"hash":"wzieac","at":1751815146580},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{34}","lines":[326,326],"size":240,"outlinks":[{"title":"https://asicmarketplace.com/blog/how-to-calculate-profit-in-crypto-mining/","target":"https://asicmarketplace.com/blog/how-to-calculate-profit-in-crypto-mining/","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"wzieac","at":1751815146580}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{35}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03155946,-0.02727624,-0.0622043,-0.01505059,0.06463971,0.00593504,-0.0457462,0.01782954,-0.02620271,-0.00195511,0.0654782,-0.09350928,0.04184529,0.03074657,0.01356215,-0.00785454,0.00536991,-0.04542032,-0.00891012,0.04280392,0.1274372,-0.06723217,0.03592178,-0.01371787,0.06561002,0.06144705,0.01107808,-0.01770988,-0.031836,-0.19271089,0.00259685,-0.01887775,-0.02275027,0.00643081,0.0195889,-0.03440756,-0.02225234,0.05810986,-0.04719044,0.01299234,0.01621026,-0.00290361,0.00386332,-0.00458041,0.07039215,-0.08403645,0.00573706,-0.02989467,0.02002132,-0.03218063,-0.00543147,-0.07725827,-0.00916488,0.03400291,0.03852677,0.04300845,0.00912584,0.03326904,0.04309894,0.04204954,0.09290507,0.03958704,-0.21360943,0.01985304,0.09620331,0.05670314,-0.01209433,0.02075301,0.06036435,0.07925943,0.0303002,0.03400285,-0.00647499,0.03125414,0.04074298,0.0081856,0.03481356,0.01659677,-0.01258095,-0.08492165,-0.01863458,0.06864654,0.00025412,-0.01860103,-0.00824609,-0.02157886,-0.00035247,-0.02940722,-0.04192759,0.01364889,0.01301356,0.00439263,0.03712361,-0.00919408,-0.05644108,-0.00484569,0.00675787,0.01302147,-0.07186047,0.12112383,-0.0313392,0.06172024,0.01204479,-0.04548194,0.02861609,-0.00889772,-0.02639452,-0.02419817,0.0355885,0.02123242,0.00143548,0.03495498,0.04897666,-0.0649863,0.023289,-0.01219212,-0.04616108,-0.02537941,-0.02849359,0.02927794,-0.02096101,0.03755341,0.05188202,-0.02033268,-0.0570804,-0.02581227,0.00081159,0.04285683,0.07377722,0.05737924,0.0573858,0.0314371,-0.08627552,0.00137674,-0.03463761,-0.03203108,0.02370957,-0.00091722,-0.03807364,-0.02483026,-0.03623983,-0.09138702,-0.03305072,-0.10611082,-0.10047058,0.08238623,-0.00548215,-0.01380964,0.02007355,-0.02241605,0.01349467,0.08406615,0.00119993,-0.06290393,-0.00810398,-0.01479153,0.04190494,0.09623953,-0.06575248,0.01192123,-0.04598161,-0.04532046,-0.05612626,0.14953464,0.02544811,-0.14281003,0.02679605,0.02136296,-0.00439708,-0.06838615,-0.00629864,0.02763673,-0.02886559,-0.03055425,0.05921174,-0.02397318,-0.058817,-0.02093257,0.00344285,0.04724069,-0.006176,-0.00532775,-0.08401461,-0.01621738,-0.01580554,-0.04664125,-0.04105269,0.02932711,-0.0083272,0.03852127,-0.10307058,-0.00254555,-0.02480211,0.01614727,0.00148433,-0.01578649,-0.02099267,-0.02252954,-0.0000737,-0.03553125,0.08072441,0.04860291,0.02237768,0.03792974,-0.05146417,0.04059102,-0.03866597,-0.04027797,-0.02569628,0.04043873,-0.02908836,-0.00854623,-0.02208162,0.05033112,-0.05073293,0.03414188,0.05765171,0.03215163,-0.00968282,0.07515369,0.01468024,0.00389711,-0.06102899,-0.18785925,-0.04948138,0.03590085,-0.0324721,0.00831034,-0.00829475,0.05645186,0.03227396,0.02193109,0.05331732,0.08365764,0.04040146,-0.02532482,-0.00848207,-0.01915026,0.05177773,0.04872822,-0.00740643,0.0064254,0.0124782,-0.02673108,0.02582052,0.00768181,-0.06239231,0.04332716,-0.06704834,0.10736018,-0.02141533,-0.03613722,0.04165369,0.07332881,-0.00335588,-0.04662447,-0.14818721,0.03816959,0.03558864,0.00748777,0.01357484,-0.07711789,-0.0194074,0.00208985,0.00554059,0.04436256,-0.11291718,0.01960042,-0.06064152,-0.04393444,-0.00208491,0.0069161,-0.00791626,-0.02142286,0.03091597,0.03002265,0.0963975,-0.00608324,0.00823266,0.01823217,-0.00605035,-0.00454963,0.01107183,-0.0084567,0.00541806,-0.01595838,0.01202561,0.02763561,-0.03838339,-0.03582241,-0.00113385,0.00909471,-0.04368514,-0.02208867,0.09931222,0.05657761,-0.01043016,0.03474962,0.0487633,-0.03661522,-0.10198954,0.0262644,0.00296469,0.00515205,-0.01240464,0.04638671,0.06841971,0.01529201,0.06253331,-0.01975221,-0.0174429,0.00406312,-0.0055169,-0.01506603,-0.04866621,-0.04549487,-0.03472222,0.07828647,0.03083263,-0.28627959,-0.01193513,0.01582904,-0.00935231,0.04677093,0.02931066,0.06843477,0.01889727,-0.07166746,0.02242551,0.00997008,0.04953721,0.00372354,-0.02263699,-0.01167586,0.0007445,0.09665229,-0.01144856,0.047007,0.03583998,0.02106537,0.06716298,0.20788106,-0.03530435,0.02420079,0.02950186,-0.08853389,0.05144574,0.06793653,0.02498387,-0.04210835,-0.01276411,0.038005,-0.04645233,0.03348938,0.04131247,0.0178024,-0.01823392,0.00275301,0.01485693,-0.05294836,-0.0136512,-0.01920555,0.0261293,0.10133383,-0.02559266,-0.08131783,-0.09248123,0.00601656,0.04146873,-0.03988355,0.01404029,-0.00831026,0.04665791,-0.01678816,0.08311833,0.03512345,-0.03336073,-0.05010946,-0.02829167,-0.04150156,-0.0240545,-0.03111106,0.05898581,0.02211196],"last_embed":{"hash":"1mcp1d4","tokens":163}}},"text":null,"length":0,"last_read":{"hash":"1mcp1d4","at":1751815146589},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{35}","lines":[327,327],"size":220,"outlinks":[{"title":"https://www.rootdata.com/Projects/detail/Nockpool?k=MTc2Mzg%3D","target":"https://www.rootdata.com/Projects/detail/Nockpool?k=MTc2Mzg%3D","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1mcp1d4","at":1751815146589}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{39}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03933368,-0.03245035,-0.06057264,-0.01683348,0.06702815,0.01037582,-0.05485554,0.03202529,-0.03990234,0.00603653,0.06658553,-0.08179767,0.0303187,0.03159286,0.03536831,-0.02234832,-0.00162532,-0.05139434,-0.02919233,0.02171601,0.14025769,-0.06198419,0.03356452,0.00633833,0.08518833,0.07737704,0.00784855,-0.02769223,-0.02523916,-0.18940888,-0.01193333,-0.0239024,-0.00110924,0.00203501,0.03007705,-0.0332229,-0.02499981,0.06234868,-0.03288675,0.02161874,0.02806799,-0.00827562,0.00318322,0.00135881,0.07672483,-0.08168634,0.00142865,-0.04919437,0.00888776,-0.03664466,-0.00453817,-0.08481899,-0.0109596,0.03604352,0.02015693,0.05003311,0.00792398,0.04557148,0.03710264,0.05660407,0.11119494,0.05990688,-0.2224928,0.03255392,0.09581244,0.05180993,-0.01886121,0.00802077,0.05321581,0.07230247,0.02266328,0.0332678,0.00185012,0.0427775,0.03303558,0.00200037,0.02955285,0.00783087,-0.01268922,-0.08516472,-0.00643193,0.0863997,-0.01037035,-0.01125991,-0.02348532,-0.03008639,-0.00615646,-0.02460696,-0.05018472,0.01937837,-0.00400193,-0.00930622,0.05719891,0.00550617,-0.06333118,-0.01112881,-0.01044786,0.02324437,-0.05846881,0.12138398,-0.04286069,0.05702399,0.01015213,-0.03048938,0.03617005,-0.00178824,-0.02456459,-0.04294701,0.04337113,0.0330767,0.00168321,0.01973937,0.02697168,-0.05593874,0.01685454,-0.01554872,-0.02938041,-0.02211537,-0.02935356,0.00488229,-0.02327712,0.04156968,0.04561195,-0.02051905,-0.06642708,-0.02175952,0.01920754,0.03972987,0.06489487,0.05924614,0.05545402,0.03707929,-0.09671082,-0.00962401,-0.03382652,-0.0184238,0.00702408,0.00024119,-0.04596787,-0.0384244,-0.02873571,-0.10106526,-0.01925209,-0.0949111,-0.10172985,0.08191014,0.00478349,0.01325833,0.00971788,-0.02127298,0.01916826,0.07362598,-0.00074916,-0.06777015,-0.01601699,-0.01227932,0.03891861,0.08456428,-0.06654154,0.00272077,-0.03427104,-0.03748231,-0.04505397,0.15064603,0.02640843,-0.13648687,0.01853808,0.0397386,-0.00026517,-0.07125975,-0.00323845,0.00348524,-0.02874112,-0.01922414,0.0788912,-0.02052871,-0.04224866,-0.03019,-0.00002454,0.05469704,-0.00008112,-0.01515355,-0.08755255,-0.00287257,-0.02429107,-0.05136356,-0.03169714,0.03417799,-0.01634446,0.04481445,-0.0703856,0.00909209,-0.03243922,0.01905887,-0.00322909,-0.00229555,-0.01266943,-0.03247905,-0.0054963,-0.02070617,0.06232159,0.0352191,0.00685905,0.0337917,-0.03211608,0.03588104,-0.04098596,-0.03040578,-0.01049758,0.01430362,-0.03996548,0.02620043,-0.00862069,0.0477264,-0.05546058,0.03577728,0.04978728,0.03253944,-0.00665717,0.07838067,0.00300911,0.0102853,-0.04355779,-0.19486971,-0.04344732,0.04490955,-0.03919512,0.00867446,-0.01429515,0.04885229,0.02443266,0.02284185,0.05450789,0.09725293,0.03982396,-0.02433432,0.00979852,-0.02023321,0.04584227,0.04283737,-0.00971203,0.00483817,0.00748351,-0.02725855,0.0217469,-0.01164885,-0.08373936,0.03071452,-0.0524679,0.09879716,-0.02441331,-0.01931252,0.00926119,0.07396284,-0.00380221,-0.04390268,-0.1457081,0.04132161,0.01124506,0.0046924,-0.00218708,-0.08397629,-0.02820733,0.00905291,0.0044487,0.05058448,-0.09691574,0.02666511,-0.07370535,-0.0563775,0.0005535,0.01204571,0.02081449,-0.0147974,0.03105633,0.0300555,0.10898649,-0.01340679,-0.00742875,0.0295451,-0.02007874,-0.00824944,0.03153439,-0.01119211,0.01302085,-0.02548959,0.00375082,0.03950112,-0.0241884,-0.02749472,-0.01420105,0.02684161,-0.04256258,-0.01047723,0.10570687,0.04995393,-0.00599644,0.0357996,0.05766834,-0.03471668,-0.09772293,0.02348653,0.00400019,0.02612156,-0.00975525,0.03598057,0.05791863,0.00987787,0.05360983,0.01348145,-0.0229165,0.01403255,-0.00680897,-0.00317034,-0.04452086,-0.03774707,-0.03913354,0.07309268,0.03061788,-0.28636381,-0.0061308,0.01775613,-0.02741498,0.04875487,0.02977034,0.04206697,0.0047526,-0.06160379,0.02051583,0.0042,0.06413322,0.00249522,-0.03527445,-0.00048457,-0.00804089,0.08125883,0.00193792,0.06094706,0.00444498,0.00450741,0.06371878,0.20113382,-0.03465248,0.00647816,0.02709935,-0.08310375,0.04099232,0.0606569,0.03130915,-0.06139963,-0.012941,0.03955573,-0.02728791,0.01792891,0.05462822,0.01755763,-0.00371492,-0.00645183,0.02417604,-0.04555769,-0.01996503,-0.01734158,0.02003046,0.11596834,-0.02495373,-0.06779458,-0.08692922,-0.00330054,0.03682218,-0.04840785,0.01530198,-0.01885883,0.04186587,-0.02739143,0.07511097,0.04902812,-0.01814528,-0.05078708,-0.03525136,-0.03905175,-0.03615779,-0.04858607,0.0817541,0.02697264],"last_embed":{"hash":"95ix0t","tokens":159}}},"text":null,"length":0,"last_read":{"hash":"95ix0t","at":1751815146599},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{39}","lines":[331,331],"size":221,"outlinks":[{"title":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA=","target":"https://www.rootdata.com/Projects/detail/Nockchain?k=MTEwNTA%3D","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"95ix0t","at":1751815146599}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{42}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03390861,-0.02145271,-0.0357064,-0.00951255,0.00086218,0.00161805,0.03156974,-0.00647031,-0.00685956,-0.02707963,0.02923542,-0.05284981,0.02332717,0.01043337,0.04281737,-0.03860642,0.0109694,-0.03831855,0.00072653,0.02060227,0.124617,-0.04386962,-0.00040884,-0.02094094,0.03730472,0.04777017,-0.00241311,-0.02197819,-0.06719194,-0.16609578,0.01098766,-0.06770867,-0.02757926,-0.01573851,0.03341176,-0.04939778,0.0178473,0.06650561,-0.08489014,-0.00003551,0.02088817,0.02039408,0.00870874,-0.04960542,0.05083252,-0.10486431,0.02667267,-0.03102998,0.03846427,-0.06854935,-0.03696432,-0.05502982,0.0309131,0.03294327,-0.00080582,0.00863853,0.0230532,0.03764134,0.05892143,0.02992204,0.08900091,0.05774021,-0.1938763,0.04495103,0.06930895,0.0702016,-0.05708411,0.03641088,0.06303828,0.08017172,0.00276796,-0.01270954,-0.05326679,0.04377427,0.04184064,0.01797433,0.00460411,0.01228665,-0.03979227,-0.06015458,-0.06646417,0.04064693,0.03619403,0.00094803,-0.00903373,0.00802205,0.03091269,-0.03257087,-0.00491117,0.03011788,0.01830553,-0.02239995,0.03693645,-0.00238254,-0.05873398,-0.03912007,0.00748505,0.00841436,-0.08077157,0.13008636,-0.03882306,0.07120677,-0.02658956,-0.02649902,0.04871736,-0.01221494,0.0120992,-0.03792683,0.01337818,-0.01024069,0.03827209,0.03988067,0.04525268,-0.05340878,0.01066646,0.01369065,0.00334528,0.00279778,0.02831788,0.03791857,0.03498035,0.03827446,0.05670103,0.00540781,0.01465928,-0.02898994,0.01592895,0.05257749,0.06857409,0.07977144,0.02950742,0.03361841,-0.06568705,-0.03593111,-0.01828502,-0.00076397,-0.03767894,0.01221239,0.01590289,-0.03745201,-0.00695387,-0.05414356,-0.00366825,-0.09172008,-0.05522319,0.06168535,-0.02357826,0.01133009,-0.03154114,-0.0538899,0.02504887,0.04694223,-0.02077825,-0.09974355,-0.0054625,-0.01168772,0.06247499,0.09710613,-0.07513698,0.01791104,-0.01238826,-0.00230918,-0.04715353,0.19508825,0.05964101,-0.06906983,0.03775348,0.03605217,-0.00412476,-0.07862996,-0.00815357,0.00364517,-0.06953803,0.01232578,0.05284381,-0.03535271,-0.04342074,-0.03263368,0.00826844,0.02714481,0.02619477,-0.0527804,-0.07692181,-0.00498401,-0.00041255,0.00771791,-0.0032462,-0.00406228,0.02238067,0.03727477,-0.1438725,0.03649884,0.02671672,0.07771159,-0.05892314,-0.00283691,-0.06333744,-0.04808214,-0.0268919,-0.04408721,0.0964777,0.04309754,-0.02474426,0.04105249,-0.0617258,0.02962673,0.01239882,-0.06954918,0.01210447,0.04131203,-0.035999,0.05573539,0.00325712,0.0430756,-0.01178682,0.03945052,0.0309527,0.02949776,-0.01168925,-0.00329181,0.03886799,0.01638008,-0.07641245,-0.19678675,-0.01888382,0.04001979,-0.00287588,0.00992094,-0.01601077,0.02574771,0.02717975,0.00591534,0.08304518,0.09877165,0.04462969,-0.07540348,0.02587918,-0.00496167,0.06822698,0.01773728,-0.01887268,-0.00615959,0.0402615,-0.04190768,0.00900379,0.01684852,-0.06297763,0.0816303,-0.03567379,0.09578155,-0.02881937,0.02724271,-0.04359364,0.06045362,0.00813641,-0.03971947,-0.15387744,0.05466017,-0.00046228,-0.02419278,-0.00642097,-0.03162266,-0.04846577,-0.00401607,0.00007799,0.00825579,-0.08040483,0.00566563,-0.08289486,-0.02339492,-0.0246195,-0.00109435,0.00848076,0.01566928,0.04511843,0.01773589,0.09721888,-0.02420045,-0.05687906,0.02967925,-0.06169893,-0.01263411,0.05333003,-0.01221189,0.02251069,-0.04777094,-0.04202138,0.01678625,-0.00972407,-0.00010979,0.01993725,0.01401488,-0.03963962,-0.04653278,0.14787766,0.03513938,-0.07041823,0.01376557,0.04709478,-0.06665941,-0.07669317,-0.00900454,0.01231685,0.03830781,0.02569597,0.03671305,0.02153674,0.00278704,0.05384692,-0.0412801,0.01216043,0.01843475,-0.00114647,-0.03137935,-0.05715916,-0.0739617,-0.07677058,0.04311397,0.02009231,-0.28395024,-0.02873751,0.01689413,0.01676817,0.02105802,0.0161143,0.07706089,-0.03096079,-0.08921662,0.00995631,-0.01448426,0.05775743,0.02592158,-0.01983093,0.00604044,-0.03953002,0.03232137,-0.02121945,0.00063961,0.03376759,-0.01758141,0.06073833,0.18392782,0.01025427,0.05407798,0.01284019,-0.01971264,0.05308466,0.08126025,0.00029645,-0.0053207,0.00304666,-0.01983459,-0.00451892,0.04080377,0.04271324,0.01592693,0.00728383,-0.01072334,0.0161199,-0.00851976,-0.02534178,-0.01369,0.04343206,0.09632972,-0.01892619,-0.05214363,-0.06841064,0.02168704,0.05599602,-0.05131078,0.02656175,0.05336969,0.06794144,-0.04115465,0.06987885,0.00353129,-0.04923714,-0.07603189,-0.00171304,0.01685087,-0.01837304,-0.04005986,0.02772656,0.01557236],"last_embed":{"hash":"1ut5hz1","tokens":149}}},"text":null,"length":0,"last_read":{"hash":"1ut5hz1","at":1751815146608},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{42}","lines":[334,334],"size":200,"outlinks":[{"title":"https://www.lcx.com/how-to-conduct-blockchain-security-audit/","target":"https://www.lcx.com/how-to-conduct-blockchain-security-audit/","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1ut5hz1","at":1751815146608}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{44}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0486422,-0.03277813,-0.04791048,-0.00776231,0.02765364,0.01801583,0.01152674,0.02261876,-0.00110829,-0.02543103,0.05309833,-0.05754398,0.03776906,0.01443186,0.02266384,-0.02527098,0.00470657,-0.03097854,-0.01461473,0.04318864,0.1178248,-0.05899987,0.03312312,-0.01683092,0.072877,0.05039421,0.01824895,-0.01436603,-0.06611836,-0.17262256,-0.01584041,-0.07834404,-0.01841455,-0.01198723,0.01613951,-0.04836373,-0.00228912,0.05108073,-0.02257421,0.00808042,-0.00046458,0.01317697,-0.00278544,-0.03140457,0.03340054,-0.10001421,0.01474537,-0.04900346,-0.0133594,-0.08475733,-0.04998946,-0.05314021,-0.01250238,0.05003352,0.01112587,-0.01616204,0.01493758,0.03948719,0.04023904,0.04011744,0.09022003,0.04702184,-0.20751955,0.02956805,0.1027899,0.04277427,-0.04566425,0.00391823,0.06970768,0.05650018,0.01592145,0.00504076,-0.02382562,0.05374533,0.01761267,0.00985144,0.01220722,0.00233702,-0.01405388,-0.04168514,-0.06921196,0.06850147,0.0354748,-0.0383672,-0.01516034,-0.00166051,0.01828562,-0.05463072,-0.01204849,0.0275403,0.02481378,-0.01945476,0.05976967,0.00497666,-0.03035609,-0.03813238,0.01657975,-0.01263884,-0.07156339,0.13121052,-0.03671176,0.05288819,-0.01487505,-0.02661888,0.04147783,0.00468769,-0.00221109,-0.0627441,0.00621425,0.02252216,0.03691525,0.01547235,0.05969265,-0.05386603,0.04282471,0.02328291,0.01703304,0.00642067,0.03212638,0.02791889,0.01996749,0.03092293,0.08700539,-0.02363534,-0.02069606,0.00669944,0.0063799,0.05906758,0.06184732,0.06367777,0.06629702,0.04381463,-0.06319098,-0.00941516,-0.02577079,-0.01490815,-0.02338777,-0.00369443,0.01691164,-0.04202724,-0.02530259,-0.04974895,-0.01859293,-0.11956475,-0.05846648,0.08522461,-0.00885298,0.01266717,-0.0301525,-0.06048564,0.03130459,0.08467991,-0.0123253,-0.09045967,-0.00393608,0.00859197,0.03301559,0.12043839,-0.068344,-0.01767557,-0.01623298,-0.04024832,-0.06437545,0.15202874,0.0385992,-0.0823864,0.04126395,0.03650304,-0.00704449,-0.08203267,0.002985,-0.00398787,-0.06614465,-0.00124092,0.0531837,-0.02281825,-0.02681827,-0.04744899,0.00960702,0.02010061,0.00616244,-0.06474226,-0.09947734,0.00895275,-0.00721033,-0.01359991,0.00060438,0.00595011,0.02830585,0.05059214,-0.10234027,0.02137274,0.00704115,0.05087854,-0.05162952,-0.00391161,-0.05132431,-0.03742898,-0.02790034,-0.06441915,0.10052937,0.02637326,-0.00832033,-0.00083641,-0.04007683,0.03061401,-0.01153392,-0.0653916,0.0498101,0.0135464,-0.0334708,0.07186179,-0.01600306,0.06675185,-0.01568592,0.01828793,0.03996429,0.02675272,-0.02094888,0.0062676,0.02651436,0.03620956,-0.10282699,-0.1849898,-0.04488492,0.00011264,-0.0057346,-0.00918824,0.00763689,0.01243121,0.04160321,0.02345059,0.05595445,0.12891923,0.07581947,-0.04452503,0.01950339,-0.00510009,0.08338695,0.0254348,-0.03167506,-0.03180807,0.06212604,-0.02176797,0.03771921,0.0159355,-0.05231011,0.08045021,-0.03893126,0.10524179,-0.01165874,0.03174273,-0.01130066,0.03416755,0.00831404,-0.00241989,-0.15044171,0.0162106,0.03334342,-0.02013111,0.00009914,-0.04113679,-0.05832046,-0.00282361,0.02309696,0.02361847,-0.08611444,0.00466414,-0.06552314,-0.03200514,-0.00293787,-0.01303382,-0.00754439,0.0169315,0.05431251,0.02222762,0.11126779,-0.02676065,-0.03253881,0.01512727,-0.06830363,-0.02149061,0.08229889,-0.0084064,-0.00057893,-0.0649495,-0.03267307,0.00947251,-0.01163391,-0.00411202,0.05225202,0.024524,-0.0366249,-0.04679006,0.13318989,0.03861481,-0.05691694,0.00829812,0.03673159,-0.02952011,-0.09015149,-0.01265542,-0.0061738,0.02283836,-0.01141811,0.02597987,0.04283918,0.00970947,0.05458696,-0.00521022,-0.00092494,0.03401143,0.00608898,-0.0152852,-0.06980689,-0.07696892,-0.081581,0.0612774,0.02435219,-0.26891196,-0.02153845,0.00969486,0.00479393,0.03102981,0.00699944,0.05459336,-0.01435636,-0.06007084,0.00919726,-0.00554593,0.06612471,0.01363833,-0.03938304,-0.01337781,-0.01941889,0.05254203,-0.01708388,0.02561063,0.03944017,-0.0046175,0.05643361,0.20569672,0.01604202,0.0153422,0.02720508,-0.03407774,0.03788343,0.04433942,0.0170336,-0.01055805,-0.01823947,-0.00837534,0.00340106,0.02799669,0.06634133,0.00821324,0.00067025,-0.0108603,0.02276221,-0.03700777,-0.01846749,0.0072164,0.04900585,0.11363217,-0.04246264,-0.06490907,-0.07587291,0.01903115,0.04639822,-0.03768488,0.0176238,0.03398086,0.03593799,-0.0175158,0.05784107,0.03213149,-0.05579065,-0.07843477,-0.02611897,-0.00870376,0.00990433,-0.06723899,0.02707905,0.02389905],"last_embed":{"hash":"pe27k5","tokens":153}}},"text":null,"length":0,"last_read":{"hash":"pe27k5","at":1751815146617},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{44}","lines":[336,336],"size":239,"outlinks":[{"title":"https://immunebytes.com/blog/blockchain-security-audit-the-ultimate-guide/","target":"https://immunebytes.com/blog/blockchain-security-audit-the-ultimate-guide/","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"pe27k5","at":1751815146617}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{45}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04214426,-0.04713347,-0.0587928,-0.04192256,0.0303822,0.00234897,0.00874894,0.01315967,0.00255269,-0.02936753,0.05453457,-0.06689533,0.02545164,0.02146962,0.03296176,-0.03743269,0.01143248,-0.05018748,0.0131985,0.01530316,0.1152464,-0.06391485,0.03128368,-0.01521161,0.04810847,0.05758082,-0.01542091,-0.02371173,-0.06678752,-0.17179954,0.02302889,-0.08331867,-0.01072835,0.00893036,0.03486251,-0.04226206,0.0005252,0.05710734,-0.03462162,0.00843812,0.02595882,-0.00408396,-0.01465699,-0.0452817,0.02496076,-0.09380423,0.03035266,-0.04536345,-0.00677054,-0.06070904,-0.04620684,-0.05970933,0.0149435,0.02982009,0.03157758,-0.00288392,0.01052725,0.03984807,0.03770699,0.01380812,0.07511574,0.04194221,-0.2010818,0.0268198,0.09101607,0.06182747,-0.05602376,-0.00012299,0.07761248,0.04337376,0.02493706,0.01878436,-0.02412022,0.04424816,0.03433148,0.02916992,0.00569466,0.01399366,-0.04547849,-0.04960546,-0.06745286,0.0524349,0.03807398,0.00853242,-0.03706409,0.0079658,0.00620506,-0.02630602,0.00051625,0.01881464,0.00118447,-0.01147871,0.05460862,0.00258333,-0.01330784,-0.02553368,0.00099953,0.00683911,-0.07817128,0.12633181,-0.05302779,0.05740319,-0.01143227,-0.03295463,0.04932622,-0.01531605,-0.01812986,-0.0686368,0.04539119,0.03092179,0.0199626,0.02442244,0.0602125,-0.05967597,0.02853805,0.0200908,0.00627498,0.01621057,0.02139876,0.02289278,0.02608751,0.06618477,0.08163455,-0.01673488,-0.02669712,-0.02302587,0.02057577,0.04863316,0.0477857,0.07336355,0.02041978,0.04115384,-0.07174937,-0.01915121,-0.03576068,-0.03097678,-0.01234944,0.01011405,-0.02222417,-0.03675598,-0.02603663,-0.04967567,-0.00432643,-0.10027726,-0.0379646,0.08034302,0.00144338,0.0450622,-0.01540327,-0.05959606,0.00428389,0.08677121,-0.02898593,-0.09474256,0.00240474,0.01272089,0.02664099,0.10570838,-0.04910973,0.00010832,-0.00504589,-0.02078332,-0.05138742,0.15782577,0.03369125,-0.08226472,0.02744559,0.04575504,-0.02241367,-0.07199444,-0.01026217,0.01749465,-0.05531989,-0.00342017,0.04800757,-0.04561175,-0.02927984,-0.04228218,0.00983074,0.02781782,0.00474053,-0.03160201,-0.08134277,0.01147068,0.00959216,-0.0237699,-0.00152972,0.01492782,0.01132792,0.06326296,-0.13527465,0.03139511,0.00521141,0.05858467,-0.04391016,-0.01513777,-0.05500524,-0.02956177,-0.03643451,-0.0591198,0.1225614,0.05063106,0.0021259,0.01050325,-0.06155941,0.0181086,-0.02134585,-0.0385089,0.00682776,0.036922,-0.03092445,0.05365498,-0.0339198,0.03581421,0.00513123,0.00883296,0.03752857,0.03713293,0.00215366,0.02105131,0.02555462,0.01175974,-0.0892584,-0.17583989,-0.04868561,0.03159673,-0.01557227,0.0281398,-0.00513336,0.03360081,0.02593952,0.01946993,0.05364364,0.10125657,0.08534724,-0.08444319,0.02554341,0.00918536,0.07778411,0.02431346,-0.03193207,-0.00828454,0.06807292,-0.02414365,0.03682053,0.00795445,-0.04175607,0.06362312,-0.04994851,0.10994728,-0.03776122,0.03270819,-0.01410454,0.02417976,-0.00281828,-0.02034483,-0.14427845,0.06045994,0.04674819,0.00587113,0.01638019,-0.05900049,-0.04447005,-0.03312048,-0.00752536,0.02880223,-0.09998556,-0.00808196,-0.07454018,-0.0443018,-0.01634528,-0.00624341,-0.00740354,0.01063943,0.05370701,0.04253941,0.10987571,-0.01349253,-0.0332456,0.02538608,-0.06042395,-0.00183902,0.06853157,-0.01754883,0.01258879,-0.06751146,-0.04119316,0.03106958,-0.02038588,-0.01848598,0.02061385,0.01631033,-0.04478281,-0.03216048,0.15363468,0.02922238,-0.04761766,0.02012542,0.05742021,-0.03823138,-0.10035637,0.00240857,0.00935746,0.0337489,-0.00447973,0.01672631,0.02745332,-0.03078143,0.05951785,-0.01158774,0.01121641,0.02695691,0.008595,-0.03632765,-0.07048608,-0.07101551,-0.07838773,0.03321381,0.03804175,-0.27461785,-0.02191144,0.01312355,0.01406957,0.04828548,0.02487295,0.04401772,-0.01040866,-0.07124247,0.00403927,-0.00072404,0.0570619,0.03638232,-0.03815269,-0.01171913,-0.02909357,0.05772544,-0.02611812,0.02236946,0.03482283,-0.00262929,0.08389712,0.21019337,-0.00323387,0.04271327,0.02121093,-0.03303172,0.03864209,0.06838109,0.01241203,-0.02803695,-0.01123249,-0.00415581,-0.01479308,0.00326595,0.06065394,0.01743775,-0.00231904,0.00863016,0.00362324,-0.03046576,-0.01856826,-0.01512565,0.0277343,0.10515664,-0.02460791,-0.062012,-0.07976153,0.03449528,0.05543719,-0.04967732,-0.01424883,0.02839749,0.06214293,-0.00652136,0.0538398,0.01887014,-0.02422098,-0.09300294,0.00666808,0.00194101,-0.01024175,-0.03596393,0.02947324,0.01675867],"last_embed":{"hash":"12350os","tokens":154}}},"text":null,"length":0,"last_read":{"hash":"12350os","at":1751815146626},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{45}","lines":[337,337],"size":245,"outlinks":[{"title":"https://hacken.io/services/blockchain-security/blockchain-protocol-security/","target":"https://hacken.io/services/blockchain-security/blockchain-protocol-security/","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"12350os","at":1751815146626}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{47}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05061502,-0.02557653,-0.07060116,-0.00874582,0.03846357,0.02543414,-0.00976255,-0.00571187,-0.02286826,-0.01344292,0.07377251,-0.06163244,0.03122756,0.01565986,0.0361615,-0.00429632,0.01411617,-0.06389669,-0.03135946,0.00976041,0.12802751,-0.0810409,0.03390023,-0.02952198,0.06296305,0.02681408,-0.01498259,0.01492805,-0.05371658,-0.17532532,-0.03122963,-0.04908065,0.0066429,-0.00222341,0.02007768,-0.03737551,0.00231955,0.0396123,-0.06273169,0.02109413,-0.01617158,-0.00208538,-0.00186467,0.02684753,0.04896846,-0.06260771,0.01805559,-0.0473917,-0.00698739,-0.05441185,0.00309154,-0.03912558,-0.00572449,0.04081222,0.00368045,0.01915425,0.00312639,0.03228542,0.07102868,0.01524038,0.09953489,0.06381853,-0.16927408,0.03179168,0.11195426,0.0540618,-0.00490292,-0.02723187,0.06597775,0.03511016,0.05964346,0.02925252,-0.02853022,0.05013084,0.04511067,-0.00432419,0.00478974,0.01133545,-0.05213289,-0.06097415,-0.02297694,0.08175156,-0.01169696,-0.02136321,-0.02666831,-0.02770187,0.02041182,-0.02134454,-0.03766888,0.01638975,-0.01153269,0.04298782,0.06747849,0.00666039,-0.01284664,-0.02433004,0.03188857,0.02097885,-0.07489842,0.1110115,-0.02017331,0.07176605,0.00902319,-0.06275957,0.04318805,0.01947883,0.00320944,-0.0280481,0.02956722,0.0402278,0.00716579,0.02719841,0.0512498,-0.05271777,0.02734108,-0.02170663,0.01445738,0.00230564,-0.00635786,0.01013816,-0.01496585,0.02554046,0.07870589,-0.00817801,-0.00185334,-0.0164961,0.00845149,0.05328767,0.05935769,0.04426674,0.02708365,0.04087222,-0.07003176,-0.01047246,-0.03307729,-0.01330412,-0.04088693,0.01815068,-0.0021469,-0.04208883,-0.02869914,-0.0789989,0.00275544,-0.09893222,-0.03524774,0.05724842,-0.00366953,0.01101713,-0.00587381,-0.03777079,0.01738853,0.04445121,-0.03767943,-0.05468156,-0.0146569,-0.03853992,0.0543911,0.09039277,-0.08103675,-0.01912752,-0.01100245,-0.02202806,-0.04237224,0.14448439,0.05200001,-0.10543017,0.01605068,0.01967778,0.00030939,-0.0622822,0.01947283,-0.01213314,-0.03484768,-0.02174685,0.07026469,-0.01876654,-0.04634231,-0.02574613,-0.0106783,0.06157735,-0.01340946,-0.04312223,-0.07792051,-0.0084295,-0.02532906,-0.0148909,-0.02439806,0.0209231,-0.00061619,0.06604135,-0.10511864,-0.00774841,-0.03329312,0.02968585,-0.04211992,-0.01924819,-0.01551036,-0.05812155,0.01208196,-0.02343207,0.09358632,0.045917,0.01030094,-0.0164459,-0.10193635,0.01302186,0.00272951,-0.04518079,0.01926401,0.03000172,-0.05315176,0.04533382,-0.05430944,0.02480822,-0.00683641,0.01632165,0.04571854,0.02236029,0.0012758,0.02085844,-0.00403131,-0.01184853,-0.09784897,-0.19486575,-0.05226255,0.02660947,-0.05910224,0.06074017,-0.0220585,0.06498548,0.04173939,0.06177225,0.06402931,0.10386132,0.03858233,-0.06815066,0.01823422,-0.0005527,0.06838357,0.05058352,-0.03519887,0.01872099,0.03912885,-0.04281365,-0.00005964,0.00867438,-0.07177471,0.06280185,-0.06712139,0.11498933,0.0156886,0.02742865,-0.02415316,0.04380284,0.00410912,-0.0197327,-0.15521519,0.03178644,0.02628364,-0.0143576,-0.02307467,-0.06247143,-0.00984215,-0.00878792,0.00984565,0.03023694,-0.08523881,-0.00560866,-0.05640116,-0.03169146,-0.00798246,0.01812848,0.01549353,-0.00412886,0.03413017,0.01589278,0.1014013,0.01239337,-0.0330329,0.02815557,-0.06613272,0.01163739,0.09595206,-0.00094793,0.01247481,-0.04583193,0.00582751,0.05650784,-0.01814581,-0.01575492,0.00783099,0.05667993,-0.01694368,-0.02838953,0.14265512,0.02100617,-0.05103447,0.02496857,0.01077977,-0.02438764,-0.07697908,0.00256248,0.02746477,0.04196258,-0.03586172,0.0456292,0.05749242,-0.01695603,0.0686452,-0.03359617,-0.01538323,0.00020725,-0.022102,-0.03374942,-0.01960344,-0.06178204,-0.09863976,0.03372741,0.0443929,-0.28545815,-0.0232508,0.02858477,0.01548299,0.05631676,0.05413479,0.1128597,0.02929261,-0.09739057,-0.00048843,0.00609784,0.10507737,0.0259414,-0.04341606,-0.04651656,-0.00503036,0.02615965,-0.020847,0.012769,0.03031258,-0.01102633,0.07387502,0.19943687,-0.01720688,0.01555294,0.03212055,-0.02631169,0.0509943,0.02248623,-0.0050044,-0.03863628,0.01565708,-0.02793196,-0.06455382,0.00486189,0.02469004,0.0028793,0.02284316,0.0122355,0.0322267,-0.01098318,-0.00562483,-0.03495505,0.04152397,0.13444611,-0.00964885,-0.05141852,-0.09045948,0.0228194,0.02707674,-0.06192421,-0.02221979,-0.00991939,0.05915234,-0.00740126,0.08269533,0.00957359,-0.03452905,-0.05688206,-0.00727763,-0.02094106,-0.00305126,-0.04735199,0.02395149,0.02420111],"last_embed":{"hash":"1fkjo9o","tokens":209}}},"text":null,"length":0,"last_read":{"hash":"1fkjo9o","at":1751815146635},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{47}","lines":[339,339],"size":340,"outlinks":[{"title":"https://www.reddit.com/r/CryptoCurrency/comments/1lhoxfc/outside\\_of\\_the\\_crypto\\_community\\_crypto\\_is\\_still/","target":"https://www.reddit.com/r/CryptoCurrency/comments/1lhoxfc/outside_of_the_crypto_community_crypto_is_still/","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1fkjo9o","at":1751815146635}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{48}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07192773,-0.03577566,-0.06284951,0.00982013,0.0000717,0.02829388,-0.00170213,-0.01678275,-0.02164516,-0.01236492,0.05838165,-0.082807,0.02823595,0.02671095,0.0726986,0.00112833,0.0332327,-0.07747693,-0.0305149,0.05672272,0.08601777,-0.08178672,0.04741645,-0.02458947,0.07119789,0.01515748,0.00799302,-0.01057759,-0.05579162,-0.17216496,-0.05664145,-0.02168123,0.02229797,-0.01938415,0.05797246,-0.02969291,-0.00358487,0.03511249,-0.04765494,0.02040732,-0.00679817,-0.01553901,-0.01267424,0.03366768,0.07805876,-0.09098236,0.02932946,0.01198037,0.00979351,-0.02455688,-0.00083044,-0.01844268,0.00344779,0.05016363,0.00341743,0.04293533,-0.00856448,0.04555288,0.08724726,0.00419341,0.09243647,0.0514614,-0.16046238,0.03486758,0.05613752,0.02540181,-0.01525292,-0.04737545,0.05011869,0.05039295,0.03886392,0.02251057,-0.03822502,0.05278283,0.03376693,-0.00304744,0.050028,0.00356367,-0.05674965,-0.05374029,-0.0434972,0.08883607,-0.01158736,-0.03483777,-0.05382913,0.00796634,0.03757959,-0.04060365,-0.06000118,0.01110933,-0.01362961,0.02899941,0.05652831,0.01795533,-0.0286082,0.00532263,0.00074777,0.04371748,-0.07185443,0.12464807,-0.04123304,0.05917348,-0.0015536,-0.08821681,0.02681499,0.01744261,-0.00957032,-0.02416336,0.00323341,0.02739014,-0.03619695,0.05086434,0.08206179,-0.04885528,0.04099157,0.00728037,0.03576426,-0.00136308,-0.00481298,0.01128294,-0.01337212,0.04897503,0.07513557,-0.02813218,-0.00285925,-0.03554346,0.02367133,0.02654177,0.0421538,0.05600863,0.01028551,0.04504357,-0.07165671,-0.00239406,-0.02470328,-0.01631525,-0.05644215,0.00792427,-0.00695477,-0.01544886,-0.02209479,-0.06309911,-0.00990077,-0.11467252,-0.05638915,0.04191184,-0.02133482,0.02300713,0.01595961,-0.00604776,0.01015016,0.05056419,-0.03314002,-0.04806603,0.00463566,-0.03285989,0.0523829,0.08482698,-0.0666557,-0.04942717,0.02365024,-0.03975072,-0.05331235,0.14017035,0.04154131,-0.11434765,0.02062854,0.00865597,-0.00947711,-0.05167514,0.01539839,-0.02365282,-0.02932443,-0.01674516,0.08109809,-0.03608327,0.0119757,-0.0370581,-0.00887401,0.06391764,0.01802902,-0.05241493,-0.07403713,0.0179294,-0.02994961,-0.02571133,-0.02605027,0.02215193,0.00166927,0.01123596,-0.0578128,-0.04558897,-0.01819176,0.04550635,-0.03647539,-0.02478483,-0.02549925,-0.05866285,0.00238715,-0.03919856,0.06686763,0.03194009,0.01924353,-0.03371432,-0.05938426,0.0532239,0.03498678,-0.02363562,0.0294013,0.03213131,-0.03899749,0.04467547,-0.02289266,0.00713962,-0.0016326,-0.0105421,0.03921771,0.03732664,-0.0165923,0.03023818,-0.04612382,0.01413385,-0.10620673,-0.19968745,-0.02112947,0.0195041,-0.02027013,0.09543564,-0.0122354,0.03965252,0.02372676,0.07159099,0.06108853,0.1147074,0.02253093,-0.044433,0.02018604,0.042651,0.06086118,0.03419088,-0.00846993,0.03257512,0.01920803,-0.05508888,0.00701831,-0.02264519,-0.0981441,0.05835637,-0.04558064,0.11621115,0.01945405,0.00376876,-0.01362043,0.03462405,0.00665725,0.00805697,-0.1922745,0.0383802,0.02611728,-0.0148965,-0.03215042,-0.02489923,-0.01926878,0.01347212,0.00115553,0.00605146,-0.10646716,0.00314247,-0.06621066,0.00951852,0.0066733,-0.0126758,0.00385018,0.02025848,0.02378069,0.03247799,0.08684102,-0.01370574,-0.04103081,-0.02009004,-0.044685,0.01265999,0.09996561,-0.02176898,0.01704468,-0.05514432,-0.00864852,0.05389615,-0.00682923,-0.00982115,0.02909577,0.05066467,-0.01013249,-0.02734817,0.14145353,0.00350527,-0.07334238,0.03103204,0.02486106,-0.02971219,-0.06290992,0.03342715,-0.0106821,0.02803563,-0.03231643,0.02850291,0.06371874,0.01275053,0.07931886,-0.00404424,-0.00502686,0.00697476,-0.01688496,-0.02204021,-0.01473561,-0.05805397,-0.10190915,0.0329049,0.01364312,-0.27000013,-0.01894269,0.01086198,0.01081795,0.02355647,0.04909223,0.09510318,0.01949676,-0.14178681,-0.00377378,0.00835558,0.10966884,0.03861728,-0.02128745,-0.01439803,0.0032033,0.02271373,-0.04876961,-0.0004933,0.02110936,-0.02260096,0.0666126,0.20949577,-0.00524439,0.02636442,0.02315866,-0.00521886,0.0362147,0.04406677,-0.02941375,-0.06347136,-0.00472309,-0.02677872,-0.03514694,0.0026706,0.01080114,-0.01097998,0.0018079,-0.03672165,0.02305653,-0.01076693,0.00597219,-0.00760671,0.04862023,0.1348388,-0.02669179,-0.04079866,-0.0817911,-0.0088581,0.01004913,-0.03502523,0.00836398,0.01363335,0.02151617,-0.00330204,0.08639779,0.02284054,-0.04880608,-0.06681931,-0.0195341,-0.05405078,0.02978393,0.00539477,0.03054729,0.01637547],"last_embed":{"hash":"wvf4lg","tokens":191}}},"text":null,"length":0,"last_read":{"hash":"wvf4lg","at":1751815146647},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{48}","lines":[340,340],"size":324,"outlinks":[{"title":"https://www.reddit.com/r/CryptoMarkets/comments/1l5jqcu/which\\_crypto\\_community\\_still\\_feels\\_genuinely/","target":"https://www.reddit.com/r/CryptoMarkets/comments/1l5jqcu/which_crypto_community_still_feels_genuinely/","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"wvf4lg","at":1751815146647}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{49}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0229031,-0.01600744,-0.07678737,0.01623236,0.0224094,0.00525889,0.00731533,0.01484632,0.00720423,-0.03081202,0.05899925,-0.05091135,0.05518194,0.01673997,0.05774779,0.03612657,0.02330507,-0.06945987,-0.03594855,0.02595548,0.09350908,-0.06953373,0.05360349,-0.05550803,0.08092418,-0.00487048,-0.02070598,0.0046977,-0.07432593,-0.16956191,-0.00702326,-0.01882881,0.07818107,0.0009144,0.05205512,-0.02181249,-0.00039946,0.04565873,-0.06173201,0.01605755,-0.02354079,0.02494451,-0.00613216,0.01914619,0.08429359,-0.09483637,0.02795524,-0.02372572,-0.02410993,-0.00306458,0.00630024,-0.05345834,-0.01784446,0.01564114,0.01467476,0.01022329,0.00882697,0.04959393,0.05570008,0.00372646,0.10603932,0.05199886,-0.19295081,0.03141757,0.10452386,0.00492825,-0.00057417,0.02231182,0.00496272,0.0597827,0.05318918,0.03230008,0.01219505,0.03250501,0.01528911,0.00906195,0.02612673,0.00765033,-0.03542534,-0.05635254,-0.01336829,0.06904409,-0.04016648,-0.02461946,-0.02179454,-0.06307014,0.03662586,-0.00871742,-0.04489469,0.00677536,0.02562061,-0.01300689,0.05084146,0.00928987,-0.03312294,0.00183139,0.02418714,0.01748382,-0.08207781,0.1179827,-0.01654571,0.05626865,-0.00853004,-0.09365321,0.05761029,0.00342149,-0.00527644,-0.03578464,0.01972786,0.00448816,-0.01174656,0.01025751,0.07491687,-0.04748017,0.0200304,-0.04250373,-0.01675296,0.00086528,0.02538548,0.00282619,-0.02468927,0.0311669,0.05203837,-0.0089029,0.01471968,-0.03833217,0.00391166,0.05863214,0.07114618,0.01856437,-0.00075812,0.06162958,-0.0848612,0.00934369,-0.0204913,0.00910672,-0.04043698,-0.01693914,-0.00169146,-0.02282302,-0.03227738,-0.0677653,-0.02758636,-0.11046773,-0.04222315,0.05758901,0.03527705,0.00608921,0.01440176,-0.01228597,-0.03715611,0.03609348,-0.06740322,-0.05334612,-0.02121639,-0.00233943,0.04580658,0.0840062,-0.09375035,-0.01346418,-0.0224868,-0.01633927,-0.04907487,0.10854007,0.03626223,-0.10409453,0.02231804,-0.00774407,0.00474065,-0.03861964,0.01394303,0.01637189,-0.05195279,-0.01679355,0.04847194,-0.01995032,-0.01998717,-0.04826981,0.00790779,0.04939527,0.03211614,0.00621458,-0.07680547,0.00439752,-0.04130046,-0.01442527,-0.02842308,0.012786,0.00230291,0.02896597,-0.08404106,-0.04338209,-0.01306601,0.05487665,-0.05988854,-0.0047233,-0.01248304,-0.03875825,0.01464198,-0.0273484,0.05089271,0.03453948,-0.01903553,-0.01021659,-0.0689435,0.04710702,0.03143607,-0.05714493,0.00968363,0.02538524,-0.01938607,0.0460693,-0.04775105,0.03181182,-0.02047844,-0.00567245,0.03271151,0.04521871,0.03334151,0.0214923,0.01163993,0.03752508,-0.12277219,-0.21405862,-0.06887619,0.03048857,-0.01895042,0.07863398,-0.02070323,0.08272088,0.02013911,0.05801726,0.07068692,0.11212604,0.03072613,-0.04293221,-0.00948649,0.03519676,0.04423225,-0.02803032,-0.01954097,0.00681909,0.04095831,-0.02957587,0.02831929,0.02622026,-0.10332754,0.07624198,-0.04195532,0.11106776,0.03157112,0.00552666,-0.00460078,0.05405099,-0.01307363,-0.03180935,-0.15775885,0.05206084,0.02367653,0.02505022,-0.02012786,-0.08503884,-0.01071733,-0.00277625,0.03080211,0.03008885,-0.07876947,-0.03149102,-0.05679427,0.01274071,0.01265069,-0.0184683,0.05085262,-0.00036399,0.04277084,0.04117165,0.1216867,0.03187337,-0.03203415,-0.00166818,-0.0458344,0.01837571,0.08900876,0.02682127,0.01140793,-0.01135202,-0.01678829,0.03220803,-0.00498179,0.00090144,0.00551618,0.00456652,0.01223541,-0.06658094,0.1478112,0.00657292,-0.03901571,0.01813556,0.01922745,-0.0479027,-0.06002958,0.01509206,0.00768296,0.03236945,-0.02345537,0.02292034,0.07904854,-0.02898721,0.04812527,-0.01213424,-0.04101297,0.007033,-0.01134306,-0.06088185,-0.00855459,-0.08331712,-0.07971444,0.05161138,0.03367051,-0.28349623,-0.04227811,0.00633132,0.02477929,0.07700707,0.05332046,0.08748262,-0.00479949,-0.10494383,0.02412887,-0.00243112,0.12073556,0.00834052,-0.05828036,-0.01858132,-0.02796949,0.02263918,-0.00805181,0.03584576,0.02202119,-0.01361606,0.05681486,0.19132726,0.01873916,0.00860758,0.02886854,-0.02021655,0.01842805,0.02540318,0.00321316,-0.03529362,-0.00680164,-0.00584434,-0.06746417,0.01437702,0.01766467,-0.0278368,-0.01712051,-0.01447527,0.01841388,0.01243004,0.02239114,-0.00647245,0.03834163,0.11738083,-0.01153539,-0.0276685,-0.10093908,0.01982399,0.01299521,-0.05899544,-0.02852648,-0.02754082,0.01529935,-0.01484769,0.09082444,-0.0087661,-0.04315449,-0.0170432,0.00101841,-0.02489023,-0.03536246,-0.02461044,0.05179956,0.01245208],"last_embed":{"hash":"1ph1gaa","tokens":196}}},"text":null,"length":0,"last_read":{"hash":"1ph1gaa","at":1751815146657},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{49}","lines":[341,341],"size":315,"outlinks":[{"title":"https://www.reddit.com/r/CryptoMarkets/comments/1jakyzw/why\\_community\\_sentiment\\_of\\_every\\_crypto\\_on/","target":"https://www.reddit.com/r/CryptoMarkets/comments/1jakyzw/why_community_sentiment_of_every_crypto_on/","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1ph1gaa","at":1751815146657}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{50}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06338497,0.01216599,-0.05356049,-0.00195629,0.04243347,0.02202695,-0.04805761,0.03972793,-0.01886756,0.01678674,0.03721924,-0.03837828,-0.00753478,0.04211831,0.03549901,-0.01744762,-0.01997485,-0.06662934,-0.03693697,-0.00394783,0.07680381,-0.05080956,0.07390074,-0.03327213,0.05744683,0.06486926,0.01354407,-0.053304,-0.05369601,-0.21395396,-0.02171033,-0.07512558,0.04607123,-0.00261113,-0.00093029,-0.00446759,0.01385542,0.02035142,-0.05635767,0.03836209,0.04427642,0.00802841,0.02986274,0.01261063,0.04532615,-0.05407833,0.03450669,-0.03978138,-0.0163029,-0.04141751,-0.05508267,-0.07810084,0.01633409,-0.00799603,0.0202963,0.02395855,0.01153575,0.05839757,0.0192502,0.08240955,0.07133008,0.02475677,-0.2395851,0.04735823,0.07882959,0.02581,-0.03875999,0.0165368,-0.00207334,0.0455864,0.01552343,0.01149516,0.04854788,0.04591874,0.01935209,0.03262968,0.03074801,0.00755626,-0.0230182,-0.0685648,-0.06966288,0.04238002,0.01419111,-0.04495864,-0.02712683,0.01266375,-0.02165225,-0.02761133,-0.04054148,0.01727177,0.0037115,0.00515176,0.09558599,0.00976857,-0.05579634,-0.02863788,0.01462911,-0.01279647,-0.07975841,0.11316422,-0.04863715,0.0883237,-0.02040486,-0.0287076,0.05487933,-0.00734714,0.01668983,-0.06561192,0.03575714,0.03658995,0.00454597,0.02759198,0.06393432,-0.08450897,0.06033023,0.06232311,0.05055489,0.04748718,-0.00513151,-0.02636488,0.02564246,0.02651716,0.05276643,-0.04225713,-0.01932614,-0.04153134,0.01893976,0.01863834,0.00691573,0.02008911,0.07380299,0.03076512,-0.08934173,-0.01817845,-0.01253515,-0.02280278,-0.0387563,-0.01967025,-0.00024082,-0.03194438,-0.02095911,-0.04998389,-0.07676857,-0.09197984,-0.09814103,0.02464309,0.05055453,0.02689509,-0.01613577,-0.01650805,0.02327687,0.03884232,-0.00443945,-0.05717684,-0.02057717,-0.0104983,0.06704114,0.10395559,-0.1001536,-0.01296872,-0.02219895,-0.02744966,-0.06886853,0.1345384,0.03763413,-0.07885066,0.0354177,0.03182469,-0.0270099,-0.038966,0.0406599,0.00591468,-0.08352653,0.08081371,0.09799524,-0.03289784,-0.01824765,0.01788196,0.02817045,0.01490015,0.00023222,-0.03579202,-0.04951682,0.00969271,-0.06236871,-0.04985922,-0.00767812,0.02938454,0.0023676,0.05616742,-0.0124248,0.03791968,0.05450769,0.0700851,-0.02507549,-0.03299864,-0.01911177,-0.04916432,-0.00718316,-0.08256587,0.05381297,-0.00560436,-0.03879777,0.00979606,-0.07486026,0.00941931,-0.04355565,0.00678944,0.05264106,0.0051947,-0.03537272,0.0177834,-0.0493104,0.02911534,-0.02513078,0.0207146,0.02332489,0.03651033,-0.03461504,0.03976521,0.04263305,0.00151435,-0.09223393,-0.2047082,-0.01811632,0.01856937,-0.02965135,0.02527743,-0.01870125,0.06663339,0.02864522,0.06405281,0.10410716,0.05637322,0.01729386,-0.03365846,0.04656339,0.00129821,0.03697213,-0.01619298,0.00236941,-0.0062684,0.03614249,-0.02188466,0.03025087,-0.00648526,-0.09179116,0.01919522,-0.04351982,0.10412032,0.05791252,0.03672211,-0.03188827,0.04061783,0.02905742,0.00121186,-0.17365192,0.03227071,0.06265477,0.02617369,-0.03332278,-0.07655143,-0.02909664,-0.02163548,0.04789726,0.036569,-0.07719681,-0.02830953,-0.06252506,-0.04448254,-0.01999271,-0.01132488,-0.02380277,0.01180789,0.03951579,-0.00419102,0.05732278,-0.01696648,-0.02660582,-0.01506783,-0.03111102,-0.01517522,0.05900882,-0.01272141,0.02132009,-0.0499742,-0.00470971,0.05083149,-0.01324421,-0.03378361,0.05104493,0.05204042,-0.07021102,-0.0054221,0.08998356,-0.0053592,0.03716265,0.02093899,0.01602607,0.01270804,-0.07911506,0.01828094,-0.03968026,-0.00975507,0.00670151,0.06313426,0.02881283,0.0110534,0.03883567,-0.00310654,-0.03086419,0.0490957,-0.01840013,-0.03082434,0.03685813,-0.05439675,-0.03537896,0.08679974,0.04700222,-0.2563383,-0.02447919,-0.02490863,0.03793765,0.06090417,0.03896106,0.0707801,0.03254836,-0.02127893,-0.00777308,0.01804974,0.07314034,-0.00809725,-0.02960418,0.00019532,0.02120346,0.05507826,-0.01595894,0.01569697,0.00287857,0.00948363,0.0456019,0.18335812,0.01283735,-0.00115029,0.02467605,-0.08632112,0.0566113,0.03783043,0.02943601,-0.00488984,0.00633425,0.03549222,-0.04141403,-0.00021183,0.02708672,-0.01507758,0.00204493,0.0175856,0.00287083,0.00161101,-0.02971574,0.00246091,0.05596844,0.10197174,-0.0611776,-0.07540605,-0.06398307,0.00298201,0.01921111,-0.02030014,-0.03781971,0.02695137,0.06861188,-0.02416629,0.07577674,0.04462576,-0.03957732,-0.07483753,-0.02925866,-0.03599066,-0.05821262,-0.02997917,0.05434271,0.02218621],"last_embed":{"hash":"1gmm3wl","tokens":233}}},"text":null,"length":0,"last_read":{"hash":"1gmm3wl","at":1751815146666},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{50}","lines":[342,342],"size":427,"outlinks":[{"title":"https://www.everand.com/podcast/593966517/Launching-the-Mars-Review-of-Books-with-Noah-Kumin-Noah-Kumin-is-the-founder-and-editor-of-the-Mars-Review-of-Books","target":"https://www.everand.com/podcast/593966517/Launching-the-Mars-Review-of-Books-with-Noah-Kumin-Noah-Kumin-is-the-founder-and-editor-of-the-Mars-Review-of-Books","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1gmm3wl","at":1751815146666}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{51}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05610633,-0.03382809,-0.02356006,-0.03630733,0.07039393,0.03144737,0.0111297,0.01851107,-0.00833386,0.02480358,0.03418821,-0.07644918,0.0307384,0.04282594,0.02785284,-0.02287146,0.02080469,-0.05818712,-0.00188362,0.02803092,0.13521315,-0.05956516,0.0098756,0.00857552,0.01905817,0.06283368,0.02665949,-0.02197761,-0.02207966,-0.2070923,-0.00364723,-0.04009147,-0.01595719,0.000581,0.01300548,-0.0530114,-0.02558669,0.04775732,-0.04870927,0.00642242,0.03410083,0.01887164,0.0024751,-0.00609788,0.04209514,-0.07531749,0.01464841,-0.01996157,0.00607116,-0.05419846,-0.00218587,-0.03624683,-0.00668576,0.0184115,0.0257663,0.01753174,0.02918281,0.04534723,0.0504253,0.03920471,0.07120974,0.046347,-0.22975363,0.06706147,0.10824708,0.05092941,0.00348742,0.01997644,0.03259628,0.04434365,0.01259075,0.04014778,-0.0031344,0.03071809,0.01620867,0.02648195,0.04939045,0.03240487,-0.01204408,-0.05824331,-0.07476413,0.08484355,-0.02103159,-0.01724961,-0.00106706,-0.06212372,-0.02452438,-0.04372361,-0.03623299,-0.00023976,-0.04541094,0.02351598,0.09951405,-0.00020235,-0.02577157,0.00181362,0.00565235,0.0111556,-0.07752436,0.11437395,-0.0422558,0.06832142,-0.00100707,-0.03273441,0.06918637,0.00948048,-0.00051992,-0.04659003,0.0223739,0.04237495,0.0320755,0.03898204,0.07661859,-0.03059993,0.04360479,0.02833674,0.02624095,0.03235746,0.00878251,0.01141775,-0.01646494,0.05745192,0.0421251,-0.02834919,-0.02248737,-0.04185619,0.03057099,0.0320243,0.01553783,0.0323029,0.03326893,0.01049077,-0.06034421,-0.0177526,0.00358027,-0.0192949,-0.02510162,-0.02314112,-0.01543477,-0.0195348,-0.03326822,-0.08238641,-0.01694458,-0.09183308,-0.06208352,0.07894545,0.02747892,0.02144873,-0.0281858,0.00762201,0.01403643,0.07759136,-0.00360323,-0.08370378,-0.00539743,0.0066873,0.05153386,0.0986653,-0.04896439,-0.02010149,0.00491261,-0.05383847,-0.04471935,0.1495841,0.0641464,-0.08233222,0.03225093,0.04662394,0.01034135,-0.03953131,-0.00726759,0.01627567,-0.02445185,-0.0001176,0.07436342,-0.0425149,-0.01457976,-0.0248045,-0.00646661,0.0408833,-0.00324704,-0.06613511,-0.08916155,-0.00768968,-0.04438482,-0.03616262,-0.03948609,0.0212622,0.00553254,0.03257297,-0.02175622,0.04541457,-0.00412079,0.08303221,-0.04938377,-0.03438631,-0.01405631,-0.01998076,0.01494118,-0.03287705,0.09123798,0.02667504,0.01576121,-0.00111615,-0.03977186,0.01003891,-0.04186641,-0.03596341,-0.01313559,0.0318448,-0.01389992,0.03967086,-0.01819925,0.02870905,-0.04307012,-0.03474234,0.03046102,0.0223104,-0.0188089,0.04122293,-0.00480972,0.00663421,-0.09531868,-0.19767402,-0.0062621,0.01252642,-0.0549127,-0.00291652,-0.01202602,0.04548652,0.04005045,0.06477649,0.08563509,0.04451256,0.03310612,-0.00504953,0.02293798,0.00735536,0.02509468,0.05709047,0.00816845,-0.01157375,0.07845179,-0.04970521,-0.01097658,-0.03869965,-0.10589022,0.04114344,-0.05772483,0.09667133,0.00766038,-0.00161425,0.00956648,0.06591584,0.0398999,-0.0240222,-0.19723916,-0.0034924,0.05599497,-0.00667254,-0.0138473,-0.05481921,-0.06105088,-0.05596868,0.03952762,0.04743475,-0.10940783,0.02458928,-0.07756341,-0.06114177,-0.01489122,-0.00512002,-0.02163855,0.0328688,0.0356234,-0.00909476,0.08998853,-0.04462452,-0.02374171,-0.00390275,-0.06467333,-0.00483282,0.08107652,-0.02620473,0.0353895,-0.06923682,0.00707541,0.03685547,0.01348213,-0.04117655,0.00988774,0.01718125,-0.04512924,-0.01249596,0.11572655,0.03140871,-0.02283161,0.00530076,0.02500224,0.00160246,-0.04630697,0.0087235,-0.02911321,0.03036497,0.00999598,0.05808193,0.02592843,0.0060366,0.05355754,0.04991931,0.00765809,0.04572387,-0.01733962,0.00499113,-0.01924367,-0.03653525,-0.06643049,0.03329038,0.0201079,-0.26903969,0.01959548,-0.00333755,-0.02578621,0.03650203,0.01537739,0.10341072,0.03228204,-0.09947667,-0.0017048,-0.02584483,0.07217322,0.00021715,-0.02602035,-0.00266625,0.00317577,0.08537066,0.00871412,-0.01626253,-0.00541627,-0.02167775,0.06997779,0.18313971,-0.01077421,0.04394172,0.00619226,-0.05592543,0.04059797,0.02519207,0.01185195,-0.05522949,-0.0133494,0.0318062,-0.06118709,0.00118231,0.02045123,-0.02141085,-0.02282547,-0.01571066,0.02065019,-0.02983786,-0.05261338,0.00281642,0.06995671,0.09362478,-0.01522604,-0.06937469,-0.07562947,0.0260308,0.03403167,-0.04162562,-0.00678399,0.00578055,0.0239576,-0.00793195,0.09975027,0.01641724,-0.04887402,-0.0838891,-0.04041687,-0.04166349,-0.01634058,-0.05492825,0.05044429,0.03495231],"last_embed":{"hash":"1y35y7z","tokens":201}}},"text":null,"length":0,"last_read":{"hash":"1y35y7z","at":1751815146679},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{51}","lines":[343,343],"size":322,"outlinks":[{"title":"https://podcasts.apple.com/bt/podcast/urbit-nockchain-and-the-current-state/id1195362330?i=1000709342627","target":"https://podcasts.apple.com/bt/podcast/urbit-nockchain-and-the-current-state/id1195362330?i=1000709342627","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1y35y7z","at":1751815146679}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{52}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04190558,-0.02110264,-0.09463286,-0.04835125,0.03348775,0.00679508,0.01015484,0.02364435,-0.02157703,0.00979203,0.04693586,-0.09093051,0.01162077,0.03732025,0.0526578,-0.00119126,0.02380642,-0.06709675,-0.04457393,0.02228956,0.11617795,-0.06314924,0.02943518,0.00105051,0.08563612,0.02773561,-0.00393887,-0.01507771,-0.04698932,-0.16948976,-0.03385493,-0.05551951,0.01967819,-0.00077397,0.00085451,-0.02185897,-0.00327671,0.02291468,-0.03150763,0.00003213,-0.01958268,0.00324427,0.00957925,-0.00921326,0.05968637,-0.08101889,0.0388918,-0.05537408,-0.00573872,-0.04087874,0.01197367,-0.04185679,0.01117636,0.04881497,0.02583639,0.02831876,0.0100774,0.07520793,0.0405605,0.06222707,0.07618437,0.03986477,-0.19614619,0.052045,0.04811939,0.04606436,0.00012994,0.02239628,0.03727334,0.08948196,0.05247247,0.0181086,-0.02324527,0.02106667,0.03753124,0.03186712,0.01923784,0.01568425,-0.00173314,-0.04699083,-0.03689164,0.05046155,0.00747409,-0.02847451,-0.00626813,-0.02855528,-0.00188663,-0.03480449,-0.06793673,0.02162896,-0.01048926,0.03703423,0.05782862,0.00190371,-0.04321108,-0.00584002,-0.00743347,0.01859517,-0.06158124,0.12183914,-0.03908401,0.04568504,0.00027443,-0.06644369,0.04621748,-0.00059857,-0.0087675,-0.07275137,-0.0029367,0.02404674,-0.02798485,0.00709892,0.08815099,-0.04905378,0.01489212,0.00631687,0.06172897,-0.01525752,-0.01709802,0.0415402,-0.01772517,0.03860892,0.07517127,-0.01853833,-0.01024144,-0.01638755,0.02455532,0.0592332,0.02456673,0.05308756,0.03993449,0.06724932,-0.05751528,-0.00072638,-0.02424059,-0.02064921,-0.02767943,-0.02930332,-0.02860236,-0.06018387,-0.01202371,-0.09048513,0.02593186,-0.10622863,-0.06468189,0.04130057,0.00648813,0.01488632,-0.01419467,-0.03189535,0.02313391,0.0433127,-0.02690339,-0.07475331,-0.00403959,-0.01717229,0.017118,0.09901093,-0.1045436,-0.00714801,-0.00466334,-0.04768744,-0.03732913,0.13252379,0.03730264,-0.13571933,0.00958523,0.02645551,-0.00331965,-0.07186425,-0.00233234,0.00334445,-0.05262191,-0.00429483,0.06238822,-0.02475042,-0.04243786,-0.01209222,-0.00631213,0.03170465,-0.02202246,-0.03084058,-0.02498518,0.00502018,-0.02152127,-0.01978836,0.01763338,0.02557833,0.04102996,0.01733823,-0.08793311,0.00330052,0.0402544,0.04228039,-0.04879435,0.01074698,-0.04645464,-0.02338212,-0.02034327,-0.03421409,0.08595029,0.00205421,-0.03030154,-0.00938824,-0.06823839,0.01232049,0.00388661,-0.05928865,0.04731398,0.02728138,-0.01506787,0.03598168,-0.00613923,0.01522355,-0.03759495,-0.00815668,0.03615367,0.05324313,-0.03666043,0.04294109,0.0228876,0.02512019,-0.09536275,-0.19393125,-0.07975977,0.05383881,-0.04319211,0.05645504,-0.02285794,0.07251085,0.01158564,0.07159059,0.092268,0.09489009,0.05674161,-0.06217231,-0.02943313,0.02714102,0.06911202,0.01463899,-0.00074013,0.0059403,0.00308424,-0.04472213,0.01943247,0.02729088,-0.09432071,0.04708547,-0.0317478,0.0874403,0.00013293,0.0160797,-0.00216705,0.07081964,0.00956487,-0.01614718,-0.13283771,0.05250411,0.02165159,0.03549141,0.03098339,-0.03547907,-0.04535323,-0.00262488,0.01738826,0.04995039,-0.10555693,-0.03029171,-0.09270407,-0.05398717,-0.03691309,-0.0116597,-0.01021802,-0.01967066,0.06434263,0.03011218,0.08956994,0.02392582,-0.00090112,0.01520847,-0.0762509,-0.01116587,0.09019776,-0.01482224,-0.00389272,-0.07217955,-0.0048637,0.01797069,0.01197669,0.02115735,0.03506488,0.01824876,-0.01041042,-0.01131577,0.14156336,-0.0080957,-0.00217247,0.01911135,0.04974157,-0.00952174,-0.09509411,0.00898522,0.01785829,0.04416424,0.00052844,0.04326764,0.05181418,-0.01027492,0.07769512,0.02445933,-0.01572644,0.05402143,0.0185773,-0.0130347,-0.01390122,-0.05736572,-0.07194509,0.04685469,0.00309757,-0.28455582,-0.03125399,-0.00202985,-0.0383159,0.04371075,0.04427492,0.06842642,-0.00076491,-0.11342716,0.04880533,-0.025788,0.07645795,0.0026872,-0.03072301,0.00933452,0.0034908,0.0345023,-0.00771481,-0.00034704,0.01903692,0.01632305,0.0256286,0.19200356,0.01143672,0.00633661,0.01917333,-0.08289211,0.06020028,0.05607345,-0.01171563,-0.02395398,0.01813022,-0.00459239,-0.05501776,0.02490901,0.05027094,-0.01433407,-0.021929,-0.02081521,-0.00210363,-0.00585443,0.00986234,-0.03942275,0.03885668,0.11742039,0.00431158,-0.08048278,-0.05378777,0.01070704,0.03696007,-0.08793719,-0.01894192,0.00752036,0.04649859,-0.01176839,0.07745276,0.04077573,-0.0440337,-0.06686692,-0.0046166,-0.01969734,-0.03883892,-0.02800511,0.05098204,0.00424821],"last_embed":{"hash":"82550w","tokens":151}}},"text":null,"length":0,"last_read":{"hash":"82550w","at":1751815146690},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{52}","lines":[344,344],"size":212,"outlinks":[{"title":"https://www.nansen.ai/post/best-telegram-channels-for-crypto-chats","target":"https://www.nansen.ai/post/best-telegram-channels-for-crypto-chats","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"82550w","at":1751815146690}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{53}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03026658,0.00259703,-0.07149166,-0.03189394,-0.0023699,0.00704399,-0.02277789,0.02679651,-0.01481485,0.02333076,0.04659751,-0.06180311,0.03692568,0.04749216,0.03173372,0.00562261,0.02033806,-0.07539125,-0.00403107,0.01094361,0.06439687,-0.09600952,0.04774592,0.00325193,0.0436835,0.04306196,-0.01263415,-0.07178131,-0.02353074,-0.17248943,-0.02911667,-0.01636592,0.02884064,-0.00080811,0.00900264,-0.02139742,0.00837664,0.04535276,-0.05531681,0.02703066,-0.0009565,-0.01291689,0.00805038,-0.02851869,0.05628028,-0.05681228,-0.00867103,-0.06376881,-0.03156055,-0.0392522,-0.02736615,-0.08136535,-0.01689435,0.0155895,-0.01565543,0.02171424,0.02860849,0.03960409,0.04485913,-0.0108074,0.08990707,0.00520279,-0.19677861,0.06305704,0.05593946,0.05829888,-0.02049552,-0.00003059,0.051165,0.06451702,0.05020461,0.05327856,-0.00363066,0.02445961,0.06840352,0.03760359,0.02695088,0.02389604,-0.0184188,-0.05805457,-0.00606579,0.07114954,-0.02640704,-0.00058951,-0.01005859,-0.02980734,0.02436928,-0.0030294,-0.03629097,0.0206989,-0.03634799,0.03438523,0.04378077,0.0191534,-0.04004309,-0.02692154,0.02169944,0.00074516,-0.0455013,0.13587689,-0.04698362,0.03449067,0.04084698,-0.06444204,0.00063761,0.01921819,-0.0181534,-0.08526593,0.03490079,0.03597283,-0.03167787,0.06100854,0.09175356,-0.07651543,-0.01767304,0.00719978,0.03527134,0.01057622,-0.01324518,0.00429002,0.00858442,0.0741948,0.0686478,-0.02545488,-0.01302812,-0.02005382,0.02381238,0.05779973,0.09491958,0.04385693,0.04820115,0.07739567,-0.08308506,0.00426399,-0.04460719,-0.05182303,-0.01330297,-0.03466285,-0.02942706,-0.03489378,-0.00899815,-0.01549434,0.03020712,-0.11820009,-0.06341959,0.06151637,0.04448431,-0.00657937,-0.01621648,0.00693932,0.00576354,0.0469423,-0.04769044,-0.04643422,-0.02175474,-0.04810964,0.02722508,0.1144264,-0.10257507,0.01827203,-0.00713087,-0.04620376,-0.04322723,0.16030613,0.02734391,-0.13874879,0.02199893,0.01519568,0.02436534,-0.08376986,-0.01933948,0.0183806,-0.02702806,-0.00889957,0.0870717,-0.0079789,-0.09388617,-0.01864853,-0.0214136,0.05576582,0.0063594,-0.02227421,-0.06686043,-0.0120288,-0.0309951,-0.04679053,0.00764047,0.00693408,0.01936005,0.02122955,-0.06800628,-0.04974668,-0.01579227,0.06309149,-0.01045128,0.01719721,-0.02493178,-0.02852766,-0.0001477,-0.00529068,0.0245715,0.002708,-0.02177174,0.03796935,-0.04527844,0.00734245,-0.004674,-0.04341082,0.03294891,0.05898698,-0.02327274,0.02603612,-0.03123066,0.03171556,-0.03983313,-0.00070151,-0.0041284,0.03571412,-0.04250617,0.04345724,0.02733053,0.0182445,-0.06623406,-0.19567721,-0.0723929,0.02831976,-0.02612592,0.01392716,-0.03627513,0.07832139,-0.00977024,0.04643599,0.08189862,0.08563728,0.03168346,-0.05630305,-0.01002915,0.02971525,0.06009465,0.01348474,-0.00278474,-0.01737851,0.01748227,-0.01407481,0.01134629,-0.01637446,-0.11974367,0.05442563,-0.04087344,0.11235474,0.02771127,0.00933627,0.03728769,0.09401996,0.0521262,-0.02698473,-0.17140254,0.03986937,0.01591387,0.04996366,-0.00641838,-0.06328273,-0.03671251,-0.00534518,0.02187357,0.01787293,-0.11493097,-0.01607643,-0.04715503,-0.04772664,-0.01250953,0.00044064,0.01418595,0.01318525,0.01522517,0.02373626,0.09436899,0.01359232,-0.01168002,0.0067015,-0.07563218,0.02430956,0.08594925,-0.03010234,0.01859903,-0.05635479,0.01487241,0.04885443,0.02620809,-0.00158394,0.0251187,0.03654113,-0.03301395,-0.01650823,0.15331441,0.00858622,-0.01973332,0.01246696,0.00764589,-0.02412383,-0.0567633,0.03802016,0.00898397,0.02645385,0.00369982,-0.00650451,0.06312233,-0.00670929,0.02515126,-0.01212536,-0.01292081,0.00943395,-0.03961634,-0.00480623,-0.0216948,-0.06054067,-0.07071782,0.09117219,0.00361733,-0.25158885,-0.01644367,0.03635216,-0.04787749,0.04121801,0.0447498,0.05257286,0.01026908,-0.06962018,0.00143349,0.03422873,0.07093585,-0.00101754,-0.02419099,0.01551034,0.00050806,0.04572054,-0.02698128,0.02413741,-0.001131,-0.01263362,0.05327322,0.21774359,-0.00812358,0.01482932,0.01166924,-0.0645652,0.06128492,0.01889604,0.00868998,-0.07059377,-0.00075368,0.02830644,-0.0542335,-0.01100952,0.0213443,-0.02190565,-0.01320658,-0.00378342,0.01272938,0.00290995,-0.0080736,-0.00132171,0.01651235,0.13662234,0.05102563,-0.06041437,-0.07070139,0.00011637,0.01604463,-0.05715207,-0.04898,0.00681277,0.02738933,0.00517596,0.08818924,0.01300532,-0.0495247,-0.02475085,-0.02632475,-0.04297511,0.00807693,-0.02202992,0.05852102,0.01001184],"last_embed":{"hash":"1nvyii2","tokens":211}}},"text":null,"length":0,"last_read":{"hash":"1nvyii2","at":1751815146700},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{53}","lines":[345,345],"size":444,"outlinks":[{"title":"https://ryanschultz.com/2018/07/17/the-telegram-discussion-groups-for-the-various-blockchain-based-vr-projects-have-been-endlessly-entertaining/","target":"https://ryanschultz.com/2018/07/17/the-telegram-discussion-groups-for-the-various-blockchain-based-vr-projects-have-been-endlessly-entertaining/","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1nvyii2","at":1751815146700}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{54}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03709391,-0.03423919,-0.07488022,0.0030274,0.04385865,-0.01807838,0.00582122,-0.00193247,-0.03190498,-0.01504721,0.08797505,-0.0679638,0.04891095,0.01911043,0.03020044,-0.02974221,0.02569382,-0.08309729,-0.0240799,0.02981251,0.12300255,-0.06175759,0.03537036,-0.02786898,0.05961274,0.05138713,0.00571445,-0.01851162,-0.08036438,-0.15729442,0.00737932,-0.04759696,-0.03788016,0.01838858,0.03007204,-0.03482677,-0.00575963,0.05532198,-0.04144687,0.00382595,-0.03044409,0.00949997,0.00176161,0.00537751,0.0590927,-0.05585827,-0.01368662,-0.00136291,-0.0071112,-0.02924432,-0.01292043,-0.046059,-0.00041012,0.04986937,0.00245229,-0.02508555,0.00153941,0.02210725,0.07497993,-0.00299829,0.09176433,0.03360242,-0.17609197,0.02479913,0.03376597,0.05106744,0.0229751,-0.00058013,0.06274403,0.06084808,0.03969908,0.01188634,-0.04038696,0.0226242,0.01221459,0.02663541,0.01086085,0.04107426,-0.0622131,-0.02061938,-0.02453698,0.03277813,0.0241555,-0.03580543,-0.01333012,-0.05670593,0.02917048,-0.01450134,-0.02195258,0.00913858,-0.00240433,0.0443231,0.03855909,0.00740338,-0.02644562,-0.00988481,0.00143224,0.03222313,-0.05890075,0.12487155,-0.02663268,0.04003768,0.01479568,-0.05481914,0.0371462,-0.02324531,-0.02368945,-0.0088914,0.0003667,0.00570632,-0.0269961,0.03223923,0.09259474,-0.06779773,-0.01093293,-0.01389761,0.00987729,-0.0052,-0.03668119,0.04327229,-0.01293247,0.06338955,0.07035222,0.00212973,-0.02521863,-0.04001098,-0.01818845,0.07096757,0.05701979,0.08305661,0.00465041,0.07492407,-0.05567772,0.01382339,0.0001677,-0.02838361,-0.04964359,-0.00102034,0.01209382,-0.03769135,-0.04889777,-0.08334708,0.00174373,-0.10064167,-0.04799041,0.0398966,0.0039284,0.00302085,-0.02333969,-0.00752811,-0.01015966,0.02408029,-0.03686931,-0.0467369,-0.02158271,-0.04027213,0.06691325,0.12174533,-0.07738773,-0.01508805,-0.00879464,-0.02886043,-0.0446943,0.13071801,0.06497892,-0.10706997,0.03119563,0.00686307,-0.01171011,-0.06662222,0.00402574,0.00301979,-0.04216479,-0.00433708,0.04482721,-0.0274929,-0.01746866,-0.04940868,-0.00388062,0.04103814,-0.04357464,-0.00108575,-0.0513286,-0.00809959,-0.01893363,-0.01143935,-0.04383031,0.05460273,0.01566796,0.06042156,-0.10378872,-0.04080518,-0.03429441,-0.00515718,-0.04585043,0.00464283,-0.0407744,-0.02395093,0.00348007,-0.04682722,0.07215482,0.03546096,-0.01364243,0.0129473,-0.06925089,0.05592357,-0.00160603,-0.05512329,0.01019279,0.02630525,0.00409967,0.04606482,-0.05541063,-0.008835,-0.01372504,0.00964184,0.02880826,0.01136161,-0.02353957,0.02508302,0.00734118,0.00875605,-0.09923764,-0.20434588,-0.08922765,0.06991091,-0.02415083,0.05688776,-0.0126918,0.07121617,0.01468434,0.05793572,0.07810056,0.08115574,0.04523896,-0.06016251,0.01611139,0.04565144,0.09448222,0.02406107,-0.00940488,0.02895239,0.04074186,-0.07361315,0.03533977,0.02485995,-0.05364102,0.04075745,-0.02999127,0.12449681,0.06174601,-0.02493593,0.0106536,0.06089571,0.03067408,-0.00621317,-0.12261898,0.0735584,0.02150065,0.0080211,-0.02259238,-0.00403632,-0.03834064,-0.02094683,0.00911142,0.01974123,-0.09596533,0.00930547,-0.07935351,-0.05877934,0.02389996,-0.01818698,0.05132488,0.02855768,0.04975688,0.03572844,0.10966814,0.00644319,-0.03441716,0.011084,-0.0779451,-0.00812149,0.05072805,-0.0227698,-0.00256665,-0.03643221,-0.00635771,0.03127781,-0.03541411,-0.04941732,0.02154899,0.05830233,0.01964394,-0.01117663,0.16968064,0.02203973,-0.0343767,0.02410812,0.02102428,-0.03827916,-0.13003427,0.0204446,0.02295009,0.01492296,0.00632104,0.03003568,0.07472532,0.00264328,0.04244889,-0.03707814,-0.00505559,0.03534832,-0.01109827,-0.04094779,-0.008844,-0.05070689,-0.03990747,0.06033631,0.02785315,-0.28339642,-0.04799972,0.02293003,0.04375938,0.05069156,0.02668468,0.07552421,0.0023966,-0.08318257,0.0136831,-0.01517639,0.08511415,0.01133775,-0.01222816,-0.01939435,-0.00883183,0.02326006,-0.06973081,0.01173281,0.0050064,-0.0158342,0.04331273,0.21788,0.01999719,0.00637623,0.04923434,-0.0201124,0.07510843,0.05775668,0.00788955,-0.02640226,-0.00826586,-0.02243087,-0.06289877,0.00034557,0.00853976,-0.01577721,-0.02237423,-0.01715644,-0.00890329,-0.02283725,0.00769795,-0.02011063,0.02352717,0.11532309,-0.02407655,-0.04754935,-0.08751129,0.02791423,0.05719028,-0.0384199,-0.03254838,-0.01579609,0.02399284,0.0137229,0.11071604,0.00894328,-0.06271476,-0.0231714,0.00898332,-0.0404131,-0.01939482,-0.01211294,0.04719264,0.00782712],"last_embed":{"hash":"3sw8h8","tokens":209}}},"text":null,"length":0,"last_read":{"hash":"3sw8h8","at":1751815146711},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{54}","lines":[346,346],"size":327,"outlinks":[{"title":"https://www.reddit.com/r/CryptoCurrency/comments/lz1f54/beware\\_of\\_fake\\_pump\\_coins\\_groups\\_on\\_telegram/","target":"https://www.reddit.com/r/CryptoCurrency/comments/lz1f54/beware_of_fake_pump_coins_groups_on_telegram/","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"3sw8h8","at":1751815146711}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{57}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03335999,-0.0463365,-0.05118586,-0.03565894,0.07209923,0.00483593,-0.05204186,0.04837144,-0.02761768,0.011673,0.07041968,-0.06088797,0.02431694,0.03115151,0.0160852,-0.02392931,0.02607068,-0.02532627,-0.00947644,0.03508398,0.13461639,-0.08398634,0.01370854,-0.03907469,0.07959701,0.05952608,0.02798686,-0.03351941,-0.03032618,-0.22381721,0.002598,-0.03575998,0.02041466,0.01711767,0.01378143,-0.01141183,-0.02725736,0.08265369,-0.04102104,0.01676707,0.02475089,-0.01552134,0.02030259,-0.0071967,0.04867543,-0.08236229,-0.00954609,-0.05126354,0.01619802,-0.03860726,-0.01643904,-0.07131361,-0.01692717,0.02616471,0.00734702,0.0597248,0.00257624,0.01583143,0.03222922,0.07007255,0.08456535,0.06115484,-0.23717517,0.02195541,0.08158859,0.04259619,0.00337157,0.00233443,0.04913465,0.07920165,0.02932489,0.02870029,-0.00450912,0.04278367,0.0420287,0.00862966,0.03444915,0.00235695,-0.00948733,-0.07381657,-0.0129572,0.08402121,-0.02019843,-0.01632223,-0.02034209,-0.00125035,-0.00763551,-0.02516009,-0.0484485,0.03039493,-0.00667021,-0.00103797,0.05652883,-0.01469335,-0.06169265,0.00104088,0.00782113,0.02213139,-0.07938095,0.1195375,-0.02158743,0.0519858,0.01959223,-0.03185115,0.02463632,0.0200398,0.00250599,-0.03039136,0.01485844,0.03594263,0.00574945,0.01420365,0.01604832,-0.07463147,0.02807015,0.00026835,-0.02958366,-0.00117876,-0.00829651,-0.00515094,0.01326763,0.01665405,0.04637611,-0.04079262,-0.05408232,-0.0203094,-0.00861083,0.06701276,0.05701959,0.03708941,0.05157609,0.03968967,-0.07364533,-0.00527676,-0.06965939,-0.02902765,-0.00722879,0.00585488,-0.03804779,-0.04590882,-0.03966701,-0.08562702,-0.02076495,-0.09947742,-0.09912714,0.09397123,-0.01370212,-0.00741829,0.01378984,-0.01513482,0.03334622,0.07640257,-0.00231771,-0.06740814,0.00052035,-0.03400042,0.02940041,0.07808456,-0.05131285,-0.02064403,-0.04271747,-0.06687263,-0.05119731,0.14985789,0.04480185,-0.13576111,0.05451887,0.04776116,0.00993478,-0.07507934,-0.00590828,-0.03806811,-0.02197222,-0.02577409,0.07330261,-0.0143908,-0.02673317,-0.04052369,-0.01217952,0.03656485,0.00270397,-0.02901147,-0.06924251,0.0034124,-0.00443026,-0.04419042,-0.03700845,0.02304235,-0.02563551,0.01203546,-0.07918253,0.01581675,-0.00218338,0.02711032,0.01159438,-0.01328934,-0.01813256,-0.01497837,0.00484967,-0.02763902,0.07115141,0.01892989,0.02438355,0.00999446,-0.01756447,0.06602987,-0.03034176,-0.02526385,-0.02442458,0.03592017,-0.02824703,0.00158549,0.00582375,0.02412676,-0.05512725,0.03377307,0.04392497,0.02850886,-0.04224771,0.06344375,-0.00482584,0.02149766,-0.0681398,-0.19547392,-0.05037253,0.01396204,-0.04847286,0.01952937,-0.00424909,0.06063793,0.01139848,0.00747337,0.07812119,0.08786998,0.01559574,-0.02350129,0.01234573,-0.00468544,0.03967652,0.0295282,-0.01260658,0.00935379,-0.00164904,-0.01494937,0.02531841,-0.00467996,-0.05615666,0.03720926,-0.03898162,0.10141265,-0.00646318,-0.01265764,0.016639,0.07265591,0.01153636,-0.03550904,-0.14705151,0.02748221,0.03517926,-0.00556515,-0.01422225,-0.07107409,-0.00975827,0.00449225,0.00643338,0.04380663,-0.08124655,0.02275316,-0.05631265,-0.04691395,-0.00440083,-0.01028416,0.01303466,-0.02184771,0.04428929,0.04217044,0.11427093,-0.00210036,-0.00009924,0.01869749,-0.0496373,0.00302799,0.0393952,-0.00653725,0.00604732,-0.028115,-0.008311,0.03203123,-0.02396223,-0.02766647,0.01285542,0.04283784,-0.0332704,0.00486074,0.11859537,0.03488966,-0.01942605,0.04257296,0.05154162,-0.04232116,-0.09658261,-0.00747323,0.02768837,0.00796595,0.02589836,0.00917844,0.06074369,0.02963669,0.0533325,0.03779657,-0.01616716,0.01238637,0.00231593,-0.00169721,-0.0324012,-0.04188171,-0.01443255,0.0948844,-0.03242598,-0.30477422,-0.00857123,-0.01620632,-0.03612227,0.05032641,0.04578067,0.04200169,-0.00674944,-0.07171315,0.03736679,0.0042759,0.04220808,0.0109433,-0.03587566,0.02368962,0.00336452,0.08544943,-0.01100239,0.05283813,0.01204927,0.01486595,0.05600528,0.20121945,-0.0493839,0.02088693,0.01311387,-0.06607523,0.05989172,0.04698339,0.03081455,-0.02337646,0.00416383,0.02969215,-0.0234189,0.02739006,0.05619373,-0.01890925,-0.00629351,-0.00355132,0.03794957,-0.03120216,-0.01106177,-0.02876062,0.03758937,0.11105384,-0.03346885,-0.05315993,-0.10265878,0.01392804,0.0399708,-0.05554119,0.01322499,-0.00604361,0.0234432,-0.01313884,0.06833838,0.02903624,-0.04803408,-0.05488851,-0.02734462,-0.03028755,-0.00162607,-0.03572296,0.05335093,0.02776406],"last_embed":{"hash":"1iifbwq","tokens":173}}},"text":null,"length":0,"last_read":{"hash":"1iifbwq","at":1751815146723},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{57}","lines":[349,349],"size":231,"outlinks":[{"title":"https://www.rootdata.com/Projects/detail/RISC%20Zero?k=MjgwMw%3D%3D","target":"https://www.rootdata.com/Projects/detail/RISC%20Zero?k=MjgwMw%3D%3D","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1iifbwq","at":1751815146723}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{58}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04903603,-0.01671407,-0.05049082,-0.02668069,0.02594825,0.04149434,0.04143391,0.01877194,0.03178783,-0.00199137,0.05988167,-0.04174505,0.0274689,0.05101781,0.0082413,0.00340758,-0.00476772,-0.01287003,-0.00008998,0.03413447,0.11460829,-0.07596685,0.01123511,-0.05397765,0.02484459,0.05955216,0.00379177,-0.03825483,-0.04857581,-0.19087324,0.00781694,-0.06284678,-0.00937656,0.01058001,0.03149145,-0.05430002,-0.00025483,0.06799964,-0.05097324,0.00726842,0.0203781,0.01166673,0.0004642,-0.00405879,0.0420426,-0.07554616,0.02472218,-0.02342717,-0.0651006,-0.11220186,-0.00698162,-0.0402932,-0.02594839,0.07112484,0.00638469,0.03654791,0.03119761,0.00726589,0.01607807,0.03759678,0.08632988,0.04120206,-0.19970971,0.03568428,0.08064925,0.07669126,-0.01634845,-0.03384553,0.04088362,0.0162495,0.02025768,0.04616986,0.00989515,0.04322005,0.02097271,0.03050661,0.00252412,0.00129231,-0.04653124,-0.03835362,-0.03127023,0.06936219,0.0426765,-0.02092459,-0.04905023,-0.02792989,-0.0001702,-0.06965031,-0.00293631,-0.0357,-0.0052579,0.00700229,0.03687339,-0.02968341,-0.00221158,-0.03161061,-0.0053054,0.01220122,-0.05239312,0.109537,-0.02619531,0.05640668,0.01097419,-0.05797272,0.04050342,-0.00576498,-0.01141043,-0.06335592,0.01934636,0.02719955,0.01618932,-0.00294674,0.07429365,-0.04131363,0.02263515,0.01399456,-0.00351006,0.02549323,0.00413239,-0.01575213,0.02251034,0.02609852,0.06898454,0.01061618,-0.03037497,-0.00996874,0.01262707,0.07883122,-0.01102875,0.03975683,0.04993998,0.00239299,-0.0552928,-0.01039031,-0.02732291,-0.00527245,-0.02523997,0.02952148,-0.02400333,-0.0171956,-0.01835688,-0.04580575,0.0168265,-0.08528385,-0.06085536,0.11832114,0.04821277,0.05302079,0.00059797,-0.04458736,0.04827588,0.07529417,-0.00699457,-0.07928997,0.001268,-0.01982178,-0.00380313,0.06200913,-0.09939864,0.03096997,-0.01171704,-0.02736701,-0.02977887,0.2024994,0.0474397,-0.11874942,0.04829411,0.03903448,-0.01941542,-0.0885189,-0.00677145,-0.01140081,0.00394925,0.00397475,0.05439694,0.01805664,-0.01390984,-0.04688653,-0.01896724,0.0331754,-0.03905673,-0.05096793,-0.05371285,-0.03985268,-0.02711137,-0.03983158,0.01095697,0.03615286,0.00133143,0.05328301,-0.13012858,-0.00962916,-0.00960002,0.02815717,-0.03204574,-0.02957433,-0.05649415,-0.03511348,0.02348888,-0.05303861,0.09182916,0.0450994,0.01326661,0.00103706,-0.03229285,0.01801223,-0.02736937,-0.05629214,0.00885548,0.01481705,-0.04179929,0.06576529,-0.02254808,0.00950791,-0.02648033,0.013885,0.00891981,0.01751791,-0.03540426,0.006027,0.03237507,-0.01313796,-0.08041552,-0.18330283,-0.04747589,-0.01268842,-0.03314508,0.03165253,-0.01064325,0.05120253,0.00255711,0.03801312,0.06964573,0.09564172,0.0380563,-0.0609811,0.03756671,-0.0203798,0.03492538,0.02255345,-0.026346,-0.01166737,0.02099197,-0.03853263,0.03854077,0.0122122,-0.10450912,0.03667726,-0.03164901,0.10434159,-0.01180861,0.01862211,-0.02594541,0.0275692,-0.00908202,-0.01343142,-0.15302821,0.01929939,0.01980854,-0.00752105,-0.01117069,-0.03254106,-0.0658735,0.02564838,0.0014623,0.03233142,-0.07792286,-0.01684985,-0.09074394,-0.06567635,0.01144653,-0.03119182,0.0173468,0.01418359,0.06115273,0.0340206,0.08692167,0.00393559,-0.03491054,0.03188309,-0.04576107,-0.01580035,0.07198783,-0.00247905,-0.0034065,-0.0517724,-0.02012367,0.03804103,-0.01443427,0.00844904,0.01595996,0.02677554,-0.03820991,-0.0163252,0.14404348,0.01601361,-0.02605531,0.02295703,0.05995879,0.02720848,-0.10793718,-0.00961722,0.01788598,0.04815965,0.02620498,0.02329631,0.05942677,0.02176328,0.06431562,0.02995177,-0.00653199,-0.00231784,-0.0457399,0.00403374,-0.03618636,-0.02369595,-0.06646904,0.06391087,0.02147974,-0.29374787,-0.0222241,-0.02986167,-0.00931682,0.02874471,0.02361775,0.04929449,0.01559922,-0.07068558,-0.00636806,-0.03417909,0.08224624,0.01213053,-0.0396151,-0.00217368,-0.00152878,0.06201952,-0.04845519,0.01297551,0.03215215,0.01805277,0.07354584,0.23881572,0.00042656,-0.00010838,0.00731732,-0.01911593,0.03634802,0.04305943,0.02371168,-0.01945924,-0.00542184,-0.00781261,-0.01655814,0.00261321,0.06331757,0.02172461,-0.00858656,-0.00130314,0.05094156,0.01131805,-0.03697157,0.02069949,0.04470619,0.134413,0.00750553,-0.05855917,-0.05927802,0.00227903,0.01436529,-0.04695639,0.00074083,0.02707629,0.02502406,0.01016065,0.06667574,0.03243493,-0.04871335,-0.07196464,-0.00467219,-0.03789023,0.00517993,-0.0470579,0.04103655,0.01159252],"last_embed":{"hash":"titdlg","tokens":173}}},"text":null,"length":0,"last_read":{"hash":"titdlg","at":1751815146733},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{58}","lines":[350,350],"size":295,"outlinks":[{"title":"https://en.foresightnews.pro/an-overview-of-privacy-solutions-based-on-zero-knowlege-cryptography/","target":"https://en.foresightnews.pro/an-overview-of-privacy-solutions-based-on-zero-knowlege-cryptography/","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"titdlg","at":1751815146733}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{59}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05499258,-0.01294981,-0.03746384,-0.00956827,0.01000423,0.01649732,0.04307798,-0.0008915,0.02362368,-0.02276936,0.05862276,-0.06587468,0.04681276,0.00545247,0.0461727,0.00046345,-0.00534639,-0.04543113,-0.03459914,0.00760148,0.11165677,-0.09332509,0.01830643,-0.05658331,0.02242943,0.03258571,-0.01174414,0.01206227,-0.02698119,-0.17348528,0.00637644,-0.06158787,-0.01811402,-0.03024829,0.04189015,-0.02350487,-0.01866213,0.0298842,-0.06987937,0.0114288,0.01710927,0.01054914,-0.00016998,0.01020023,0.03504644,-0.0536644,0.02442409,-0.07574583,-0.03492833,-0.05433875,-0.01368622,-0.07464101,-0.01902114,0.06192234,0.01855188,0.03375361,0.03216222,-0.00649281,0.02538498,0.05105126,0.08719227,0.05615527,-0.20514554,0.04548279,0.08546059,0.05023126,-0.04208111,0.00968342,0.03486887,0.03724962,0.03920399,0.04307939,0.00713696,0.02299073,0.03197398,0.03494063,0.01685308,0.00219147,-0.01945351,-0.052401,-0.0361402,0.04915461,0.03211124,0.00484336,-0.02921985,-0.04548682,0.04379401,-0.03075938,-0.03978952,-0.00595602,-0.03696667,-0.00042236,0.05348142,-0.00029282,-0.00937832,0.00055951,-0.03221376,0.02465546,-0.0520847,0.10468625,-0.0262398,0.0876765,-0.02082665,-0.03211515,0.0449608,0.02328447,-0.00983623,-0.06145943,0.03143789,0.03327466,0.02881682,-0.00137458,0.10290252,-0.04406036,0.0032622,0.01238384,0.00685039,0.00217393,-0.0041029,0.01081771,0.00289555,0.0447327,0.05329488,0.03703164,-0.0029171,-0.0070858,0.00251966,0.05411489,0.0550133,0.03257495,0.04545696,0.01383194,-0.05666471,-0.00373965,-0.01805181,-0.02894481,-0.04663942,0.00161627,-0.00953223,-0.02933265,-0.02096622,-0.01914624,0.00442586,-0.10356554,-0.05501399,0.11306624,0.06279761,0.02129059,-0.00399547,-0.04040871,0.02254602,0.05525888,-0.00687404,-0.06932278,0.00631153,-0.01699344,0.00122938,0.1244293,-0.09691726,0.01878777,-0.02713736,-0.00189898,-0.01416506,0.15032731,0.03176865,-0.11217271,0.03414784,0.06753254,-0.00127711,-0.07267347,0.03238533,0.01032361,-0.01282214,-0.01438294,0.04328994,-0.00996057,-0.04120453,-0.01314911,-0.03804631,0.03702945,-0.03287296,-0.05949869,-0.07585274,-0.05523899,0.0054169,-0.02289519,-0.01745637,-0.00282338,-0.01610015,0.09132674,-0.10267648,0.00933735,-0.00804799,0.06379946,-0.03356733,-0.01083363,-0.04910504,-0.03535709,-0.02914777,-0.02844453,0.08180404,0.06248143,0.02851626,0.0129711,-0.05366458,0.04170806,0.01231415,-0.03779057,0.03344277,0.03345357,-0.04449936,0.06738742,-0.073475,0.01976785,-0.02551151,0.00540269,0.01535875,0.02888019,-0.00995887,0.01693466,0.0134294,-0.01366175,-0.08643987,-0.20218281,-0.01525157,-0.00298884,-0.0343737,0.02310457,-0.00688987,0.05590527,0.01654355,0.05804606,0.07589863,0.10181258,0.03189067,-0.07244415,0.02039115,-0.01413894,0.04665861,0.04320158,-0.01885474,0.00751192,0.04571298,-0.03258171,-0.01002433,0.02558496,-0.10475735,0.04645449,-0.02734316,0.10614998,-0.02722329,0.04963968,0.01185463,0.0408975,0.00042683,-0.02842299,-0.17719626,0.03460411,0.02858274,-0.00303815,-0.0049387,-0.04705672,-0.07129036,-0.00675388,0.01067625,0.01972817,-0.08205124,0.00831489,-0.05359549,-0.04091211,0.00880299,-0.04433865,-0.04576413,0.0123815,0.0581272,0.01875358,0.08862059,-0.00525468,-0.00936131,0.00845921,-0.06953628,-0.03297503,0.06104616,-0.01321863,-0.00032967,-0.05439666,-0.02400654,0.01041879,-0.0033153,-0.01104759,-0.00734707,0.02923421,-0.02337748,-0.02353402,0.13139601,0.04812424,-0.02129107,0.03239021,0.02293533,-0.00090104,-0.07613822,-0.01637893,-0.00206722,0.03576227,0.00324661,0.01733324,0.07879064,-0.01328188,0.07362398,0.05324596,-0.04625345,-0.00267172,-0.00950367,-0.00911284,-0.0278223,-0.05845021,-0.05911074,0.07474055,0.03122985,-0.28462541,-0.03193979,-0.02486721,0.01519004,0.06226734,0.02664357,0.06959146,0.00744933,-0.06191178,-0.00811427,-0.02570011,0.09766213,0.01517509,-0.00684434,-0.0317678,-0.01969125,0.07061597,-0.01192067,0.03569055,0.05859585,0.02224365,0.04479536,0.20473292,0.00326668,0.00352451,0.03964222,-0.05633264,0.05041359,0.00664942,0.0213051,-0.02943989,-0.01319311,0.00480666,-0.06487368,-0.01073332,0.10134705,-0.00775034,-0.01757369,0.00297235,0.04745241,-0.022344,-0.03787048,0.00084741,0.03913597,0.09377217,0.01652397,-0.07955333,-0.07523853,0.00662627,0.01767287,-0.06959499,-0.00160772,-0.02312472,0.04115316,0.0278107,0.06681713,0.01593485,-0.04110901,-0.07178772,-0.02190669,0.0030468,0.01776504,-0.08145883,0.03667376,-0.01525786],"last_embed":{"hash":"1p1fq0a","tokens":151}}},"text":null,"length":0,"last_read":{"hash":"1p1fq0a","at":1751815146744},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{59}","lines":[351,351],"size":240,"outlinks":[{"title":"https://equilibrium.co/writing/privacy-blockchains-and-aleo-deep-dive","target":"https://equilibrium.co/writing/privacy-blockchains-and-aleo-deep-dive","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1p1fq0a","at":1751815146744}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{60}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03991041,-0.03017642,-0.01538723,-0.00345839,0.02330093,-0.02280135,0.00243104,0.01862529,0.00373771,0.00667431,0.08742502,-0.03523835,0.01675522,0.00545554,0.00426795,-0.01142917,0.03076907,0.00136753,-0.03117001,0.03145425,0.138906,-0.11327085,-0.00876994,-0.01918886,0.11176106,0.0172819,-0.01426608,-0.04549668,-0.0391632,-0.23539928,0.02450966,-0.06146976,0.00983095,0.01066128,0.00158161,-0.00087322,0.00145668,0.04709109,-0.0869428,0.01679716,0.03024791,-0.00724039,0.01971228,-0.00409227,0.02321521,-0.06999175,0.00632901,-0.05222173,0.01701814,-0.05518952,-0.0168188,-0.05024477,-0.00016023,0.01716613,0.02287111,0.01396058,-0.00872614,0.02768465,0.06185454,0.04320805,0.08940294,0.07907455,-0.22972526,0.05766066,0.10775115,0.01877211,-7.5e-7,0.0218428,0.04789395,0.06362545,0.02229169,-0.00284675,-0.04499455,0.03729383,0.00496819,0.02354595,-0.00158598,0.00312152,-0.02439715,-0.04793482,-0.00856569,0.0076408,0.00519163,-0.01142485,-0.05573991,-0.01399701,0.00416857,-0.01102377,-0.03769971,0.01150866,-0.02302981,0.03043229,0.08673047,-0.0223419,-0.03292036,-0.00346733,0.01152114,0.03073383,-0.08362439,0.10219792,-0.02257692,0.0322451,-0.00087508,-0.05641492,0.01146151,0.04755459,-0.00453936,-0.0424301,-0.02356184,-0.00439344,0.03383168,-0.00609198,0.02470216,-0.05034781,0.04990922,0.02725294,0.00771615,0.03034255,0.00210717,0.0323162,-0.01273426,0.03546161,0.07769459,-0.02976646,-0.02187945,-0.02667895,0.01183984,0.07380242,0.00879857,0.04630688,0.04326751,0.02543662,-0.04917082,-0.00383082,-0.00293608,-0.02251065,-0.0331433,0.01206002,-0.02319513,-0.00061147,-0.01435659,-0.05771437,-0.0288441,-0.12288506,-0.02497487,0.08431699,0.02560151,0.00032252,0.01251207,-0.03523274,0.01217572,0.06959862,-0.01115829,-0.11122318,0.00337403,-0.00379095,0.0387978,0.10245791,-0.05184153,-0.04225668,-0.03868799,-0.02226127,-0.06867456,0.1614563,0.04301872,-0.09444252,0.0590259,0.0515563,0.00144225,-0.0553025,-0.0119489,-0.05156124,-0.03790054,-0.03356655,0.07030238,-0.01866956,-0.04043579,-0.06921434,-0.01951871,0.01759071,0.01275572,-0.03029449,-0.0472029,0.00116666,0.01330479,-0.01165567,-0.02123237,0.0090843,0.01043422,0.01165863,-0.11183712,0.00930198,0.00868418,0.05794713,-0.0050695,0.01668816,-0.04663077,-0.01333843,-0.01336707,-0.03952711,0.07141341,0.00178044,0.00041596,0.00853406,-0.01229542,0.07917865,-0.00576745,-0.01863405,-0.00563179,0.02494184,0.03116524,-0.00302335,0.01478094,0.04328589,-0.04676242,-0.00388822,0.05829396,0.01589242,-0.04442568,-0.01535623,0.02973558,0.03940282,-0.10149559,-0.19252557,-0.04676668,-0.03576157,-0.05689643,0.03929071,-0.03862381,0.03571716,-0.00711884,0.03346123,0.08681069,0.0469311,0.03978129,-0.05977067,0.02784341,-0.00764616,0.04453025,0.04669205,-0.00785368,-0.01475417,0.01261372,0.00389417,0.03333721,-0.01214663,-0.06653458,0.0401904,-0.01502522,0.09972743,-0.00359507,0.0394216,0.04139276,0.07002694,0.03033197,-0.05057105,-0.07332657,-0.00569712,0.05043055,-0.01588871,-0.03944159,-0.00415097,0.02191321,-0.0248747,0.02076894,0.00416173,-0.11018647,0.02432449,-0.03629248,-0.04476874,-0.01016876,0.01357759,0.04191285,0.00994198,0.02410852,0.03190045,0.10530433,0.0435525,-0.02081483,-0.03545636,-0.06199844,0.00558801,0.07874943,-0.02547358,-0.0059177,-0.05545276,-0.01441065,0.01981753,0.00950678,-0.02336327,0.04939922,0.04077785,0.01121558,-0.01113069,0.15477264,0.03880774,-0.01525601,-0.01031767,0.04327389,-0.04447585,-0.08732073,-0.02029005,0.03384791,0.01782313,0.05146158,0.04589052,0.06096487,-0.00793931,0.04264802,0.03391156,-0.02502222,-0.01358506,-0.01612182,-0.00567596,-0.02050894,-0.05357416,-0.02288759,0.08230173,-0.03499987,-0.28563064,-0.00580963,-0.01163096,-0.05167668,0.04234488,0.06188403,0.05631853,-0.00269163,-0.07228987,0.04020953,-0.09012428,0.0541046,0.02115466,-0.03039287,0.00323741,0.00989285,0.05841526,-0.04087923,0.04160572,0.04106082,0.02450873,0.02531109,0.18878178,-0.03226229,0.03379802,0.02287551,-0.0501103,0.06623858,0.02588379,0.00462617,-0.03696081,0.0260199,-0.01228566,-0.03704301,0.00643309,0.07439882,-0.06227137,-0.00809728,0.00505775,-0.00891058,-0.03416127,-0.01101989,0.01787588,0.02195617,0.1305116,-0.04353875,-0.03039676,-0.10658707,0.03975486,0.04222797,-0.05100071,0.02223249,-0.01212948,0.03197005,-0.02795888,0.02680621,0.0179748,-0.06575219,-0.03198567,-0.06116877,0.0000757,0.02818522,-0.07954157,0.02312472,0.05321084],"last_embed":{"hash":"naaicw","tokens":199}}},"text":null,"length":0,"last_read":{"hash":"naaicw","at":1751815146753},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{60}","lines":[352,352],"size":371,"outlinks":[{"title":"https://www.blockchaincoinvestors.com/blog/risczero-eliminating-tradeoffs-and-unleashing-the-power-of-blockchain-technology-254ec","target":"https://www.blockchaincoinvestors.com/blog/risczero-eliminating-tradeoffs-and-unleashing-the-power-of-blockchain-technology-254ec","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"naaicw","at":1751815146753}},
"smart_blocks:web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{61}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08579388,-0.02192313,-0.04474436,-0.00116672,0.03591656,-0.0179353,-0.01616467,0.03186295,-0.04903743,-0.00820866,0.029919,-0.05020439,0.00697847,0.025735,0.01750211,-0.00058928,-0.01522744,-0.00889632,-0.0188418,0.0143664,0.07646198,-0.06503127,0.01540453,-0.05774515,0.07753734,0.03569915,0.0243706,-0.0502241,-0.02617537,-0.23894618,0.00436537,-0.05547846,0.0419254,-0.0175898,0.02302255,-0.04871944,-0.03202153,0.07685886,-0.10095832,0.01801469,0.04095588,-0.00439675,-0.0066117,-0.02327947,0.02948595,-0.08539596,0.02497839,-0.02573808,-0.03498382,-0.00881725,0.0054263,-0.06935139,-0.01360535,0.01529686,0.04443996,0.01189953,0.01996939,0.03800745,0.02042461,0.01814048,0.05614174,0.0366143,-0.23247543,0.04634627,0.09606054,0.07477038,-0.03473313,0.0595771,0.02058087,0.08502822,0.05177064,0.03214476,0.03087869,0.0177002,0.0206281,0.05783788,0.03736854,-0.04525128,-0.045032,-0.08137168,-0.07146855,0.03222138,-0.03412186,-0.00804906,-0.03151225,-0.00521594,0.01099702,-0.0652274,0.00538226,-0.02068647,0.00602856,0.01858578,0.06338027,0.02454541,-0.00218497,-0.0166263,0.03020313,0.03968751,-0.06242748,0.11587348,-0.01244865,0.06567471,-0.01132814,-0.05452389,0.06267003,-0.03265302,-0.02342933,-0.02745602,0.03134053,0.02667351,0.03493476,0.02252992,0.07600001,-0.03908768,0.01570296,0.03782897,0.0469019,0.0115257,-0.00192015,-0.00605283,0.03679242,0.0774195,0.00160296,-0.03045695,-0.06666848,-0.03172167,0.0351922,0.06022656,0.05400082,0.00416222,0.06306993,0.02363627,-0.0309141,0.0310666,0.00633783,-0.02177941,0.00297163,0.00647143,-0.07484274,0.01247001,0.00431162,-0.05246859,-0.02875607,-0.09862844,-0.03739554,0.0816247,0.01330715,-0.02212015,-0.00188848,-0.05237286,0.0083268,0.01504466,0.00848618,-0.04825126,0.02796576,0.02406749,0.07598338,0.08604303,-0.0572681,-0.00889734,-0.06041477,0.00472949,-0.02315772,0.15737714,0.06825539,-0.10145657,0.01000172,0.03513986,0.01212979,-0.0557702,0.02509906,0.00148086,-0.06995174,0.00434502,0.04383543,-0.07612672,-0.06706416,0.01989315,0.01366994,0.04057485,0.0075361,-0.01497024,-0.06782056,-0.01475074,-0.01369781,-0.0373128,-0.00428886,0.00418758,0.02455369,0.05709647,-0.06869741,0.0310887,-0.02353714,0.02965514,-0.06007456,-0.05359532,-0.02809145,-0.04366788,0.02807735,-0.01885171,0.0449756,0.04670162,-0.02203526,0.01301551,-0.07330346,0.01255516,-0.04489081,-0.05735452,0.03718128,0.04197209,-0.04177867,-0.00733361,-0.05635981,0.03861766,0.01527645,0.01304,0.04229943,0.00215502,-0.04265331,0.02435592,-0.01184822,-0.0062446,-0.05643791,-0.20441456,-0.03124044,0.00143823,0.02651686,0.04072136,-0.03135982,0.06838418,0.01889719,0.05052083,0.07429159,0.07138783,0.03001241,-0.05031895,0.01105361,0.00912279,0.01131466,0.00202394,-0.01965104,0.02912255,0.04233294,0.00912183,0.037202,0.00850304,-0.0851754,0.02606653,-0.04376427,0.11198677,0.01903621,0.0262794,-0.00818876,0.01744793,0.01030649,-0.02335142,-0.13293092,-0.01581309,0.07984281,0.05210633,-0.0401849,0.00175498,-0.05153763,-0.05848046,0.05735131,-0.00197828,-0.12356636,0.0302955,-0.0602532,-0.0777051,0.01476202,-0.04224417,0.03477108,0.00438151,0.05808657,0.05230387,0.08093142,-0.02597312,-0.01893675,-0.00055685,-0.06832287,0.01349305,0.02814618,-0.02170102,0.0111496,-0.06387832,0.01465832,0.05342684,0.01974098,0.01982362,0.02335364,0.0230007,-0.0369556,-0.02342717,0.08683459,0.05645296,-0.01718517,-0.00122615,0.02242426,-0.00855303,-0.09367655,0.04029511,-0.00993505,0.05229189,0.01394743,0.05206769,0.0573963,0.05291086,0.08933919,0.0360351,0.01220777,0.05065135,-0.00614161,-0.02292624,-0.01200136,-0.0531165,-0.08096834,0.05046277,0.00554664,-0.26728287,0.0017613,-0.00501256,-0.01732269,-0.00730227,-0.00123072,0.07004665,0.00934197,-0.07139588,-0.00562711,-0.0100739,0.05443806,-0.0118835,-0.02711001,0.01349729,0.00135328,0.07315866,0.0314962,0.02160416,0.05055715,-0.0220387,0.07532448,0.17143252,-0.02247593,0.07182311,0.03854525,-0.03135909,0.00911126,0.05059941,0.00998592,-0.02701468,-0.01556765,0.01989023,-0.0513061,0.00600941,0.06724932,-0.06851915,-0.01987967,0.00934797,0.01130204,-0.01350877,-0.04623517,0.00997406,0.02686161,0.12033928,-0.07052222,-0.06522406,-0.09332342,0.00243793,0.01643245,-0.04098384,-0.02221296,0.01338961,-0.00510862,-0.02114595,0.06073442,0.04722462,-0.02012385,-0.05051874,-0.02368718,-0.01288592,-0.00031609,-0.05857979,0.05282575,0.01304046],"last_embed":{"hash":"1kpccu0","tokens":277}}},"text":null,"length":0,"last_read":{"hash":"1kpccu0","at":1751815146765},"key":"web3/summary/research/Nockchain.md#**Nockchain 综合研究报告：可验证计算的新范式**##**第八部分：战略展望与最终建议**#**引用的著作**#{61}","lines":[353,353],"size":496,"outlinks":[{"title":"https://podcasts.musixmatch.com/podcast/the-delphi-podcast-01hpa65hx48s2p8649mhbr8ke5/episode/logan-allen-nockchains-global-competition-for-useful-01jvmp9agdsppxbp1n6gwtz1ck","target":"https://podcasts.musixmatch.com/podcast/the-delphi-podcast-01hpa65hx48s2p8649mhbr8ke5/episode/logan-allen-nockchains-global-competition-for-useful-01jvmp9agdsppxbp1n6gwtz1ck","line":1}],"class_name":"SmartBlock","last_embed":{"hash":"1kpccu0","at":1751815146765}},

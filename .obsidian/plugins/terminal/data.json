{"addToCommand": true, "addToContextMenu": true, "createInstanceNearExistingOnes": true, "errorNoticeTimeout": 0, "exposeInternalModules": true, "focusOnNewInstance": true, "hideStatusBar": "focused", "interceptLogging": true, "language": "", "newInstanceBehavior": "newHorizontalSplit", "noticeTimeout": 5, "openChangelogOnUpdate": true, "pinNewInstance": true, "preferredRenderer": "webgl", "profiles": {"darwinExternalDefault": {"args": ["\"$PWD\""], "executable": "/System/Applications/Utilities/Terminal.app/Contents/macOS/Terminal", "name": "", "platforms": {"darwin": true}, "restoreHistory": false, "successExitCodes": ["0", "SIGINT", "SIGTERM"], "terminalOptions": {"documentOverride": null}, "type": "external"}, "darwinIntegratedDefault": {"args": [], "executable": "/bin/zsh", "name": "", "platforms": {"darwin": true}, "pythonExecutable": "python3", "restoreHistory": false, "successExitCodes": ["0", "SIGINT", "SIGTERM"], "terminalOptions": {"documentOverride": null}, "type": "integrated", "useWin32Conhost": true}, "developerConsole": {"name": "", "restoreHistory": false, "successExitCodes": ["0", "SIGINT", "SIGTERM"], "terminalOptions": {"documentOverride": null}, "type": "developerConsole"}, "linuxExternalDefault": {"args": [], "executable": "xterm", "name": "", "platforms": {"linux": true}, "restoreHistory": false, "successExitCodes": ["0", "SIGINT", "SIGTERM"], "terminalOptions": {"documentOverride": null}, "type": "external"}, "linuxIntegratedDefault": {"args": [], "executable": "/bin/sh", "name": "", "platforms": {"linux": true}, "pythonExecutable": "python3", "restoreHistory": false, "successExitCodes": ["0", "SIGINT", "SIGTERM"], "terminalOptions": {"documentOverride": null}, "type": "integrated", "useWin32Conhost": true}, "win32ExternalDefault": {"args": [], "executable": "C:\\Windows\\System32\\cmd.exe", "name": "", "platforms": {"win32": true}, "restoreHistory": false, "successExitCodes": ["0", "SIGINT", "SIGTERM"], "terminalOptions": {"documentOverride": null}, "type": "external"}, "win32IntegratedDefault": {"args": [], "executable": "C:\\Windows\\System32\\cmd.exe", "name": "", "platforms": {"win32": true}, "pythonExecutable": "python3", "restoreHistory": false, "successExitCodes": ["0", "SIGINT", "SIGTERM"], "terminalOptions": {"documentOverride": null}, "type": "integrated", "useWin32Conhost": true}}}
{"recentFiles": [{"basename": "dashboard", "path": "web3/farm/dashboard.md"}, {"basename": "20250613-Origami.Leverage.iBGT", "path": "web3/farm/history/doing/2025/20250613-Origami.Leverage.iBGT.md"}, {"basename": "todo", "path": "todo.md"}, {"basename": "20250608-BitEqual.Mint.USDC", "path": "web3/farm/history/doing/2025/20250608-BitEqual.Mint.USDC.md"}, {"basename": "Pasted image 20250710103145", "path": "Pasted image 20250710103145.png"}, {"basename": "xStock.LP.TSLAx", "path": "web3/farm/pools/chain/xStock.LP.TSLAx.md"}, {"basename": "Backpack.Tade.Alpha", "path": "web3/farm/pools/cex/Backpack.Tade.Alpha.md"}, {"basename": "Binance.Trade.Alpha", "path": "web3/farm/pools/cex/Binance.Trade.Alpha.md"}, {"basename": "20250621-Hyperion.StableLP.USDT+USDC", "path": "web3/farm/history/doing/2025/20250621-Hyperion.StableLP.USDT+USDC.md"}, {"basename": "20250529-LPAgent.LP.SOL", "path": "web3/farm/history/doing/2025/20250529-LPAgent.LP.SOL.md"}, {"basename": "README_dashboard", "path": "web3/farm/README_dashboard.md"}, {"basename": "research_pools", "path": "web3/farm/research_pools.md"}, {"basename": "token_prices", "path": "web3/farm/data/token_prices.md"}, {"basename": "backup", "path": "backup/backup.md"}, {"basename": "dashboard_bak_from_trash_18_57", "path": "backup/dashboard_bak_from_trash_18_57.md"}, {"basename": "update_prices", "path": "web3/farm/update_prices.py"}, {"basename": "dashboard_bak", "path": "backup/dashboard_bak.md"}, {"basename": "补番 Nov 19 2024", "path": "todo/补番 Nov 19 2024.md"}, {"basename": "overview", "path": "backup/overview.md"}, {"basename": "template-farm-history", "path": "templates/template-farm-history.md"}, {"basename": "template-farm-pools", "path": "templates/template-farm-pools.md"}, {"basename": "20250512-Infrared.LP.iBGT", "path": "web3/farm/history/done/2025/20250512-Infrared.LP.iBGT.md"}, {"basename": "20250510-Cakepie.LP.BTC", "path": "web3/farm/history/done/2025/20250510-Cakepie.LP.BTC.md"}, {"basename": "20250310-Meteora.LP.wBTC+USDC", "path": "web3/farm/history/done/2025/20250310-Meteora.LP.wBTC+USDC.md"}, {"basename": "20250217-Cetus.LP.SUI+DEEP", "path": "web3/farm/history/done/2025/20250217-Cetus.LP.SUI+DEEP.md"}, {"basename": "20240919-Kamino.Lending.USDT", "path": "web3/farm/history/done/2024/20240919-Kamino.Lending.USDT.md"}, {"basename": "20240723-Pendle.Staking.PENDLE", "path": "web3/farm/history/done/2024/20240723-Pendle.Staking.PENDLE.md"}, {"basename": "20240723-Cake.Staking.CAKE", "path": "web3/farm/history/done/2024/20240723-Cake.Staking.CAKE.md"}, {"basename": "20240625-Pancake.StableLP.CAKE+mCAKE", "path": "web3/farm/history/done/2024/20240625-Pancake.StableLP.CAKE+mCAKE.md"}, {"basename": "20240622-<PERSON><PERSON>.Lending.USDT", "path": "web3/farm/history/done/2024/20240622-Aave.Lending.USDT.md"}, {"basename": "icbc", "path": "todo/icbc.md"}, {"basename": "risk_percentage", "path": "web3/farm/data/risk_percentage.md"}, {"basename": "Euler.Lending.USDT", "path": "web3/farm/pools/chain/Euler.Lending.USDT.md"}, {"basename": "20250528-Pear.Trade.USDT", "path": "web3/farm/history/done/2025/20250528-Pear.Trade.USDT.md"}, {"basename": "x+tg自动化", "path": "todo/x+tg自动化.md"}, {"basename": "AI 工具汇总", "path": "AI/AI 工具汇总.md"}, {"basename": "QuBit.Lending.RDO", "path": "web3/farm/pools/chain/QuBit.Lending.RDO.md"}, {"basename": "HappyFarm", "path": "backup/HappyFarm.md"}, {"basename": "20250621-Hyper<PERSON>.StableLP.USDT+USDC+vault 1", "path": "web3/farm/history/done/2025/20250621-Hyperion.StableLP.USDT+USDC+vault 1.md"}, {"basename": "<PERSON><PERSON><PERSON>", "path": "web3/summary/DeFi.md"}, {"basename": "Apex.Finance.USDT", "path": "web3/farm/pools/cex/Apex.Finance.USDT.md"}, {"basename": "invest_level_limit", "path": "web3/farm/data/invest_level_limit.md"}, {"basename": "Trade", "path": "web3/summary/Trade.md"}, {"basename": "defix 网站开发初始 prompt", "path": "web3/dev/defix/defix 网站开发初始 prompt.md"}, {"basename": "Rclone记录", "path": "todo/Rclone记录.md"}, {"basename": "OKX.Finance.抄底止盈策略", "path": "web3/farm/pools/cex/OKX.Finance.抄底止盈策略.md"}, {"basename": "Gate.Finance.量化基金", "path": "web3/farm/pools/cex/Gate.Finance.量化基金.md"}, {"basename": "Gate.Finance.链上赚币", "path": "web3/farm/pools/cex/Gate.Finance.链上赚币.md"}, {"basename": "Gate.Finance.简单赚币", "path": "web3/farm/pools/cex/Gate.Finance.简单赚币.md"}, {"basename": "Bitget.Staking.BGSOL", "path": "web3/farm/pools/cex/Bitget.Staking.BGSOL.md"}], "omittedPaths": [], "omittedTags": [], "updateOn": "file-open", "omitBookmarks": false}
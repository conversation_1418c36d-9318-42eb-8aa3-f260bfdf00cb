{"recentFiles": [{"basename": "20241108-Bucket.Leverage.SUI", "path": "web3/farm/history/2024/20241108-Bucket.Leverage.SUI.md"}, {"basename": "AI 工具汇总", "path": "AI/AI 工具汇总.md"}, {"basename": "overview", "path": "web3/farm/overview.md"}, {"basename": "dashboard", "path": "web3/farm/dashboard.md"}, {"basename": "20250621-Hyperion.StableLP.USDT+USDC+vault", "path": "web3/farm/history/2025/20250621-Hyperion.StableLP.USDT+USDC+vault.md"}, {"basename": "20250529-LPAgent.LP.SOL", "path": "web3/farm/history/2025/20250529-LPAgent.LP.SOL.md"}, {"basename": "20250613-Origami.Leverage.iBGT", "path": "web3/farm/history/2025/20250613-Origami.Leverage.iBGT.md"}, {"basename": "todo", "path": "todo.md"}, {"basename": "research_pools", "path": "web3/farm/research_pools.md"}, {"basename": "QuBit.Lending.RDO", "path": "web3/farm/pools/OnChain/QuBit.Lending.RDO.md"}, {"basename": "20250608-BitEqual.Mint.USDC", "path": "web3/farm/history/2025/20250608-BitEqual.Mint.USDC.md"}, {"basename": "HappyFarm", "path": "backup/HappyFarm.md"}, {"basename": "20250621-Hyper<PERSON>.StableLP.USDT+USDC+vault 1", "path": "web3/farm/history/2025/20250621-Hyperion.StableLP.USDT+USDC+vault 1.md"}, {"basename": "<PERSON><PERSON><PERSON>", "path": "web3/summary/DeFi.md"}, {"basename": "update_prices", "path": "web3/farm/update_prices.py"}, {"basename": "Apex.Finance.USDT", "path": "web3/farm/pools/CEX/Apex.Finance.USDT.md"}, {"basename": "20250625-Pancake.Mint.JAGER", "path": "web3/farm/history/2025/20250625-Pancake.Mint.JAGER.md"}, {"basename": "invest_level_limit", "path": "web3/farm/data/invest_level_limit.md"}, {"basename": "token_prices", "path": "web3/farm/data/token_prices.md"}, {"basename": "risk_percentage", "path": "web3/farm/data/risk_percentage.md"}, {"basename": "20240622-<PERSON><PERSON>.Lending.USDT", "path": "web3/farm/history/2024/20240622-Aave.Lending.USDT.md"}, {"basename": "template-farm-history", "path": "templates/template-farm-history.md"}, {"basename": "Trade", "path": "web3/summary/Trade.md"}, {"basename": "defix 网站开发初始 prompt", "path": "web3/dev/defix/defix 网站开发初始 prompt.md"}, {"basename": "Rclone记录", "path": "tmp/Rclone记录.md"}, {"basename": "OKX.Finance.抄底止盈策略", "path": "web3/farm/pools/CEX/OKX.Finance.抄底止盈策略.md"}, {"basename": "Gate.Finance.量化基金", "path": "web3/farm/pools/CEX/Gate.Finance.量化基金.md"}, {"basename": "Gate.Finance.链上赚币", "path": "web3/farm/pools/CEX/Gate.Finance.链上赚币.md"}, {"basename": "Gate.Finance.简单赚币", "path": "web3/farm/pools/CEX/Gate.Finance.简单赚币.md"}, {"basename": "Bitget.Staking.BGSOL", "path": "web3/farm/pools/CEX/Bitget.Staking.BGSOL.md"}, {"basename": "Bitget.LaunchPool.BGB", "path": "web3/farm/pools/CEX/Bitget.LaunchPool.BGB.md"}, {"basename": "Bitget.Finance.双币赢", "path": "web3/farm/pools/CEX/Bitget.Finance.双币赢.md"}, {"basename": "Binance.Staking.BNSOL", "path": "web3/farm/pools/CEX/Binance.Staking.BNSOL.md"}, {"basename": "Binance.Trade.BNB合约网格", "path": "web3/farm/pools/CEX/Binance.Trade.BNB合约网格.md"}, {"basename": "Binance.LaunchPool.BNB", "path": "web3/farm/pools/CEX/Binance.LaunchPool.BNB.md"}, {"basename": "Lighter.Finance.USDT", "path": "web3/farm/pools/CEX/Lighter.Finance.USDT.md"}, {"basename": "Aerodrome.ve33.AREO", "path": "web3/farm/pools/OnChain/Aerodrome.ve33.AREO.md"}, {"basename": "Aave.Lending.USDT", "path": "web3/farm/pools/OnChain/Aave.Lending.USDT.md"}, {"basename": "Pear.Trade.资金费套利", "path": "web3/farm/pools/OnChain/Pear.Trade.资金费套利.md"}, {"basename": "fProtocol.Finance.USDT->fxSave", "path": "web3/farm/pools/OnChain/fProtocol.Finance.USDT->fxSave.md"}, {"basename": "Extrafi.Leverage.USDT", "path": "web3/farm/pools/OnChain/Extrafi.Leverage.USDT.md"}, {"basename": "Tapp.StableLP.APT+kAPT", "path": "web3/farm/pools/OnChain/Tapp.StableLP.APT+kAPT.md"}, {"basename": "Momentum.LP.SUI+BTC", "path": "web3/farm/pools/OnChain/Momentum.LP.SUI+BTC.md"}, {"basename": "Pancake.LP.Alpha", "path": "web3/farm/pools/OnChain/Pancake.LP.Alpha.md"}, {"basename": "Equilibria.ve33.EQB", "path": "web3/farm/pools/OnChain/Equilibria.ve33.EQB.md"}, {"basename": "Turbos.LP.USDT+SUI", "path": "web3/farm/pools/OnChain/Turbos.LP.USDT+SUI.md"}, {"basename": "Turbos.StableLP.USDT+USDC", "path": "web3/farm/pools/OnChain/Turbos.StableLP.USDT+USDC.md"}, {"basename": "Turbos.LP.SUI+TURBOS", "path": "web3/farm/pools/OnChain/Turbos.LP.SUI+TURBOS.md"}, {"basename": "Save.Lending.USDT", "path": "web3/farm/pools/OnChain/Save.Lending.USDT.md"}, {"basename": "Penpie.ve33.PNP", "path": "web3/farm/pools/OnChain/Penpie.ve33.PNP.md"}], "omittedPaths": [], "omittedTags": [], "updateOn": "file-open", "omitBookmarks": false}
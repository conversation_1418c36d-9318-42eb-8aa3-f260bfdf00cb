{"recentFiles": [{"basename": "Apex.Vault", "path": "web3/farm/pools/CEX/Apex.Vault.md"}, {"basename": "template-farm-pools", "path": "templates/template-farm-pools.md"}, {"basename": "2025-06-27", "path": "daily/2025-06-27.md"}, {"basename": "2025-07-02", "path": "daily/2025-07-02.md"}, {"basename": "Happy Farm", "path": "web3/Happy Farm.md"}, {"basename": "Daily_Profit_Summary", "path": "web3/farm/summary/Daily_Profit_Summary.md"}, {"basename": "2025-06-29", "path": "daily/2025-06-29.md"}, {"basename": "2025-06-28", "path": "daily/2025-06-28.md"}, {"basename": "Aerodrome.AREO", "path": "web3/farm/pools/OnChain/Aerodrome.AREO.md"}, {"basename": "Equilibria.EQB", "path": "web3/farm/pools/OnChain/Equilibria.EQB.md"}, {"basename": "Penpie.PNP", "path": "web3/farm/pools/OnChain/Penpie.PNP.md"}, {"basename": "Meteoria.SOL", "path": "web3/farm/pools/OnChain/Meteoria.SOL.md"}, {"basename": "Hyperion.APT", "path": "web3/farm/pools/OnChain/Hyperion.APT.md"}, {"basename": "MMT.SUI", "path": "web3/farm/pools/OnChain/MMT.SUI.md"}, {"basename": "Pancake.Alpha", "path": "web3/farm/pools/OnChain/Pancake.Alpha.md"}, {"basename": "Infrared.iBGT", "path": "web3/farm/pools/OnChain/Infrared.iBGT.md"}, {"basename": "<PERSON><PERSON><PERSON>", "path": "web3/farm/pools/CEX/Lighter.Vault.md"}, {"basename": "fxSave.清算费", "path": "web3/farm/pools/OnChain/fxSave.清算费.md"}, {"basename": "Binance.BNB合约网格", "path": "web3/farm/pools/CEX/Binance.BNB合约网格.md"}, {"basename": "Pear.资金费套利", "path": "web3/farm/pools/OnChain/Pear.资金费套利.md"}, {"basename": "Harmonix.Hype", "path": "web3/farm/pools/OnChain/Harmonix.Hype.md"}, {"basename": "Trade", "path": "web3/Trade.md"}, {"basename": "OKX.抄底止盈策略", "path": "web3/farm/pools/CEX/OKX.抄底止盈策略.md"}, {"basename": "Pendle.套保", "path": "web3/farm/pools/OnChain/Pendle.套保.md"}, {"basename": "Aave.USDT", "path": "web3/farm/pools/OnChain/Aave.USDT.md"}, {"basename": "Turbos.SUI-TURBOS", "path": "web3/farm/pools/OnChain/Turbos.SUI-TURBOS.md"}, {"basename": "webthinker", "path": "tmp/webthinker.md"}, {"basename": "2025-06-27", "path": "2025-06-27.md"}, {"basename": "2025-06-28", "path": "2025-06-28.md"}, {"basename": "<PERSON><PERSON><PERSON>", "path": "web3/DeFi.md"}, {"basename": "DeFi三阶段", "path": "web3/summary/DeFi三阶段.md"}], "omittedPaths": [], "omittedTags": [], "updateOn": "file-open", "omitBookmarks": false}
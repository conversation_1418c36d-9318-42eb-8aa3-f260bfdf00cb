/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source visit the plugins github repository (https://github.com/phibr0/obsidian-dictionary)
*/

var vr=Object.create;var qe=Object.defineProperty;var xr=Object.getOwnPropertyDescriptor;var wr=Object.getOwnPropertyNames;var br=Object.getPrototypeOf,Sr=Object.prototype.hasOwnProperty;var $t=n=>qe(n,"__esModule",{value:!0});var Ye=(n,e)=>()=>(e||n((e={exports:{}}).exports,e),e.exports),Cr=(n,e)=>{$t(n);for(var t in e)qe(n,t,{get:e[t],enumerable:!0})},jr=(n,e,t)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of wr(e))!Sr.call(n,i)&&i!=="default"&&qe(n,i,{get:()=>e[i],enumerable:!(t=xr(e,i))||t.enumerable});return n},G=n=>jr($t(qe(n!=null?vr(br(n)):{},"default",n&&n.__esModule&&"default"in n?{get:()=>n.default,enumerable:!0}:{value:n,enumerable:!0})),n);var M=(n,e,t)=>new Promise((i,r)=>{var o=l=>{try{s(t.next(l))}catch(c){r(c)}},a=l=>{try{s(t.throw(l))}catch(c){r(c)}},s=l=>l.done?i(l.value):Promise.resolve(l.value).then(o,a);s((t=t.apply(n,e)).next())});var fi=Ye((_a,rt)=>{var $n,Gn,qn,Yn,Jn,Kn,Qn,Xn,Zn,nt,Dt,ei,ti,ni,Ee,ii,ri,oi,ai,si,li,ci,ui,di,it;(function(n){var e=typeof global=="object"?global:typeof self=="object"?self:typeof this=="object"?this:{};typeof define=="function"&&define.amd?define("tslib",["exports"],function(i){n(t(e,t(i)))}):typeof rt=="object"&&typeof rt.exports=="object"?n(t(e,t(rt.exports))):n(t(e));function t(i,r){return i!==e&&(typeof Object.create=="function"?Object.defineProperty(i,"__esModule",{value:!0}):i.__esModule=!0),function(o,a){return i[o]=r?r(o,a):a}}})(function(n){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,r){i.__proto__=r}||function(i,r){for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(i[o]=r[o])};$n=function(i,r){if(typeof r!="function"&&r!==null)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");e(i,r);function o(){this.constructor=i}i.prototype=r===null?Object.create(r):(o.prototype=r.prototype,new o)},Gn=Object.assign||function(i){for(var r,o=1,a=arguments.length;o<a;o++){r=arguments[o];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(i[s]=r[s])}return i},qn=function(i,r){var o={};for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&r.indexOf(a)<0&&(o[a]=i[a]);if(i!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,a=Object.getOwnPropertySymbols(i);s<a.length;s++)r.indexOf(a[s])<0&&Object.prototype.propertyIsEnumerable.call(i,a[s])&&(o[a[s]]=i[a[s]]);return o},Yn=function(i,r,o,a){var s=arguments.length,l=s<3?r:a===null?a=Object.getOwnPropertyDescriptor(r,o):a,c;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")l=Reflect.decorate(i,r,o,a);else for(var u=i.length-1;u>=0;u--)(c=i[u])&&(l=(s<3?c(l):s>3?c(r,o,l):c(r,o))||l);return s>3&&l&&Object.defineProperty(r,o,l),l},Jn=function(i,r){return function(o,a){r(o,a,i)}},Kn=function(i,r){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(i,r)},Qn=function(i,r,o,a){function s(l){return l instanceof o?l:new o(function(c){c(l)})}return new(o||(o=Promise))(function(l,c){function u(y){try{p(a.next(y))}catch(g){c(g)}}function f(y){try{p(a.throw(y))}catch(g){c(g)}}function p(y){y.done?l(y.value):s(y.value).then(u,f)}p((a=a.apply(i,r||[])).next())})},Xn=function(i,r){var o={label:0,sent:function(){if(l[0]&1)throw l[1];return l[1]},trys:[],ops:[]},a,s,l,c;return c={next:u(0),throw:u(1),return:u(2)},typeof Symbol=="function"&&(c[Symbol.iterator]=function(){return this}),c;function u(p){return function(y){return f([p,y])}}function f(p){if(a)throw new TypeError("Generator is already executing.");for(;c&&(c=0,p[0]&&(o=0)),o;)try{if(a=1,s&&(l=p[0]&2?s.return:p[0]?s.throw||((l=s.return)&&l.call(s),0):s.next)&&!(l=l.call(s,p[1])).done)return l;switch(s=0,l&&(p=[p[0]&2,l.value]),p[0]){case 0:case 1:l=p;break;case 4:return o.label++,{value:p[1],done:!1};case 5:o.label++,s=p[1],p=[0];continue;case 7:p=o.ops.pop(),o.trys.pop();continue;default:if(l=o.trys,!(l=l.length>0&&l[l.length-1])&&(p[0]===6||p[0]===2)){o=0;continue}if(p[0]===3&&(!l||p[1]>l[0]&&p[1]<l[3])){o.label=p[1];break}if(p[0]===6&&o.label<l[1]){o.label=l[1],l=p;break}if(l&&o.label<l[2]){o.label=l[2],o.ops.push(p);break}l[2]&&o.ops.pop(),o.trys.pop();continue}p=r.call(i,o)}catch(y){p=[6,y],s=0}finally{a=l=0}if(p[0]&5)throw p[1];return{value:p[0]?p[1]:void 0,done:!0}}},Zn=function(i,r){for(var o in i)o!=="default"&&!Object.prototype.hasOwnProperty.call(r,o)&&it(r,i,o)},it=Object.create?function(i,r,o,a){a===void 0&&(a=o);var s=Object.getOwnPropertyDescriptor(r,o);(!s||("get"in s?!r.__esModule:s.writable||s.configurable))&&(s={enumerable:!0,get:function(){return r[o]}}),Object.defineProperty(i,a,s)}:function(i,r,o,a){a===void 0&&(a=o),i[a]=r[o]},nt=function(i){var r=typeof Symbol=="function"&&Symbol.iterator,o=r&&i[r],a=0;if(o)return o.call(i);if(i&&typeof i.length=="number")return{next:function(){return i&&a>=i.length&&(i=void 0),{value:i&&i[a++],done:!i}}};throw new TypeError(r?"Object is not iterable.":"Symbol.iterator is not defined.")},Dt=function(i,r){var o=typeof Symbol=="function"&&i[Symbol.iterator];if(!o)return i;var a=o.call(i),s,l=[],c;try{for(;(r===void 0||r-- >0)&&!(s=a.next()).done;)l.push(s.value)}catch(u){c={error:u}}finally{try{s&&!s.done&&(o=a.return)&&o.call(a)}finally{if(c)throw c.error}}return l},ei=function(){for(var i=[],r=0;r<arguments.length;r++)i=i.concat(Dt(arguments[r]));return i},ti=function(){for(var i=0,r=0,o=arguments.length;r<o;r++)i+=arguments[r].length;for(var a=Array(i),s=0,r=0;r<o;r++)for(var l=arguments[r],c=0,u=l.length;c<u;c++,s++)a[s]=l[c];return a},ni=function(i,r,o){if(o||arguments.length===2)for(var a=0,s=r.length,l;a<s;a++)(l||!(a in r))&&(l||(l=Array.prototype.slice.call(r,0,a)),l[a]=r[a]);return i.concat(l||Array.prototype.slice.call(r))},Ee=function(i){return this instanceof Ee?(this.v=i,this):new Ee(i)},ii=function(i,r,o){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var a=o.apply(i,r||[]),s,l=[];return s={},c("next"),c("throw"),c("return"),s[Symbol.asyncIterator]=function(){return this},s;function c(v){a[v]&&(s[v]=function(m){return new Promise(function(x,C){l.push([v,m,x,C])>1||u(v,m)})})}function u(v,m){try{f(a[v](m))}catch(x){g(l[0][3],x)}}function f(v){v.value instanceof Ee?Promise.resolve(v.value.v).then(p,y):g(l[0][2],v)}function p(v){u("next",v)}function y(v){u("throw",v)}function g(v,m){v(m),l.shift(),l.length&&u(l[0][0],l[0][1])}},ri=function(i){var r,o;return r={},a("next"),a("throw",function(s){throw s}),a("return"),r[Symbol.iterator]=function(){return this},r;function a(s,l){r[s]=i[s]?function(c){return(o=!o)?{value:Ee(i[s](c)),done:s==="return"}:l?l(c):c}:l}},oi=function(i){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=i[Symbol.asyncIterator],o;return r?r.call(i):(i=typeof nt=="function"?nt(i):i[Symbol.iterator](),o={},a("next"),a("throw"),a("return"),o[Symbol.asyncIterator]=function(){return this},o);function a(l){o[l]=i[l]&&function(c){return new Promise(function(u,f){c=i[l](c),s(u,f,c.done,c.value)})}}function s(l,c,u,f){Promise.resolve(f).then(function(p){l({value:p,done:u})},c)}},ai=function(i,r){return Object.defineProperty?Object.defineProperty(i,"raw",{value:r}):i.raw=r,i};var t=Object.create?function(i,r){Object.defineProperty(i,"default",{enumerable:!0,value:r})}:function(i,r){i.default=r};si=function(i){if(i&&i.__esModule)return i;var r={};if(i!=null)for(var o in i)o!=="default"&&Object.prototype.hasOwnProperty.call(i,o)&&it(r,i,o);return t(r,i),r},li=function(i){return i&&i.__esModule?i:{default:i}},ci=function(i,r,o,a){if(o==="a"&&!a)throw new TypeError("Private accessor was defined without a getter");if(typeof r=="function"?i!==r||!a:!r.has(i))throw new TypeError("Cannot read private member from an object whose class did not declare it");return o==="m"?a:o==="a"?a.call(i):a?a.value:r.get(i)},ui=function(i,r,o,a,s){if(a==="m")throw new TypeError("Private method is not writable");if(a==="a"&&!s)throw new TypeError("Private accessor was defined without a setter");if(typeof r=="function"?i!==r||!s:!r.has(i))throw new TypeError("Cannot write private member to an object whose class did not declare it");return a==="a"?s.call(i,o):s?s.value=o:r.set(i,o),o},di=function(i,r){if(r===null||typeof r!="object"&&typeof r!="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof i=="function"?r===i:i.has(r)},n("__extends",$n),n("__assign",Gn),n("__rest",qn),n("__decorate",Yn),n("__param",Jn),n("__metadata",Kn),n("__awaiter",Qn),n("__generator",Xn),n("__exportStar",Zn),n("__createBinding",it),n("__values",nt),n("__read",Dt),n("__spread",ei),n("__spreadArrays",ti),n("__spreadArray",ni),n("__await",Ee),n("__asyncGenerator",ii),n("__asyncDelegator",ri),n("__asyncValues",oi),n("__makeTemplateObject",ai),n("__importStar",si),n("__importDefault",li),n("__classPrivateFieldGet",ci),n("__classPrivateFieldSet",ui),n("__classPrivateFieldIn",di)})});var wi=Ye(($e,It)=>{(function(e,t){typeof $e=="object"&&typeof It=="object"?It.exports=t():typeof define=="function"&&define.amd?define([],t):typeof $e=="object"?$e.feather=t():e.feather=t()})(typeof self!="undefined"?self:$e,function(){return function(n){var e={};function t(i){if(e[i])return e[i].exports;var r=e[i]={i,l:!1,exports:{}};return n[i].call(r.exports,r,r.exports,t),r.l=!0,r.exports}return t.m=n,t.c=e,t.d=function(i,r,o){t.o(i,r)||Object.defineProperty(i,r,{configurable:!1,enumerable:!0,get:o})},t.r=function(i){Object.defineProperty(i,"__esModule",{value:!0})},t.n=function(i){var r=i&&i.__esModule?function(){return i.default}:function(){return i};return t.d(r,"a",r),r},t.o=function(i,r){return Object.prototype.hasOwnProperty.call(i,r)},t.p="",t(t.s=0)}({"./dist/icons.json":function(n){n.exports={activity:'<polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>',airplay:'<path d="M5 17H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-1"></path><polygon points="12 15 17 21 7 21 12 15"></polygon>',"alert-circle":'<circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line>',"alert-octagon":'<polygon points="7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2"></polygon><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line>',"alert-triangle":'<path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line>',"align-center":'<line x1="18" y1="10" x2="6" y2="10"></line><line x1="21" y1="6" x2="3" y2="6"></line><line x1="21" y1="14" x2="3" y2="14"></line><line x1="18" y1="18" x2="6" y2="18"></line>',"align-justify":'<line x1="21" y1="10" x2="3" y2="10"></line><line x1="21" y1="6" x2="3" y2="6"></line><line x1="21" y1="14" x2="3" y2="14"></line><line x1="21" y1="18" x2="3" y2="18"></line>',"align-left":'<line x1="17" y1="10" x2="3" y2="10"></line><line x1="21" y1="6" x2="3" y2="6"></line><line x1="21" y1="14" x2="3" y2="14"></line><line x1="17" y1="18" x2="3" y2="18"></line>',"align-right":'<line x1="21" y1="10" x2="7" y2="10"></line><line x1="21" y1="6" x2="3" y2="6"></line><line x1="21" y1="14" x2="3" y2="14"></line><line x1="21" y1="18" x2="7" y2="18"></line>',anchor:'<circle cx="12" cy="5" r="3"></circle><line x1="12" y1="22" x2="12" y2="8"></line><path d="M5 12H2a10 10 0 0 0 20 0h-3"></path>',aperture:'<circle cx="12" cy="12" r="10"></circle><line x1="14.31" y1="8" x2="20.05" y2="17.94"></line><line x1="9.69" y1="8" x2="21.17" y2="8"></line><line x1="7.38" y1="12" x2="13.12" y2="2.06"></line><line x1="9.69" y1="16" x2="3.95" y2="6.06"></line><line x1="14.31" y1="16" x2="2.83" y2="16"></line><line x1="16.62" y1="12" x2="10.88" y2="21.94"></line>',archive:'<polyline points="21 8 21 21 3 21 3 8"></polyline><rect x="1" y="3" width="22" height="5"></rect><line x1="10" y1="12" x2="14" y2="12"></line>',"arrow-down-circle":'<circle cx="12" cy="12" r="10"></circle><polyline points="8 12 12 16 16 12"></polyline><line x1="12" y1="8" x2="12" y2="16"></line>',"arrow-down-left":'<line x1="17" y1="7" x2="7" y2="17"></line><polyline points="17 17 7 17 7 7"></polyline>',"arrow-down-right":'<line x1="7" y1="7" x2="17" y2="17"></line><polyline points="17 7 17 17 7 17"></polyline>',"arrow-down":'<line x1="12" y1="5" x2="12" y2="19"></line><polyline points="19 12 12 19 5 12"></polyline>',"arrow-left-circle":'<circle cx="12" cy="12" r="10"></circle><polyline points="12 8 8 12 12 16"></polyline><line x1="16" y1="12" x2="8" y2="12"></line>',"arrow-left":'<line x1="19" y1="12" x2="5" y2="12"></line><polyline points="12 19 5 12 12 5"></polyline>',"arrow-right-circle":'<circle cx="12" cy="12" r="10"></circle><polyline points="12 16 16 12 12 8"></polyline><line x1="8" y1="12" x2="16" y2="12"></line>',"arrow-right":'<line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline>',"arrow-up-circle":'<circle cx="12" cy="12" r="10"></circle><polyline points="16 12 12 8 8 12"></polyline><line x1="12" y1="16" x2="12" y2="8"></line>',"arrow-up-left":'<line x1="17" y1="17" x2="7" y2="7"></line><polyline points="7 17 7 7 17 7"></polyline>',"arrow-up-right":'<line x1="7" y1="17" x2="17" y2="7"></line><polyline points="7 7 17 7 17 17"></polyline>',"arrow-up":'<line x1="12" y1="19" x2="12" y2="5"></line><polyline points="5 12 12 5 19 12"></polyline>',"at-sign":'<circle cx="12" cy="12" r="4"></circle><path d="M16 8v5a3 3 0 0 0 6 0v-1a10 10 0 1 0-3.92 7.94"></path>',award:'<circle cx="12" cy="8" r="7"></circle><polyline points="8.21 13.89 7 23 12 20 17 23 15.79 13.88"></polyline>',"bar-chart-2":'<line x1="18" y1="20" x2="18" y2="10"></line><line x1="12" y1="20" x2="12" y2="4"></line><line x1="6" y1="20" x2="6" y2="14"></line>',"bar-chart":'<line x1="12" y1="20" x2="12" y2="10"></line><line x1="18" y1="20" x2="18" y2="4"></line><line x1="6" y1="20" x2="6" y2="16"></line>',"battery-charging":'<path d="M5 18H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h3.19M15 6h2a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-3.19"></path><line x1="23" y1="13" x2="23" y2="11"></line><polyline points="11 6 7 12 13 12 9 18"></polyline>',battery:'<rect x="1" y="6" width="18" height="12" rx="2" ry="2"></rect><line x1="23" y1="13" x2="23" y2="11"></line>',"bell-off":'<path d="M13.73 21a2 2 0 0 1-3.46 0"></path><path d="M18.63 13A17.89 17.89 0 0 1 18 8"></path><path d="M6.26 6.26A5.86 5.86 0 0 0 6 8c0 7-3 9-3 9h14"></path><path d="M18 8a6 6 0 0 0-9.33-5"></path><line x1="1" y1="1" x2="23" y2="23"></line>',bell:'<path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path><path d="M13.73 21a2 2 0 0 1-3.46 0"></path>',bluetooth:'<polyline points="6.5 6.5 17.5 17.5 12 23 12 1 17.5 6.5 6.5 17.5"></polyline>',bold:'<path d="M6 4h8a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"></path><path d="M6 12h9a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"></path>',"book-open":'<path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path><path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>',book:'<path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path><path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path>',bookmark:'<path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>',box:'<path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path><polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline><line x1="12" y1="22.08" x2="12" y2="12"></line>',briefcase:'<rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect><path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>',calendar:'<rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line>',"camera-off":'<line x1="1" y1="1" x2="23" y2="23"></line><path d="M21 21H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h3m3-3h6l2 3h4a2 2 0 0 1 2 2v9.34m-7.72-2.06a4 4 0 1 1-5.56-5.56"></path>',camera:'<path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path><circle cx="12" cy="13" r="4"></circle>',cast:'<path d="M2 16.1A5 5 0 0 1 5.9 20M2 12.05A9 9 0 0 1 9.95 20M2 8V6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2h-6"></path><line x1="2" y1="20" x2="2.01" y2="20"></line>',"check-circle":'<path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline>',"check-square":'<polyline points="9 11 12 14 22 4"></polyline><path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>',check:'<polyline points="20 6 9 17 4 12"></polyline>',"chevron-down":'<polyline points="6 9 12 15 18 9"></polyline>',"chevron-left":'<polyline points="15 18 9 12 15 6"></polyline>',"chevron-right":'<polyline points="9 18 15 12 9 6"></polyline>',"chevron-up":'<polyline points="18 15 12 9 6 15"></polyline>',"chevrons-down":'<polyline points="7 13 12 18 17 13"></polyline><polyline points="7 6 12 11 17 6"></polyline>',"chevrons-left":'<polyline points="11 17 6 12 11 7"></polyline><polyline points="18 17 13 12 18 7"></polyline>',"chevrons-right":'<polyline points="13 17 18 12 13 7"></polyline><polyline points="6 17 11 12 6 7"></polyline>',"chevrons-up":'<polyline points="17 11 12 6 7 11"></polyline><polyline points="17 18 12 13 7 18"></polyline>',chrome:'<circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="12" r="4"></circle><line x1="21.17" y1="8" x2="12" y2="8"></line><line x1="3.95" y1="6.06" x2="8.54" y2="14"></line><line x1="10.88" y1="21.94" x2="15.46" y2="14"></line>',circle:'<circle cx="12" cy="12" r="10"></circle>',clipboard:'<path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path><rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>',clock:'<circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline>',"cloud-drizzle":'<line x1="8" y1="19" x2="8" y2="21"></line><line x1="8" y1="13" x2="8" y2="15"></line><line x1="16" y1="19" x2="16" y2="21"></line><line x1="16" y1="13" x2="16" y2="15"></line><line x1="12" y1="21" x2="12" y2="23"></line><line x1="12" y1="15" x2="12" y2="17"></line><path d="M20 16.58A5 5 0 0 0 18 7h-1.26A8 8 0 1 0 4 15.25"></path>',"cloud-lightning":'<path d="M19 16.9A5 5 0 0 0 18 7h-1.26a8 8 0 1 0-11.62 9"></path><polyline points="13 11 9 17 15 17 11 23"></polyline>',"cloud-off":'<path d="M22.61 16.95A5 5 0 0 0 18 10h-1.26a8 8 0 0 0-7.05-6M5 5a8 8 0 0 0 4 15h9a5 5 0 0 0 1.7-.3"></path><line x1="1" y1="1" x2="23" y2="23"></line>',"cloud-rain":'<line x1="16" y1="13" x2="16" y2="21"></line><line x1="8" y1="13" x2="8" y2="21"></line><line x1="12" y1="15" x2="12" y2="23"></line><path d="M20 16.58A5 5 0 0 0 18 7h-1.26A8 8 0 1 0 4 15.25"></path>',"cloud-snow":'<path d="M20 17.58A5 5 0 0 0 18 8h-1.26A8 8 0 1 0 4 16.25"></path><line x1="8" y1="16" x2="8.01" y2="16"></line><line x1="8" y1="20" x2="8.01" y2="20"></line><line x1="12" y1="18" x2="12.01" y2="18"></line><line x1="12" y1="22" x2="12.01" y2="22"></line><line x1="16" y1="16" x2="16.01" y2="16"></line><line x1="16" y1="20" x2="16.01" y2="20"></line>',cloud:'<path d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z"></path>',code:'<polyline points="16 18 22 12 16 6"></polyline><polyline points="8 6 2 12 8 18"></polyline>',codepen:'<polygon points="12 2 22 8.5 22 15.5 12 22 2 15.5 2 8.5 12 2"></polygon><line x1="12" y1="22" x2="12" y2="15.5"></line><polyline points="22 8.5 12 15.5 2 8.5"></polyline><polyline points="2 15.5 12 8.5 22 15.5"></polyline><line x1="12" y1="2" x2="12" y2="8.5"></line>',codesandbox:'<path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path><polyline points="7.5 4.21 12 6.81 16.5 4.21"></polyline><polyline points="7.5 19.79 7.5 14.6 3 12"></polyline><polyline points="21 12 16.5 14.6 16.5 19.79"></polyline><polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline><line x1="12" y1="22.08" x2="12" y2="12"></line>',coffee:'<path d="M18 8h1a4 4 0 0 1 0 8h-1"></path><path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"></path><line x1="6" y1="1" x2="6" y2="4"></line><line x1="10" y1="1" x2="10" y2="4"></line><line x1="14" y1="1" x2="14" y2="4"></line>',columns:'<path d="M12 3h7a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-7m0-18H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h7m0-18v18"></path>',command:'<path d="M18 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3 3 3 0 0 0 3-3 3 3 0 0 0-3-3H6a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 3-3V6a3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3h12a3 3 0 0 0 3-3 3 3 0 0 0-3-3z"></path>',compass:'<circle cx="12" cy="12" r="10"></circle><polygon points="16.24 7.76 14.12 14.12 7.76 16.24 9.88 9.88 16.24 7.76"></polygon>',copy:'<rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect><path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>',"corner-down-left":'<polyline points="9 10 4 15 9 20"></polyline><path d="M20 4v7a4 4 0 0 1-4 4H4"></path>',"corner-down-right":'<polyline points="15 10 20 15 15 20"></polyline><path d="M4 4v7a4 4 0 0 0 4 4h12"></path>',"corner-left-down":'<polyline points="14 15 9 20 4 15"></polyline><path d="M20 4h-7a4 4 0 0 0-4 4v12"></path>',"corner-left-up":'<polyline points="14 9 9 4 4 9"></polyline><path d="M20 20h-7a4 4 0 0 1-4-4V4"></path>',"corner-right-down":'<polyline points="10 15 15 20 20 15"></polyline><path d="M4 4h7a4 4 0 0 1 4 4v12"></path>',"corner-right-up":'<polyline points="10 9 15 4 20 9"></polyline><path d="M4 20h7a4 4 0 0 0 4-4V4"></path>',"corner-up-left":'<polyline points="9 14 4 9 9 4"></polyline><path d="M20 20v-7a4 4 0 0 0-4-4H4"></path>',"corner-up-right":'<polyline points="15 14 20 9 15 4"></polyline><path d="M4 20v-7a4 4 0 0 1 4-4h12"></path>',cpu:'<rect x="4" y="4" width="16" height="16" rx="2" ry="2"></rect><rect x="9" y="9" width="6" height="6"></rect><line x1="9" y1="1" x2="9" y2="4"></line><line x1="15" y1="1" x2="15" y2="4"></line><line x1="9" y1="20" x2="9" y2="23"></line><line x1="15" y1="20" x2="15" y2="23"></line><line x1="20" y1="9" x2="23" y2="9"></line><line x1="20" y1="14" x2="23" y2="14"></line><line x1="1" y1="9" x2="4" y2="9"></line><line x1="1" y1="14" x2="4" y2="14"></line>',"credit-card":'<rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect><line x1="1" y1="10" x2="23" y2="10"></line>',crop:'<path d="M6.13 1L6 16a2 2 0 0 0 2 2h15"></path><path d="M1 6.13L16 6a2 2 0 0 1 2 2v15"></path>',crosshair:'<circle cx="12" cy="12" r="10"></circle><line x1="22" y1="12" x2="18" y2="12"></line><line x1="6" y1="12" x2="2" y2="12"></line><line x1="12" y1="6" x2="12" y2="2"></line><line x1="12" y1="22" x2="12" y2="18"></line>',database:'<ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"></path><path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"></path>',delete:'<path d="M21 4H8l-7 8 7 8h13a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2z"></path><line x1="18" y1="9" x2="12" y2="15"></line><line x1="12" y1="9" x2="18" y2="15"></line>',disc:'<circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="12" r="3"></circle>',"divide-circle":'<line x1="8" y1="12" x2="16" y2="12"></line><line x1="12" y1="16" x2="12" y2="16"></line><line x1="12" y1="8" x2="12" y2="8"></line><circle cx="12" cy="12" r="10"></circle>',"divide-square":'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="8" y1="12" x2="16" y2="12"></line><line x1="12" y1="16" x2="12" y2="16"></line><line x1="12" y1="8" x2="12" y2="8"></line>',divide:'<circle cx="12" cy="6" r="2"></circle><line x1="5" y1="12" x2="19" y2="12"></line><circle cx="12" cy="18" r="2"></circle>',"dollar-sign":'<line x1="12" y1="1" x2="12" y2="23"></line><path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>',"download-cloud":'<polyline points="8 17 12 21 16 17"></polyline><line x1="12" y1="12" x2="12" y2="21"></line><path d="M20.88 18.09A5 5 0 0 0 18 9h-1.26A8 8 0 1 0 3 16.29"></path>',download:'<path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" y1="15" x2="12" y2="3"></line>',dribbble:'<circle cx="12" cy="12" r="10"></circle><path d="M8.56 2.75c4.37 6.03 6.02 9.42 8.03 17.72m2.54-15.38c-3.72 4.35-8.94 5.66-16.88 5.85m19.5 1.9c-3.5-.93-6.63-.82-8.94 0-2.58.92-5.01 2.86-7.44 6.32"></path>',droplet:'<path d="M12 2.69l5.66 5.66a8 8 0 1 1-11.31 0z"></path>',"edit-2":'<path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path>',"edit-3":'<path d="M12 20h9"></path><path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>',edit:'<path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>',"external-link":'<path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" y1="14" x2="21" y2="3"></line>',"eye-off":'<path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path><line x1="1" y1="1" x2="23" y2="23"></line>',eye:'<path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path><circle cx="12" cy="12" r="3"></circle>',facebook:'<path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>',"fast-forward":'<polygon points="13 19 22 12 13 5 13 19"></polygon><polygon points="2 19 11 12 2 5 2 19"></polygon>',feather:'<path d="M20.24 12.24a6 6 0 0 0-8.49-8.49L5 10.5V19h8.5z"></path><line x1="16" y1="8" x2="2" y2="22"></line><line x1="17.5" y1="15" x2="9" y2="15"></line>',figma:'<path d="M5 5.5A3.5 3.5 0 0 1 8.5 2H12v7H8.5A3.5 3.5 0 0 1 5 5.5z"></path><path d="M12 2h3.5a3.5 3.5 0 1 1 0 7H12V2z"></path><path d="M12 12.5a3.5 3.5 0 1 1 7 0 3.5 3.5 0 1 1-7 0z"></path><path d="M5 19.5A3.5 3.5 0 0 1 8.5 16H12v3.5a3.5 3.5 0 1 1-7 0z"></path><path d="M5 12.5A3.5 3.5 0 0 1 8.5 9H12v7H8.5A3.5 3.5 0 0 1 5 12.5z"></path>',"file-minus":'<path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="9" y1="15" x2="15" y2="15"></line>',"file-plus":'<path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="12" y1="18" x2="12" y2="12"></line><line x1="9" y1="15" x2="15" y2="15"></line>',"file-text":'<path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline>',file:'<path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path><polyline points="13 2 13 9 20 9"></polyline>',film:'<rect x="2" y="2" width="20" height="20" rx="2.18" ry="2.18"></rect><line x1="7" y1="2" x2="7" y2="22"></line><line x1="17" y1="2" x2="17" y2="22"></line><line x1="2" y1="12" x2="22" y2="12"></line><line x1="2" y1="7" x2="7" y2="7"></line><line x1="2" y1="17" x2="7" y2="17"></line><line x1="17" y1="17" x2="22" y2="17"></line><line x1="17" y1="7" x2="22" y2="7"></line>',filter:'<polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>',flag:'<path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"></path><line x1="4" y1="22" x2="4" y2="15"></line>',"folder-minus":'<path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path><line x1="9" y1="14" x2="15" y2="14"></line>',"folder-plus":'<path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path><line x1="12" y1="11" x2="12" y2="17"></line><line x1="9" y1="14" x2="15" y2="14"></line>',folder:'<path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>',framer:'<path d="M5 16V9h14V2H5l14 14h-7m-7 0l7 7v-7m-7 0h7"></path>',frown:'<circle cx="12" cy="12" r="10"></circle><path d="M16 16s-1.5-2-4-2-4 2-4 2"></path><line x1="9" y1="9" x2="9.01" y2="9"></line><line x1="15" y1="9" x2="15.01" y2="9"></line>',gift:'<polyline points="20 12 20 22 4 22 4 12"></polyline><rect x="2" y="7" width="20" height="5"></rect><line x1="12" y1="22" x2="12" y2="7"></line><path d="M12 7H7.5a2.5 2.5 0 0 1 0-5C11 2 12 7 12 7z"></path><path d="M12 7h4.5a2.5 2.5 0 0 0 0-5C13 2 12 7 12 7z"></path>',"git-branch":'<line x1="6" y1="3" x2="6" y2="15"></line><circle cx="18" cy="6" r="3"></circle><circle cx="6" cy="18" r="3"></circle><path d="M18 9a9 9 0 0 1-9 9"></path>',"git-commit":'<circle cx="12" cy="12" r="4"></circle><line x1="1.05" y1="12" x2="7" y2="12"></line><line x1="17.01" y1="12" x2="22.96" y2="12"></line>',"git-merge":'<circle cx="18" cy="18" r="3"></circle><circle cx="6" cy="6" r="3"></circle><path d="M6 21V9a9 9 0 0 0 9 9"></path>',"git-pull-request":'<circle cx="18" cy="18" r="3"></circle><circle cx="6" cy="6" r="3"></circle><path d="M13 6h3a2 2 0 0 1 2 2v7"></path><line x1="6" y1="9" x2="6" y2="21"></line>',github:'<path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>',gitlab:'<path d="M22.65 14.39L12 22.13 1.35 14.39a.84.84 0 0 1-.3-.94l1.22-3.78 2.44-7.51A.42.42 0 0 1 4.82 2a.43.43 0 0 1 .58 0 .42.42 0 0 1 .11.18l2.44 7.49h8.1l2.44-7.51A.42.42 0 0 1 18.6 2a.43.43 0 0 1 .58 0 .42.42 0 0 1 .11.18l2.44 7.51L23 13.45a.84.84 0 0 1-.35.94z"></path>',globe:'<circle cx="12" cy="12" r="10"></circle><line x1="2" y1="12" x2="22" y2="12"></line><path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>',grid:'<rect x="3" y="3" width="7" height="7"></rect><rect x="14" y="3" width="7" height="7"></rect><rect x="14" y="14" width="7" height="7"></rect><rect x="3" y="14" width="7" height="7"></rect>',"hard-drive":'<line x1="22" y1="12" x2="2" y2="12"></line><path d="M5.45 5.11L2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z"></path><line x1="6" y1="16" x2="6.01" y2="16"></line><line x1="10" y1="16" x2="10.01" y2="16"></line>',hash:'<line x1="4" y1="9" x2="20" y2="9"></line><line x1="4" y1="15" x2="20" y2="15"></line><line x1="10" y1="3" x2="8" y2="21"></line><line x1="16" y1="3" x2="14" y2="21"></line>',headphones:'<path d="M3 18v-6a9 9 0 0 1 18 0v6"></path><path d="M21 19a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3zM3 19a2 2 0 0 0 2 2h1a2 2 0 0 0 2-2v-3a2 2 0 0 0-2-2H3z"></path>',heart:'<path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>',"help-circle":'<circle cx="12" cy="12" r="10"></circle><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path><line x1="12" y1="17" x2="12.01" y2="17"></line>',hexagon:'<path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>',home:'<path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline>',image:'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><circle cx="8.5" cy="8.5" r="1.5"></circle><polyline points="21 15 16 10 5 21"></polyline>',inbox:'<polyline points="22 12 16 12 14 15 10 15 8 12 2 12"></polyline><path d="M5.45 5.11L2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z"></path>',info:'<circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line>',instagram:'<rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path><line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>',italic:'<line x1="19" y1="4" x2="10" y2="4"></line><line x1="14" y1="20" x2="5" y2="20"></line><line x1="15" y1="4" x2="9" y2="20"></line>',key:'<path d="M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4"></path>',layers:'<polygon points="12 2 2 7 12 12 22 7 12 2"></polygon><polyline points="2 17 12 22 22 17"></polyline><polyline points="2 12 12 17 22 12"></polyline>',layout:'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="3" y1="9" x2="21" y2="9"></line><line x1="9" y1="21" x2="9" y2="9"></line>',"life-buoy":'<circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="12" r="4"></circle><line x1="4.93" y1="4.93" x2="9.17" y2="9.17"></line><line x1="14.83" y1="14.83" x2="19.07" y2="19.07"></line><line x1="14.83" y1="9.17" x2="19.07" y2="4.93"></line><line x1="14.83" y1="9.17" x2="18.36" y2="5.64"></line><line x1="4.93" y1="19.07" x2="9.17" y2="14.83"></line>',"link-2":'<path d="M15 7h3a5 5 0 0 1 5 5 5 5 0 0 1-5 5h-3m-6 0H6a5 5 0 0 1-5-5 5 5 0 0 1 5-5h3"></path><line x1="8" y1="12" x2="16" y2="12"></line>',link:'<path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path><path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>',linkedin:'<path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path><rect x="2" y="9" width="4" height="12"></rect><circle cx="4" cy="4" r="2"></circle>',list:'<line x1="8" y1="6" x2="21" y2="6"></line><line x1="8" y1="12" x2="21" y2="12"></line><line x1="8" y1="18" x2="21" y2="18"></line><line x1="3" y1="6" x2="3.01" y2="6"></line><line x1="3" y1="12" x2="3.01" y2="12"></line><line x1="3" y1="18" x2="3.01" y2="18"></line>',loader:'<line x1="12" y1="2" x2="12" y2="6"></line><line x1="12" y1="18" x2="12" y2="22"></line><line x1="4.93" y1="4.93" x2="7.76" y2="7.76"></line><line x1="16.24" y1="16.24" x2="19.07" y2="19.07"></line><line x1="2" y1="12" x2="6" y2="12"></line><line x1="18" y1="12" x2="22" y2="12"></line><line x1="4.93" y1="19.07" x2="7.76" y2="16.24"></line><line x1="16.24" y1="7.76" x2="19.07" y2="4.93"></line>',lock:'<rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 10 0v4"></path>',"log-in":'<path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path><polyline points="10 17 15 12 10 7"></polyline><line x1="15" y1="12" x2="3" y2="12"></line>',"log-out":'<path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path><polyline points="16 17 21 12 16 7"></polyline><line x1="21" y1="12" x2="9" y2="12"></line>',mail:'<path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path><polyline points="22,6 12,13 2,6"></polyline>',"map-pin":'<path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path><circle cx="12" cy="10" r="3"></circle>',map:'<polygon points="1 6 1 22 8 18 16 22 23 18 23 2 16 6 8 2 1 6"></polygon><line x1="8" y1="2" x2="8" y2="18"></line><line x1="16" y1="6" x2="16" y2="22"></line>',"maximize-2":'<polyline points="15 3 21 3 21 9"></polyline><polyline points="9 21 3 21 3 15"></polyline><line x1="21" y1="3" x2="14" y2="10"></line><line x1="3" y1="21" x2="10" y2="14"></line>',maximize:'<path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"></path>',meh:'<circle cx="12" cy="12" r="10"></circle><line x1="8" y1="15" x2="16" y2="15"></line><line x1="9" y1="9" x2="9.01" y2="9"></line><line x1="15" y1="9" x2="15.01" y2="9"></line>',menu:'<line x1="3" y1="12" x2="21" y2="12"></line><line x1="3" y1="6" x2="21" y2="6"></line><line x1="3" y1="18" x2="21" y2="18"></line>',"message-circle":'<path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>',"message-square":'<path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>',"mic-off":'<line x1="1" y1="1" x2="23" y2="23"></line><path d="M9 9v3a3 3 0 0 0 5.12 2.12M15 9.34V4a3 3 0 0 0-5.94-.6"></path><path d="M17 16.95A7 7 0 0 1 5 12v-2m14 0v2a7 7 0 0 1-.11 1.23"></path><line x1="12" y1="19" x2="12" y2="23"></line><line x1="8" y1="23" x2="16" y2="23"></line>',mic:'<path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path><path d="M19 10v2a7 7 0 0 1-14 0v-2"></path><line x1="12" y1="19" x2="12" y2="23"></line><line x1="8" y1="23" x2="16" y2="23"></line>',"minimize-2":'<polyline points="4 14 10 14 10 20"></polyline><polyline points="20 10 14 10 14 4"></polyline><line x1="14" y1="10" x2="21" y2="3"></line><line x1="3" y1="21" x2="10" y2="14"></line>',minimize:'<path d="M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3"></path>',"minus-circle":'<circle cx="12" cy="12" r="10"></circle><line x1="8" y1="12" x2="16" y2="12"></line>',"minus-square":'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="8" y1="12" x2="16" y2="12"></line>',minus:'<line x1="5" y1="12" x2="19" y2="12"></line>',monitor:'<rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect><line x1="8" y1="21" x2="16" y2="21"></line><line x1="12" y1="17" x2="12" y2="21"></line>',moon:'<path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>',"more-horizontal":'<circle cx="12" cy="12" r="1"></circle><circle cx="19" cy="12" r="1"></circle><circle cx="5" cy="12" r="1"></circle>',"more-vertical":'<circle cx="12" cy="12" r="1"></circle><circle cx="12" cy="5" r="1"></circle><circle cx="12" cy="19" r="1"></circle>',"mouse-pointer":'<path d="M3 3l7.07 16.97 2.51-7.39 7.39-2.51L3 3z"></path><path d="M13 13l6 6"></path>',move:'<polyline points="5 9 2 12 5 15"></polyline><polyline points="9 5 12 2 15 5"></polyline><polyline points="15 19 12 22 9 19"></polyline><polyline points="19 9 22 12 19 15"></polyline><line x1="2" y1="12" x2="22" y2="12"></line><line x1="12" y1="2" x2="12" y2="22"></line>',music:'<path d="M9 18V5l12-2v13"></path><circle cx="6" cy="18" r="3"></circle><circle cx="18" cy="16" r="3"></circle>',"navigation-2":'<polygon points="12 2 19 21 12 17 5 21 12 2"></polygon>',navigation:'<polygon points="3 11 22 2 13 21 11 13 3 11"></polygon>',octagon:'<polygon points="7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2"></polygon>',package:'<line x1="16.5" y1="9.4" x2="7.5" y2="4.21"></line><path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path><polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline><line x1="12" y1="22.08" x2="12" y2="12"></line>',paperclip:'<path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"></path>',"pause-circle":'<circle cx="12" cy="12" r="10"></circle><line x1="10" y1="15" x2="10" y2="9"></line><line x1="14" y1="15" x2="14" y2="9"></line>',pause:'<rect x="6" y="4" width="4" height="16"></rect><rect x="14" y="4" width="4" height="16"></rect>',"pen-tool":'<path d="M12 19l7-7 3 3-7 7-3-3z"></path><path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"></path><path d="M2 2l7.586 7.586"></path><circle cx="11" cy="11" r="2"></circle>',percent:'<line x1="19" y1="5" x2="5" y2="19"></line><circle cx="6.5" cy="6.5" r="2.5"></circle><circle cx="17.5" cy="17.5" r="2.5"></circle>',"phone-call":'<path d="M15.05 5A5 5 0 0 1 19 8.95M15.05 1A9 9 0 0 1 23 8.94m-1 7.98v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>',"phone-forwarded":'<polyline points="19 1 23 5 19 9"></polyline><line x1="15" y1="5" x2="23" y2="5"></line><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>',"phone-incoming":'<polyline points="16 2 16 8 22 8"></polyline><line x1="23" y1="1" x2="16" y2="8"></line><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>',"phone-missed":'<line x1="23" y1="1" x2="17" y2="7"></line><line x1="17" y1="1" x2="23" y2="7"></line><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>',"phone-off":'<path d="M10.68 13.31a16 16 0 0 0 3.41 2.6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7 2 2 0 0 1 1.72 2v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.42 19.42 0 0 1-3.33-2.67m-2.67-3.34a19.79 19.79 0 0 1-3.07-8.63A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91"></path><line x1="23" y1="1" x2="1" y2="23"></line>',"phone-outgoing":'<polyline points="23 7 23 1 17 1"></polyline><line x1="16" y1="8" x2="23" y2="1"></line><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>',phone:'<path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>',"pie-chart":'<path d="M21.21 15.89A10 10 0 1 1 8 2.83"></path><path d="M22 12A10 10 0 0 0 12 2v10z"></path>',"play-circle":'<circle cx="12" cy="12" r="10"></circle><polygon points="10 8 16 12 10 16 10 8"></polygon>',play:'<polygon points="5 3 19 12 5 21 5 3"></polygon>',"plus-circle":'<circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line>',"plus-square":'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line>',plus:'<line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line>',pocket:'<path d="M4 3h16a2 2 0 0 1 2 2v6a10 10 0 0 1-10 10A10 10 0 0 1 2 11V5a2 2 0 0 1 2-2z"></path><polyline points="8 10 12 14 16 10"></polyline>',power:'<path d="M18.36 6.64a9 9 0 1 1-12.73 0"></path><line x1="12" y1="2" x2="12" y2="12"></line>',printer:'<polyline points="6 9 6 2 18 2 18 9"></polyline><path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"></path><rect x="6" y="14" width="12" height="8"></rect>',radio:'<circle cx="12" cy="12" r="2"></circle><path d="M16.24 7.76a6 6 0 0 1 0 8.49m-8.48-.01a6 6 0 0 1 0-8.49m11.31-2.82a10 10 0 0 1 0 14.14m-14.14 0a10 10 0 0 1 0-14.14"></path>',"refresh-ccw":'<polyline points="1 4 1 10 7 10"></polyline><polyline points="23 20 23 14 17 14"></polyline><path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>',"refresh-cw":'<polyline points="23 4 23 10 17 10"></polyline><polyline points="1 20 1 14 7 14"></polyline><path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>',repeat:'<polyline points="17 1 21 5 17 9"></polyline><path d="M3 11V9a4 4 0 0 1 4-4h14"></path><polyline points="7 23 3 19 7 15"></polyline><path d="M21 13v2a4 4 0 0 1-4 4H3"></path>',rewind:'<polygon points="11 19 2 12 11 5 11 19"></polygon><polygon points="22 19 13 12 22 5 22 19"></polygon>',"rotate-ccw":'<polyline points="1 4 1 10 7 10"></polyline><path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"></path>',"rotate-cw":'<polyline points="23 4 23 10 17 10"></polyline><path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10"></path>',rss:'<path d="M4 11a9 9 0 0 1 9 9"></path><path d="M4 4a16 16 0 0 1 16 16"></path><circle cx="5" cy="19" r="1"></circle>',save:'<path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path><polyline points="17 21 17 13 7 13 7 21"></polyline><polyline points="7 3 7 8 15 8"></polyline>',scissors:'<circle cx="6" cy="6" r="3"></circle><circle cx="6" cy="18" r="3"></circle><line x1="20" y1="4" x2="8.12" y2="15.88"></line><line x1="14.47" y1="14.48" x2="20" y2="20"></line><line x1="8.12" y1="8.12" x2="12" y2="12"></line>',search:'<circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line>',send:'<line x1="22" y1="2" x2="11" y2="13"></line><polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>',server:'<rect x="2" y="2" width="20" height="8" rx="2" ry="2"></rect><rect x="2" y="14" width="20" height="8" rx="2" ry="2"></rect><line x1="6" y1="6" x2="6.01" y2="6"></line><line x1="6" y1="18" x2="6.01" y2="18"></line>',settings:'<circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>',"share-2":'<circle cx="18" cy="5" r="3"></circle><circle cx="6" cy="12" r="3"></circle><circle cx="18" cy="19" r="3"></circle><line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line><line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>',share:'<path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"></path><polyline points="16 6 12 2 8 6"></polyline><line x1="12" y1="2" x2="12" y2="15"></line>',"shield-off":'<path d="M19.69 14a6.9 6.9 0 0 0 .31-2V5l-8-3-3.16 1.18"></path><path d="M4.73 4.73L4 5v7c0 6 8 10 8 10a20.29 20.29 0 0 0 5.62-4.38"></path><line x1="1" y1="1" x2="23" y2="23"></line>',shield:'<path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>',"shopping-bag":'<path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path><line x1="3" y1="6" x2="21" y2="6"></line><path d="M16 10a4 4 0 0 1-8 0"></path>',"shopping-cart":'<circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>',shuffle:'<polyline points="16 3 21 3 21 8"></polyline><line x1="4" y1="20" x2="21" y2="3"></line><polyline points="21 16 21 21 16 21"></polyline><line x1="15" y1="15" x2="21" y2="21"></line><line x1="4" y1="4" x2="9" y2="9"></line>',sidebar:'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="9" y1="3" x2="9" y2="21"></line>',"skip-back":'<polygon points="19 20 9 12 19 4 19 20"></polygon><line x1="5" y1="19" x2="5" y2="5"></line>',"skip-forward":'<polygon points="5 4 15 12 5 20 5 4"></polygon><line x1="19" y1="5" x2="19" y2="19"></line>',slack:'<path d="M14.5 10c-.83 0-1.5-.67-1.5-1.5v-5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5v5c0 .83-.67 1.5-1.5 1.5z"></path><path d="M20.5 10H19V8.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5-.67 1.5-1.5 1.5z"></path><path d="M9.5 14c.83 0 1.5.67 1.5 1.5v5c0 .83-.67 1.5-1.5 1.5S8 21.33 8 20.5v-5c0-.83.67-1.5 1.5-1.5z"></path><path d="M3.5 14H5v1.5c0 .83-.67 1.5-1.5 1.5S2 16.33 2 15.5 2.67 14 3.5 14z"></path><path d="M14 14.5c0-.83.67-1.5 1.5-1.5h5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5h-5c-.83 0-1.5-.67-1.5-1.5z"></path><path d="M15.5 19H14v1.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5-.67-1.5-1.5-1.5z"></path><path d="M10 9.5C10 8.67 9.33 8 8.5 8h-5C2.67 8 2 8.67 2 9.5S2.67 11 3.5 11h5c.83 0 1.5-.67 1.5-1.5z"></path><path d="M8.5 5H10V3.5C10 2.67 9.33 2 8.5 2S7 2.67 7 3.5 7.67 5 8.5 5z"></path>',slash:'<circle cx="12" cy="12" r="10"></circle><line x1="4.93" y1="4.93" x2="19.07" y2="19.07"></line>',sliders:'<line x1="4" y1="21" x2="4" y2="14"></line><line x1="4" y1="10" x2="4" y2="3"></line><line x1="12" y1="21" x2="12" y2="12"></line><line x1="12" y1="8" x2="12" y2="3"></line><line x1="20" y1="21" x2="20" y2="16"></line><line x1="20" y1="12" x2="20" y2="3"></line><line x1="1" y1="14" x2="7" y2="14"></line><line x1="9" y1="8" x2="15" y2="8"></line><line x1="17" y1="16" x2="23" y2="16"></line>',smartphone:'<rect x="5" y="2" width="14" height="20" rx="2" ry="2"></rect><line x1="12" y1="18" x2="12.01" y2="18"></line>',smile:'<circle cx="12" cy="12" r="10"></circle><path d="M8 14s1.5 2 4 2 4-2 4-2"></path><line x1="9" y1="9" x2="9.01" y2="9"></line><line x1="15" y1="9" x2="15.01" y2="9"></line>',speaker:'<rect x="4" y="2" width="16" height="20" rx="2" ry="2"></rect><circle cx="12" cy="14" r="4"></circle><line x1="12" y1="6" x2="12.01" y2="6"></line>',square:'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>',star:'<polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>',"stop-circle":'<circle cx="12" cy="12" r="10"></circle><rect x="9" y="9" width="6" height="6"></rect>',sun:'<circle cx="12" cy="12" r="5"></circle><line x1="12" y1="1" x2="12" y2="3"></line><line x1="12" y1="21" x2="12" y2="23"></line><line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line><line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line><line x1="1" y1="12" x2="3" y2="12"></line><line x1="21" y1="12" x2="23" y2="12"></line><line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line><line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>',sunrise:'<path d="M17 18a5 5 0 0 0-10 0"></path><line x1="12" y1="2" x2="12" y2="9"></line><line x1="4.22" y1="10.22" x2="5.64" y2="11.64"></line><line x1="1" y1="18" x2="3" y2="18"></line><line x1="21" y1="18" x2="23" y2="18"></line><line x1="18.36" y1="11.64" x2="19.78" y2="10.22"></line><line x1="23" y1="22" x2="1" y2="22"></line><polyline points="8 6 12 2 16 6"></polyline>',sunset:'<path d="M17 18a5 5 0 0 0-10 0"></path><line x1="12" y1="9" x2="12" y2="2"></line><line x1="4.22" y1="10.22" x2="5.64" y2="11.64"></line><line x1="1" y1="18" x2="3" y2="18"></line><line x1="21" y1="18" x2="23" y2="18"></line><line x1="18.36" y1="11.64" x2="19.78" y2="10.22"></line><line x1="23" y1="22" x2="1" y2="22"></line><polyline points="16 5 12 9 8 5"></polyline>',table:'<path d="M9 3H5a2 2 0 0 0-2 2v4m6-6h10a2 2 0 0 1 2 2v4M9 3v18m0 0h10a2 2 0 0 0 2-2V9M9 21H5a2 2 0 0 1-2-2V9m0 0h18"></path>',tablet:'<rect x="4" y="2" width="16" height="20" rx="2" ry="2"></rect><line x1="12" y1="18" x2="12.01" y2="18"></line>',tag:'<path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path><line x1="7" y1="7" x2="7.01" y2="7"></line>',target:'<circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="12" r="6"></circle><circle cx="12" cy="12" r="2"></circle>',terminal:'<polyline points="4 17 10 11 4 5"></polyline><line x1="12" y1="19" x2="20" y2="19"></line>',thermometer:'<path d="M14 14.76V3.5a2.5 2.5 0 0 0-5 0v11.26a4.5 4.5 0 1 0 5 0z"></path>',"thumbs-down":'<path d="M10 15v4a3 3 0 0 0 3 3l4-9V2H5.72a2 2 0 0 0-2 1.7l-1.38 9a2 2 0 0 0 2 2.3zm7-13h2.67A2.31 2.31 0 0 1 22 4v7a2.31 2.31 0 0 1-2.33 2H17"></path>',"thumbs-up":'<path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"></path>',"toggle-left":'<rect x="1" y="5" width="22" height="14" rx="7" ry="7"></rect><circle cx="8" cy="12" r="3"></circle>',"toggle-right":'<rect x="1" y="5" width="22" height="14" rx="7" ry="7"></rect><circle cx="16" cy="12" r="3"></circle>',tool:'<path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"></path>',"trash-2":'<polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line>',trash:'<polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>',trello:'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><rect x="7" y="7" width="3" height="9"></rect><rect x="14" y="7" width="3" height="5"></rect>',"trending-down":'<polyline points="23 18 13.5 8.5 8.5 13.5 1 6"></polyline><polyline points="17 18 23 18 23 12"></polyline>',"trending-up":'<polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline><polyline points="17 6 23 6 23 12"></polyline>',triangle:'<path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>',truck:'<rect x="1" y="3" width="15" height="13"></rect><polygon points="16 8 20 8 23 11 23 16 16 16 16 8"></polygon><circle cx="5.5" cy="18.5" r="2.5"></circle><circle cx="18.5" cy="18.5" r="2.5"></circle>',tv:'<rect x="2" y="7" width="20" height="15" rx="2" ry="2"></rect><polyline points="17 2 12 7 7 2"></polyline>',twitch:'<path d="M21 2H3v16h5v4l4-4h5l4-4V2zm-10 9V7m5 4V7"></path>',twitter:'<path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"></path>',type:'<polyline points="4 7 4 4 20 4 20 7"></polyline><line x1="9" y1="20" x2="15" y2="20"></line><line x1="12" y1="4" x2="12" y2="20"></line>',umbrella:'<path d="M23 12a11.05 11.05 0 0 0-22 0zm-5 7a3 3 0 0 1-6 0v-7"></path>',underline:'<path d="M6 3v7a6 6 0 0 0 6 6 6 6 0 0 0 6-6V3"></path><line x1="4" y1="21" x2="20" y2="21"></line>',unlock:'<rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 9.9-1"></path>',"upload-cloud":'<polyline points="16 16 12 12 8 16"></polyline><line x1="12" y1="12" x2="12" y2="21"></line><path d="M20.39 18.39A5 5 0 0 0 18 9h-1.26A8 8 0 1 0 3 16.3"></path><polyline points="16 16 12 12 8 16"></polyline>',upload:'<path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="17 8 12 3 7 8"></polyline><line x1="12" y1="3" x2="12" y2="15"></line>',"user-check":'<path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="8.5" cy="7" r="4"></circle><polyline points="17 11 19 13 23 9"></polyline>',"user-minus":'<path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="8.5" cy="7" r="4"></circle><line x1="23" y1="11" x2="17" y2="11"></line>',"user-plus":'<path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="8.5" cy="7" r="4"></circle><line x1="20" y1="8" x2="20" y2="14"></line><line x1="23" y1="11" x2="17" y2="11"></line>',"user-x":'<path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="8.5" cy="7" r="4"></circle><line x1="18" y1="8" x2="23" y2="13"></line><line x1="23" y1="8" x2="18" y2="13"></line>',user:'<path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle>',users:'<path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M23 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path>',"video-off":'<path d="M16 16v1a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2h2m5.66 0H14a2 2 0 0 1 2 2v3.34l1 1L23 7v10"></path><line x1="1" y1="1" x2="23" y2="23"></line>',video:'<polygon points="23 7 16 12 23 17 23 7"></polygon><rect x="1" y="5" width="15" height="14" rx="2" ry="2"></rect>',voicemail:'<circle cx="5.5" cy="11.5" r="4.5"></circle><circle cx="18.5" cy="11.5" r="4.5"></circle><line x1="5.5" y1="16" x2="18.5" y2="16"></line>',"volume-1":'<polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon><path d="M15.54 8.46a5 5 0 0 1 0 7.07"></path>',"volume-2":'<polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon><path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"></path>',"volume-x":'<polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon><line x1="23" y1="9" x2="17" y2="15"></line><line x1="17" y1="9" x2="23" y2="15"></line>',volume:'<polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>',watch:'<circle cx="12" cy="12" r="7"></circle><polyline points="12 9 12 12 13.5 13.5"></polyline><path d="M16.51 17.35l-.35 3.83a2 2 0 0 1-2 1.82H9.83a2 2 0 0 1-2-1.82l-.35-3.83m.01-10.7l.35-3.83A2 2 0 0 1 9.83 1h4.35a2 2 0 0 1 2 1.82l.35 3.83"></path>',"wifi-off":'<line x1="1" y1="1" x2="23" y2="23"></line><path d="M16.72 11.06A10.94 10.94 0 0 1 19 12.55"></path><path d="M5 12.55a10.94 10.94 0 0 1 5.17-2.39"></path><path d="M10.71 5.05A16 16 0 0 1 22.58 9"></path><path d="M1.42 9a15.91 15.91 0 0 1 4.7-2.88"></path><path d="M8.53 16.11a6 6 0 0 1 6.95 0"></path><line x1="12" y1="20" x2="12.01" y2="20"></line>',wifi:'<path d="M5 12.55a11 11 0 0 1 14.08 0"></path><path d="M1.42 9a16 16 0 0 1 21.16 0"></path><path d="M8.53 16.11a6 6 0 0 1 6.95 0"></path><line x1="12" y1="20" x2="12.01" y2="20"></line>',wind:'<path d="M9.59 4.59A2 2 0 1 1 11 8H2m10.59 11.41A2 2 0 1 0 14 16H2m15.73-8.27A2.5 2.5 0 1 1 19.5 12H2"></path>',"x-circle":'<circle cx="12" cy="12" r="10"></circle><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line>',"x-octagon":'<polygon points="7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2"></polygon><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line>',"x-square":'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="9" y1="9" x2="15" y2="15"></line><line x1="15" y1="9" x2="9" y2="15"></line>',x:'<line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line>',youtube:'<path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z"></path><polygon points="9.75 15.02 15.5 11.75 9.75 8.48 9.75 15.02"></polygon>',"zap-off":'<polyline points="12.41 6.75 13 2 10.57 4.92"></polyline><polyline points="18.57 12.91 21 10 15.66 10"></polyline><polyline points="8 8 3 14 12 14 11 22 16 16"></polyline><line x1="1" y1="1" x2="23" y2="23"></line>',zap:'<polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon>',"zoom-in":'<circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line><line x1="11" y1="8" x2="11" y2="14"></line><line x1="8" y1="11" x2="14" y2="11"></line>',"zoom-out":'<circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line><line x1="8" y1="11" x2="14" y2="11"></line>'}},"./node_modules/classnames/dedupe.js":function(n,e,t){var i,r;(function(){"use strict";var o=function(){function a(){}a.prototype=Object.create(null);function s(v,m){for(var x=m.length,C=0;C<x;++C)y(v,m[C])}var l={}.hasOwnProperty;function c(v,m){v[m]=!0}function u(v,m){for(var x in m)l.call(m,x)&&(v[x]=!!m[x])}var f=/\s+/;function p(v,m){for(var x=m.split(f),C=x.length,E=0;E<C;++E)v[x[E]]=!0}function y(v,m){if(!!m){var x=typeof m;x==="string"?p(v,m):Array.isArray(m)?s(v,m):x==="object"?u(v,m):x==="number"&&c(v,m)}}function g(){for(var v=arguments.length,m=Array(v),x=0;x<v;x++)m[x]=arguments[x];var C=new a;s(C,m);var E=[];for(var D in C)C[D]&&E.push(D);return E.join(" ")}return g}();typeof n!="undefined"&&n.exports?n.exports=o:(i=[],r=function(){return o}.apply(e,i),r!==void 0&&(n.exports=r))})()},"./node_modules/core-js/es/array/from.js":function(n,e,t){t("./node_modules/core-js/modules/es.string.iterator.js"),t("./node_modules/core-js/modules/es.array.from.js");var i=t("./node_modules/core-js/internals/path.js");n.exports=i.Array.from},"./node_modules/core-js/internals/a-function.js":function(n,e){n.exports=function(t){if(typeof t!="function")throw TypeError(String(t)+" is not a function");return t}},"./node_modules/core-js/internals/an-object.js":function(n,e,t){var i=t("./node_modules/core-js/internals/is-object.js");n.exports=function(r){if(!i(r))throw TypeError(String(r)+" is not an object");return r}},"./node_modules/core-js/internals/array-from.js":function(n,e,t){"use strict";var i=t("./node_modules/core-js/internals/bind-context.js"),r=t("./node_modules/core-js/internals/to-object.js"),o=t("./node_modules/core-js/internals/call-with-safe-iteration-closing.js"),a=t("./node_modules/core-js/internals/is-array-iterator-method.js"),s=t("./node_modules/core-js/internals/to-length.js"),l=t("./node_modules/core-js/internals/create-property.js"),c=t("./node_modules/core-js/internals/get-iterator-method.js");n.exports=function(f){var p=r(f),y=typeof this=="function"?this:Array,g=arguments.length,v=g>1?arguments[1]:void 0,m=v!==void 0,x=0,C=c(p),E,D,j,k;if(m&&(v=i(v,g>2?arguments[2]:void 0,2)),C!=null&&!(y==Array&&a(C)))for(k=C.call(p),D=new y;!(j=k.next()).done;x++)l(D,x,m?o(k,v,[j.value,x],!0):j.value);else for(E=s(p.length),D=new y(E);E>x;x++)l(D,x,m?v(p[x],x):p[x]);return D.length=x,D}},"./node_modules/core-js/internals/array-includes.js":function(n,e,t){var i=t("./node_modules/core-js/internals/to-indexed-object.js"),r=t("./node_modules/core-js/internals/to-length.js"),o=t("./node_modules/core-js/internals/to-absolute-index.js");n.exports=function(a){return function(s,l,c){var u=i(s),f=r(u.length),p=o(c,f),y;if(a&&l!=l){for(;f>p;)if(y=u[p++],y!=y)return!0}else for(;f>p;p++)if((a||p in u)&&u[p]===l)return a||p||0;return!a&&-1}}},"./node_modules/core-js/internals/bind-context.js":function(n,e,t){var i=t("./node_modules/core-js/internals/a-function.js");n.exports=function(r,o,a){if(i(r),o===void 0)return r;switch(a){case 0:return function(){return r.call(o)};case 1:return function(s){return r.call(o,s)};case 2:return function(s,l){return r.call(o,s,l)};case 3:return function(s,l,c){return r.call(o,s,l,c)}}return function(){return r.apply(o,arguments)}}},"./node_modules/core-js/internals/call-with-safe-iteration-closing.js":function(n,e,t){var i=t("./node_modules/core-js/internals/an-object.js");n.exports=function(r,o,a,s){try{return s?o(i(a)[0],a[1]):o(a)}catch(c){var l=r.return;throw l!==void 0&&i(l.call(r)),c}}},"./node_modules/core-js/internals/check-correctness-of-iteration.js":function(n,e,t){var i=t("./node_modules/core-js/internals/well-known-symbol.js"),r=i("iterator"),o=!1;try{var a=0,s={next:function(){return{done:!!a++}},return:function(){o=!0}};s[r]=function(){return this},Array.from(s,function(){throw 2})}catch(l){}n.exports=function(l,c){if(!c&&!o)return!1;var u=!1;try{var f={};f[r]=function(){return{next:function(){return{done:u=!0}}}},l(f)}catch(p){}return u}},"./node_modules/core-js/internals/classof-raw.js":function(n,e){var t={}.toString;n.exports=function(i){return t.call(i).slice(8,-1)}},"./node_modules/core-js/internals/classof.js":function(n,e,t){var i=t("./node_modules/core-js/internals/classof-raw.js"),r=t("./node_modules/core-js/internals/well-known-symbol.js"),o=r("toStringTag"),a=i(function(){return arguments}())=="Arguments",s=function(l,c){try{return l[c]}catch(u){}};n.exports=function(l){var c,u,f;return l===void 0?"Undefined":l===null?"Null":typeof(u=s(c=Object(l),o))=="string"?u:a?i(c):(f=i(c))=="Object"&&typeof c.callee=="function"?"Arguments":f}},"./node_modules/core-js/internals/copy-constructor-properties.js":function(n,e,t){var i=t("./node_modules/core-js/internals/has.js"),r=t("./node_modules/core-js/internals/own-keys.js"),o=t("./node_modules/core-js/internals/object-get-own-property-descriptor.js"),a=t("./node_modules/core-js/internals/object-define-property.js");n.exports=function(s,l){for(var c=r(l),u=a.f,f=o.f,p=0;p<c.length;p++){var y=c[p];i(s,y)||u(s,y,f(l,y))}}},"./node_modules/core-js/internals/correct-prototype-getter.js":function(n,e,t){var i=t("./node_modules/core-js/internals/fails.js");n.exports=!i(function(){function r(){}return r.prototype.constructor=null,Object.getPrototypeOf(new r)!==r.prototype})},"./node_modules/core-js/internals/create-iterator-constructor.js":function(n,e,t){"use strict";var i=t("./node_modules/core-js/internals/iterators-core.js").IteratorPrototype,r=t("./node_modules/core-js/internals/object-create.js"),o=t("./node_modules/core-js/internals/create-property-descriptor.js"),a=t("./node_modules/core-js/internals/set-to-string-tag.js"),s=t("./node_modules/core-js/internals/iterators.js"),l=function(){return this};n.exports=function(c,u,f){var p=u+" Iterator";return c.prototype=r(i,{next:o(1,f)}),a(c,p,!1,!0),s[p]=l,c}},"./node_modules/core-js/internals/create-property-descriptor.js":function(n,e){n.exports=function(t,i){return{enumerable:!(t&1),configurable:!(t&2),writable:!(t&4),value:i}}},"./node_modules/core-js/internals/create-property.js":function(n,e,t){"use strict";var i=t("./node_modules/core-js/internals/to-primitive.js"),r=t("./node_modules/core-js/internals/object-define-property.js"),o=t("./node_modules/core-js/internals/create-property-descriptor.js");n.exports=function(a,s,l){var c=i(s);c in a?r.f(a,c,o(0,l)):a[c]=l}},"./node_modules/core-js/internals/define-iterator.js":function(n,e,t){"use strict";var i=t("./node_modules/core-js/internals/export.js"),r=t("./node_modules/core-js/internals/create-iterator-constructor.js"),o=t("./node_modules/core-js/internals/object-get-prototype-of.js"),a=t("./node_modules/core-js/internals/object-set-prototype-of.js"),s=t("./node_modules/core-js/internals/set-to-string-tag.js"),l=t("./node_modules/core-js/internals/hide.js"),c=t("./node_modules/core-js/internals/redefine.js"),u=t("./node_modules/core-js/internals/well-known-symbol.js"),f=t("./node_modules/core-js/internals/is-pure.js"),p=t("./node_modules/core-js/internals/iterators.js"),y=t("./node_modules/core-js/internals/iterators-core.js"),g=y.IteratorPrototype,v=y.BUGGY_SAFARI_ITERATORS,m=u("iterator"),x="keys",C="values",E="entries",D=function(){return this};n.exports=function(j,k,I,O,B,ye,Ae){r(I,k,O);var be=function(me){if(me===B&&L)return L;if(!v&&me in $)return $[me];switch(me){case x:return function(){return new I(this,me)};case C:return function(){return new I(this,me)};case E:return function(){return new I(this,me)}}return function(){return new I(this)}},J=k+" Iterator",De=!1,$=j.prototype,ue=$[m]||$["@@iterator"]||B&&$[B],L=!v&&ue||be(B),q=k=="Array"&&$.entries||ue,ie,Te,Ge;if(q&&(ie=o(q.call(new j)),g!==Object.prototype&&ie.next&&(!f&&o(ie)!==g&&(a?a(ie,g):typeof ie[m]!="function"&&l(ie,m,D)),s(ie,J,!0,!0),f&&(p[J]=D))),B==C&&ue&&ue.name!==C&&(De=!0,L=function(){return ue.call(this)}),(!f||Ae)&&$[m]!==L&&l($,m,L),p[k]=L,B)if(Te={values:be(C),keys:ye?L:be(x),entries:be(E)},Ae)for(Ge in Te)(v||De||!(Ge in $))&&c($,Ge,Te[Ge]);else i({target:k,proto:!0,forced:v||De},Te);return Te}},"./node_modules/core-js/internals/descriptors.js":function(n,e,t){var i=t("./node_modules/core-js/internals/fails.js");n.exports=!i(function(){return Object.defineProperty({},"a",{get:function(){return 7}}).a!=7})},"./node_modules/core-js/internals/document-create-element.js":function(n,e,t){var i=t("./node_modules/core-js/internals/global.js"),r=t("./node_modules/core-js/internals/is-object.js"),o=i.document,a=r(o)&&r(o.createElement);n.exports=function(s){return a?o.createElement(s):{}}},"./node_modules/core-js/internals/enum-bug-keys.js":function(n,e){n.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},"./node_modules/core-js/internals/export.js":function(n,e,t){var i=t("./node_modules/core-js/internals/global.js"),r=t("./node_modules/core-js/internals/object-get-own-property-descriptor.js").f,o=t("./node_modules/core-js/internals/hide.js"),a=t("./node_modules/core-js/internals/redefine.js"),s=t("./node_modules/core-js/internals/set-global.js"),l=t("./node_modules/core-js/internals/copy-constructor-properties.js"),c=t("./node_modules/core-js/internals/is-forced.js");n.exports=function(u,f){var p=u.target,y=u.global,g=u.stat,v,m,x,C,E,D;if(y?m=i:g?m=i[p]||s(p,{}):m=(i[p]||{}).prototype,m)for(x in f){if(E=f[x],u.noTargetGet?(D=r(m,x),C=D&&D.value):C=m[x],v=c(y?x:p+(g?".":"#")+x,u.forced),!v&&C!==void 0){if(typeof E==typeof C)continue;l(E,C)}(u.sham||C&&C.sham)&&o(E,"sham",!0),a(m,x,E,u)}}},"./node_modules/core-js/internals/fails.js":function(n,e){n.exports=function(t){try{return!!t()}catch(i){return!0}}},"./node_modules/core-js/internals/function-to-string.js":function(n,e,t){var i=t("./node_modules/core-js/internals/shared.js");n.exports=i("native-function-to-string",Function.toString)},"./node_modules/core-js/internals/get-iterator-method.js":function(n,e,t){var i=t("./node_modules/core-js/internals/classof.js"),r=t("./node_modules/core-js/internals/iterators.js"),o=t("./node_modules/core-js/internals/well-known-symbol.js"),a=o("iterator");n.exports=function(s){if(s!=null)return s[a]||s["@@iterator"]||r[i(s)]}},"./node_modules/core-js/internals/global.js":function(n,e,t){(function(i){var r="object",o=function(a){return a&&a.Math==Math&&a};n.exports=o(typeof globalThis==r&&globalThis)||o(typeof window==r&&window)||o(typeof self==r&&self)||o(typeof i==r&&i)||Function("return this")()}).call(this,t("./node_modules/webpack/buildin/global.js"))},"./node_modules/core-js/internals/has.js":function(n,e){var t={}.hasOwnProperty;n.exports=function(i,r){return t.call(i,r)}},"./node_modules/core-js/internals/hidden-keys.js":function(n,e){n.exports={}},"./node_modules/core-js/internals/hide.js":function(n,e,t){var i=t("./node_modules/core-js/internals/descriptors.js"),r=t("./node_modules/core-js/internals/object-define-property.js"),o=t("./node_modules/core-js/internals/create-property-descriptor.js");n.exports=i?function(a,s,l){return r.f(a,s,o(1,l))}:function(a,s,l){return a[s]=l,a}},"./node_modules/core-js/internals/html.js":function(n,e,t){var i=t("./node_modules/core-js/internals/global.js"),r=i.document;n.exports=r&&r.documentElement},"./node_modules/core-js/internals/ie8-dom-define.js":function(n,e,t){var i=t("./node_modules/core-js/internals/descriptors.js"),r=t("./node_modules/core-js/internals/fails.js"),o=t("./node_modules/core-js/internals/document-create-element.js");n.exports=!i&&!r(function(){return Object.defineProperty(o("div"),"a",{get:function(){return 7}}).a!=7})},"./node_modules/core-js/internals/indexed-object.js":function(n,e,t){var i=t("./node_modules/core-js/internals/fails.js"),r=t("./node_modules/core-js/internals/classof-raw.js"),o="".split;n.exports=i(function(){return!Object("z").propertyIsEnumerable(0)})?function(a){return r(a)=="String"?o.call(a,""):Object(a)}:Object},"./node_modules/core-js/internals/internal-state.js":function(n,e,t){var i=t("./node_modules/core-js/internals/native-weak-map.js"),r=t("./node_modules/core-js/internals/global.js"),o=t("./node_modules/core-js/internals/is-object.js"),a=t("./node_modules/core-js/internals/hide.js"),s=t("./node_modules/core-js/internals/has.js"),l=t("./node_modules/core-js/internals/shared-key.js"),c=t("./node_modules/core-js/internals/hidden-keys.js"),u=r.WeakMap,f,p,y,g=function(j){return y(j)?p(j):f(j,{})},v=function(j){return function(k){var I;if(!o(k)||(I=p(k)).type!==j)throw TypeError("Incompatible receiver, "+j+" required");return I}};if(i){var m=new u,x=m.get,C=m.has,E=m.set;f=function(j,k){return E.call(m,j,k),k},p=function(j){return x.call(m,j)||{}},y=function(j){return C.call(m,j)}}else{var D=l("state");c[D]=!0,f=function(j,k){return a(j,D,k),k},p=function(j){return s(j,D)?j[D]:{}},y=function(j){return s(j,D)}}n.exports={set:f,get:p,has:y,enforce:g,getterFor:v}},"./node_modules/core-js/internals/is-array-iterator-method.js":function(n,e,t){var i=t("./node_modules/core-js/internals/well-known-symbol.js"),r=t("./node_modules/core-js/internals/iterators.js"),o=i("iterator"),a=Array.prototype;n.exports=function(s){return s!==void 0&&(r.Array===s||a[o]===s)}},"./node_modules/core-js/internals/is-forced.js":function(n,e,t){var i=t("./node_modules/core-js/internals/fails.js"),r=/#|\.prototype\./,o=function(u,f){var p=s[a(u)];return p==c?!0:p==l?!1:typeof f=="function"?i(f):!!f},a=o.normalize=function(u){return String(u).replace(r,".").toLowerCase()},s=o.data={},l=o.NATIVE="N",c=o.POLYFILL="P";n.exports=o},"./node_modules/core-js/internals/is-object.js":function(n,e){n.exports=function(t){return typeof t=="object"?t!==null:typeof t=="function"}},"./node_modules/core-js/internals/is-pure.js":function(n,e){n.exports=!1},"./node_modules/core-js/internals/iterators-core.js":function(n,e,t){"use strict";var i=t("./node_modules/core-js/internals/object-get-prototype-of.js"),r=t("./node_modules/core-js/internals/hide.js"),o=t("./node_modules/core-js/internals/has.js"),a=t("./node_modules/core-js/internals/well-known-symbol.js"),s=t("./node_modules/core-js/internals/is-pure.js"),l=a("iterator"),c=!1,u=function(){return this},f,p,y;[].keys&&(y=[].keys(),"next"in y?(p=i(i(y)),p!==Object.prototype&&(f=p)):c=!0),f==null&&(f={}),!s&&!o(f,l)&&r(f,l,u),n.exports={IteratorPrototype:f,BUGGY_SAFARI_ITERATORS:c}},"./node_modules/core-js/internals/iterators.js":function(n,e){n.exports={}},"./node_modules/core-js/internals/native-symbol.js":function(n,e,t){var i=t("./node_modules/core-js/internals/fails.js");n.exports=!!Object.getOwnPropertySymbols&&!i(function(){return!String(Symbol())})},"./node_modules/core-js/internals/native-weak-map.js":function(n,e,t){var i=t("./node_modules/core-js/internals/global.js"),r=t("./node_modules/core-js/internals/function-to-string.js"),o=i.WeakMap;n.exports=typeof o=="function"&&/native code/.test(r.call(o))},"./node_modules/core-js/internals/object-create.js":function(n,e,t){var i=t("./node_modules/core-js/internals/an-object.js"),r=t("./node_modules/core-js/internals/object-define-properties.js"),o=t("./node_modules/core-js/internals/enum-bug-keys.js"),a=t("./node_modules/core-js/internals/hidden-keys.js"),s=t("./node_modules/core-js/internals/html.js"),l=t("./node_modules/core-js/internals/document-create-element.js"),c=t("./node_modules/core-js/internals/shared-key.js"),u=c("IE_PROTO"),f="prototype",p=function(){},y=function(){var g=l("iframe"),v=o.length,m="<",x="script",C=">",E="java"+x+":",D;for(g.style.display="none",s.appendChild(g),g.src=String(E),D=g.contentWindow.document,D.open(),D.write(m+x+C+"document.F=Object"+m+"/"+x+C),D.close(),y=D.F;v--;)delete y[f][o[v]];return y()};n.exports=Object.create||function(v,m){var x;return v!==null?(p[f]=i(v),x=new p,p[f]=null,x[u]=v):x=y(),m===void 0?x:r(x,m)},a[u]=!0},"./node_modules/core-js/internals/object-define-properties.js":function(n,e,t){var i=t("./node_modules/core-js/internals/descriptors.js"),r=t("./node_modules/core-js/internals/object-define-property.js"),o=t("./node_modules/core-js/internals/an-object.js"),a=t("./node_modules/core-js/internals/object-keys.js");n.exports=i?Object.defineProperties:function(l,c){o(l);for(var u=a(c),f=u.length,p=0,y;f>p;)r.f(l,y=u[p++],c[y]);return l}},"./node_modules/core-js/internals/object-define-property.js":function(n,e,t){var i=t("./node_modules/core-js/internals/descriptors.js"),r=t("./node_modules/core-js/internals/ie8-dom-define.js"),o=t("./node_modules/core-js/internals/an-object.js"),a=t("./node_modules/core-js/internals/to-primitive.js"),s=Object.defineProperty;e.f=i?s:function(c,u,f){if(o(c),u=a(u,!0),o(f),r)try{return s(c,u,f)}catch(p){}if("get"in f||"set"in f)throw TypeError("Accessors not supported");return"value"in f&&(c[u]=f.value),c}},"./node_modules/core-js/internals/object-get-own-property-descriptor.js":function(n,e,t){var i=t("./node_modules/core-js/internals/descriptors.js"),r=t("./node_modules/core-js/internals/object-property-is-enumerable.js"),o=t("./node_modules/core-js/internals/create-property-descriptor.js"),a=t("./node_modules/core-js/internals/to-indexed-object.js"),s=t("./node_modules/core-js/internals/to-primitive.js"),l=t("./node_modules/core-js/internals/has.js"),c=t("./node_modules/core-js/internals/ie8-dom-define.js"),u=Object.getOwnPropertyDescriptor;e.f=i?u:function(p,y){if(p=a(p),y=s(y,!0),c)try{return u(p,y)}catch(g){}if(l(p,y))return o(!r.f.call(p,y),p[y])}},"./node_modules/core-js/internals/object-get-own-property-names.js":function(n,e,t){var i=t("./node_modules/core-js/internals/object-keys-internal.js"),r=t("./node_modules/core-js/internals/enum-bug-keys.js"),o=r.concat("length","prototype");e.f=Object.getOwnPropertyNames||function(s){return i(s,o)}},"./node_modules/core-js/internals/object-get-own-property-symbols.js":function(n,e){e.f=Object.getOwnPropertySymbols},"./node_modules/core-js/internals/object-get-prototype-of.js":function(n,e,t){var i=t("./node_modules/core-js/internals/has.js"),r=t("./node_modules/core-js/internals/to-object.js"),o=t("./node_modules/core-js/internals/shared-key.js"),a=t("./node_modules/core-js/internals/correct-prototype-getter.js"),s=o("IE_PROTO"),l=Object.prototype;n.exports=a?Object.getPrototypeOf:function(c){return c=r(c),i(c,s)?c[s]:typeof c.constructor=="function"&&c instanceof c.constructor?c.constructor.prototype:c instanceof Object?l:null}},"./node_modules/core-js/internals/object-keys-internal.js":function(n,e,t){var i=t("./node_modules/core-js/internals/has.js"),r=t("./node_modules/core-js/internals/to-indexed-object.js"),o=t("./node_modules/core-js/internals/array-includes.js"),a=t("./node_modules/core-js/internals/hidden-keys.js"),s=o(!1);n.exports=function(l,c){var u=r(l),f=0,p=[],y;for(y in u)!i(a,y)&&i(u,y)&&p.push(y);for(;c.length>f;)i(u,y=c[f++])&&(~s(p,y)||p.push(y));return p}},"./node_modules/core-js/internals/object-keys.js":function(n,e,t){var i=t("./node_modules/core-js/internals/object-keys-internal.js"),r=t("./node_modules/core-js/internals/enum-bug-keys.js");n.exports=Object.keys||function(a){return i(a,r)}},"./node_modules/core-js/internals/object-property-is-enumerable.js":function(n,e,t){"use strict";var i={}.propertyIsEnumerable,r=Object.getOwnPropertyDescriptor,o=r&&!i.call({1:2},1);e.f=o?function(s){var l=r(this,s);return!!l&&l.enumerable}:i},"./node_modules/core-js/internals/object-set-prototype-of.js":function(n,e,t){var i=t("./node_modules/core-js/internals/validate-set-prototype-of-arguments.js");n.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var r=!1,o={},a;try{a=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set,a.call(o,[]),r=o instanceof Array}catch(s){}return function(l,c){return i(l,c),r?a.call(l,c):l.__proto__=c,l}}():void 0)},"./node_modules/core-js/internals/own-keys.js":function(n,e,t){var i=t("./node_modules/core-js/internals/global.js"),r=t("./node_modules/core-js/internals/object-get-own-property-names.js"),o=t("./node_modules/core-js/internals/object-get-own-property-symbols.js"),a=t("./node_modules/core-js/internals/an-object.js"),s=i.Reflect;n.exports=s&&s.ownKeys||function(c){var u=r.f(a(c)),f=o.f;return f?u.concat(f(c)):u}},"./node_modules/core-js/internals/path.js":function(n,e,t){n.exports=t("./node_modules/core-js/internals/global.js")},"./node_modules/core-js/internals/redefine.js":function(n,e,t){var i=t("./node_modules/core-js/internals/global.js"),r=t("./node_modules/core-js/internals/shared.js"),o=t("./node_modules/core-js/internals/hide.js"),a=t("./node_modules/core-js/internals/has.js"),s=t("./node_modules/core-js/internals/set-global.js"),l=t("./node_modules/core-js/internals/function-to-string.js"),c=t("./node_modules/core-js/internals/internal-state.js"),u=c.get,f=c.enforce,p=String(l).split("toString");r("inspectSource",function(y){return l.call(y)}),(n.exports=function(y,g,v,m){var x=m?!!m.unsafe:!1,C=m?!!m.enumerable:!1,E=m?!!m.noTargetGet:!1;if(typeof v=="function"&&(typeof g=="string"&&!a(v,"name")&&o(v,"name",g),f(v).source=p.join(typeof g=="string"?g:"")),y===i){C?y[g]=v:s(g,v);return}else x?!E&&y[g]&&(C=!0):delete y[g];C?y[g]=v:o(y,g,v)})(Function.prototype,"toString",function(){return typeof this=="function"&&u(this).source||l.call(this)})},"./node_modules/core-js/internals/require-object-coercible.js":function(n,e){n.exports=function(t){if(t==null)throw TypeError("Can't call method on "+t);return t}},"./node_modules/core-js/internals/set-global.js":function(n,e,t){var i=t("./node_modules/core-js/internals/global.js"),r=t("./node_modules/core-js/internals/hide.js");n.exports=function(o,a){try{r(i,o,a)}catch(s){i[o]=a}return a}},"./node_modules/core-js/internals/set-to-string-tag.js":function(n,e,t){var i=t("./node_modules/core-js/internals/object-define-property.js").f,r=t("./node_modules/core-js/internals/has.js"),o=t("./node_modules/core-js/internals/well-known-symbol.js"),a=o("toStringTag");n.exports=function(s,l,c){s&&!r(s=c?s:s.prototype,a)&&i(s,a,{configurable:!0,value:l})}},"./node_modules/core-js/internals/shared-key.js":function(n,e,t){var i=t("./node_modules/core-js/internals/shared.js"),r=t("./node_modules/core-js/internals/uid.js"),o=i("keys");n.exports=function(a){return o[a]||(o[a]=r(a))}},"./node_modules/core-js/internals/shared.js":function(n,e,t){var i=t("./node_modules/core-js/internals/global.js"),r=t("./node_modules/core-js/internals/set-global.js"),o=t("./node_modules/core-js/internals/is-pure.js"),a="__core-js_shared__",s=i[a]||r(a,{});(n.exports=function(l,c){return s[l]||(s[l]=c!==void 0?c:{})})("versions",[]).push({version:"3.1.3",mode:o?"pure":"global",copyright:"\xA9 2019 Denis Pushkarev (zloirock.ru)"})},"./node_modules/core-js/internals/string-at.js":function(n,e,t){var i=t("./node_modules/core-js/internals/to-integer.js"),r=t("./node_modules/core-js/internals/require-object-coercible.js");n.exports=function(o,a,s){var l=String(r(o)),c=i(a),u=l.length,f,p;return c<0||c>=u?s?"":void 0:(f=l.charCodeAt(c),f<55296||f>56319||c+1===u||(p=l.charCodeAt(c+1))<56320||p>57343?s?l.charAt(c):f:s?l.slice(c,c+2):(f-55296<<10)+(p-56320)+65536)}},"./node_modules/core-js/internals/to-absolute-index.js":function(n,e,t){var i=t("./node_modules/core-js/internals/to-integer.js"),r=Math.max,o=Math.min;n.exports=function(a,s){var l=i(a);return l<0?r(l+s,0):o(l,s)}},"./node_modules/core-js/internals/to-indexed-object.js":function(n,e,t){var i=t("./node_modules/core-js/internals/indexed-object.js"),r=t("./node_modules/core-js/internals/require-object-coercible.js");n.exports=function(o){return i(r(o))}},"./node_modules/core-js/internals/to-integer.js":function(n,e){var t=Math.ceil,i=Math.floor;n.exports=function(r){return isNaN(r=+r)?0:(r>0?i:t)(r)}},"./node_modules/core-js/internals/to-length.js":function(n,e,t){var i=t("./node_modules/core-js/internals/to-integer.js"),r=Math.min;n.exports=function(o){return o>0?r(i(o),9007199254740991):0}},"./node_modules/core-js/internals/to-object.js":function(n,e,t){var i=t("./node_modules/core-js/internals/require-object-coercible.js");n.exports=function(r){return Object(i(r))}},"./node_modules/core-js/internals/to-primitive.js":function(n,e,t){var i=t("./node_modules/core-js/internals/is-object.js");n.exports=function(r,o){if(!i(r))return r;var a,s;if(o&&typeof(a=r.toString)=="function"&&!i(s=a.call(r))||typeof(a=r.valueOf)=="function"&&!i(s=a.call(r))||!o&&typeof(a=r.toString)=="function"&&!i(s=a.call(r)))return s;throw TypeError("Can't convert object to primitive value")}},"./node_modules/core-js/internals/uid.js":function(n,e){var t=0,i=Math.random();n.exports=function(r){return"Symbol(".concat(r===void 0?"":r,")_",(++t+i).toString(36))}},"./node_modules/core-js/internals/validate-set-prototype-of-arguments.js":function(n,e,t){var i=t("./node_modules/core-js/internals/is-object.js"),r=t("./node_modules/core-js/internals/an-object.js");n.exports=function(o,a){if(r(o),!i(a)&&a!==null)throw TypeError("Can't set "+String(a)+" as a prototype")}},"./node_modules/core-js/internals/well-known-symbol.js":function(n,e,t){var i=t("./node_modules/core-js/internals/global.js"),r=t("./node_modules/core-js/internals/shared.js"),o=t("./node_modules/core-js/internals/uid.js"),a=t("./node_modules/core-js/internals/native-symbol.js"),s=i.Symbol,l=r("wks");n.exports=function(c){return l[c]||(l[c]=a&&s[c]||(a?s:o)("Symbol."+c))}},"./node_modules/core-js/modules/es.array.from.js":function(n,e,t){var i=t("./node_modules/core-js/internals/export.js"),r=t("./node_modules/core-js/internals/array-from.js"),o=t("./node_modules/core-js/internals/check-correctness-of-iteration.js"),a=!o(function(s){Array.from(s)});i({target:"Array",stat:!0,forced:a},{from:r})},"./node_modules/core-js/modules/es.string.iterator.js":function(n,e,t){"use strict";var i=t("./node_modules/core-js/internals/string-at.js"),r=t("./node_modules/core-js/internals/internal-state.js"),o=t("./node_modules/core-js/internals/define-iterator.js"),a="String Iterator",s=r.set,l=r.getterFor(a);o(String,"String",function(c){s(this,{type:a,string:String(c),index:0})},function(){var u=l(this),f=u.string,p=u.index,y;return p>=f.length?{value:void 0,done:!0}:(y=i(f,p,!0),u.index+=y.length,{value:y,done:!1})})},"./node_modules/webpack/buildin/global.js":function(n,e){var t;t=function(){return this}();try{t=t||Function("return this")()||(0,eval)("this")}catch(i){typeof window=="object"&&(t=window)}n.exports=t},"./src/default-attrs.json":function(n){n.exports={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":2,"stroke-linecap":"round","stroke-linejoin":"round"}},"./src/icon.js":function(n,e,t){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=Object.assign||function(y){for(var g=1;g<arguments.length;g++){var v=arguments[g];for(var m in v)Object.prototype.hasOwnProperty.call(v,m)&&(y[m]=v[m])}return y},r=function(){function y(g,v){for(var m=0;m<v.length;m++){var x=v[m];x.enumerable=x.enumerable||!1,x.configurable=!0,"value"in x&&(x.writable=!0),Object.defineProperty(g,x.key,x)}}return function(g,v,m){return v&&y(g.prototype,v),m&&y(g,m),g}}(),o=t("./node_modules/classnames/dedupe.js"),a=c(o),s=t("./src/default-attrs.json"),l=c(s);function c(y){return y&&y.__esModule?y:{default:y}}function u(y,g){if(!(y instanceof g))throw new TypeError("Cannot call a class as a function")}var f=function(){function y(g,v){var m=arguments.length>2&&arguments[2]!==void 0?arguments[2]:[];u(this,y),this.name=g,this.contents=v,this.tags=m,this.attrs=i({},l.default,{class:"feather feather-"+g})}return r(y,[{key:"toSvg",value:function(){var v=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},m=i({},this.attrs,v,{class:(0,a.default)(this.attrs.class,v.class)});return"<svg "+p(m)+">"+this.contents+"</svg>"}},{key:"toString",value:function(){return this.contents}}]),y}();function p(y){return Object.keys(y).map(function(g){return g+'="'+y[g]+'"'}).join(" ")}e.default=f},"./src/icons.js":function(n,e,t){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=t("./src/icon.js"),r=c(i),o=t("./dist/icons.json"),a=c(o),s=t("./src/tags.json"),l=c(s);function c(u){return u&&u.__esModule?u:{default:u}}e.default=Object.keys(a.default).map(function(u){return new r.default(u,a.default[u],l.default[u])}).reduce(function(u,f){return u[f.name]=f,u},{})},"./src/index.js":function(n,e,t){"use strict";var i=t("./src/icons.js"),r=c(i),o=t("./src/to-svg.js"),a=c(o),s=t("./src/replace.js"),l=c(s);function c(u){return u&&u.__esModule?u:{default:u}}n.exports={icons:r.default,toSvg:a.default,replace:l.default}},"./src/replace.js":function(n,e,t){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=Object.assign||function(p){for(var y=1;y<arguments.length;y++){var g=arguments[y];for(var v in g)Object.prototype.hasOwnProperty.call(g,v)&&(p[v]=g[v])}return p},r=t("./node_modules/classnames/dedupe.js"),o=l(r),a=t("./src/icons.js"),s=l(a);function l(p){return p&&p.__esModule?p:{default:p}}function c(){var p=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(typeof document=="undefined")throw new Error("`feather.replace()` only works in a browser environment.");var y=document.querySelectorAll("[data-feather]");Array.from(y).forEach(function(g){return u(g,p)})}function u(p){var y=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},g=f(p),v=g["data-feather"];delete g["data-feather"];var m=s.default[v].toSvg(i({},y,g,{class:(0,o.default)(y.class,g.class)})),x=new DOMParser().parseFromString(m,"image/svg+xml"),C=x.querySelector("svg");p.parentNode.replaceChild(C,p)}function f(p){return Array.from(p.attributes).reduce(function(y,g){return y[g.name]=g.value,y},{})}e.default=c},"./src/tags.json":function(n){n.exports={activity:["pulse","health","action","motion"],airplay:["stream","cast","mirroring"],"alert-circle":["warning","alert","danger"],"alert-octagon":["warning","alert","danger"],"alert-triangle":["warning","alert","danger"],"align-center":["text alignment","center"],"align-justify":["text alignment","justified"],"align-left":["text alignment","left"],"align-right":["text alignment","right"],anchor:[],archive:["index","box"],"at-sign":["mention","at","email","message"],award:["achievement","badge"],aperture:["camera","photo"],"bar-chart":["statistics","diagram","graph"],"bar-chart-2":["statistics","diagram","graph"],battery:["power","electricity"],"battery-charging":["power","electricity"],bell:["alarm","notification","sound"],"bell-off":["alarm","notification","silent"],bluetooth:["wireless"],"book-open":["read","library"],book:["read","dictionary","booklet","magazine","library"],bookmark:["read","clip","marker","tag"],box:["cube"],briefcase:["work","bag","baggage","folder"],calendar:["date"],camera:["photo"],cast:["chromecast","airplay"],"chevron-down":["expand"],"chevron-up":["collapse"],circle:["off","zero","record"],clipboard:["copy"],clock:["time","watch","alarm"],"cloud-drizzle":["weather","shower"],"cloud-lightning":["weather","bolt"],"cloud-rain":["weather"],"cloud-snow":["weather","blizzard"],cloud:["weather"],codepen:["logo"],codesandbox:["logo"],code:["source","programming"],coffee:["drink","cup","mug","tea","cafe","hot","beverage"],columns:["layout"],command:["keyboard","cmd","terminal","prompt"],compass:["navigation","safari","travel","direction"],copy:["clone","duplicate"],"corner-down-left":["arrow","return"],"corner-down-right":["arrow"],"corner-left-down":["arrow"],"corner-left-up":["arrow"],"corner-right-down":["arrow"],"corner-right-up":["arrow"],"corner-up-left":["arrow"],"corner-up-right":["arrow"],cpu:["processor","technology"],"credit-card":["purchase","payment","cc"],crop:["photo","image"],crosshair:["aim","target"],database:["storage","memory"],delete:["remove"],disc:["album","cd","dvd","music"],"dollar-sign":["currency","money","payment"],droplet:["water"],edit:["pencil","change"],"edit-2":["pencil","change"],"edit-3":["pencil","change"],eye:["view","watch"],"eye-off":["view","watch","hide","hidden"],"external-link":["outbound"],facebook:["logo","social"],"fast-forward":["music"],figma:["logo","design","tool"],"file-minus":["delete","remove","erase"],"file-plus":["add","create","new"],"file-text":["data","txt","pdf"],film:["movie","video"],filter:["funnel","hopper"],flag:["report"],"folder-minus":["directory"],"folder-plus":["directory"],folder:["directory"],framer:["logo","design","tool"],frown:["emoji","face","bad","sad","emotion"],gift:["present","box","birthday","party"],"git-branch":["code","version control"],"git-commit":["code","version control"],"git-merge":["code","version control"],"git-pull-request":["code","version control"],github:["logo","version control"],gitlab:["logo","version control"],globe:["world","browser","language","translate"],"hard-drive":["computer","server","memory","data"],hash:["hashtag","number","pound"],headphones:["music","audio","sound"],heart:["like","love","emotion"],"help-circle":["question mark"],hexagon:["shape","node.js","logo"],home:["house","living"],image:["picture"],inbox:["email"],instagram:["logo","camera"],key:["password","login","authentication","secure"],layers:["stack"],layout:["window","webpage"],"life-bouy":["help","life ring","support"],link:["chain","url"],"link-2":["chain","url"],linkedin:["logo","social media"],list:["options"],lock:["security","password","secure"],"log-in":["sign in","arrow","enter"],"log-out":["sign out","arrow","exit"],mail:["email","message"],"map-pin":["location","navigation","travel","marker"],map:["location","navigation","travel"],maximize:["fullscreen"],"maximize-2":["fullscreen","arrows","expand"],meh:["emoji","face","neutral","emotion"],menu:["bars","navigation","hamburger"],"message-circle":["comment","chat"],"message-square":["comment","chat"],"mic-off":["record","sound","mute"],mic:["record","sound","listen"],minimize:["exit fullscreen","close"],"minimize-2":["exit fullscreen","arrows","close"],minus:["subtract"],monitor:["tv","screen","display"],moon:["dark","night"],"more-horizontal":["ellipsis"],"more-vertical":["ellipsis"],"mouse-pointer":["arrow","cursor"],move:["arrows"],music:["note"],navigation:["location","travel"],"navigation-2":["location","travel"],octagon:["stop"],package:["box","container"],paperclip:["attachment"],pause:["music","stop"],"pause-circle":["music","audio","stop"],"pen-tool":["vector","drawing"],percent:["discount"],"phone-call":["ring"],"phone-forwarded":["call"],"phone-incoming":["call"],"phone-missed":["call"],"phone-off":["call","mute"],"phone-outgoing":["call"],phone:["call"],play:["music","start"],"pie-chart":["statistics","diagram"],"play-circle":["music","start"],plus:["add","new"],"plus-circle":["add","new"],"plus-square":["add","new"],pocket:["logo","save"],power:["on","off"],printer:["fax","office","device"],radio:["signal"],"refresh-cw":["synchronise","arrows"],"refresh-ccw":["arrows"],repeat:["loop","arrows"],rewind:["music"],"rotate-ccw":["arrow"],"rotate-cw":["arrow"],rss:["feed","subscribe"],save:["floppy disk"],scissors:["cut"],search:["find","magnifier","magnifying glass"],send:["message","mail","email","paper airplane","paper aeroplane"],settings:["cog","edit","gear","preferences"],"share-2":["network","connections"],shield:["security","secure"],"shield-off":["security","insecure"],"shopping-bag":["ecommerce","cart","purchase","store"],"shopping-cart":["ecommerce","cart","purchase","store"],shuffle:["music"],"skip-back":["music"],"skip-forward":["music"],slack:["logo"],slash:["ban","no"],sliders:["settings","controls"],smartphone:["cellphone","device"],smile:["emoji","face","happy","good","emotion"],speaker:["audio","music"],star:["bookmark","favorite","like"],"stop-circle":["media","music"],sun:["brightness","weather","light"],sunrise:["weather","time","morning","day"],sunset:["weather","time","evening","night"],tablet:["device"],tag:["label"],target:["logo","bullseye"],terminal:["code","command line","prompt"],thermometer:["temperature","celsius","fahrenheit","weather"],"thumbs-down":["dislike","bad","emotion"],"thumbs-up":["like","good","emotion"],"toggle-left":["on","off","switch"],"toggle-right":["on","off","switch"],tool:["settings","spanner"],trash:["garbage","delete","remove","bin"],"trash-2":["garbage","delete","remove","bin"],triangle:["delta"],truck:["delivery","van","shipping","transport","lorry"],tv:["television","stream"],twitch:["logo"],twitter:["logo","social"],type:["text"],umbrella:["rain","weather"],unlock:["security"],"user-check":["followed","subscribed"],"user-minus":["delete","remove","unfollow","unsubscribe"],"user-plus":["new","add","create","follow","subscribe"],"user-x":["delete","remove","unfollow","unsubscribe","unavailable"],user:["person","account"],users:["group"],"video-off":["camera","movie","film"],video:["camera","movie","film"],voicemail:["phone"],volume:["music","sound","mute"],"volume-1":["music","sound"],"volume-2":["music","sound"],"volume-x":["music","sound","mute"],watch:["clock","time"],"wifi-off":["disabled"],wifi:["connection","signal","wireless"],wind:["weather","air"],"x-circle":["cancel","close","delete","remove","times","clear"],"x-octagon":["delete","stop","alert","warning","times","clear"],"x-square":["cancel","close","delete","remove","times","clear"],x:["cancel","close","delete","remove","times","clear"],youtube:["logo","video","play"],"zap-off":["flash","camera","lightning"],zap:["flash","camera","lightning"],"zoom-in":["magnifying glass"],"zoom-out":["magnifying glass"]}},"./src/to-svg.js":function(n,e,t){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=t("./src/icons.js"),r=o(i);function o(s){return s&&s.__esModule?s:{default:s}}function a(s){var l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(console.warn("feather.toSvg() is deprecated. Please use feather.icons[name].toSvg() instead."),!s)throw new Error("The required `key` (icon name) parameter is missing.");if(!r.default[s])throw new Error("No icon matching '"+s+"'. See the complete list of icons at https://feathericons.com");return r.default[s].toSvg(l)}e.default=a},0:function(n,e,t){t("./node_modules/core-js/es/array/from.js"),n.exports=t("./src/index.js")}})})});var Si=Ye(z=>{"use strict";Object.defineProperty(z,"__esModule",{value:!0});z.linkedQ=z.openOrSwitch=z.createNewMDNote=z.hoverPreview=z.isInVault=z.getSelectionFromCurrFile=z.getSelectionFromEditor=z.copy=z.getAvailablePathForAttachments=z.base64ToArrayBuffer=z.addFeatherIcon=z.addAllFeatherIcons=z.wait=void 0;var Mt=wi(),xe=require("obsidian");function Yr(n){return M(this,null,function*(){return new Promise(e=>setTimeout(e,n))})}z.wait=Yr;function Jr(n={viewBox:"0 0 24 24",width:"100",height:"100"}){Object.values(Mt.icons).forEach(e=>{let t=e.toSvg(n);(0,xe.addIcon)("feather-"+e.name,t)})}z.addAllFeatherIcons=Jr;function Kr(n,e={viewBox:"0 0 24 24",width:"100",height:"100"}){if(Mt.icons[n])(0,xe.addIcon)(`feather-${n}`,Mt.icons[n].toSvg(e));else throw Error(`This Icon (${n}) doesn't exist in the Feather Library.`)}z.addFeatherIcon=Kr;function Qr(n){let e=window.atob(n),t=e.length,i=new Uint8Array(t);for(let r=0;r<t;r++)i[r]=e.charCodeAt(r);return i.buffer}z.base64ToArrayBuffer=Qr;function Xr(n,e,t,i){return n.getAvailablePathForAttachments(e,t,i)}z.getAvailablePathForAttachments=Xr;function Zr(n,e=()=>new xe.Notice("Copied to clipboard"),t=i=>{new xe.Notice("Could not copy to clipboard"),console.log({reason:i})}){return M(this,null,function*(){yield navigator.clipboard.writeText(n).then(e,t)})}z.copy=Zr;function eo(n){return n.somethingSelected()?n.getSelection():n.getValue()}z.getSelectionFromEditor=eo;function to(n,e=!0){return M(this,null,function*(){var i;let t=(i=window==null?void 0:window.getSelection())==null?void 0:i.toString();if(t)return t;{let r=n.workspace.getActiveFile();if(r instanceof xe.TFile)return e?yield n.vault.cachedRead(r):yield n.vault.read(r);new xe.Notice("You must be focused on a markdown file.")}})}z.getSelectionFromCurrFile=to;var no=(n,e,t="")=>!!n.metadataCache.getFirstLinkpathDest(e,t);z.isInVault=no;function io(n,e,t){let i=n.target;e.app.workspace.trigger("hover-link",{event:n,source:e.getViewType(),hoverParent:e,targetEl:i,linktext:t})}z.hoverPreview=io;function bi(n,e,t=""){return M(this,null,function*(){let i=n.fileManager.getNewFileParent(t).path;e.endsWith(".md")||(e+=".md");let r=(0,xe.normalizePath)(`${i}${i==="/"?"":"/"}${e}.md`);return yield n.vault.create(r,"")})}z.createNewMDNote=bi;function ro(r,o,a){return M(this,arguments,function*(n,e,t,i={createNewFile:!0}){let{workspace:s}=n,l=s.getActiveFile(),c=n.metadataCache.getFirstLinkpathDest(e,l.path);if(!c)if(i.createNewFile)c=yield bi(n,e,l.path);else return;let u=[];if(s.iterateAllLeaves(f=>{var p,y;f.view instanceof xe.MarkdownView&&((y=(p=f.view)==null?void 0:p.file)==null?void 0:y.basename)===e&&u.push(f)}),u.length>0)s.setActiveLeaf(u[0]);else{let f=n.vault.getConfig("defaultViewMode");yield(t.ctrlKey||t.getModifierState("Meta")?s.splitActiveLeaf():s.getUnpinnedLeaf()).openFile(c,{active:!0,mode:f})}})}z.openOrSwitch=ro;function oo(n,e,t){var i;return e.endsWith(".md")||(e+=".md"),t.endsWith(".md")||(t+=".md"),(i=n[e])==null?void 0:i.hasOwnProperty(t)}z.linkedQ=oo});var Ot=Ye(V=>{"use strict";Object.defineProperty(V,"__esModule",{value:!0});V.linkedQ=V.openOrSwitch=V.createNewMDNote=V.isInVault=V.hoverPreview=V.getSelectionFromEditor=V.getSelectionFromCurrFile=V.copy=V.wait=V.getAvailablePathForAttachments=V.base64ToArrayBuffer=V.addFeatherIcon=V.addAllFeatherIcons=void 0;var te=Si();Object.defineProperty(V,"addAllFeatherIcons",{enumerable:!0,get:function(){return te.addAllFeatherIcons}});Object.defineProperty(V,"addFeatherIcon",{enumerable:!0,get:function(){return te.addFeatherIcon}});Object.defineProperty(V,"base64ToArrayBuffer",{enumerable:!0,get:function(){return te.base64ToArrayBuffer}});Object.defineProperty(V,"getAvailablePathForAttachments",{enumerable:!0,get:function(){return te.getAvailablePathForAttachments}});Object.defineProperty(V,"wait",{enumerable:!0,get:function(){return te.wait}});Object.defineProperty(V,"copy",{enumerable:!0,get:function(){return te.copy}});Object.defineProperty(V,"getSelectionFromCurrFile",{enumerable:!0,get:function(){return te.getSelectionFromCurrFile}});Object.defineProperty(V,"getSelectionFromEditor",{enumerable:!0,get:function(){return te.getSelectionFromEditor}});Object.defineProperty(V,"hoverPreview",{enumerable:!0,get:function(){return te.hoverPreview}});Object.defineProperty(V,"isInVault",{enumerable:!0,get:function(){return te.isInVault}});Object.defineProperty(V,"createNewMDNote",{enumerable:!0,get:function(){return te.createNewMDNote}});Object.defineProperty(V,"openOrSwitch",{enumerable:!0,get:function(){return te.openOrSwitch}});Object.defineProperty(V,"linkedQ",{enumerable:!0,get:function(){return te.linkedQ}})});Cr(exports,{default:()=>Bt});var le=G(require("obsidian"));function Gt(n,e){let t=-1,i=!1;if(e.toUpperCase()===e)return n.toUpperCase();if(e.toLowerCase()===e)return n.toLowerCase();for(;++t<e.length;){let r=e.charAt(t);if(r.toUpperCase()!==r.toLowerCase()){let o=e.slice(t+1);i=r===r.toUpperCase()&&o===o.toLowerCase();break}}if(i)for(t=-1;++t<n.length;){let r=n.charAt(t).toUpperCase();if(r!==r.toLowerCase())return n.slice(0,t)+r+n.slice(t+1).toLowerCase()}return n}var U=G(require("obsidian"));var pt=G(require("obsidian"));var qt={};var Yt={};var Jt={};var Kt={"Open Dictionary View":"\xD6ffne W\xF6rterbuch","Open Language Switcher":"Sprache wechseln",Dictionary:"W\xF6rterbuch",Cut:"Ausschneiden",Copy:"Kopieren",Paste:"Einf\xFCgen","Show Synonyms":"Zeige Synonyme","Look up":"Nachschlagen","Reset to default":"Zur\xFCcksetzen","Dictionary Settings":"W\xF6rterbuch Einstellungen",Language:"Sprache","The Language the Plugin will use to search for Definitions and Pronunciations.":"Die Sprache, welche von dieser Erweiterung verwendet wird, um nach Definitionen zu suchen.","Synonym Suggestions":"Synonym Vorschl\xE4ge","Show synonyms for highlighted words":"Zeige Synonyme f\xFCr markierte W\xF6rter","Enabling this will allow the Plugin to analyze full sentences to better suggest synonyms based on the context.":"Dies wird der Erweiterung erlauben ganze S\xE4tze zu analysieren, um anschlie\xDFend bessere Vorschl\xE4ge f\xFCr Synonyme basierend auf dem Kontext bereitzustellen.","Click ":"Klicke ",here:"hier"," for Privacy Concerns.":" bei Datenschutzbedenken","Advanced Synonym Search":"Erweiterte Synonym Suche","Show Options in Context Menu":"Zeige Optionen im Kontextmen\xFC","Enable custom Context Menu with options to search for synonyms (only if the auto suggestions are disabled) and to look up a full definition in the Sidebar. Warning: This will override Obsidian's default Context Menu.":"Aktiviere das benutzerdefinierte Kontextmen\xFC mit mehr Einstellungen, um Synoynme anzuzeigen (falls die automatischen Vorschl\xE4ge deaktiviert sind) oder ein Wort nachzuschlagen.","Click Here":"Klicke hier","Definition Provider":"Definitionen Anbieter","The API the Plugin will use to search for Definitions.":"Die API die von der Erweiterung verwendet werden wird, um Definitionen zu suchen.","Synonym Provider":"Synonym Anbieter","The API the Plugin will use to search for Synonyms.":"Die API die von der Erweiterung verwendet werden wird, um Synonyme zu suchen.","More Information":"Mehr Informationen","View Information about the API's and the Plugin itself.":"Schau dir mehr Informationen \xFCber die APIs und die Erweiterung an.","More Info":"Mehr Infos",Donate:"Spenden","If you like this Plugin, consider donating to support continued development:":"Wenn du die Erweiterung hilfreich findest, kannst du hier etwas spenden um die weitere Entwicklung zu unterst\xFCtzen:","Local Dictionary Folder":"Ordner f\xFCr das Lokale W\xF6rterbuch","Specify a Folder, where all new Notes created by the Dictionary are placed. Please note that this Folder needs to already exist.":"Gebe einen Ordner an, in dem alle vom W\xF6rterbuch erstellten neuen Notizen abgelegt werden. Bitte beachte, dass dieser Ordner bereits vorhanden sein muss.","Capitalize File Name":"Dateinamen gro\xDF schreiben","If you disable this, the names of newly created files will be all lowercase.":"Wenn dies deaktiviert wird, werden die Namen neu erstellter Dateien nur in Kleinbuchstaben geschrieben.","Filename Prefix and Suffix":"Dateinamen-Pr\xE4fix und -Suffix","Here you can add a Prefix and Suffix for your newly created Files.":"Hier kann ein ein Pr\xE4fix und ein Suffix f\xFCr neu erstellte Dateien hinzugef\xFCgt werden. Hier kann die {{lang}} Variable verwendet werden, um die momentane Sprache einzuf\xFCgen.",Prefix:"Prefix",Suffix:"Suffix","Here you can edit the Template for newly created Files.":"Hier kann die Vorlage f\xFCr neu erstellte Dateien angepasst werden.","Click for a List of Variables":"Klicke hier f\xFCr eine Liste aller Variablen.",Template:"Vorlage","Local-Dictionary-Builder Settings":"Einstellungen f\xFCr den lokalen W\xF6rterbuch-Builder",Miscellaneous:"Sonstiges","Caching Settings":"Zwischenspeicher Einstellungen","Use Caching":"Nutze den Zwischenspeicher","Enable or disable caching. Caching provides a semi-offline experience by saving every result for later use.":"Aktiviere oder deaktivere den Zwischenspeicher. Durch den Zwischenspeicher kannst du das W\xF6rterbuch teilweise offline nutzen.","Here you can delete all cached Data.":"Hier kannst du alle zwischengespeicherten Daten l\xF6schen.","You currently have ":"Momentan hast du "," cached Definitions and ":" gespeicherte Definitionen und "," cached Synonyms.":" gespeicherte Synonyme.","Delete Cache":"Zwischenspeicher l\xF6schen",Delete:"L\xF6schen",Success:"Erfolgreich gel\xF6scht","Use Language specific Subfolders":"Verwende untergeordnete Ordner f\xFCr jede Sprache",'Create Subfolders for every language, e.g. "Dictionary/en-US/Cake"':'Verwende untergeordnete Ordner f\xFCr jede Sprache, zum Beispiel "Vokabeln/de/Kuchen"',"Autogenerated by Obsidian Dictionary Plugin":"Automatisch erstellt durch die W\xF6rterbuch Erweiterung","No, keep the old File.":"Nein, behalte die alte Datei","Yes, overwrite the old File.":"Ja, \xFCberschreibe die alte Datei","A existing File with the same Name was found, do you want to overwrite it?":"Eine bereits existierende Datei mit dem gleichen Namen wurde gefunden, m\xF6chtest du diese \xFCberschreiben?","Meaning {{i}}":"Bedeutung {{i}}","API Information":"API Informationen","Definition API's":"Definitionen APIs",Website:"Webseite","Synonym API's":"Synonym APIs","Part of Speech API's":"Wortart APIs",'This Plugin is using <a href="https://feathericons.com/">Feather Icons</a>':'Diese Erweiterung verwendet <a href="https://feathericons.com/">Feather Icons</a>',Clear:"Eingabe l\xF6schen","Change Language":"Sprache \xE4ndern","Change Provider":"API \xE4ndern","Collapse Results":"Ergebnisse ausklappen","Enter a word":"Gebe ein Wort ein",Pronunciation:"Aussprache",Meanings:"Bedeutungen",Origin:"Ursprung","New Note":"Neue Notiz","Match Case":"Match Case","View Error":"Fehler anzeigen",'Copied "{{word}}" to clipboard':'"{{word}}" wurde in die Zwischenablage kopiert',"I can't find the word you are looking for or the server can't be reached. You can try again in a few minutes.":"Ich kann das Wort nicht finden oder der Server kann nicht erreicht werden. Bitte probiere es in eingigen Minuten nocheinmal.","Definition:":"Definition:","Synonyms:":"Synonyme:","Antonyms:":"Antonyme:","Choose a Definition Provider Service":"API f\xFCr Definitionen ausw\xE4hlen","Choose a Language":"Sprache ausw\xE4hlen","Choose a Synonym Provider Service":"API f\xFCr Synonyme ausw\xE4hlen"};var ft={"Open Dictionary View":"Open Dictionary View","Open Language Switcher":"Open Language Switcher",Dictionary:"Dictionary",Cut:"Cut",Copy:"Copy",Paste:"Paste","Show Synonyms":"Show Synonyms","Look up":"Look up","Reset to default":"Reset to default","Dictionary Settings":"Dictionary Settings",Language:"Language","The Language the Plugin will use to search for Definitions and Pronunciations.":"The Language the Plugin will use to search for Definitions and Pronunciations.","Synonym Suggestions":"Synonym Suggestions","Show synonyms for highlighted words":"Show synonyms for highlighted words","Enabling this will allow the Plugin to analyze full sentences to better suggest synonyms based on the context.":"Enabling this will allow the Plugin to analyze full sentences to better suggest synonyms based on the context.","Click ":"Click ",here:"here"," for Privacy Concerns.":" for Privacy Concerns.","Advanced Synonym Search":"Advanced Synonym Search","Show Options in Context Menu":"Show Options in Context Menu","Enable custom Context Menu with options to search for synonyms (only if the auto suggestions are disabled) and to look up a full definition in the Sidebar. Warning: This will override Obsidian's default Context Menu.":"Enable custom Context Menu with options to search for synonyms (only if the auto suggestions are disabled) and to look up a full definition in the Sidebar.","Click Here":"Click Here","Definition Provider":"Definition Provider","The API the Plugin will use to search for Definitions.":"The API the Plugin will use to search for Definitions.","Synonym Provider":"Synonym Provider","The API the Plugin will use to search for Synonyms.":"The API the Plugin will use to search for Synonyms.","More Information":"More Information","View Information about the API's and the Plugin itself.":"View Information about the API's and the Plugin itself.","More Info":"More Info",Donate:"Donate","If you like this Plugin, consider donating to support continued development:":"If you like this Plugin, consider donating to support continued development:","Local Dictionary Folder":"Local Dictionary Folder","Specify a Folder, where all new Notes created by the Dictionary are placed. Please note that this Folder needs to already exist.":"Specify a Folder, where all new Notes created by the Dictionary are placed. Please note that this Folder needs to already exist.","Capitalize File Name":"Capitalize File Name","If you disable this, the names of newly created files will be all lowercase.":"If you disable this, the names of newly created files will be all lowercase.","Filename Prefix and Suffix":"Filename Prefix and Suffix","Here you can add a Prefix and Suffix for your newly created Files.":"Here you can add a Prefix and Suffix for your newly created Files. You can use the {{lang}} variable here.",Prefix:"Prefix",Suffix:"Suffix","Here you can edit the Template for newly created Files.":"Here you can edit the Template for newly created Files.","Click for a List of Variables":"Click for a List of Variables",Template:"Template","Local-Dictionary-Builder Settings":"Local-Dictionary-Builder Settings",Miscellaneous:"Miscellaneous","Caching Settings":"Caching Settings","Use Caching":"Use Caching","Enable or disable caching. Caching provides a semi-offline experience by saving every result for later use.":"Enable or disable caching. Caching provides a semi-offline experience by saving every result for later use.","Here you can delete all cached Data.":"Here you can delete all cached Data.","You currently have ":"You currently have "," cached Definitions and ":" cached Definitions and "," cached Synonyms.":" cached Synonyms.","Delete Cache":"Delete Cache",Delete:"Delete",Success:"Success","Use Language specific Subfolders":"Use Language specific Subfolders",'Create Subfolders for every language, e.g. "Dictionary/en-US/Cake"':'Create Subfolders for every language, e.g. "Dictionary/en-US/Cake"',"Autogenerated by Obsidian Dictionary Plugin":"Autogenerated by Obsidian Dictionary Plugin","Yes, overwrite the old File.":"Yes, overwrite the old File.","A existing File with the same Name was found, do you want to overwrite it?":"A existing File with the same Name was found, do you want to overwrite it?","No, keep the old File.":"No, keep the old File.","Meaning {{i}}":"Meaning {{i}}","API Information":"API Information","Definition API's":"Definition API's",Website:"Website","Synonym API's":"Synonym API's","Part of Speech API's":"Part of Speech API's",'This Plugin is using <a href="https://feathericons.com/">Feather Icons</a>':'This Plugin is using <a href="https://feathericons.com/">Feather Icons</a>',"Enter a word":"Enter a word",Clear:"Clear","Change Language":"Change Language","Change Provider":"Change Provider","Collapse Results":"Collapse Results",Pronunciation:"Pronunciation",Meanings:"Meanings",Origin:"Origin","New Note":"New Note","View Error":"View Error","Match Case":"Match Case",'Copied "{{word}}" to clipboard':'Copied "{{word}}" to clipboard',"I can't find the word you are looking for or the server can't be reached. You can try again in a few minutes.":"I can't find the word you are looking for or the server can't be reached. You can try again in a few minutes.","Definition:":"Definition:","Synonyms:":"Synonyms:","Antonyms:":"Antonyms:","Choose a Definition Provider Service":"Choose a Definition Provider Service","Choose a Language":"Choose a Language","Choose a Synonym Provider Service":"Choose a Synonym Provider Service"};var Qt={};var Xt={"Open Dictionary View":"Abrir vista de Diccionario","Open Language Switcher":"Abrir Selector de Idioma",Dictionary:"Diccionario",Cut:"Cortar",Copy:"Copiar",Paste:"Pegar","Show Synonyms":"Mostrar Sin\xF3nimos","Look up":"Buscar","Reset to default":"Resetear a configuraci\xF3n por defecto","Dictionary Settings":"Configuraci\xF3n del Diccionario",Language:"Idioma","The Language the Plugin will use to search for Definitions and Pronunciations.":"El Idioma que el Plugin usar\xE1 para buscar Definiciones y Pronunciaciones.","Synonym Suggestions":"Sugerencias de Sin\xF3nimos","Show synonyms for highlighted words":"Mostrar sin\xF3nimos para palabras resaltadas","Enabling this will allow the Plugin to analyze full sentences to better suggest synonyms based on the context.":"Activar esto habilitar\xE1 al plugin para analizar frases enteras para sugerir mejores sin\xF3nimos bas\xE1ndose en el contexto.","Click ":"Click ",here:"aqu\xED"," for Privacy Concerns.":" for Privacy Concerns.","Advanced Synonym Search":"B\xFAsqueda Avanzada de Sin\xF3nimos","Show Options in Context Menu":"Mostrar opciones en el Men\xFA Contextual","Enable custom Context Menu with options to search for synonyms (only if the auto suggestions are disabled) and to look up a full definition in the Sidebar. Warning: This will override Obsidian's default Context Menu.":"Habilitar el Men\xFA Contextual personalizado con opciones para buscar sin\xF3nimos (s\xF3lo si las sugerencias autom\xE1ticas est\xE1n deshabilitadas) y para buscar una definici\xF3n completa en la Barra Lateral.","Click Here":"Clica Aqu\xED","Definition Provider":"Proveedor de Definiciones","The API the Plugin will use to search for Definitions.":"La API que el Plugin utilizar\xE1 para buscar Definiciones.","Synonym Provider":"Proveedor de Sin\xF3nimos","The API the Plugin will use to search for Synonyms.":"La API que el Plugin utilizar\xE1 para buscar Sin\xF3nimos.","More Information":"M\xE1s Informaci\xF3n","View Information about the API's and the Plugin itself.":"Ver informaci\xF3n sobre la API y el propio Plugin.","More Info":"M\xE1s Info",Donate:"Donar","If you like this Plugin, consider donating to support continued development:":"Si te gusta este Plugin, considera donar para apoyar un desarrollo cont\xEDnuo:","Local Dictionary Folder":"Carpeta del Diccionario Local","Specify a Folder, where all new Notes created by the Dictionary are placed. Please note that this Folder needs to already exist.":"Especifica una Carpeta, donde se encuentran todas las nuevas Notas creadas por el Diccionario. La Carpeta debe existir.","Capitalize File Name":"Capitaliar Nombre de Fichero","If you disable this, the names of newly created files will be all lowercase.":"Si desactivas esto, los nombres de nuevos ficheros estar\xE1n en min\xFAsculas.","Filename Prefix and Suffix":"Prefijo y Sufijo del Nombre del Fichero","Here you can add a Prefix and Suffix for your newly created Files.":"Aqu\xED puedes a\xF1adir un Prefijo y un Sufijo para los nuevos Ficheros creados. Puedes utilizar la variable {{lang}}.",Prefix:"Prefijo",Suffix:"Sufijo","Here you can edit the Template for newly created Files.":"Aqu\xED puedes editar la Plantilla nuevos Ficheros","Click for a List of Variables":"Clica para una Lista de Variables",Template:"Plantilla","Local-Dictionary-Builder Settings":"Configuraci\xF3n del Builder del Diccionario Local",Miscellaneous:"Miscel\xE1neo","Caching Settings":"Cach\xE9 de Configuraciones","Use Caching":"Utilizar Cach\xE9","Enable or disable caching. Caching provides a semi-offline experience by saving every result for later use.":"Activar o desactivar Cach\xE9. La memoria Cach\xE9 proporciona una experiencia semi-offline al guardar cada resultado para utilizarlo m\xE1s tarde.","Here you can delete all cached Data.":"Aqu\xED puedes borrar todos los datos en Cach\xE9.","You currently have ":"Actualmente tienes "," cached Definitions and ":" Definiciones y "," cached Synonyms.":" Sin\xF3nimos en Cach\xE9.","Delete Cache":"Borrar Cach\xE9",Delete:"Borrar",Success:"\xC9xito","Use Language specific Subfolders":"Utilizar Subcarpetas por cada Idioma",'Create Subfolders for every language, e.g. "Dictionary/en-US/Cake"':'Crear una Subcarpeta por cada Idioma. P.E.: "Dictionary/en-US/Cake"',"Autogenerated by Obsidian Dictionary Plugin":"Autogenerado por Obsidian Dictionary Plugin","Yes, overwrite the old File.":"S\xED, sobreescribir el fichero antig\xFCo.","A existing File with the same Name was found, do you want to overwrite it?":"Se ha encontrado un Fichero con el mismo nombre. \xBFDesea sobreescribirlo?","No, keep the old File.":"No, conservar el Fichero antig\xFCo.","Meaning {{i}}":"Significa {{i}}","API Information":"Informaci\xF3n de la API","Definition API's":"API de Definiciones",Website:"P\xE1gina Web","Synonym API's":"API de Sin\xF3nimos","Part of Speech API's":"Part of Speech API's",'This Plugin is using <a href="https://feathericons.com/">Feather Icons</a>':'Este Plugin utiliza <a href="https://feathericons.com/">Feather Icons</a>',"Enter a word":"Introduce una palabra",Clear:"Limpiar","Change Language":"Cambiar Idioma","Change Provider":"Cambiar Proveedor","Collapse Results":"Colapsar Resultados",Pronunciation:"Pronunciaci\xF3n",Meanings:"Significados",Origin:"Etimolog\xEDa","New Note":"Nueva Nota","View Error":"Ver Error","Match Case":"Match Case",'Copied "{{word}}" to clipboard':'"{{word}}" copiada al portapapeles',"I can't find the word you are looking for or the server can't be reached. You can try again in a few minutes.":"No puedo encontrar la palabra que buscas, o bien no se puede contactar con el servidor. Vuelve a intentarlo dentro de unos minutos.","Definition:":"Definici\xF3n:","Synonyms:":"Sin\xF3nimos:","Antonyms:":"Ant\xF3nimos:","Choose a Definition Provider Service":"Elige un Servicio de Provisi\xF3n de Definiciones","Choose a Language":"Elige un Idioma","Choose a Synonym Provider Service":"Elige un Servicio de Provisi\xF3n de Sin\xF3nimos"};var Zt={};var en={};var tn={};var nn={"Open Dictionary View":"Apri la schermata del dizionario","Open Language Switcher":"Apri le impostazioni della lingua",Dictionary:"Dizionario",Cut:"Taglia",Copy:"Copia",Paste:"Incolla","Show Synonyms":"Mostra i sinonimi","Look up":"Cerca","Reset to default":"Resetta le impostazioni","Dictionary Settings":"Impostazioni del dizionario",Language:"Lingua","The Language the Plugin will use to search for Definitions and Pronunciations.":"La lingua che il plugin utilizzar\xE0 per cercare le definizioni e la pronuncia.","Synonym Suggestions":"Suggerisci sinonimi","Show synonyms for highlighted words":"Mostra i sinonimi per le parole evidenziate","Enabling this will allow the Plugin to analyze full sentences to better suggest synonyms based on the context.":"Attivando questa opzione il plugin analizzar\xE0 l'intera frase per migliorare il suggerimento dei sinonimi basati sul contesto.","Click ":"Clicca ",here:"qui"," for Privacy Concerns.":" for Privacy Concerns.","Advanced Synonym Search":"Ricerca sinonimi avanzata","Show Options in Context Menu":"Mostra le opzioni nel menu contestuale","Enable custom Context Menu with options to search for synonyms (only if the auto suggestions are disabled) and to look up a full definition in the Sidebar. Warning: This will override Obsidian's default Context Menu.":"Abilita i menucontestuali personalizzati con le opzioni di ricerca dei sinonimi (solo se i suggerimenti automatici sono disattivati) e cerca una definizione intera nella barra laterale. Attenzione: questa opzione sovrascriver\xE0 il menu contestuale di default di Obsidian.","Click Here":"Clicca qui","Definition Provider":"Provider di definizioni","The API the Plugin will use to search for Definitions.":"Le API utilizzate dal plugin per cercare le definizioni.","Synonym Provider":"Provider di sinonimi","The API the Plugin will use to search for Synonyms.":"Le API utilizzate dal plugin per cercare i sinonimi.","More Information":"Altre informazioni","View Information about the API's and the Plugin itself.":"Vai alle informazioni riguardanti le API e il plugin.","More Info":"Altre informazioni",Donate:"Dona","If you like this Plugin, consider donating to support continued development:":"Se ti piace il plugin, considera di donare per sostenere lo sviluppo","Local Dictionary Folder":"Cartella locale del dizionario","Specify a Folder, where all new Notes created by the Dictionary are placed. Please note that this Folder needs to already exist.":"Specifica una cartella dove inserire tutte le note create dal dizionario. Fai attenzione che questa cartella deve gi\xE0 esistere.","Capitalize File Name":"Rendi il nome del file maiuscolo","If you disable this, the names of newly created files will be all lowercase.":"Se disabiliti questa opzione i nomi dei file creati saranno tutti in minuscolo.","Filename Prefix and Suffix":"Suffisso e prefisso del nome del file","Here you can add a Prefix and Suffix for your newly created Files.":"Qui puoi aggiungere un prefisso e un suffisso per i nuovi file creati. Puoi usare la variabile {{lang}} qui.",Prefix:"Prefisso",Suffix:"Suffisso","Here you can edit the Template for newly created Files.":"Qui puoi modificare il template dei nuovi file creati.","Click for a List of Variables":"Clicca per vedere la lista delle variabili",Template:"Template","Local-Dictionary-Builder Settings":"Impostazioni del dizionario locale",Miscellaneous:"Altre","Caching Settings":"Impostazioni di caching","Use Caching":"Usa il caching","Enable or disable caching. Caching provides a semi-offline experience by saving every result for later use.":"Attiva o disattiva la cache. La cache fornisce un'esperienza offline tramite il salvataggio di ogni risultato per un uso successivo.","Here you can delete all cached Data.":"Qui puoi cancellare tutti i dati nella cache.","You currently have ":"Al momento ci sono "," cached Definitions and ":" definizioni nella cache e "," cached Synonyms.":" sinonimi nella cache.","Delete Cache":"Cancella la cache",Delete:"Elimina",Success:"Operazione avvenuta con successo","Use Language specific Subfolders":"Usa cartelle specifiche per la lingua",'Create Subfolders for every language, e.g. "Dictionary/en-US/Cake"':'Crea cartelle specifiche per ogni lingua, ad esempio "Dictionary/en-US/Cake"',"Autogenerated by Obsidian Dictionary Plugin":"Generate in automatico da Obsidian Dictionary Plugin","Yes, overwrite the old File.":"Si, sovrascrivi i vecchi file.","A existing File with the same Name was found, do you want to overwrite it?":"\xC8 stato trovato un file con lo stesso nome, lo vuoi sovrascrivere?","No, keep the old File.":"No, mantieni il vecchio file.","Meaning {{i}}":"Significato {{i}}","API Information":"Informazioni API","Definition API's":"Definizioni API",Website:"Sito web","Synonym API's":"API dei sinonimi","Part of Speech API's":"API per la parte parlata",'This Plugin is using <a href="https://feathericons.com/">Feather Icons</a>':'Questo plugin sta usando <a href="https://feathericons.com/">Feather Icons</a>',"Enter a word":"Inserisci una parola",Clear:"Cancella","Change Language":"Cambia lingua","Change Provider":"Cambia provider","Collapse Results":"Comprimi i risultati",Pronunciation:"Pronuncia",Meanings:"Significati",Origin:"Origini","New Note":"Nuova nota","View Error":"Vedi gli errori","Match Case":"Match Case",'Copied "{{word}}" to clipboard':'"{{word}}" copiato negli appunti',"I can't find the word you are looking for or the server can't be reached. You can try again in a few minutes.":"Non posso trovare le parole che stai cercando o il server non \xE8 raggiungibile. Riprova tra qualche minuto.","Definition:":"Definizione:","Synonyms:":"Sinonimi:","Antonyms:":"Contrari:","Choose a Definition Provider Service":"Scegli un provider di definizioni","Choose a Language":"Scegli una lingua","Choose a Synonym Provider Service":"Scegli un provider di sinonimi"};var rn={"Open Dictionary View":"\u8F9E\u66F8\u3092\u958B\u304F","Open Language Switcher":"\u8A00\u8A9E\u30B9\u30A4\u30C3\u30C1\u30E3\u30FC\u3092\u958B\u304F",Dictionary:"\u8F9E\u66F8",Cut:"\u30AB\u30C3\u30C8",Copy:"\u30B3\u30D4\u30FC",Paste:"\u30DA\u30FC\u30B9\u30C8","Show Synonyms":"\u540C\u7FA9\u8A9E\u3092\u8868\u793A","Look up":"\u8F9E\u66F8\u3067\u691C\u7D22","Reset to default":"\u30C7\u30D5\u30A9\u30EB\u30C8\u306B\u30EA\u30BB\u30C3\u30C8\u3059\u308B","Dictionary Settings":"\u8F9E\u66F8\u8A2D\u5B9A",Language:"\u8A00\u8A9E","The Language the Plugin will use to search for Definitions and Pronunciations.":"\u8A9E\u7FA9\u3068\u767A\u97F3\u306E\u691C\u7D22\u306B\u4F7F\u7528\u3059\u308B\u8A00\u8A9E\u3092\u9078\u629E\u3057\u3066\u304F\u3060\u3055\u3044\u3002","Synonym Suggestions":"\u540C\u7FA9\u8A9E\u306E\u30B5\u30B8\u30A7\u30B9\u30C8","Show synonyms for highlighted words":"\u30CF\u30A4\u30E9\u30A4\u30C8\u3055\u308C\u305F\u5358\u8A9E\u306E\u540C\u7FA9\u8A9E\u3092\u8868\u793A\u3057\u307E\u3059\u3002","Enabling this will allow the Plugin to analyze full sentences to better suggest synonyms based on the context.":"\u3053\u306E\u30AA\u30D7\u30B7\u30E7\u30F3\u3092\u6709\u52B9\u5316\u3059\u308B\u3068\u3001\u6587\u7AE0\u306E\u5B8C\u5168\u89E3\u6790\u3068\u305D\u306E\u5185\u5BB9\u306B\u57FA\u3065\u3044\u305F\u540C\u7FA9\u8A9E\u306E\u30B5\u30B8\u30A7\u30B9\u30C8\u3092\u884C\u3044\u307E\u3059\u3002","Click ":"\u30AF\u30EA\u30C3\u30AF ",here:"\u3053\u308C"," for Privacy Concerns.":" \u30D7\u30E9\u30A4\u30D0\u30B7\u30FC\u306B\u3064\u3044\u3066","Advanced Synonym Search":"\u9AD8\u5EA6\u306A\u540C\u7FA9\u8A9E\u691C\u7D22","Show Options in Context Menu":"\u30B3\u30F3\u30C6\u30AD\u30B9\u30C8\u30E1\u30CB\u30E5\u30FC\u3067\u306E\u30AA\u30D7\u30B7\u30E7\u30F3\u8868\u793A","Enable custom Context Menu with options to search for synonyms (only if the auto suggestions are disabled) and to look up a full definition in the Sidebar. Warning: This will override Obsidian's default Context Menu.":"\u30AB\u30B9\u30BF\u30E0\u30B3\u30F3\u30C6\u30AD\u30B9\u30C8\u30E1\u30CB\u30E5\u30FC\u3067\u306E\u540C\u7FA9\u8A9E\u691C\u7D22\u30AA\u30D7\u30B7\u30E7\u30F3\u3092\u6709\u52B9\u5316\u3057\u307E\u3059\u3002(\u81EA\u52D5\u30B5\u30B8\u30A7\u30B9\u30C8\u304C\u7121\u52B9\u5316\u3055\u308C\u3066\u3044\u308B\u5834\u5408\u306E\u307F)\u30B5\u30A4\u30C9\u30D0\u30FC\u3067\u5B8C\u5168\u306A\u8A9E\u7FA9\u3092\u8ABF\u3079\u308B\u3053\u3068\u304C\u3067\u304D\u307E\u3059\u3002","Click Here":"\u3053\u3053\u3092\u30AF\u30EA\u30C3\u30AF","Definition Provider":"\u8A9E\u7FA9\u30D7\u30ED\u30D0\u30A4\u30C0","The API the Plugin will use to search for Definitions.":"\u30D7\u30E9\u30B0\u30A4\u30F3\u304C\u8A9E\u7FA9\u306E\u691C\u7D22\u306B\u4F7F\u7528\u3059\u308BAPI\u3092\u9078\u629E\u3057\u3066\u304F\u3060\u3055\u3044\u3002","Synonym Provider":"\u540C\u7FA9\u8A9E\u30D7\u30ED\u30D0\u30A4\u30C0","The API the Plugin will use to search for Synonyms.":"\u30D7\u30E9\u30B0\u30A4\u30F3\u304C\u540C\u7FA9\u8A9E\u306E\u691C\u7D22\u306B\u4F7F\u7528\u3059\u308BAPI\u3092\u9078\u629E\u3057\u3066\u304F\u3060\u3055\u3044\u3002","More Information":"\u8A73\u3057\u3044\u60C5\u5831","View Information about the API's and the Plugin itself.":"API\u3068\u30D7\u30E9\u30B0\u30A4\u30F3\u306B\u3064\u3044\u3066\u306E\u60C5\u5831\u3092\u95B2\u89A7\u3067\u304D\u307E\u3059\u3002","More Info":"\u66F4\u306B\u8A73\u3057\u304F",Donate:"\u5BC4\u4ED8\u3059\u308B","If you like this Plugin, consider donating to support continued development:":"\u3053\u306E\u30D7\u30E9\u30B0\u30A4\u30F3\u304C\u6C17\u306B\u5165\u3063\u305F\u5834\u5408\u306B\u306F\u3001\u7D99\u7D9A\u7684\u306A\u958B\u767A\u306E\u30B5\u30DD\u30FC\u30C8\u306E\u305F\u3081\u306E\u5BC4\u4ED8\u3092\u691C\u8A0E\u3057\u3066\u307F\u3066\u304F\u3060\u3055\u3044\u3002","Local Dictionary Folder":"\u30ED\u30FC\u30AB\u30EB\u8F9E\u66F8\u30D5\u30A9\u30EB\u30C0","Specify a Folder, where all new Notes created by the Dictionary are placed. Please note that this Folder needs to already exist.":"\u8F9E\u66F8\u306B\u3088\u308A\u4F5C\u6210\u3055\u308C\u305F\u65B0\u898F\u30CE\u30FC\u30C8\u304C\u914D\u7F6E\u3055\u308C\u308B\u30D5\u30A9\u30EB\u30C0\u3092\u6307\u5B9A\u3057\u3066\u304F\u3060\u3055\u3044\u3002\u3059\u3067\u306B\u5B58\u5728\u3059\u308B\u30D5\u30A9\u30EB\u30C0\u3092\u6307\u5B9A\u3057\u3066\u304F\u3060\u3055\u3044\u3002","Capitalize File Name":"\u30D5\u30A1\u30A4\u30EB\u540D\u3092\u5927\u6587\u5B57\u304B\u3089\u59CB\u3081\u3066\u4F5C\u6210\u3059\u308B","If you disable this, the names of newly created files will be all lowercase.":"\u3053\u306E\u30AA\u30D7\u30B7\u30E7\u30F3\u3092\u7121\u52B9\u5316\u3059\u308B\u3068\u65B0\u898F\u4F5C\u6210\u3055\u308C\u305F\u30D5\u30A1\u30A4\u30EB\u540D\u306F\u3059\u3079\u3066\u5C0F\u6587\u5B57\u306B\u306A\u308A\u307E\u3059\u3002","Filename Prefix and Suffix":"\u30D5\u30A1\u30A4\u30EB\u540D\u306E\u30D7\u30EC\u30D5\u30A3\u30C3\u30AF\u30B9\u3068\u30B5\u30D5\u30A3\u30C3\u30AF\u30B9","Here you can add a Prefix and Suffix for your newly created Files.":"\u3053\u306E\u30AA\u30D7\u30B7\u30E7\u30F3\u3067\u306F\u3001\u65B0\u898F\u4F5C\u6210\u3055\u308C\u308B\u30D5\u30A1\u30A4\u30EB\u306B\u5BFE\u3057\u3066\u30D7\u30EC\u30D5\u30A3\u30C3\u30AF\u30B9\u307E\u305F\u306F\u30B5\u30D5\u30A3\u30C3\u30AF\u30B9\u3092\u8FFD\u52A0\u3059\u308B\u3053\u3068\u304C\u3067\u304D\u307E\u3059\u3002\u3053\u306E\u9805\u76EE\u3067\u306F{{lang}}\u5909\u6570\u3092\u4F7F\u7528\u3067\u304D\u307E\u3059\u3002",Prefix:"\u30D7\u30EC\u30D5\u30A3\u30C3\u30AF\u30B9",Suffix:"\u30B5\u30D5\u30A3\u30C3\u30AF\u30B9","Here you can edit the Template for newly created Files.":"\u3053\u306E\u30AA\u30D7\u30B7\u30E7\u30F3\u3067\u306F\u65B0\u898F\u4F5C\u6210\u3055\u308C\u308B\u30D5\u30A1\u30A4\u30EB\u306B\u3064\u3044\u3066\u306E\u30C6\u30F3\u30D7\u30EC\u30FC\u30C8\u3092\u7DE8\u96C6\u3067\u304D\u307E\u3059\u3002","Click for a List of Variables":"\u5909\u6570\u306E\u30EA\u30B9\u30C8\u3092\u95B2\u89A7\u3059\u308B",Template:"\u30C6\u30F3\u30D7\u30EC\u30FC\u30C8","Local-Dictionary-Builder Settings":"\u30ED\u30FC\u30AB\u30EB\u8F9E\u66F8\u30D3\u30EB\u30C0\u30FC\u306E\u8A2D\u5B9A",Miscellaneous:"\u305D\u306E\u4ED6\u306E\u30AA\u30D7\u30B7\u30E7\u30F3","Caching Settings":"\u30AD\u30E3\u30C3\u30B7\u30E5\u306E\u8A2D\u5B9A","Use Caching":"\u30AD\u30E3\u30C3\u30B7\u30E5\u3092\u4F7F\u7528\u3059\u308B","Enable or disable caching. Caching provides a semi-offline experience by saving every result for later use.":"\u30AD\u30E3\u30C3\u30B7\u30E5\u306E\u4F7F\u7528\u3092\u9078\u629E\u3067\u304D\u307E\u3059\u3002\u30AD\u30E3\u30C3\u30B7\u30E5\u3092\u6709\u52B9\u5316\u3059\u308B\u3068\u3059\u3079\u3066\u306E\u691C\u7D22\u7D50\u679C\u3092\u4FDD\u5B58\u3057\u3066\u304A\u304D\u3001\u5F8C\u304B\u3089\u30BB\u30DF\u30AA\u30D5\u30E9\u30A4\u30F3\u3067\u306E\u6A5F\u80FD\u63D0\u4F9B\u304C\u53EF\u80FD\u3067\u3059\u3002","Here you can delete all cached Data.":"\u3053\u306E\u30AA\u30D7\u30B7\u30E7\u30F3\u3067\u3059\u3079\u3066\u306E\u30AD\u30E3\u30C3\u30B7\u30E5\u30C7\u30FC\u30BF\u3092\u524A\u9664\u3059\u308B\u3053\u3068\u304C\u3067\u304D\u307E\u3059\u3002","You currently have ":"\u73FE\u5728\u3001\u6B21\u306E\u9805\u76EE\u3092\u30AD\u30E3\u30C3\u30B7\u30E5\u3057\u3066\u3044\u307E\u3059: "," cached Definitions and ":" \u8A9E\u7FA9 "," cached Synonyms.":" \u540C\u7FA9\u8A9E","Delete Cache":"\u30AD\u30E3\u30C3\u30B7\u30E5\u306E\u524A\u9664",Delete:"\u524A\u9664\u3059\u308B",Success:"\u6210\u529F\u3057\u307E\u3057\u305F","Use Language specific Subfolders":"\u7279\u5B9A\u8A00\u8A9E\u306E\u30B5\u30D6\u30D5\u30A9\u30EB\u30C0\u3092\u4F7F\u7528\u3059\u308B",'Create Subfolders for every language, e.g. "Dictionary/en-US/Cake"':'\u6307\u5B9A\u306E\u8A00\u8A9E\u306B\u3064\u3044\u3066\u306E\u30B5\u30D6\u30D5\u30A9\u30EB\u30C0\u3092\u4F5C\u6210\u3057\u307E\u3059\u3002\u4F8B: "Dictionary/en-US/Cake"',"Autogenerated by Obsidian Dictionary Plugin":"Obsidian Dictionary Plugin\u306B\u3088\u3063\u3066\u81EA\u52D5\u751F\u6210\u3055\u308C\u3066\u3044\u307E\u3059","Yes, overwrite the old File.":"\u306F\u3044\u3001\u53E4\u3044\u30D5\u30A1\u30A4\u30EB\u3092\u4E0A\u66F8\u304D\u3057\u307E\u3059\u3002","A existing File with the same Name was found, do you want to overwrite it?":"\u540C\u4E00\u540D\u306E\u65E2\u5B58\u30D5\u30A1\u30A4\u30EB\u304C\u898B\u3064\u304B\u308A\u307E\u3057\u305F\u3002\u65B0\u3057\u3044\u5185\u5BB9\u3067\u4E0A\u66F8\u304D\u3057\u307E\u3059\u304B\uFF1F","No, keep the old File.":"\u3044\u3044\u3048\u3001\u53E4\u3044\u30D5\u30A1\u30A4\u30EB\u306F\u4FDD\u5B58\u3057\u3066\u304F\u3060\u3055\u3044\u3002","Meaning {{i}}":"\u610F\u5473 {{i}}","API Information":"API\u306B\u3064\u3044\u3066\u306E\u60C5\u5831","Definition API's":"\u8A9E\u7FA9API",Website:"\u30A6\u30A7\u30D6\u30B5\u30A4\u30C8","Synonym API's":"\u540C\u7FA9\u8A9EAPI","Part of Speech API's":"\u54C1\u8A5EAPI",'This Plugin is using <a href="https://feathericons.com/">Feather Icons</a>':'\u3053\u306E\u30D7\u30E9\u30B0\u30A4\u30F3\u306F<a href="https://feathericons.com/">Feather Icons</a>\u3092\u4F7F\u7528\u3057\u3066\u3044\u307E\u3059',"Enter a word":"\u5358\u8A9E\u3092\u5165\u529B",Clear:"\u30AF\u30EA\u30A2","Change Language":"\u8A00\u8A9E\u3092\u5909\u66F4","Change Provider":"\u30D7\u30ED\u30D0\u30A4\u30C0\u3092\u5909\u66F4","Collapse Results":"\u7D50\u679C\u3092\u5C55\u958B",Pronunciation:"\u767A\u97F3",Meanings:"\u610F\u5473",Origin:"\u8A9E\u6E90","New Note":"\u65B0\u898F\u30CE\u30FC\u30C8","View Error":"\u30A8\u30E9\u30FC\u3092\u8868\u793A","Match Case":"\u5927\u6587\u5B57/\u5C0F\u6587\u5B57\u3092\u533A\u5225",'Copied "{{word}}" to clipboard':'"{{word}}"\u3092\u30AF\u30EA\u30C3\u30D7\u30DC\u30FC\u30C9\u306B\u30B3\u30D4\u30FC\u3057\u307E\u3057\u305F',"I can't find the word you are looking for or the server can't be reached. You can try again in a few minutes.":"\u63A2\u3057\u3066\u3044\u308B\u5358\u8A9E\u304C\u898B\u3064\u304B\u3089\u306A\u3044\u304B\u3001\u30B5\u30FC\u30D0\u30FC\u304C\u63A5\u7D9A\u3067\u304D\u307E\u305B\u3093\u3002\u6570\u5206\u7ACB\u3063\u3066\u304B\u3089\u30EA\u30C8\u30E9\u30A4\u3057\u3066\u304F\u3060\u3055\u3044\u3002","Definition:":"\u8A9E\u7FA9:","Synonyms:":"\u540C\u7FA9\u8A9E:","Antonyms:":"\u53CD\u610F\u8A9E:","Choose a Definition Provider Service":"\u5B9A\u7FA9\u306E\u30D7\u30ED\u30D0\u30A4\u30C0\u30B5\u30FC\u30D3\u30B9\u3092\u9078\u629E","Choose a Language":"\u8A00\u8A9E\u3092\u9078\u629E","Choose a Synonym Provider Service":"\u540C\u7FA9\u8A9E\u306E\u30D7\u30ED\u30D0\u30A4\u30C0\u30B5\u30FC\u30D3\u30B9\u3092\u9078\u629E"};var on={"Open Dictionary View":"\uC0AC\uC804 \uBCF4\uAE30 \uC5F4\uAE30","Open Language Switcher":"\uC5B8\uC5B4 \uC804\uD658 \uC5F4\uAE30",Dictionary:"\uC0AC\uC804",Cut:"\uC798\uB77C\uB0B4\uAE30",Copy:"\uBCF5\uC0AC",Paste:"\uBD99\uC5EC\uB123\uAE30","Show Synonyms":"\uC720\uC758\uC5B4 \uD45C\uC2DC","Look up":"\uCC3E\uAE30","Reset to default":"\uAE30\uBCF8\uAC12\uC73C\uB85C \uC7AC\uC124\uC815","Dictionary Settings":"\uC0AC\uC804 \uC124\uC815",Language:"\uC5B8\uC5B4","The Language the Plugin will use to search for Definitions and Pronunciations.":"\uC0AC\uC804 \uC815\uC758 \uBC0F \uBC1C\uC74C \uAC80\uC0C9\uC744 \uC704\uD55C \uC5B8\uC5B4\uC785\uB2C8\uB2E4.","Synonym Suggestions":"\uC720\uC758\uC5B4 \uC81C\uC548","Show synonyms for highlighted words":"\uAC15\uC870\uB41C \uB2E8\uC5B4\uC758 \uC720\uC758\uC5B4\uB97C \uD45C\uC2DC\uD569\uB2C8\uB2E4.","Enabling this will allow the Plugin to analyze full sentences to better suggest synonyms based on the context.":"\uD65C\uC131\uD654 \uC2DC \uC804\uCCB4 \uBB38\uC7A5\uC744 \uBD84\uC11D\uD558\uC5EC \uCEE8\uD14D\uC2A4\uD2B8\uB97C \uAE30\uBC18\uC73C\uB85C \uC720\uC758\uC5B4\uB97C \uB354 \uC798 \uC81C\uC548\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.","Click ":"Click ",here:"here"," for Privacy Concerns.":" for Privacy Concerns.","Advanced Synonym Search":"\uACE0\uAE09 \uC720\uC758\uC5B4 \uAC80\uC0C9","Show Options in Context Menu":"\uC0C1\uD669\uC5D0 \uB9DE\uB294 \uBA54\uB274 \uD45C\uC2DC","Enable custom Context Menu with options to search for synonyms (only if the auto suggestions are disabled) and to look up a full definition in the Sidebar. Warning: This will override Obsidian's default Context Menu.":"\uC790\uB3D9 \uC81C\uC548\uC774 \uBE44\uD65C\uC131\uD654\uB41C \uACBD\uC6B0\uC5D0\uB9CC \uB3D9\uC758\uC5B4\uB97C \uAC80\uC0C9\uD558\uACE0 \uC0AC\uC774\uB4DC\uBC14\uC5D0\uC11C \uC804\uCCB4 \uC815\uC758\uB97C \uC870\uD68C\uD558\uB294 \uB9DE\uCDA4 \uCEE8\uD14D\uC2A4\uD2B8 \uBA54\uB274\uB97C \uD65C\uC131\uD654\uD569\uB2C8\uB2E4.","Click Here":"\uC5EC\uAE30\uB97C \uD074\uB9AD\uD558\uC138\uC694.","Definition Provider":"\uC0AC\uC804 \uC815\uC758 \uC81C\uACF5\uC790","The API the Plugin will use to search for Definitions.":"\uC0AC\uC804 \uC815\uC758 \uAC80\uC0C9\uC5D0 \uC0AC\uC6A9\uD560 API","Synonym Provider":"\uC720\uC758\uC5B4 \uC81C\uACF5\uC790","The API the Plugin will use to search for Synonyms.":"\uC720\uC758\uC5B4 \uAC80\uC0C9\uC5D0 \uC0AC\uC6A9\uD560 API","More Information":"More Information","View Information about the API's and the Plugin itself.":"API \uBC0F \uD50C\uB7EC\uADF8\uC778 \uB300\uD55C \uC815\uBCF4\uB97C \uBD05\uB2C8\uB2E4.","More Info":"\uB354\uBCF4\uAE30",Donate:"\uAE30\uBD80","If you like this Plugin, consider donating to support continued development:":"\uC774 \uD50C\uB7EC\uADF8\uC778\uC774 \uB9D8\uC5D0\uB4E0\uB2E4\uBA74 \uC9C0\uC18D\uC801\uC73C\uB85C \uAC1C\uBC1C\uD560 \uC218 \uC788\uB3C4\uB85D \uAE30\uBD80\uD574 \uC8FC\uC138\uC694.","Local Dictionary Folder":"\uB85C\uCEEC \uC0AC\uC804 \uD3F4\uB354","Specify a Folder, where all new Notes created by the Dictionary are placed. Please note that this Folder needs to already exist.":"\uC0AC\uC804\uC774 \uB9CC\uB4E4\uC5B4\uC9C8 \uD3F4\uB354\uB97C \uC9C0\uC815\uD558\uC138\uC694. \uCC38\uACE0\uB85C \uC774 \uD3F4\uB354\uB294 \uC0AC\uC804\uC5D0 \uB9CC\uB4E4\uC5B4\uC838 \uC788\uC5B4\uC57C \uD569\uB2C8\uB2E4.","Capitalize File Name":"\uB300\uBB38\uC790 \uD30C\uC77C\uBA85","If you disable this, the names of newly created files will be all lowercase.":"\uBE44\uD65C\uC131\uD654 \uC2DC \uC0C8\uB85C \uC791\uC131\uB41C \uD30C\uC77C\uBA85\uC740 \uC18C\uBB38\uC790\uB85C \uC791\uC131\uB429\uB2C8\uB2E4.","Filename Prefix and Suffix":"\uD30C\uC77C\uBA85 \uC811\uB450\uC0AC \uBC0F \uC811\uBBF8\uC5B4","Here you can add a Prefix and Suffix for your newly created Files.":"\uC0C8\uB85C \uB9CC\uB4E4\uC5B4\uC9C4 \uD30C\uC77C\uC758 \uC811\uB450\uC0AC \uBC0F \uC811\uBBF8\uC5B4\uB97C \uCD94\uAC00\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4. {{lang}} \uBCC0\uC218\uB97C \uC0AC\uC6A9\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.",Prefix:"\uC811\uB450\uC0AC",Suffix:"\uC811\uBBF8\uC5B4","Here you can edit the Template for newly created Files.":"\uC0C8\uB85C \uB9CC\uB4E4\uC5B4\uC9C4 \uD30C\uC77C\uC758 \uD15C\uD50C\uB9BF\uC744 \uC218\uC815\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.","Click for a List of Variables":"\uBCC0\uC218 \uBAA9\uB85D \uBCF4\uB7EC\uAC00\uAE30",Template:"\uD15C\uD50C\uB9BF","Local-Dictionary-Builder Settings":"Local-Dictionary-Builder \uC124\uC815",Miscellaneous:"\uAE30\uD0C0","Caching Settings":"\uCE90\uC2F1 \uC124\uC815","Use Caching":"\uCE90\uC2F1 \uC0AC\uC6A9","Enable or disable caching. Caching provides a semi-offline experience by saving every result for later use.":"\uCE90\uC2F1\uC744 \uD65C\uC131\uD654\uD558\uAC70\uB098 \uBE44\uD65C\uC131\uD654\uD569\uB2C8\uB2E4. \uCE90\uC2F1\uC740 \uB098\uC911\uC5D0 \uC0AC\uC6A9\uD560 \uC218 \uC788\uB3C4\uB85D \uBAA8\uB4E0 \uACB0\uACFC\uB97C \uC800\uC7A5\uD558\uC5EC \uC138\uBBF8 \uC624\uD504\uB77C\uC778 \uD658\uACBD\uC744 \uC81C\uACF5\uD569\uB2C8\uB2E4.","Here you can delete all cached Data.":"\uCE90\uC2DC\uB41C \uB370\uC774\uD130\uB97C \uBAA8\uB450 \uC0AD\uC81C\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.","You currently have ":"\uD604\uC7AC "," cached Definitions and ":"\uAC1C\uC758 \uCE90\uC26C\uB41C \uC0AC\uC804\uC815\uC758 \uBC0F "," cached Synonyms.":"\uAC1C\uC758 \uCE90\uC26C\uB41C \uC720\uC758\uC5B4\uAC00 \uC788\uC2B5\uB2C8\uB2E4.","Delete Cache":"\uCE90\uC26C \uC0AD\uC81C",Delete:"\uC0AD\uC81C",Success:"\uC131\uACF5","Use Language specific Subfolders":"\uC5B8\uC5B4\uBCC4 \uD558\uC704 \uD3F4\uB354 \uC0AC\uC6A9",'Create Subfolders for every language, e.g. "Dictionary/en-US/Cake"':'\uC5B8\uC5B4\uBCC4\uB85C \uD558\uC704 \uD3F4\uB354\uB97C \uC0DD\uC131\uD569\uB2C8\uB2E4. \uC608) "Dictionary/en-US/Cake"',"Autogenerated by Obsidian Dictionary Plugin":"Obsidian \uC0AC\uC804 \uD50C\uB7EC\uADF8\uC778\uC5D0 \uC758\uD574 \uC790\uB3D9 \uC0DD\uC131","Yes, overwrite the old File.":"\uB124, \uC774\uC804 \uD30C\uC77C\uC744 \uB36E\uC5B4\uC501\uB2C8\uB2E4.","A existing File with the same Name was found, do you want to overwrite it?":"\uB3D9\uC77C\uD55C \uC774\uB984\uC758 \uAE30\uC874 \uD30C\uC77C\uC744 \uCC3E\uC558\uC2B5\uB2C8\uB2E4. \uB36E\uC5B4\uC4F0\uC2DC\uACA0\uC2B5\uB2C8\uAE4C?","No, keep the old File.":"\uC544\uB2C8\uC694, \uC774\uC804 \uD30C\uC77C\uC744 \uC720\uC9C0\uD569\uB2C8\uB2E4.","Meaning {{i}}":"\uC758\uBBF8 {{i}}","API Information":"API \uC815\uBCF4","Definition API's":"\uC0AC\uC804 \uC815\uC758 API",Website:"\uC6F9\uC0AC\uC774\uD2B8","Synonym API's":"\uC720\uC758\uC5B4 API","Part of Speech API's":"\uC74C\uC131 API",'This Plugin is using <a href="https://feathericons.com/">Feather Icons</a>':'\uC774 \uD50C\uB7EC\uADF8\uC778\uC740 <a href="https://feathericons.com/">Feather Icons</a>\uC744 \uC0AC\uC6A9\uD569\uB2C8\uB2E4.',"Enter a word":"\uB2E8\uC5B4\uB97C \uC785\uB825\uD558\uC138\uC694.",Clear:"Clear","Change Language":"\uC5B8\uC5B4 \uBCC0\uACBD","Change Provider":"Change Provider","Collapse Results":"\uACB0\uACFC \uC811\uAE30",Pronunciation:"\uBC1C\uC74C",Meanings:"\uC758\uBBF8",Origin:"\uAE30\uC6D0","New Note":"\uC2E0\uADDC \uB178\uD2B8","View Error":"\uC624\uB958 \uBCF4\uAE30","Match Case":"\uB300\uC18C\uBB38\uC790 \uAD6C\uBD84",'Copied "{{word}}" to clipboard':'"{{word}}"\uB97C \uD074\uB9BD\uBCF4\uB4DC\uC5D0 \uBCF5\uC0AC\uB418\uC5C8\uC2B5\uB2C8\uB2E4.',"I can't find the word you are looking for or the server can't be reached. You can try again in a few minutes.":"\uB2E8\uC5B4\uB97C \uCC3E\uC744 \uC218 \uC5C6\uAC70\uB098 \uC11C\uBC84\uAC00 \uC5F0\uACB0\uB418\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4. \uC7A0\uC2DC \uD6C4 \uB2E4\uC2DC \uC2DC\uB3C4\uD574 \uBCF4\uC138\uC694.","Definition:":"\uC0AC\uC804 \uC815\uC758 :","Synonyms:":"\uC720\uC758\uC5B4:","Antonyms:":"\uBC18\uC758\uC5B4:","Choose a Definition Provider Service":"\uC0AC\uC804 \uC815\uC758 \uC81C\uACF5 \uC11C\uBE44\uC2A4\uB97C \uC120\uD0DD\uD558\uC138\uC694.","Choose a Language":"\uC5B8\uC5B4\uB97C \uC120\uD0DD\uD558\uC138\uC694.","Choose a Synonym Provider Service":"\uC720\uC758\uC5B4 \uC81C\uACF5 \uC11C\uBE44\uC2A4\uB97C \uC120\uD0DD\uD558\uC138\uC694."};var an={};var sn={};var ln={};var cn={"Open Dictionary View":"Abrir Vista do Dicion\xE1rio","Open Language Switcher":"Abrir Selector de L\xEDngua",Dictionary:"Dicion\xE1rio",Cut:"Cortar",Copy:"Copiar",Paste:"Colar","Show Synonyms":"Mostrar Sin\xF3nimos","Look up":"Procurar","Reset to default":"Reinicializa\xE7\xE3o por defeito","Dictionary Settings":"Configura\xE7\xF5es de Dicion\xE1rio",Language:"L\xEDngua","The Language the Plugin will use to search for Definitions and Pronunciations.":"A L\xEDngua que o Plugin utilizar\xE1 para pesquisar Defini\xE7\xF5es e Pron\xFAncias.","Synonym Suggestions":"Sugest\xF5es de Sin\xF3nimos","Show synonyms for highlighted words":"Mostrar sin\xF3nimos para palavras em destaque","Enabling this will allow the Plugin to analyze full sentences to better suggest synonyms based on the context.":"A habilita\xE7\xE3o permitir\xE1 ao Plugin analisar as frases completas para melhor sugerir sin\xF3nimos com base no contexto.","Click ":"Clicar ",here:"aqui"," for Privacy Concerns.":" por Quest\xF5es de Privacidade.","Advanced Synonym Search":"Pesquisa Avan\xE7ada de Sin\xF3nimos","Show Options in Context Menu":"Mostrar Op\xE7\xF5es no Menu de Contexto","Enable custom Context Menu with options to search for synonyms (only if the auto suggestions are disabled) and to look up a full definition in the Sidebar. Warning: This will override Obsidian's default Context Menu.":"Permitir Menu de Contexto personalizado com op\xE7\xF5es para procurar sin\xF3nimos (apenas se as sugest\xF5es autom\xE1ticas estiverem desactivadas) e procurar uma defini\xE7\xE3o completa na Barra Lateral. Advert\xEAncia: Isto substituir\xE1 o Menu de Contexto padr\xE3o da Obsidian.","Click Here":"Clicar Aqui","Definition Provider":"Provedor de Defini\xE7\xF5es","The API the Plugin will use to search for Definitions.":"O API que o Plugin utilizar\xE1 para pesquisar Defini\xE7\xF5es.","Synonym Provider":"Provedor de Sin\xF3nimos","The API the Plugin will use to search for Synonyms.":"O API que o Plugin utilizar\xE1 para procurar Sin\xF3nimos.","More Information":"Mais Informa\xE7\xF5es","View Information about the API's and the Plugin itself.":"Ver Informa\xE7\xE3o sobre as API's e o pr\xF3prio Plugin.","More Info":"Mais Info",Donate:"Doar","If you like this Plugin, consider donating to support continued development:":"Se gostar deste Plugin, considere fazer uma doa\xE7\xE3o para apoiar o desenvolvimento cont\xEDnuo:","Local Dictionary Folder":"Pasta de Dicion\xE1rios Local","Specify a Folder, where all new Notes created by the Dictionary are placed. Please note that this Folder needs to already exist.":"Especificar uma Pasta, onde todas as novas Notas criadas pelo Dicion\xE1rio s\xE3o colocadas. \xC9 favor notar que esta Pasta j\xE1 deve existir.","Capitalize File Name":"Capitalizar Nome do Ficheiro","If you disable this, the names of newly created files will be all lowercase.":"Se desactivar isto, os nomes dos ficheiros rec\xE9m-criados ser\xE3o todos em min\xFAsculas.","Filename Prefix and Suffix":"Prefixo e Sufixo do Nome do Ficheiro","Here you can add a Prefix and Suffix for your newly created Files.":"Aqui pode adicionar um Prefixo e Sufixo para os seus Ficheiros recentemente criados. Pode utilizar a vari\xE1vel {{lang}} aqui.",Prefix:"Prefixo",Suffix:"Sufixo","Here you can edit the Template for newly created Files.":"Aqui pode editar o Template para Ficheiros rec\xE9m-criados.","Click for a List of Variables":"Clique para uma Lista de Vari\xE1veis",Template:"Template","Local-Dictionary-Builder Settings":"Configura\xE7\xF5es do Local-Dictionary-Builder",Miscellaneous:"Miscel\xE2neos","Caching Settings":"Configura\xE7\xF5es de Caching","Use Caching":"Usar Caching","Enable or disable caching. Caching provides a semi-offline experience by saving every result for later use.":"Activar ou desactivar o caching. O caching proporciona uma experi\xEAncia semi-offline ao guardar todos os resultados para utiliza\xE7\xE3o posterior.","Here you can delete all cached Data.":"Aqui pode apagar todos os dados armazenados em cache.","You currently have ":"Actualmente tem "," cached Definitions and ":" Defini\xE7\xF5es em cache e "," cached Synonyms.":" Sin\xF3nimos em cache.","Delete Cache":"Eliminar Cache",Delete:"Eliminar",Success:"\xCAxito","Use Language specific Subfolders":"Utilizar Subpastas espec\xEDficas da L\xEDngua",'Create Subfolders for every language, e.g. "Dictionary/en-US/Cake"':'Criar Subpastas para cada l\xEDngua, por exemplo, "Dicion\xE1rio/en-US/Cake".',"Autogenerated by Obsidian Dictionary Plugin":"Autogerado por Obsidian Dictionary Plugin","Yes, overwrite the old File.":"Sim, sobregravar o ficheiro antigo.","A existing File with the same Name was found, do you want to overwrite it?":"Foi encontrado um Ficheiro existente com o mesmo Nome, quer sobregrav\xE1-lo?","No, keep the old File.":"N\xE3o, manter o ficheiro antigo.","Meaning {{i}}":"Significado {{i}}","API Information":"Informa\xE7\xE3o de API","Definition API's":"API de Defini\xE7\xE3o",Website:"S\xEDtio Web","Synonym API's":"API de Sin\xF3nimos","Part of Speech API's":"Parte das API de Discurso",'This Plugin is using <a href="https://feathericons.com/">Feather Icons</a>':'Este Plugin est\xE1 a usar <a href="https://feathericons.com/">Feather Icons</a>',"Enter a word":"Introduza uma palavra",Clear:"Limpar","Change Language":"Mudar L\xEDngua","Change Provider":"Mudar Provedor","Collapse Results":"Colapsar os Resultados",Pronunciation:"Pronunciamento",Meanings:"Significados",Origin:"Origem","New Note":"Nova Nota","View Error":"Ver Erro","Match Case":"Corresponder Caixa",'Copied "{{word}}" to clipboard':'Copiado "{{word}}" para clipboard',"I can't find the word you are looking for or the server can't be reached. You can try again in a few minutes.":"N\xE3o consigo encontrar a palavra que procura ou o servidor n\xE3o pode ser alcan\xE7ado. Pode tentar novamente dentro de alguns minutos.","Definition:":"Defini\xE7\xE3o:","Synonyms:":"Sin\xF3nimos:","Antonyms:":"Ant\xF3nimos:","Choose a Definition Provider Service":"Escolha um Servi\xE7o Provedor de Defini\xE7\xF5es","Choose a Language":"Escolha uma L\xEDngua","Choose a Synonym Provider Service":"Escolha um Servi\xE7o Provedor de Sin\xF3nimos"};var un={"Open Dictionary View":"Abrir Visualiza\xE7\xE3o do Dicion\xE1rio","Open Language Switcher":"Abrir Seletor de Idioma",Dictionary:"Dicion\xE1rio",Cut:"Cortar",Copy:"Copiar",Paste:"Colar","Show Synonyms":"Mostrar Sin\xF4nimos","Look up":"Procurar","Reset to default":"Restaurar para o padr\xE3o","Dictionary Settings":"Configura\xE7\xF5es do Dicion\xE1rio",Language:"Idioma","The Language the Plugin will use to search for Definitions and Pronunciations.":"O Idioma que o Plugin ir\xE1 utilizar para pesquisar as Defini\xE7\xF5es e as Pronuncia\xE7\xF5es.","Synonym Suggestions":"Sugest\xF5es de Sin\xF4nimo","Show synonyms for highlighted words":"Mostrar sin\xF4nimos para palavras em destaque","Enabling this will allow the Plugin to analyze full sentences to better suggest synonyms based on the context.":"A ativa\xE7\xE3o disso permitir\xE1 ao Plugin analisar as frases completas para melhor sugerir sin\xF4nimos com base no contexto.","Click ":"Clique ",here:"aqui"," for Privacy Concerns.":" para Quest\xF5es de Privacidade.","Advanced Synonym Search":"Pesquisa Avan\xE7ada de Sin\xF4nimos","Show Options in Context Menu":"Mostrar Op\xE7\xF5es no Menu de Contexto","Enable custom Context Menu with options to search for synonyms (only if the auto suggestions are disabled) and to look up a full definition in the Sidebar. Warning: This will override Obsidian's default Context Menu.":"Permitir Menu de Contexto personalizado com op\xE7\xF5es para procurar sin\xF4nimos (apenas se as sugest\xF5es autom\xE1ticas estiverem desativadas) e procurar uma defini\xE7\xE3o completa na Barra Lateral. Aviso: Isso substituir\xE1 o Menu de Contexto padr\xE3o do Obsidian.","Click Here":"Clique Aqui","Definition Provider":"Provedor de Defini\xE7\xE3o","The API the Plugin will use to search for Definitions.":"A API que o plugin utilizar\xE1 para procurar por Defini\xE7\xF5es.","Synonym Provider":"Provedor de Sin\xF4nimo","The API the Plugin will use to search for Synonyms.":"A API que o plugin utilizar\xE1 para procurar por Sin\xF4nimos.","More Information":"Mais Informa\xE7\xF5es","View Information about the API's and the Plugin itself.":"Ver Informa\xE7\xF5es sobre a API e sobre o Plugin em si.","More Info":"Mais Info",Donate:"Doar","If you like this Plugin, consider donating to support continued development:":"Se voc\xEA gosta deste Plugin, considere fazer uma doa\xE7\xE3o para apoiar o desenvolvimento cont\xEDnuo:","Local Dictionary Folder":"Pasta do Dicion\xE1rio Local","Specify a Folder, where all new Notes created by the Dictionary are placed. Please note that this Folder needs to already exist.":"Especificar uma Pasta, onde todas as novas Notas criadas pelo Dicion\xE1rio ser\xE3o colocadas. \xC9 necess\xE1rio que esta Pasta j\xE1 exista em seu Obsidian.","Capitalize File Name":"Capitalizar o Nome do Arquivo","If you disable this, the names of newly created files will be all lowercase.":"Se voc\xEA desativar isso, os nomes dos arquivos rec\xE9m-criados ser\xE3o todos em min\xFAsculas.","Filename Prefix and Suffix":"Prefixo e Sufixo do nome do arquivo","Here you can add a Prefix and Suffix for your newly created Files.":"Aqui voc\xEA pode adicionar um Prefixo e Sufixo para seus arquivos rec\xE9m-criados. Voc\xEA pode usar a vari\xE1vel {{lang}} aqui.",Prefix:"Prefixo",Suffix:"Sufixo","Here you can edit the Template for newly created Files.":"Aqui voc\xEA pode editar o Template para arquivos rec\xE9m-criados.","Click for a List of Variables":"Clique para uma Lista de Vari\xE1veis",Template:"Template","Local-Dictionary-Builder Settings":"Configura\xE7\xF5es do Local-Dictionary-Builder (Construtor do Dicion\xE1rio Local)",Miscellaneous:"Miscel\xE2neas","Caching Settings":"Configura\xE7\xF5es de Cache","Use Caching":"Usar Cache","Enable or disable caching. Caching provides a semi-offline experience by saving every result for later use.":"Habilitar ou desabilitar o cache. O cache proporciona uma experi\xEAncia semi-offline ao salvar cada resultado para uso posterior.","Here you can delete all cached Data.":"Aqui voc\xEA pode excluir todos os dados armazenados em cache.","You currently have ":"Voc\xEA atualmente tem "," cached Definitions and ":" Defini\xE7\xF5es em cache e "," cached Synonyms.":" Sin\xF4nimos em cache.","Delete Cache":"Apagar Cache",Delete:"Apagar",Success:"Sucesso","Use Language specific Subfolders":"Usar Subpastas espec\xEDficas do Idioma",'Create Subfolders for every language, e.g. "Dictionary/en-US/Cake"':'Criar subpastas para cada idioma, por exemplo, "Dictionary/en-US/Cake"',"Autogenerated by Obsidian Dictionary Plugin":"Autogerado pelo Plugin Dicion\xE1rio do Obsidian","Yes, overwrite the old File.":"Sim, substitua o Arquivo antigo.","A existing File with the same Name was found, do you want to overwrite it?":"Foi encontrado um Arquivo existente com o mesmo Nome, voc\xEA quer substitu\xED-lo?","No, keep the old File.":"N\xE3o, manter o Arquivo antigo.","Meaning {{i}}":"Significado {{i}}","API Information":"Informa\xE7\xE3o da API","Definition API's":"API de Defini\xE7\xE3o",Website:"P\xE1gina Web","Synonym API's":"API de Sin\xF4nimos","Part of Speech API's":"Parte da API de Discurso",'This Plugin is using <a href="https://feathericons.com/">Feather Icons</a>':'Este Plugin est\xE1 usando <a href="https://feathericons.com/">Feather Icons</a>',"Enter a word":"Digite uma palavra",Clear:"Limpar","Change Language":"Mudar Idioma","Change Provider":"Mudar Provedor","Collapse Results":"Colapsar os Resultados",Pronunciation:"Pron\xFAncia",Meanings:"Significados",Origin:"Origem","New Note":"Nova Nota","View Error":"Ver Erro","Match Case":"Corresponder Caixa",'Copied "{{word}}" to clipboard':'Copiado "{{word}}" para a \xE1rea de transfer\xEAncia (clipboard)',"I can't find the word you are looking for or the server can't be reached. You can try again in a few minutes.":"N\xE3o consigo encontrar a palavra que procura ou o servidor n\xE3o pode ser alcan\xE7ado. Tente novamente dentro de alguns minutos.","Definition:":"Defini\xE7\xE3o:","Synonyms:":"Sin\xF4nimos:","Antonyms:":"Ant\xF4nimos:","Choose a Definition Provider Service":"Escolha um Servi\xE7o de Provedor de Defini\xE7\xE3o","Choose a Language":"Escolha um Idioma","Choose a Synonym Provider Service":"Escolha um Servi\xE7o de Provedor de Sin\xF4nimo"};var dn={};var fn={"Open Dictionary View":"\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u041C\u0435\u043D\u044E \u0421\u043B\u043E\u0432\u0430\u0440\u044F","Open Language Switcher":"\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u0412\u044B\u0431\u043E\u0440 \u042F\u0437\u044B\u043A\u0430",Dictionary:"\u0421\u043B\u043E\u0432\u0430\u0440\u044C",Cut:"\u0412\u044B\u0440\u0435\u0437\u0430\u0442\u044C",Copy:"\u0421\u043A\u043E\u043F\u0438\u0440\u043E\u0432\u0430\u0442\u044C",Paste:"\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u044C","Show Synonyms":"\u041F\u043E\u043A\u0430\u0437\u0430\u0442\u044C \u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B","Look up":"\u041D\u0430\u0439\u0442\u0438","Reset to default":"\u0421\u0431\u0440\u043E\u0441\u0438\u0442\u044C \u043A \u043D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0430\u043C \u043F\u043E \u0443\u043C\u043E\u043B\u0447\u0430\u043D\u0438\u044E","Dictionary Settings":"\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0421\u043B\u043E\u0432\u0430\u0440\u044F",Language:"\u042F\u0437\u044B\u043A","The Language the Plugin will use to search for Definitions and Pronunciations.":"\u042F\u0437\u044B\u043A, \u043A\u043E\u0442\u043E\u0440\u044B\u0439 \u0431\u0443\u0434\u0435\u0442 \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D \u043F\u043B\u0430\u0433\u0438\u043D\u043E\u043C \u043F\u0440\u0438 \u043F\u043E\u0438\u0441\u043A\u0435 \u043E\u043F\u0440\u0435\u0434\u0435\u043B\u0435\u043D\u0438\u0439 \u0438 \u043F\u0440\u043E\u0438\u0437\u043D\u043E\u0448\u0435\u043D\u0438\u044F.","Synonym Suggestions":"\u041F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u0435 \u0421\u0438\u043D\u043E\u043D\u0438\u043C\u043E\u0432","Show synonyms for highlighted words":"\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u0441\u0438\u043D\u043E\u043D\u0438\u043C\u044B \u0434\u043B\u044F \u0432\u044B\u0434\u0435\u043B\u0435\u043D\u043D\u044B\u0445 \u0441\u043B\u043E\u0432","Enabling this will allow the Plugin to analyze full sentences to better suggest synonyms based on the context.":"\u0412\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u0435 \u044D\u0442\u043E\u0439 \u043E\u043F\u0446\u0438\u0438 \u043F\u043E\u0437\u0432\u043E\u043B\u044F\u0435\u0442 \u043F\u043B\u0430\u0433\u0438\u043D\u0443 \u0430\u043D\u0430\u043B\u0438\u0437\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u043F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u044F \u0446\u0435\u043B\u0438\u043A\u043E\u043C \u0434\u043B\u044F \u043F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u044F \u0431\u043E\u043B\u0435\u0435 \u043F\u043E\u0434\u0445\u043E\u0434\u044F\u0449\u0438\u0435 \u0441\u0438\u043D\u043E\u043D\u0438\u043C\u043E\u0432 \u0432 \u0437\u0430\u0432\u0438\u0441\u0438\u043C\u043E\u0441\u0442\u0438 \u043E\u0442 \u043A\u043E\u043D\u0442\u0435\u043A\u0441\u0442\u0430.","Click ":"\u041D\u0430\u0436\u043C\u0438\u0442\u0435 ",here:"\u0441\u044E\u0434\u0430"," for Privacy Concerns.":" \u0434\u043B\u044F \u0438\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u0438 \u043E \u043A\u043E\u043D\u0444\u0438\u0434\u0435\u043D\u0446\u0438\u0430\u043B\u044C\u043D\u043E\u0441\u0442\u0438.","Advanced Synonym Search":"\u041F\u0440\u043E\u0434\u0432\u0438\u043D\u0443\u0442\u044B\u0439 \u041F\u043E\u0438\u0441\u043A \u0421\u0438\u043D\u043E\u043D\u0438\u043C\u043E\u0432","Show Options in Context Menu":"\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u0444\u0443\u043D\u043A\u0446\u0438\u0438 \u0432 \u043A\u043E\u043D\u0442\u0435\u043A\u0441\u0442\u043D\u043E\u043C \u043C\u0435\u043D\u044E","Enable custom Context Menu with options to search for synonyms (only if the auto suggestions are disabled) and to look up a full definition in the Sidebar. Warning: This will override Obsidian's default Context Menu.":"\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0432 \u043A\u043E\u043D\u0442\u0435\u043A\u0441\u0442\u043D\u043E\u0435 \u043C\u0435\u043D\u044E \u0432\u043E\u0437\u043C\u043E\u0436\u043D\u043E\u0441\u0442\u044C \u043F\u043E\u0438\u0441\u043A\u0430 \u0441\u0438\u043D\u043E\u043D\u0438\u043C\u043E\u0432 (\u0442\u043E\u043B\u044C\u043A\u043E \u043F\u0440\u0438 \u0432\u044B\u043A\u043B\u044E\u0447\u0435\u043D\u043D\u043E\u043C \u0430\u0432\u0442\u043E\u0434\u043E\u043F\u043E\u043B\u043D\u0435\u043D\u0438\u0438) \u0438 \u0432\u043E\u0437\u043C\u043E\u0436\u043D\u043E\u0441\u0442\u044C \u043F\u0440\u043E\u0441\u043C\u043E\u0442\u0440\u0430 \u043F\u043E\u043B\u043D\u043E\u0433\u043E \u043E\u043F\u0440\u0435\u0434\u0435\u043B\u0435\u043D\u0438\u044F \u0432 \u0431\u043E\u043A\u043E\u0432\u043E\u043C \u043C\u0435\u043D\u044E.","Click Here":"\u041D\u0430\u0436\u043C\u0438\u0442\u0435 \u0421\u044E\u0434\u0430","Definition Provider":"\u041F\u0440\u043E\u0432\u0430\u0439\u0434\u0435\u0440 \u041E\u043F\u0440\u0435\u0434\u0435\u043B\u0435\u043D\u0438\u0439","The API the Plugin will use to search for Definitions.":"API, \u043A\u043E\u0442\u043E\u0440\u044B\u0439 \u0431\u0443\u0434\u0435\u0442 \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D \u043F\u043B\u0430\u0433\u0438\u043D\u043E\u043C \u0434\u043B\u044F \u043F\u043E\u0438\u0441\u043A\u0430 \u043E\u043F\u0440\u0435\u0434\u0435\u043B\u0435\u043D\u0438\u0439.","Synonym Provider":"\u041F\u0440\u043E\u0432\u0430\u0439\u0434\u0435\u0440 \u0421\u0438\u043D\u043E\u043D\u0438\u043C\u043E\u0432","The API the Plugin will use to search for Synonyms.":"API, \u043A\u043E\u0442\u043E\u0440\u044B\u0439 \u0431\u0443\u0434\u0435\u0442 \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D \u043F\u043B\u0430\u0433\u0438\u043D\u043E\u043C \u0434\u043B\u044F \u043F\u043E\u0438\u0441\u043A\u0430 \u0441\u0438\u043D\u043E\u043D\u0438\u043C\u043E\u0432.","More Information":"\u0414\u043E\u043F\u043E\u043B\u043D\u0438\u0442\u0435\u043B\u044C\u043D\u0430\u044F \u0418\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u044F","View Information about the API's and the Plugin itself.":"\u0418\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u044F \u043E\u0431 \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0435\u043C\u044B\u0445 API \u0438 \u043E \u0441\u0430\u043C\u043E\u043C \u043F\u043B\u0430\u0433\u0438\u043D\u0435.","More Info":"\u0423\u0437\u043D\u0430\u0442\u044C \u041F\u043E\u0434\u0440\u043E\u0431\u043D\u0435\u0435",Donate:"\u041F\u043E\u0436\u0435\u0440\u0442\u0432\u043E\u0432\u0430\u0442\u044C","If you like this Plugin, consider donating to support continued development:":"\u0415\u0441\u043B\u0438 \u0432\u0430\u043C \u043D\u0440\u0430\u0432\u0438\u0442\u0441\u044F \u043F\u043B\u0430\u0433\u0438\u043D, \u0440\u0430\u0441\u0441\u043C\u043E\u0442\u0440\u0438\u0442\u0435 \u0432\u043E\u0437\u043C\u043E\u0436\u043D\u043E\u0441\u0442\u044C \u043F\u043E\u0436\u0435\u0440\u0442\u0432\u043E\u0432\u0430\u043D\u0438\u044F \u0434\u0430\u043B\u044C\u043D\u0435\u0439\u0448\u0443\u044E \u0435\u0433\u043E \u0440\u0430\u0437\u0440\u0430\u0431\u043E\u0442\u043A\u0443:","Local Dictionary Folder":"\u0414\u0438\u0440\u0435\u043A\u0442\u043E\u0440\u0438\u044F \u041B\u043E\u043A\u0430\u043B\u044C\u043D\u043E\u0433\u043E \u0421\u043B\u043E\u0432\u0430\u0440\u044F","Specify a Folder, where all new Notes created by the Dictionary are placed. Please note that this Folder needs to already exist.":"\u0423\u043A\u0430\u0436\u0438\u0442\u0435 \u0434\u0438\u0440\u0435\u043A\u0442\u043E\u0440\u0438\u044E, \u0432 \u043A\u043E\u0442\u043E\u0440\u043E\u0439 \u0431\u0443\u0434\u0443\u0442 \u0441\u043E\u0437\u0434\u0430\u0432\u0430\u0442\u044C\u0441\u044F \u0432\u0441\u0435 \u043D\u043E\u0432\u044B\u0435 \u0437\u0430\u043C\u0435\u0442\u043A\u0438, \u0441\u043E\u0437\u0434\u0430\u043D\u043D\u044B\u0435 \u0421\u043B\u043E\u0432\u0430\u0440\u0451\u043C. \u041F\u043E\u0436\u0430\u043B\u0443\u0439\u0441\u0442\u0430, \u0443\u0447\u0442\u0438\u0442\u0435, \u0447\u0442\u043E \u0434\u0438\u0440\u0435\u043A\u0442\u043E\u0440\u0438\u044F \u0434\u043E\u043B\u0436\u043D\u0430 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u043E\u0432\u0430\u0442\u044C.","Capitalize File Name":"\u041D\u0430\u0447\u0438\u043D\u0430\u0442\u044C \u0421\u043B\u043E\u0432\u0430 \u0432 \u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0438 \u0424\u0430\u0439\u043B\u043E\u0432 \u0441 \u0417\u0430\u0433\u043B\u0430\u0432\u043D\u043E\u0439 \u0411\u0443\u043A\u0432\u044B","If you disable this, the names of newly created files will be all lowercase.":"\u0415\u0441\u043B\u0438 \u0432\u044B \u0432\u044B\u043A\u043B\u044E\u0447\u0438\u0442\u0435 \u044D\u0442\u0443 \u043E\u043F\u0446\u0438\u044E, \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044F \u0432 \u0441\u043E\u0437\u0434\u0430\u043D\u043D\u044B\u0445 \u0444\u0430\u0439\u043B\u0430\u0445 \u0431\u0443\u0434\u0443\u0442 \u043D\u0430\u0447\u0438\u043D\u0430\u0442\u044C\u0441\u044F \u0441 \u043C\u0430\u043B\u0435\u043D\u044C\u043A\u0438\u0445 \u0431\u0443\u043A\u0432.","Filename Prefix and Suffix":"\u041F\u0440\u0435\u0444\u0438\u043A\u0441 \u0438 \u0441\u0443\u0444\u0444\u0438\u043A\u0441 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u0439 \u0444\u0430\u0439\u043B\u043E\u0432","Here you can add a Prefix and Suffix for your newly created Files.":"\u0417\u0434\u0435\u0441\u044C \u0432\u044B \u043C\u043E\u0436\u0435\u0442\u0435 \u0437\u0430\u0434\u0430\u0442\u044C \u043F\u0440\u0435\u0444\u0438\u043A\u0441 \u0438 \u0441\u0443\u0444\u0444\u0438\u043A\u0441 \u0434\u043B\u044F \u0441\u043E\u0437\u0434\u0430\u0432\u0430\u0435\u043C\u044B\u0445 \u0444\u0430\u0439\u043B\u043E\u0432. \u041C\u043E\u0436\u043D\u043E \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u044C \u043F\u0435\u0440\u0435\u043C\u0435\u043D\u043D\u0443\u044E {{lang}}.",Prefix:"\u041F\u0440\u0435\u0444\u0438\u043A\u0441",Suffix:"\u0421\u0443\u0444\u0444\u0438\u043A\u0441","Here you can edit the Template for newly created Files.":"\u0417\u0434\u0435\u0441\u044C \u043C\u043E\u0436\u043D\u043E \u0440\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D \u0434\u043B\u044F \u0441\u043E\u0437\u0434\u0430\u0432\u0430\u0435\u043C\u044B\u0445 \u0444\u0430\u0439\u043B\u043E\u0432.","Click for a List of Variables":"\u041D\u0430\u0436\u043C\u0438\u0442\u0435 \u0441\u044E\u0434\u0430, \u0447\u0442\u043E\u0431\u044B \u043F\u0435\u0440\u0435\u0439\u0442\u0438 \u043A \u0441\u043F\u0438\u0441\u043A\u0443 \u043F\u0435\u0440\u0435\u043C\u0435\u043D\u043D\u044B\u0445",Template:"\u0428\u0430\u0431\u043B\u043E\u043D","Local-Dictionary-Builder Settings":"\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0421\u043E\u0437\u0434\u0430\u043D\u0438\u044F \u041B\u043E\u043A\u0430\u043B\u044C\u043D\u043E\u0433\u043E \u0421\u043B\u043E\u0432\u0430\u0440\u044F",Miscellaneous:"\u0420\u0430\u0437\u043D\u043E\u0435","Caching Settings":"\u043D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u041A\u044D\u0448\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u044F","Use Caching":"\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u044C \u041A\u044D\u0448\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u0435","Enable or disable caching. Caching provides a semi-offline experience by saving every result for later use.":"\u0412\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u0438\u043B\u0438 \u0432\u044B\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u043A\u044D\u0448\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u0435. \u041A\u044D\u0448\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u0435 \u043E\u0431\u0435\u0441\u043F\u0435\u0447\u0438\u0432\u0430\u0435\u0442 \u043F\u043E\u043B\u0443\u0430\u0432\u0442\u043E\u043D\u043E\u043C\u043D\u0443\u044E \u0440\u0430\u0431\u043E\u0442\u0443, \u0433\u0434\u0435 \u043A\u0430\u0436\u0434\u044B\u0439 \u0440\u0435\u0437\u0443\u043B\u044C\u0442\u0430\u0442 \u0441\u043E\u0445\u0440\u0430\u043D\u044F\u0435\u0442\u0441\u044F \u0434\u043B\u044F \u0434\u0430\u043B\u044C\u043D\u0435\u0439\u0448\u0435\u0433\u043E \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u044F.","Here you can delete all cached Data.":"\u0417\u0434\u0435\u0441\u044C \u043C\u043E\u0436\u043D\u043E \u0443\u0434\u0430\u043B\u0438\u0442\u044C \u0432\u0441\u0435 \u043A\u044D\u0448\u0438\u0440\u043E\u0432\u0430\u043D\u043D\u044B\u0435 \u0434\u0430\u043D\u043D\u044B\u0435.","You currently have ":"\u0423 \u0432\u0430\u0441 \u0441\u0435\u0439\u0447\u0430\u0441 "," cached Definitions and ":" \u043A\u044D\u0448\u0438\u0440\u043E\u0432\u0430\u043D\u043D\u044B\u0445 \u043E\u043F\u0440\u0435\u0434\u0435\u043B\u0435\u043D\u0438\u0439 \u0438 "," cached Synonyms.":" \u043A\u044D\u0448\u0438\u0440\u043E\u0432\u0430\u043D\u043D\u044B\u0445 \u0441\u0438\u043D\u043E\u043D\u0438\u043C\u043E\u0432.","Delete Cache":"\u0423\u0434\u0430\u043B\u0438\u0442\u044C \u043A\u044D\u0448",Delete:"\u0423\u0434\u0430\u043B\u0438\u0442\u044C",Success:"\u0423\u0441\u043F\u0435\u0445","Use Language specific Subfolders":"\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u044C \u043F\u043E\u0434\u0434\u0438\u0440\u0435\u043A\u0442\u043E\u0440\u0438\u0438 \u0434\u043B\u044F \u044F\u0437\u044B\u043A\u043E\u0432",'Create Subfolders for every language, e.g. "Dictionary/en-US/Cake"':'\u0421\u043E\u0437\u0434\u0430\u0432\u0430\u0442\u044C \u043F\u043E\u0434\u0434\u0438\u0440\u0435\u043A\u0442\u043E\u0440\u0438\u0438 \u0434\u043B\u044F \u043A\u0430\u0436\u0434\u043E\u0433\u043E \u044F\u0437\u044B\u043A\u0430, \u0442.\u0435. "Dictionary/en-US/Cake"',"Autogenerated by Obsidian Dictionary Plugin":"\u0421\u0433\u0435\u043D\u0435\u0440\u0438\u0440\u043E\u0432\u0430\u043D\u043E \u043F\u043B\u0430\u0433\u0438\u043D\u043E\u043C Obsidian Dictionary","Yes, overwrite the old File.":"\u0414\u0430, \u043F\u0435\u0440\u0435\u0437\u0430\u043F\u0438\u0441\u0430\u0442\u044C \u0441\u0442\u0430\u0440\u044B\u0439 \u0444\u0430\u0439\u043B.","A existing File with the same Name was found, do you want to overwrite it?":"\u0424\u0430\u0439\u043B \u0441 \u0442\u0430\u043A\u0438\u043C \u0438\u043C\u0435\u043D\u0435\u043C \u0443\u0436\u0435 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u0435\u0442, \u0445\u043E\u0442\u0438\u0442\u0435 \u043B\u0438 \u0432\u044B \u0435\u0433\u043E \u043F\u0435\u0440\u0435\u0437\u0430\u043F\u0438\u0441\u0430\u0442\u044C?","No, keep the old File.":"\u041D\u0435\u0442, \u043E\u0441\u0442\u0430\u0432\u0438\u0442\u044C \u0441\u0442\u0430\u0440\u044B\u0439 \u0444\u0430\u0439\u043B.","Meaning {{i}}":"\u0417\u043D\u0430\u0447\u0435\u043D\u0438\u0435 {{i}}","API Information":"\u0418\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u044F \u043E\u0431 API","Definition API's":"API \u041E\u043F\u0440\u0435\u0434\u0435\u043B\u0435\u043D\u0438\u0439",Website:"\u0421\u0430\u0439\u0442","Synonym API's":"API \u0421\u0438\u043D\u043E\u043D\u0438\u043C\u043E\u0432","Part of Speech API's":"\u0427\u0430\u0441\u0442\u044C Speech API",'This Plugin is using <a href="https://feathericons.com/">Feather Icons</a>':'\u042D\u0442\u043E\u0442 \u043F\u043B\u0430\u0433\u0438\u043D \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0435\u0442 <a href="https://feathericons.com/">Feather Icons</a>',"Enter a word":"\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0441\u043B\u043E\u0432\u043E",Clear:"\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C","Change Language":"\u0418\u0437\u043C\u0435\u043D\u0438\u0442\u044C \u042F\u0437\u044B\u043A","Change Provider":"\u0421\u043C\u0435\u043D\u0438\u0442\u044C \u041F\u0440\u043E\u0432\u0430\u0439\u0434\u0435\u0440\u0430","Collapse Results":"\u0421\u0432\u0435\u0440\u043D\u0443\u0442\u044C \u0420\u0435\u0437\u0443\u043B\u044C\u0442\u0430\u0442\u044B",Pronunciation:"\u041F\u0440\u043E\u0438\u0437\u043D\u043E\u0448\u0435\u043D\u0438\u0435",Meanings:"\u0417\u043D\u0430\u0447\u0435\u043D\u0438\u044F",Origin:"\u041F\u0440\u043E\u0438\u0441\u0445\u043E\u0436\u0434\u0435\u043D\u0438\u0435","New Note":"\u041D\u043E\u0432\u0430\u044F \u0417\u0430\u043C\u0435\u0442\u043A\u0430","View Error":"\u0423\u0432\u0438\u0434\u0435\u0442\u044C \u043E\u0448\u0438\u0431\u043A\u0443","Match Case":"\u0423\u0447\u0438\u0442\u044B\u0432\u0430\u0442\u044C \u0420\u0435\u0433\u0438\u0441\u0442\u0440",'Copied "{{word}}" to clipboard':'"{{word}}" \u0441\u043A\u043E\u043F\u0438\u0440\u043E\u0432\u0430\u043D\u043E \u0432 \u0431\u0443\u0444\u0435\u0440 \u043E\u0431\u043C\u0435\u043D\u0430',"I can't find the word you are looking for or the server can't be reached. You can try again in a few minutes.":"\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u043D\u0430\u0439\u0442\u0438 \u0441\u043B\u043E\u0432\u043E \u0438\u043B\u0438 \u0441\u0435\u0440\u0432\u0435\u0440 \u043D\u0435 \u0434\u043E\u0441\u0442\u0443\u043F\u0435\u043D. \u0412\u044B \u043C\u043E\u0436\u0435\u0442\u0435 \u043F\u043E\u043F\u0440\u043E\u0431\u043E\u0432\u0430\u0442\u044C \u0435\u0449\u0451 \u0440\u0430\u0437 \u0447\u0435\u0440\u0435\u0437 \u043D\u0435\u0441\u043A\u043E\u043B\u044C\u043A\u043E \u043C\u0438\u043D\u0443\u0442.","Definition:":"\u041E\u043F\u0440\u0435\u0434\u0435\u043B\u0435\u043D\u0438\u0435:","Synonyms:":"\u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B:","Antonyms:":"\u0410\u043D\u0442\u043E\u043D\u0438\u043C\u044B:","Choose a Definition Provider Service":"\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u043F\u0440\u043E\u0432\u0430\u0439\u0434\u0435\u0440\u0430 \u043E\u043F\u0440\u0435\u0434\u0435\u043B\u0435\u043D\u0438\u0439","Choose a Language":"\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u044F\u0437\u044B\u043A","Choose a Synonym Provider Service":"\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u043F\u0440\u043E\u0432\u0430\u0439\u0434\u0435\u0440\u0430 \u0441\u0438\u043D\u043E\u043D\u0438\u043C\u043E\u0432"};var pn={};var hn={"Open Dictionary View":"\u6253\u5F00\u67E5\u8BCD\u9762\u677F","Open Language Switcher":"\u5207\u6362\u8BED\u8A00",Dictionary:"\u8BCD\u5178",Cut:"\u526A\u5207",Copy:"\u590D\u5236",Paste:"\u7C98\u8D34","Show Synonyms":"\u663E\u793A\u540C\u4E49\u8BCD","Look up":"\u67E5\u8BE2","Reset to default":"\u91CD\u7F6E\u4E3A\u9ED8\u8BA4\u8BBE\u7F6E","Dictionary Settings":"\u8BCD\u5178\u8BBE\u7F6E",Language:"\u8BED\u8A00","The Language the Plugin will use to search for Definitions and Pronunciations.":"\u63D2\u4EF6\u4F1A\u6839\u636E\u8BE5\u8BED\u8A00\u6765\u641C\u7D22\u8BED\u4E49\u4EE5\u53CA\u53D1\u97F3","Synonym Suggestions":"\u540C\u4E49\u8BCD\u5EFA\u8BAE","Show synonyms for highlighted words":"\u5C55\u793A\u88AB\u9AD8\u4EAE\u7684\u8BCD\u7684\u540C\u4E49\u8BCD","Enabling this will allow the Plugin to analyze full sentences to better suggest synonyms based on the context.":"\u5141\u8BB8\u8BE5\u9009\u9879\u5C06\u4F1A\u5BF9\u6574\u6BB5\u6587\u672C\u8FDB\u884C\u5206\u6790\u6765\u5EFA\u8BAE\u66F4\u597D\u7684\u540C\u4E49\u8BCD","Click ":"\u70B9\u51FB ",here:"\u8FD9\u91CC"," for Privacy Concerns.":" \u6765\u83B7\u53D6\u76F8\u5173\u9690\u79C1\u89E3\u91CA","Advanced Synonym Search":"\u540C\u4E49\u8BCD\u641C\u7D22\u8FDB\u9636","Show Options in Context Menu":"\u5728\u53F3\u952E\u83DC\u5355\u4E2D\u663E\u793A\u9009\u9879","Enable custom Context Menu with options to search for synonyms (only if the auto suggestions are disabled) and to look up a full definition in the Sidebar. Warning: This will override Obsidian's default Context Menu.":"\u5141\u8BB8\u81EA\u5B9A\u4E49\u53F3\u952E\u83DC\u5355\u6765\u66F4\u65B9\u4FBF\u641C\u7D22\u540C\u4E49\u8BCD\uFF08\u9700\u8981\u5173\u95ED\u81EA\u52A8\u5EFA\u8BAE\uFF09\u4E14\u5728\u4FA7\u680F\u7684\u8BCD\u5178\u9762\u677F\u5C55\u793A\u76F8\u5173\u91CA\u4E49\u3002\u6CE8\u610F\uFF0C\u8FD9\u4F1A\u8986\u76D6\u6389 Obsidian \u7684\u9ED8\u8BA4\u53F3\u952E\u83DC\u5355\u3002","Click Here":"\u70B9\u51FB\u8FD9\u91CC","Definition Provider":"\u91CA\u4E49\u6765\u6E90","The API the Plugin will use to search for Definitions.":"\u5C06\u57FA\u4E8E\u8BE5 API \u53BB\u641C\u7D22\u91CA\u4E49","Synonym Provider":"\u540C\u4E49\u8BCD\u6765\u6E90","The API the Plugin will use to search for Synonyms.":"\u5C06\u57FA\u4E8E\u8BE5 API \u53BB\u641C\u7D22\u540C\u4E49\u8BCD","More Information":"\u66F4\u591A\u4FE1\u606F","View Information about the API's and the Plugin itself.":"\u4E86\u89E3\u5173\u4E8E\u63D2\u4EF6\u4EE5\u53CA API \u7684\u4FE1\u606F","More Info":"\u66F4\u591A\u4FE1\u606F",Donate:"\u6350\u8D60","If you like this Plugin, consider donating to support continued development:":"\u5982\u679C\u4F60\u559C\u6B22\u8BE5\u63D2\u4EF6\uFF0C\u53EF\u4EE5\u8003\u8651\u6253\u8D4F\u652F\u6301\uFF1A","Local Dictionary Folder":"\u672C\u5730\u8BCD\u5178\u6587\u4EF6\u5939","Specify a Folder, where all new Notes created by the Dictionary are placed. Please note that this Folder needs to already exist.":"\u6307\u5B9A\u8BCD\u5178\u521B\u5EFA\u7684\u65B0\u7B14\u8BB0\u6240\u5B58\u653E\u7684\u4F4D\u7F6E\uFF0C\u6CE8\u610F\u6587\u4EF6\u5939\u9700\u8981\u5B58\u5728","Capitalize File Name":"\u5C06\u6587\u4EF6\u540D\u9996\u5B57\u6BCD\u5927\u5199","If you disable this, the names of newly created files will be all lowercase.":"\u5982\u679C\u4F60\u5173\u95ED\u8FD9\u4E2A\u9009\u9879\uFF0C\u65B0\u5EFA\u6587\u4EF6\u9996\u5B57\u6BCD\u5C06\u662F\u5C0F\u5199\u3002","Filename Prefix and Suffix":"\u6587\u4EF6\u540D\u524D\u7F00\u548C\u540E\u7F00","Here you can add a Prefix and Suffix for your newly created Files.":"\u5728\u8FD9\u91CC\u4F60\u53EF\u4EE5\u4E3A\u65B0\u5EFA\u6587\u4EF6\u6DFB\u52A0\u524D\u7F00\u548C\u540E\u7F00\u3002\u4F60\u53EF\u4EE5\u4F7F\u7528 {{lang}} \u53D8\u91CF\u3002Here you can add a Prefix and Suffix for your newly created Files. You can use the {{lang}} variable here.",Prefix:"\u524D\u7F00",Suffix:"\u540E\u7F00","Here you can edit the Template for newly created Files.":"\u5728\u8FD9\u91CC\u4F60\u53EF\u4EE5\u7F16\u8F91\u65B0\u5EFA\u6587\u6863\u7684\u6A21\u677F\u3002","Click for a List of Variables":"\u70B9\u51FB\u67E5\u770B\u53D8\u91CF\u5217\u8868",Template:"\u6A21\u677F","Local-Dictionary-Builder Settings":"\u672C\u5730\u81EA\u5EFA\u8BCD\u5178\u8BBE\u7F6E",Miscellaneous:"\u5176\u4ED6","Caching Settings":"\u7F13\u5B58\u8BBE\u7F6E","Use Caching":"\u4F7F\u7528\u7F13\u5B58","Enable or disable caching. Caching provides a semi-offline experience by saving every result for later use.":"\u542F\u7528\u6216\u7981\u7528\u7F13\u5B58\u3002\u7F13\u5B58\u53EF\u4EE5\u4FDD\u5B58\u7ED3\u679C\u4EE5\u4F9B\u540E\u7EED\u4F7F\u7528\uFF0C\u63D0\u4F9B\u63A5\u8FD1\u672C\u5730\u4FDD\u5B58\u7684\u4F53\u9A8C\u3002","Here you can delete all cached Data.":"\u5728\u8FD9\u91CC\u4F60\u53EF\u4EE5\u5220\u9664\u6240\u6709\u7F13\u5B58\u6570\u636E\u3002","You currently have ":"\u4F60\u76EE\u524D\u6709"," cached Definitions and ":" \u5DF2\u7F13\u5B58\u91CA\u4E49\u4E0E "," cached Synonyms.":" \u5DF2\u7F13\u5B58\u540C\u4E49\u8BCD\u3002","Delete Cache":"\u5220\u9664\u7F13\u5B58",Delete:"\u5220\u9664",Success:"\u6210\u529F","Use Language specific Subfolders":"\u4F7F\u7528\u7279\u5B9A\u8BED\u8A00\u7684\u5B50\u6587\u4EF6\u5939",'Create Subfolders for every language, e.g. "Dictionary/en-US/Cake"':'\u4E3A\u6240\u6709\u8BED\u8A00\u521B\u5EFA\u5B50\u6587\u4EF6\u5939\uFF0C\u4F8B\u5982"Dictionary/en-US/Cake"',"Autogenerated by Obsidian Dictionary Plugin":"\u7531 Obsidian Dictionary Plugin \u63D2\u4EF6\u81EA\u52A8\u751F\u6210","Yes, overwrite the old File.":"\u597D\uFF0C\u8986\u76D6\u65E7\u6587\u4EF6\u3002","A existing File with the same Name was found, do you want to overwrite it?":"\u53D1\u73B0\u4E00\u4E2A\u540C\u540D\u65E7\u6587\u4EF6\uFF0C\u662F\u5426\u8986\u76D6\uFF1F","No, keep the old File.":"\u4E0D\uFF0C\u4FDD\u7559\u65E7\u6587\u4EF6\u3002","Meaning {{i}}":"\u8BCD\u4E49 {{i}}","API Information":"API \u4FE1\u606F","Definition API's":"\u91CA\u4E49 API",Website:"\u7F51\u7AD9","Synonym API's":"\u540C\u4E49\u8BCD API","Part of Speech API's":"\u8BED\u97F3 API",'This Plugin is using <a href="https://feathericons.com/">Feather Icons</a>':'\u8FD9\u4E2A\u63D2\u4EF6\u4F7F\u7528 <a href="https://feathericons.com/">Feather \u56FE\u6807</a>',"Enter a word":"\u8F93\u5165\u5355\u8BCD",Clear:"\u6E05\u9664","Change Language":"\u66F4\u6539\u8BED\u8A00","Change Provider":"\u66F4\u6539\u6765\u6E90","Collapse Results":"\u6298\u53E0\u7ED3\u679C",Pronunciation:"\u53D1\u97F3",Meanings:"\u8BCD\u4E49",Origin:"\u8BCD\u6E90","New Note":"\u65B0\u7B14\u8BB0","View Error":"\u68C0\u89C6\u9519\u8BEF","Match Case":"\u533A\u5206\u5927\u5C0F\u5199",'Copied "{{word}}" to clipboard':'\u5DF2\u590D\u5236 "{{word}}" \u5230\u526A\u8D34\u677F',"I can't find the word you are looking for or the server can't be reached. You can try again in a few minutes.":"\u65E0\u6CD5\u627E\u5230\u4F60\u641C\u7D22\u7684\u5355\u8BCD\u6216\u8005\u670D\u52A1\u5668\u5F53\u524D\u4E0D\u53EF\u7528\uFF0C\u4F60\u53EF\u4EE5\u51E0\u5206\u949F\u540E\u518D\u8BD5\u4E00\u4E0B\u3002","Definition:":"\u91CA\u4E49\uFF1A","Synonyms:":"\u540C\u4E49\u8BCD\uFF1A","Antonyms:":"\u53CD\u4E49\u8BCD\uFF1A","Choose a Definition Provider Service":"\u9009\u62E9\u4E00\u4E2A\u91CA\u4E49\u8BCD\u5178\u670D\u52A1","Choose a Language":"\u9009\u62E9\u4E00\u79CD\u8BED\u8A00","Choose a Synonym Provider Service":"\u9009\u62E9\u4E00\u4E2A\u540C\u4E49\u8BCD\u8BCD\u5178\u670D\u52A1"};var yn={"Open Dictionary View":"\u6253\u958B\u5B57\u5178\u6AA2\u8996","Open Language Switcher":"\u5207\u63DB\u8A9E\u8A00",Dictionary:"\u5B57\u5178",Cut:"\u526A\u4E0B",Copy:"\u8907\u88FD",Paste:"\u8CBC\u4E0A","Show Synonyms":"\u986F\u793A\u540C\u7FA9\u8A5E","Look up":"\u67E5\u8A62","Reset to default":"\u6062\u5FA9\u9810\u8A2D\u8A2D\u5B9A","Dictionary Settings":"\u5B57\u5178\u8A2D\u5B9A",Language:"\u8A9E\u8A00","The Language the Plugin will use to search for Definitions and Pronunciations.":"\u6B64\u63D2\u4EF6\u7528\u4F86\u67E5\u5C0B\u5B9A\u7FA9\u8207\u767C\u97F3\u7684\u8A9E\u8A00","Synonym Suggestions":"\u540C\u7FA9\u8A5E\u5EFA\u8B70","Show synonyms for highlighted words":"\u986F\u793A\u88AB\u9AD8\u4EAE\u7684\u5B57\u4E4B\u540C\u7FA9\u8A5E","Enabling this will allow the Plugin to analyze full sentences to better suggest synonyms based on the context.":"\u555F\u7528\u8A72\u9078\u9805\u6703\u8B93\u6B64\u63D2\u4EF6\u80FD\u5920\u5206\u6790\u6574\u6BB5\u6587\u5B57\u4EE5\u57FA\u65BC\u5167\u5BB9\u7D66\u51FA\u66F4\u597D\u7684\u540C\u7FA9\u8A5E\u5EFA\u8B70","Click ":"\u9EDE\u64CA ",here:"\u9019\u88E1"," for Privacy Concerns.":"\u4EE5\u67E5\u770B\u76F8\u95DC\u96B1\u79C1\u8AAA\u660E","Advanced Synonym Search":"\u9032\u968E\u540C\u7FA9\u8A5E\u67E5\u8A62","Show Options in Context Menu":"\u5728\u53F3\u9375\u83DC\u55AE\u986F\u793A\u9078\u9805","Enable custom Context Menu with options to search for synonyms (only if the auto suggestions are disabled) and to look up a full definition in the Sidebar. Warning: This will override Obsidian's default Context Menu.":"\u555F\u7528\u5305\u542B\u9078\u9805\u7684\u81EA\u5B9A\u7FA9\u53F3\u9375\u83DC\u55AE\u4EE5\u641C\u5C0B\u540C\u7FA9\u8A5E\uFF08\u9700\u8981\u95DC\u9589\u81EA\u52D5\u5EFA\u8B70\uFF09\u4E26\u5728\u5074\u6B04\u67E5\u770B\u5B8C\u6574\u5B9A\u7FA9\u3002\u6CE8\u610F\uFF0C\u9019\u6703\u8986\u84CB\u6389 Obsidian \u7684\u9ED8\u8A8D\u53F3\u9375\u83DC\u55AE\u3002","Click Here":"\u9EDE\u64CA\u9019\u88E1","Definition Provider":"\u5B9A\u7FA9\u4F86\u6E90","The API the Plugin will use to search for Definitions.":"\u6B64\u63D2\u4EF6\u67E5\u8A62\u5B9A\u7FA9\u6240\u7528\u7684 API","Synonym Provider":"\u540C\u7FA9\u8A5E\u4F86\u6E90","The API the Plugin will use to search for Synonyms.":"\u6B64\u63D2\u4EF6\u67E5\u8A62\u540C\u7FA9\u8A5E\u6240\u7528\u7684 API","More Information":"\u66F4\u591A\u8CC7\u8A0A","View Information about the API's and the Plugin itself.":"\u4E86\u89E3\u66F4\u591A\u95DC\u65BC API \u4EE5\u53CA\u6B64\u63D2\u4EF6\u7684\u8CC7\u8A0A","More Info":"\u66F4\u591A\u8CC7\u8A0A",Donate:"\u6350\u6B3E","If you like this Plugin, consider donating to support continued development:":"\u5982\u679C\u4F60\u559C\u6B61\u6B64\u63D2\u4EF6\uFF0C\u53EF\u4EE5\u8003\u616E\u6597\u5167\u652F\u6301\u958B\u767C","Local Dictionary Folder":"\u672C\u5730\u5B57\u5178\u8CC7\u6599\u593E","Specify a Folder, where all new Notes created by the Dictionary are placed. Please note that this Folder needs to already exist.":"\u6307\u5B9A\u6240\u6709\u5B57\u5178\u65B0\u5275\u7684\u7B46\u8A18\u5B58\u653E\u7684\u8CC7\u6599\u593E\uFF0C\u6CE8\u610F\u8A72\u8CC7\u6599\u593E\u5FC5\u9808\u5DF2\u7D93\u5B58\u5728","Capitalize File Name":"\u5927\u5BEB\u6A94\u540D\u9996\u5B57\u6BCD","If you disable this, the names of newly created files will be all lowercase.":"\u5982\u679C\u95DC\u6389\u9019\u500B\u9078\u9805\uFF0C\u65B0\u5EFA\u6A94\u540D\u6703\u662F\u5C0F\u5BEB","Filename Prefix and Suffix":"\u6A94\u540D\u5B57\u9996\u5B57\u5C3E","Here you can add a Prefix and Suffix for your newly created Files.":"\u4F60\u53EF\u4EE5\u5728\u9019\u88E1\u70BA\u65B0\u5EFA\u6A94\u6848\u52A0\u4E0A\u5B57\u9996\u5B57\u5C3E\u3002\u4F60\u53EF\u4EE5\u4F7F\u7528 {{lang}} \u8B8A\u6578\u3002",Prefix:"\u5B57\u9996",Suffix:"\u5B57\u5C3E","Here you can edit the Template for newly created Files.":"\u4F60\u53EF\u4EE5\u5728\u9019\u88E1\u7DE8\u8F2F\u65B0\u5EFA\u6A94\u6848\u7684\u6A21\u677F","Click for a List of Variables":"\u9EDE\u64CA\u4EE5\u67E5\u770B\u8B8A\u6578\u5217\u8868",Template:"\u6A21\u677F","Local-Dictionary-Builder Settings":"\u672C\u5730\u81EA\u5EFA\u5B57\u5178\u8A2D\u5B9A",Miscellaneous:"\u5176\u4ED6","Caching Settings":"\u5FEB\u53D6\u8A2D\u5B9A","Use Caching":"\u4F7F\u7528\u5FEB\u53D6","Enable or disable caching. Caching provides a semi-offline experience by saving every result for later use.":"\u555F\u7528\u6216\u4E0D\u555F\u7528\u5FEB\u53D6\u3002\u555F\u7528\u5FEB\u53D6\u6703\u5132\u5B58\u6240\u6709\u7D50\u679C\u4EE5\u4F9B\u5F8C\u7E8C\u4F7F\u7528\uFF0C\u9054\u6210\u63A5\u8FD1\u96E2\u7DDA\u7684\u9AD4\u9A57","Here you can delete all cached Data.":"\u4F60\u53EF\u4EE5\u5728\u9019\u88E1\u522A\u9664\u6240\u6709\u5FEB\u53D6\u8CC7\u6599","You currently have ":"\u4F60\u76EE\u524D\u6709 "," cached Definitions and ":" \u5FEB\u53D6\u5B9A\u7FA9\u8207 "," cached Synonyms.":" \u5FEB\u53D6\u540C\u7FA9\u8A5E","Delete Cache":"\u522A\u9664\u5FEB\u53D6",Delete:"\u522A\u9664",Success:"\u6210\u529F","Use Language specific Subfolders":"\u4F7F\u7528\u7279\u5B9A\u8A9E\u8A00\u5B50\u8CC7\u6599\u593E",'Create Subfolders for every language, e.g. "Dictionary/en-US/Cake"':'\u70BA\u6240\u6709\u8A9E\u8A00\u5275\u5EFA\u5B50\u8CC7\u6599\u593E\uFF0C\u4F8B\u5982 "Dictionary/en-US/Cake"',"Autogenerated by Obsidian Dictionary Plugin":"\u7531 Obsidian Dictionary \u63D2\u4EF6\u81EA\u52D5\u751F\u6210","Yes, overwrite the old File.":"\u597D\uFF0C\u8986\u5BEB\u820A\u6A94\u6848","A existing File with the same Name was found, do you want to overwrite it?":"\u767C\u73FE\u4E00\u500B\u6A94\u540D\u4E00\u6A23\u7684\u820A\u6A94\u6848\uFF0C\u662F\u5426\u8907\u5BEB\u8A72\u6A94\u6848\uFF1F","No, keep the old File.":"\u4E0D\uFF0C\u4FDD\u7559\u820A\u6A94\u6848","Meaning {{i}}":"\u5B57\u7FA9 {{i}}","API Information":"API \u8CC7\u8A0A","Definition API's":"\u5B9A\u7FA9 API",Website:"\u7DB2\u7AD9","Synonym API's":"\u540C\u7FA9\u8A5E API","Part of Speech API's":"\u8A9E\u97F3 API",'This Plugin is using <a href="https://feathericons.com/">Feather Icons</a>':'\u9019\u500B\u63D2\u4EF6\u4F7F\u7528 <a href="https://feathericons.com/">Feather \u5716\u793A</a>',"Enter a word":"\u8F38\u5165\u55AE\u5B57","Change Language":"\u66F4\u6539\u8A9E\u8A00","Change Provider":"\u66F4\u6539\u4F86\u6E90","Collapse Results":"\u6536\u5408\u7D50\u679C",Pronunciation:"\u767C\u97F3",Meanings:"\u5B57\u7FA9",Origin:"\u5B57\u6E90","New Note":"\u65B0\u7B46\u8A18","View Error":"\u6AA2\u8996\u932F\u8AA4","Match Case":"\u7B26\u5408\u5927\u5C0F\u5BEB",'Copied Synonym "{{word}}" to clipboard':'\u5DF2\u8907\u88FD\u540C\u7FA9\u8A5E "{{word}}" \u5230\u526A\u8CBC\u7C3F',"I can't find the word you are looking for or the server can't be reached. You can try again in a few minutes.":"\u7121\u6CD5\u627E\u5230\u4F60\u8F38\u5165\u7684\u5B57\uFF0C\u6216\u662F\u7121\u6CD5\u9023\u4E0A\u4F3A\u670D\u5668\u3002\u53EF\u4EE5\u904E\u5E7E\u5206\u9418\u518D\u8A66\u3002","Definition:":"\u5B9A\u7FA9\uFF1A","Synonyms:":"\u540C\u7FA9\u8A5E\uFF1A","Choose a Definition Provider Service":"\u9078\u64C7\u4E00\u500B\u5B9A\u7FA9\u4F9B\u61C9\u670D\u52D9","Choose a Language":"\u9078\u64C7\u4E00\u500B\u8A9E\u8A00","Choose a Synonym Provider Service":"\u9078\u64C7\u4E00\u500B\u540C\u7FA9\u8A5E\u4F9B\u61C9\u670D\u52D9"};var Ar={ar:qt,cs:Yt,da:Jt,de:Kt,en:ft,"en-gb":Qt,es:Xt,fr:Zt,hi:en,id:tn,it:nn,ja:rn,ko:on,nl:an,nn:sn,pl:ln,pt:cn,"pt-br":un,ro:dn,ru:fn,tr:pn,"zh-cn":hn,"zh-tw":yn},ht=Ar[pt.moment.locale()];function w(n){return ht||console.error("Error: dictionary locale not found",pt.moment.locale()),ht&&ht[n]||ft[n]}var ne="dictionary-view",mn=w("Dictionary"),gn="quote-glyph",Se={en_US:"English (US)",hi:"\u0939\u093F\u0928\u094D\u0926\u0940 (Hindi)",es:"Espa\xF1ol (Spanish)",fr:"Fran\xE7ais (French)",ja:"\u65E5\u672C\u8A9E (Japanese)",ru:"\u0420\u0443\u0441\u0441\u043A\u0438\u0439 (Russian)",en_GB:"English (UK)",de:"Deutsch (German)",it:"Italiano (Italian)",ko:"\uD55C\uAD6D\uC5B4 (Korean)",pt_BR:"Portugu\xEAs do Brasil (Brazilian Portuguese)",ar:"\u0627\u064E\u0644\u0652\u0639\u064E\u0631\u064E\u0628\u0650\u064A\u064E\u0651\u0629\u064F\u200E (Arabic)",tr:"T\xFCrk\xE7e (Turkish)",cn:"\u4E2D\u6587 (Chinese)"},Ie={en_US:"en-US",hi:"hi",es:"es",fr:"fr",ja:"ja",ru:"ru",en_GB:"en-GB",de:"de",it:"it",ko:"ko",pt_BR:"pt-BR",ar:"ar",tr:"tr",cn:"zh"},vn={cachedDefinitions:[],cachedSynonyms:[]},Me={getLangFromFile:!0,defaultLanguage:"en_US",normalLang:"en_US",shouldShowSynonymPopover:!0,shouldShowCustomContextMenu:!1,apiSettings:{en_US:{definitionApiName:"Free Dictionary API",synonymApiName:"Free Dictionary API"},hi:{definitionApiName:"Free Dictionary API",synonymApiName:null},es:{definitionApiName:"Free Dictionary API",synonymApiName:"Altervista"},fr:{definitionApiName:"Free Dictionary API",synonymApiName:"Altervista"},ja:{definitionApiName:"Free Dictionary API",synonymApiName:null},ru:{definitionApiName:"Free Dictionary API",synonymApiName:null},en_GB:{definitionApiName:"Free Dictionary API",synonymApiName:null},de:{definitionApiName:"Free Dictionary API",synonymApiName:"Open Thesaurus"},it:{definitionApiName:"Free Dictionary API",synonymApiName:"Altervista"},ko:{definitionApiName:"Free Dictionary API",synonymApiName:null},pt_BR:{definitionApiName:"Free Dictionary API",synonymApiName:null},ar:{definitionApiName:"Free Dictionary API",synonymApiName:null},tr:{definitionApiName:"Free Dictionary API",synonymApiName:null},cn:{definitionApiName:"Offline Dictionary",synonymApiName:null}},partOfSpeechApiName:"Systran API",advancedSynonymAnalysis:!1,useCaching:!1,folder:"",capitalizedFileName:!0,prefix:"",suffix:" ({{lang}})",template:`---
# {{notice}}
aliases: ["{{word}}"]
---

# {{word}}

## {{pronunciationHeader}}

{{phoneticList}}

## {{meaningHeader}}

{{meanings}}

## {{originHeader}}

{{origin}}
`,languageSpecificSubFolders:!1};function T(){}var _e=n=>n;function Dr(n){return n&&typeof n=="object"&&typeof n.then=="function"}function yt(n){return n()}function xn(){return Object.create(null)}function de(n){n.forEach(yt)}function ge(n){return typeof n=="function"}function Q(n,e){return n!=n?e==e:n!==e||n&&typeof n=="object"||typeof n=="function"}var Je;function mt(n,e){return Je||(Je=document.createElement("a")),Je.href=e,n===Je.href}function wn(n){return Object.keys(n).length===0}function bn(n){return n&&ge(n.destroy)?n.destroy:T}var Sn=typeof window!="undefined",Cn=Sn?()=>window.performance.now():()=>Date.now(),gt=Sn?n=>requestAnimationFrame(n):T;var Oe=new Set;function jn(n){Oe.forEach(e=>{e.c(n)||(Oe.delete(e),e.f())}),Oe.size!==0&&gt(jn)}function An(n){let e;return Oe.size===0&&gt(jn),{promise:new Promise(t=>{Oe.add(e={c:n,f:t})}),abort(){Oe.delete(e)}}}var Dn=!1;function Ir(){Dn=!0}function Mr(){Dn=!1}function S(n,e){n.appendChild(e)}function X(n,e,t){let i=vt(n);if(!i.getElementById(e)){let r=A("style");r.id=e,r.textContent=t,In(i,r)}}function vt(n){if(!n)return document;let e=n.getRootNode?n.getRootNode():n.ownerDocument;return e&&e.host?e:n.ownerDocument}function Or(n){let e=A("style");return In(vt(n),e),e.sheet}function In(n,e){return S(n.head||n,e),e.sheet}function N(n,e,t){n.insertBefore(e,t||null)}function P(n){n.parentNode&&n.parentNode.removeChild(n)}function ee(n,e){for(let t=0;t<n.length;t+=1)n[t]&&n[t].d(e)}function A(n){return document.createElement(n)}function R(n){return document.createTextNode(n)}function F(){return R(" ")}function ce(){return R("")}function Y(n,e,t,i){return n.addEventListener(e,t,i),()=>n.removeEventListener(e,t,i)}function b(n,e,t){t==null?n.removeAttribute(e):n.getAttribute(e)!==t&&n.setAttribute(e,t)}function Pr(n){return Array.from(n.childNodes)}function W(n,e){e=""+e,n.wholeText!==e&&(n.data=e)}function xt(n,e){n.value=e==null?"":e}function ze(n,e,t,i){t===null?n.style.removeProperty(e):n.style.setProperty(e,t,i?"important":"")}function re(n,e,t){n.classList[t?"add":"remove"](e)}function Nr(n,e,{bubbles:t=!1,cancelable:i=!1}={}){let r=document.createEvent("CustomEvent");return r.initCustomEvent(n,t,i,e),r}var Ke=new Map,Qe=0;function Er(n){let e=5381,t=n.length;for(;t--;)e=(e<<5)-e^n.charCodeAt(t);return e>>>0}function Fr(n,e){let t={stylesheet:Or(e),rules:{}};return Ke.set(n,t),t}function wt(n,e,t,i,r,o,a,s=0){let l=16.666/i,c=`{
`;for(let m=0;m<=1;m+=l){let x=e+(t-e)*o(m);c+=m*100+`%{${a(x,1-x)}}
`}let u=c+`100% {${a(t,1-t)}}
}`,f=`__svelte_${Er(u)}_${s}`,p=vt(n),{stylesheet:y,rules:g}=Ke.get(p)||Fr(p,n);g[f]||(g[f]=!0,y.insertRule(`@keyframes ${f} ${u}`,y.cssRules.length));let v=n.style.animation||"";return n.style.animation=`${v?`${v}, `:""}${f} ${i}ms linear ${r}ms 1 both`,Qe+=1,f}function bt(n,e){let t=(n.style.animation||"").split(", "),i=t.filter(e?o=>o.indexOf(e)<0:o=>o.indexOf("__svelte")===-1),r=t.length-i.length;r&&(n.style.animation=i.join(", "),Qe-=r,Qe||kr())}function kr(){gt(()=>{Qe||(Ke.forEach(n=>{let{ownerNode:e}=n.stylesheet;e&&P(e)}),Ke.clear())})}var Ve;function fe(n){Ve=n}function Mn(){if(!Ve)throw new Error("Function called outside component initialization");return Ve}function Re(n){Mn().$$.on_mount.push(n)}var Ue=[];var ve=[],Xe=[],On=[],Lr=Promise.resolve(),St=!1;function Tr(){St||(St=!0,Lr.then(jt))}function oe(n){Xe.push(n)}var Ct=new Set,Ze=0;function jt(){let n=Ve;do{for(;Ze<Ue.length;){let e=Ue[Ze];Ze++,fe(e),_r(e.$$)}for(fe(null),Ue.length=0,Ze=0;ve.length;)ve.pop()();for(let e=0;e<Xe.length;e+=1){let t=Xe[e];Ct.has(t)||(Ct.add(t),t())}Xe.length=0}while(Ue.length);for(;On.length;)On.pop()();St=!1,Ct.clear(),fe(n)}function _r(n){if(n.fragment!==null){n.update(),de(n.before_update);let e=n.dirty;n.dirty=[-1],n.fragment&&n.fragment.p(n.ctx,e),n.after_update.forEach(oe)}}var He;function Pn(){return He||(He=Promise.resolve(),He.then(()=>{He=null})),He}function Be(n,e,t){n.dispatchEvent(Nr(`${e?"intro":"outro"}${t}`))}var et=new Set,pe;function ae(){pe={r:0,c:[],p:pe}}function se(){pe.r||de(pe.c),pe=pe.p}function _(n,e){n&&n.i&&(et.delete(n),n.i(e))}function H(n,e,t,i){if(n&&n.o){if(et.has(n))return;et.add(n),pe.c.push(()=>{et.delete(n),i&&(t&&n.d(1),i())}),n.o(e)}else i&&i()}var Nn={duration:0};function En(n,e,t){let i=e(n,t),r=!1,o,a,s=0;function l(){o&&bt(n,o)}function c(){let{delay:f=0,duration:p=300,easing:y=_e,tick:g=T,css:v}=i||Nn;v&&(o=wt(n,0,1,p,f,y,v,s++)),g(0,1);let m=Cn()+f,x=m+p;a&&a.abort(),r=!0,oe(()=>Be(n,!0,"start")),a=An(C=>{if(r){if(C>=x)return g(1,0),Be(n,!0,"end"),l(),r=!1;if(C>=m){let E=y((C-m)/p);g(E,1-E)}}return r})}let u=!1;return{start(){u||(u=!0,bt(n),ge(i)?(i=i(),Pn().then(c)):c())},invalidate(){u=!1},end(){r&&(l(),r=!1)}}}function Pe(n,e,t,i){let r=e(n,t),o=i?0:1,a=null,s=null,l=null;function c(){l&&bt(n,l)}function u(p,y){let g=p.b-o;return y*=Math.abs(g),{a:o,b:p.b,d:g,duration:y,start:p.start,end:p.start+y,group:p.group}}function f(p){let{delay:y=0,duration:g=300,easing:v=_e,tick:m=T,css:x}=r||Nn,C={start:Cn()+y,b:p};p||(C.group=pe,pe.r+=1),a||s?s=C:(x&&(c(),l=wt(n,o,p,g,y,v,x)),p&&m(0,1),a=u(C,g),oe(()=>Be(n,p,"start")),An(E=>{if(s&&E>s.start&&(a=u(s,g),s=null,Be(n,a.b,"start"),x&&(c(),l=wt(n,o,a.b,a.duration,0,v,r.css))),a){if(E>=a.end)m(o=a.b,1-o),Be(n,a.b,"end"),s||(a.b?c():--a.group.r||de(a.group.c)),a=null;else if(E>=a.start){let D=E-a.start;o=a.a+a.d*v(D/a.duration),m(o,1-o)}}return!!(a||s)}))}return{run(p){ge(r)?Pn().then(()=>{r=r(),f(p)}):f(p)},end(){c(),a=s=null}}}function At(n,e){let t=e.token={};function i(r,o,a,s){if(e.token!==t)return;e.resolved=s;let l=e.ctx;a!==void 0&&(l=l.slice(),l[a]=s);let c=r&&(e.current=r)(l),u=!1;e.block&&(e.blocks?e.blocks.forEach((f,p)=>{p!==o&&f&&(ae(),H(f,1,1,()=>{e.blocks[p]===f&&(e.blocks[p]=null)}),se())}):e.block.d(1),c.c(),_(c,1),c.m(e.mount(),e.anchor),u=!0),e.block=c,e.blocks&&(e.blocks[o]=c),u&&jt()}if(Dr(n)){let r=Mn();if(n.then(o=>{fe(r),i(e.then,1,e.value,o),fe(null)},o=>{if(fe(r),i(e.catch,2,e.error,o),fe(null),!e.hasCatch)throw o}),e.current!==e.pending)return i(e.pending,0),!0}else{if(e.current!==e.then)return i(e.then,1,e.value,n),!0;e.resolved=n}}function Fn(n,e,t){let i=e.slice(),{resolved:r}=n;n.current===n.then&&(i[n.value]=r),n.current===n.catch&&(i[n.error]=r),n.block.p(i,t)}var Da=typeof window!="undefined"?window:typeof globalThis!="undefined"?globalThis:global;var Ia=new Set(["allowfullscreen","allowpaymentrequest","async","autofocus","autoplay","checked","controls","default","defer","disabled","formnovalidate","hidden","inert","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","selected"]);function We(n){n&&n.c()}function Ne(n,e,t,i){let{fragment:r,after_update:o}=n.$$;r&&r.m(e,t),i||oe(()=>{let a=n.$$.on_mount.map(yt).filter(ge);n.$$.on_destroy?n.$$.on_destroy.push(...a):de(a),n.$$.on_mount=[]}),o.forEach(oe)}function Ce(n,e){let t=n.$$;t.fragment!==null&&(de(t.on_destroy),t.fragment&&t.fragment.d(e),t.on_destroy=t.fragment=null,t.ctx=[])}function zr(n,e){n.$$.dirty[0]===-1&&(Ue.push(n),Tr(),n.$$.dirty.fill(0)),n.$$.dirty[e/31|0]|=1<<e%31}function Z(n,e,t,i,r,o,a,s=[-1]){let l=Ve;fe(n);let c=n.$$={fragment:null,ctx:[],props:o,update:T,not_equal:r,bound:xn(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(e.context||(l?l.$$.context:[])),callbacks:xn(),dirty:s,skip_bound:!1,root:e.target||l.$$.root};a&&a(c.root);let u=!1;if(c.ctx=t?t(n,e.props||{},(f,p,...y)=>{let g=y.length?y[0]:p;return c.ctx&&r(c.ctx[f],c.ctx[f]=g)&&(!c.skip_bound&&c.bound[f]&&c.bound[f](g),u&&zr(n,f)),p}):[],c.update(),u=!0,de(c.before_update),c.fragment=i?i(c.ctx):!1,e.target){if(e.hydrate){Ir();let f=Pr(e.target);c.fragment&&c.fragment.l(f),f.forEach(P)}else c.fragment&&c.fragment.c();e.intro&&_(n.$$.fragment),Ne(n,e.target,e.anchor,e.customElement),Mr(),jt()}fe(l)}var Vr;typeof HTMLElement=="function"&&(Vr=class extends HTMLElement{constructor(){super();this.attachShadow({mode:"open"})}connectedCallback(){let{on_mount:n}=this.$$;this.$$.on_disconnect=n.map(yt).filter(ge);for(let e in this.$$.slotted)this.appendChild(this.$$.slotted[e])}attributeChangedCallback(n,e,t){this[n]=t}disconnectedCallback(){de(this.$$.on_disconnect)}$destroy(){Ce(this,1),this.$destroy=T}$on(n,e){if(!ge(e))return T;let t=this.$$.callbacks[n]||(this.$$.callbacks[n]=[]);return t.push(e),()=>{let i=t.indexOf(e);i!==-1&&t.splice(i,1)}}$set(n){this.$$set&&!wn(n)&&(this.$$.skip_bound=!0,this.$$set(n),this.$$.skip_bound=!1)}});var K=class{$destroy(){Ce(this,1),this.$destroy=T}$on(e,t){if(!ge(t))return T;let i=this.$$.callbacks[e]||(this.$$.callbacks[e]=[]);return i.push(t),()=>{let r=i.indexOf(t);r!==-1&&i.splice(r,1)}}$set(e){this.$$set&&!wn(e)&&(this.$$.skip_bound=!0,this.$$set(e),this.$$.skip_bound=!1)}};function Rr(n){X(n,"svelte-ja9vc4","h3.svelte-ja9vc4{font-weight:400;margin:0}.feather.svelte-ja9vc4{text-align:center;color:var(--text-muted);font-size:0.9rem}")}function kn(n,e,t){let i=n.slice();return i[3]=e[t],i}function Ln(n,e,t){let i=n.slice();return i[6]=e[t],i}function Tn(n,e,t){let i=n.slice();return i[9]=e[t],i}function _n(n){let e,t,i,r=n[9].name+"",o,a,s,l=w("Website")+"",c,u;return{c(){e=A("div"),t=A("div"),i=A("div"),o=R(r),a=F(),s=A("a"),c=R(l),b(i,"class","setting-item-name"),b(t,"class","setting-item-info"),b(s,"class","setting-item-control"),b(s,"href",u=n[9].url),b(e,"class","setting-item")},m(f,p){N(f,e,p),S(e,t),S(t,i),S(i,o),S(e,a),S(e,s),S(s,c)},p(f,p){p&2&&r!==(r=f[9].name+"")&&W(o,r),p&2&&u!==(u=f[9].url)&&b(s,"href",u)},d(f){f&&P(e)}}}function zn(n){let e,t=n[9].offline===!1&&_n(n);return{c(){t&&t.c(),e=ce()},m(i,r){t&&t.m(i,r),N(i,e,r)},p(i,r){i[9].offline===!1?t?t.p(i,r):(t=_n(i),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null)},d(i){t&&t.d(i),i&&P(e)}}}function Vn(n){let e,t,i,r=n[6].name+"",o,a,s,l=w("Website")+"",c,u;return{c(){e=A("div"),t=A("div"),i=A("div"),o=R(r),a=F(),s=A("a"),c=R(l),b(i,"class","setting-item-name"),b(t,"class","setting-item-info"),b(s,"class","setting-item-control"),b(s,"href",u=n[6].url),b(e,"class","setting-item")},m(f,p){N(f,e,p),S(e,t),S(t,i),S(i,o),S(e,a),S(e,s),S(s,c)},p(f,p){p&1&&r!==(r=f[6].name+"")&&W(o,r),p&1&&u!==(u=f[6].url)&&b(s,"href",u)},d(f){f&&P(e)}}}function Rn(n){let e,t=n[6].offline===!1&&Vn(n);return{c(){t&&t.c(),e=ce()},m(i,r){t&&t.m(i,r),N(i,e,r)},p(i,r){i[6].offline===!1?t?t.p(i,r):(t=Vn(i),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null)},d(i){t&&t.d(i),i&&P(e)}}}function Un(n){let e,t,i,r=n[3].name+"",o,a,s,l=w("Website")+"",c,u,f;return{c(){e=A("div"),t=A("div"),i=A("div"),o=R(r),a=F(),s=A("a"),c=R(l),f=F(),b(i,"class","setting-item-name"),b(t,"class","setting-item-info"),b(s,"class","setting-item-control"),b(s,"href",u=n[3].url),b(e,"class","setting-item")},m(p,y){N(p,e,y),S(e,t),S(t,i),S(i,o),S(e,a),S(e,s),S(s,c),S(e,f)},p(p,y){y&4&&r!==(r=p[3].name+"")&&W(o,r),y&4&&u!==(u=p[3].url)&&b(s,"href",u)},d(p){p&&P(e)}}}function Ur(n){let e,t,i,r,o,a,s,l,c,u,f,p,y,g=w('This Plugin is using <a href="https://feathericons.com/">Feather Icons</a>')+"",v=n[1],m=[];for(let j=0;j<v.length;j+=1)m[j]=zn(Tn(n,v,j));let x=n[0],C=[];for(let j=0;j<x.length;j+=1)C[j]=Rn(Ln(n,x,j));let E=n[2],D=[];for(let j=0;j<E.length;j+=1)D[j]=Un(kn(n,E,j));return{c(){e=A("div"),t=A("h2"),t.textContent=`${w("API Information")}`,i=F(),r=A("h3"),r.textContent=`${w("Definition API's")}`,o=F();for(let j=0;j<m.length;j+=1)m[j].c();a=F(),s=A("h3"),s.textContent=`${w("Synonym API's")}`,l=F();for(let j=0;j<C.length;j+=1)C[j].c();c=F(),u=A("h3"),u.textContent=`${w("Part of Speech API's")}`,f=F();for(let j=0;j<D.length;j+=1)D[j].c();p=F(),y=A("p"),b(r,"class","svelte-ja9vc4"),b(s,"class","svelte-ja9vc4"),b(u,"class","svelte-ja9vc4"),b(e,"class","vertical-tab-content"),b(y,"class","feather svelte-ja9vc4")},m(j,k){N(j,e,k),S(e,t),S(e,i),S(e,r),S(e,o);for(let I=0;I<m.length;I+=1)m[I].m(e,null);S(e,a),S(e,s),S(e,l);for(let I=0;I<C.length;I+=1)C[I].m(e,null);S(e,c),S(e,u),S(e,f);for(let I=0;I<D.length;I+=1)D[I].m(e,null);N(j,p,k),N(j,y,k),y.innerHTML=g},p(j,[k]){if(k&2){v=j[1];let I;for(I=0;I<v.length;I+=1){let O=Tn(j,v,I);m[I]?m[I].p(O,k):(m[I]=zn(O),m[I].c(),m[I].m(e,a))}for(;I<m.length;I+=1)m[I].d(1);m.length=v.length}if(k&1){x=j[0];let I;for(I=0;I<x.length;I+=1){let O=Ln(j,x,I);C[I]?C[I].p(O,k):(C[I]=Rn(O),C[I].c(),C[I].m(e,c))}for(;I<C.length;I+=1)C[I].d(1);C.length=x.length}if(k&4){E=j[2];let I;for(I=0;I<E.length;I+=1){let O=kn(j,E,I);D[I]?D[I].p(O,k):(D[I]=Un(O),D[I].c(),D[I].m(e,null))}for(;I<D.length;I+=1)D[I].d(1);D.length=E.length}},i:T,o:T,d(j){j&&P(e),ee(m,j),ee(C,j),ee(D,j),j&&P(p),j&&P(y)}}}function Hr(n,e,t){let{synonymAPIs:i}=e,{definitionAPIs:r}=e,{partOfSpeechAPIs:o}=e;return n.$$set=a=>{"synonymAPIs"in a&&t(0,i=a.synonymAPIs),"definitionAPIs"in a&&t(1,r=a.definitionAPIs),"partOfSpeechAPIs"in a&&t(2,o=a.partOfSpeechAPIs)},[i,r,o]}var Hn=class extends K{constructor(e){super();Z(this,e,Hr,Ur,Q,{synonymAPIs:0,definitionAPIs:1,partOfSpeechAPIs:2},Rr)}},Bn=Hn;var tt=class extends U.PluginSettingTab{constructor(e,t){super(e,t);this.plugin=t}display(){let{containerEl:e,plugin:t}=this;if(e.empty(),e.createEl("h2",{text:w("Dictionary Settings")}),new U.Setting(e).setName(w("Language")).setDesc(w("The Language the Plugin will use to search for Definitions and Pronunciations.")).addDropdown(o=>{for(let a in Se)o.addOption(a,Se[a]);o.setValue(t.settings.normalLang).onChange(a=>M(this,null,function*(){t.settings.defaultLanguage=a,t.settings.normalLang=a,yield this.save(),this.display()}))}),new U.Setting(e).setName("Get Language from File").setDesc('Use the lang or language key in the frontmatter to set the Language dependent on your open File. Example: lang: "en_US" or language: "de".').addToggle(o=>{o.setValue(this.plugin.settings.getLangFromFile).onChange(a=>M(this,null,function*(){this.plugin.settings.getLangFromFile=a,yield this.plugin.saveSettings()}))}),new U.Setting(e).setName(w("Definition Provider")).setDesc(w("The API the Plugin will use to search for Definitions.")).addDropdown(o=>{var s;let a={};for(let l of t.manager.definitionProvider)l.supportedLanguages.contains(t.settings.defaultLanguage)&&(a[l.name]=l.name);o.addOptions(a).setValue((s=t.settings.apiSettings[t.settings.defaultLanguage].definitionApiName)!=null?s:Object.keys(a).first()).onChange(l=>M(this,null,function*(){t.settings.apiSettings[t.settings.defaultLanguage].definitionApiName=l,yield this.save()}))}),new U.Setting(e).setName(w("Synonym Provider")).setDesc(w("The API the Plugin will use to search for Synonyms.")).addDropdown(o=>{var s;let a={};for(let l of t.manager.synonymProvider)l.supportedLanguages.contains(t.settings.defaultLanguage)&&(a[l.name]=l.name);o.addOptions(a).setValue((s=t.settings.apiSettings[t.settings.defaultLanguage].synonymApiName)!=null?s:Object.keys(a).first()).onChange(l=>M(this,null,function*(){t.settings.apiSettings[t.settings.defaultLanguage].synonymApiName=l,yield this.save()}))}),new U.Setting(e).setName(w("Synonym Suggestions")).setDesc(w("Show synonyms for highlighted words")).addToggle(o=>{t.settings.shouldShowSynonymPopover?o.setValue(!0):o.setValue(!1),o.onChange(a=>M(this,null,function*(){t.settings.shouldShowSynonymPopover=a,yield this.save()}))}),t.manager.partOfSpeechProvider.length){let o=document.createDocumentFragment();o.append(w("Enabling this will allow the Plugin to analyze full sentences to better suggest synonyms based on the context."),o.createEl("br"),w("Click "),o.createEl("a",{href:"https://github.com/phibr0/obsidian-dictionary#privacy",text:w("here")}),w(" for Privacy Concerns.")),new U.Setting(e).setName(w("Advanced Synonym Search")).setDesc(o).addToggle(a=>{a.setValue(t.settings.advancedSynonymAnalysis),a.onChange(s=>M(this,null,function*(){t.settings.advancedSynonymAnalysis=s,yield this.save()}))})}new U.Setting(e).setName(w("Show Options in Context Menu")).setDesc(w("Enable custom Context Menu with options to search for synonyms (only if the auto suggestions are disabled) and to look up a full definition in the Sidebar. Warning: This will override Obsidian's default Context Menu.")).addToggle(o=>{t.settings.shouldShowCustomContextMenu?o.setValue(!0):o.setValue(!1),o.onChange(a=>M(this,null,function*(){t.settings.shouldShowCustomContextMenu=a,yield this.save()}))}),e.createEl("h3",{text:w("Local-Dictionary-Builder Settings")}),new U.Setting(e).setName(w("Local Dictionary Folder")).setDesc(w("Specify a Folder, where all new Notes created by the Dictionary are placed. Please note that this Folder needs to already exist.")).addText(o=>o.setPlaceholder(w("Dictionary")).setValue(t.settings.folder).onChange(a=>M(this,null,function*(){t.settings.folder=a,yield this.save()}))),new U.Setting(e).setName(w("Use Language specific Subfolders")).setDesc(w('Create Subfolders for every language, e.g. "Dictionary/en-US/Cake"')).addToggle(o=>{o.setValue(t.settings.languageSpecificSubFolders),o.onChange(a=>M(this,null,function*(){t.settings.languageSpecificSubFolders=a,yield this.save()}))}),new U.Setting(e).setName(w("Capitalize File Name")).setDesc(w("If you disable this, the names of newly created files will be all lowercase.")).addToggle(o=>{o.setValue(t.settings.capitalizedFileName),o.onChange(a=>M(this,null,function*(){t.settings.capitalizedFileName=a,yield this.save()}))}),new U.Setting(e).setName(w("Filename Prefix and Suffix")).setDesc(w("Here you can add a Prefix and Suffix for your newly created Files.")).setClass("dictionaryprefixsuffix").addText(o=>o.setPlaceholder(w("Prefix")).setValue(t.settings.prefix).onChange(a=>M(this,null,function*(){t.settings.prefix=a,yield this.save()}))).addText(o=>o.setPlaceholder(w("Suffix")).setValue(t.settings.suffix).onChange(a=>M(this,null,function*(){t.settings.suffix=a,yield this.save()})));let i=document.createDocumentFragment();i.append(w("Here you can edit the Template for newly created Files."),i.createEl("br"),i.createEl("a",{href:"https://github.com/phibr0/obsidian-dictionary#variables",text:w("Click for a List of Variables")})),new U.Setting(e).setName(w("Template")).setDesc(i).setClass("dictionarytextarea").addTextArea(o=>o.setPlaceholder(Me.template).setValue(t.settings.template).onChange(a=>M(this,null,function*(){t.settings.template=a,yield this.save()}))).addExtraButton(o=>{o.setIcon("reset").setTooltip(w("Reset to default")).setDisabled(this.plugin.settings.template===Me.template).onClick(()=>M(this,null,function*(){this.plugin.settings.template=Me.template,yield this.plugin.saveSettings()}))}),e.createEl("h3",{text:w("Caching Settings")}),new U.Setting(e).setName(w("Use Caching")).setDesc(w("Enable or disable caching. Caching provides a semi-offline experience by saving every result for later use.")).addToggle(o=>{o.setValue(t.settings.useCaching),o.onChange(a=>M(this,null,function*(){t.settings.useCaching=a,yield this.save()}))});let r=document.createDocumentFragment();r.append(w("Here you can delete all cached Data."),i.createEl("br"),w("You currently have "),t.cache.cachedDefinitions.length.toString(),w(" cached Definitions and "),t.cache.cachedSynonyms.length.toString(),w(" cached Synonyms.")),new U.Setting(e).setName(w("Delete Cache")).setDesc(r).addButton(o=>{o.setDisabled(!t.settings.useCaching),o.setButtonText(w("Delete")),o.onClick(()=>M(this,null,function*(){t.cache.cachedSynonyms=[],t.cache.cachedDefinitions=[],yield this.plugin.saveCache(),new U.Notice(w("Success")),this.display()}))}),e.createEl("h3",{text:w("Miscellaneous")}),new U.Setting(e).setName(w("More Information")).setDesc(w("View Information about the API's and the Plugin itself.")).setClass("extra").addButton(o=>{o.setButtonText(w("More Info")),o.onClick(a=>{new Wn(t).open()})}),new U.Setting(e).setName(w("Donate")).setDesc(w("If you like this Plugin, consider donating to support continued development:")).setClass("extra").addButton(o=>{o.buttonEl.outerHTML='<a href="https://ko-fi.com/phibr0"><img src="https://uploads-ssl.webflow.com/5c14e387dab576fe667689cf/61e11e22d8ff4a5b4a1b3346_Supportbutton-1.png"></a>'})}save(){return M(this,null,function*(){yield this.plugin.saveSettings()})}},Wn=class extends U.Modal{constructor(e){super(e.app);this.plugin=e}onOpen(){this.contentEl.parentElement.style.padding="10px 12px",this._view=new Bn({target:this.contentEl,props:{synonymAPIs:this.plugin.manager.synonymProvider,definitionAPIs:this.plugin.manager.definitionProvider,partOfSpeechAPIs:this.plugin.manager.partOfSpeechProvider}})}onClose(){this._view.$destroy(),this.contentEl.empty()}};var tr=G(require("obsidian"));var pi=G(fi()),{__extends:za,__assign:Va,__rest:Ra,__decorate:Ua,__param:Ha,__metadata:Ba,__awaiter:hi,__generator:Wa,__exportStar:$a,__createBinding:Ga,__values:qa,__read:Ya,__spread:Ja,__spreadArrays:Ka,__spreadArray:Qa,__await:Xa,__asyncGenerator:Za,__asyncDelegator:es,__asyncValues:ts,__makeTemplateObject:ns,__importStar:is,__importDefault:rs,__classPrivateFieldGet:os,__classPrivateFieldSet:as,__classPrivateFieldIn:ss}=pi.default;function yi(n){let e=n-1;return e*e*e+1}function mi(n,{delay:e=0,duration:t=400,easing:i=_e}={}){let r=+getComputedStyle(n).opacity;return{delay:e,duration:t,easing:i,css:o=>`opacity: ${o*r}`}}function Fe(n,{delay:e=0,duration:t=400,easing:i=yi}={}){let r=getComputedStyle(n),o=+r.opacity,a=parseFloat(r.height),s=parseFloat(r.paddingTop),l=parseFloat(r.paddingBottom),c=parseFloat(r.marginTop),u=parseFloat(r.marginBottom),f=parseFloat(r.borderTopWidth),p=parseFloat(r.borderBottomWidth);return{delay:e,duration:t,easing:i,css:y=>`overflow: hidden;opacity: ${Math.min(y*20,1)*o};height: ${y*a}px;padding-top: ${y*s}px;padding-bottom: ${y*l}px;margin-top: ${y*c}px;margin-bottom: ${y*u}px;border-top-width: ${y*f}px;border-bottom-width: ${y*p}px;`}}function Br(n){X(n,"svelte-1ufvm6z",'@charset "UTF-8";.opener.svelte-1ufvm6z.svelte-1ufvm6z{display:flex}.opener.svelte-1ufvm6z .collapse-icon.svelte-1ufvm6z::after{content:"\xA0"}.opener.svelte-1ufvm6z svg.svelte-1ufvm6z{transform:rotate(-90deg)}.opener.open.svelte-1ufvm6z svg.svelte-1ufvm6z{transform:rotate(0)}.main.svelte-1ufvm6z.svelte-1ufvm6z{background-color:var(--background-secondary);padding-left:0.6rem;padding-right:0.6rem;padding-top:0.3rem;padding-bottom:0.3rem;margin-bottom:0.3rem;border-radius:0.3rem;overflow:hidden;text-overflow:ellipsis}audio.svelte-1ufvm6z.svelte-1ufvm6z{margin-top:0.3rem}')}function Wr(n){let e=n[0].replace("/","").replace("/","")+"",t;return{c(){t=R(e)},m(i,r){N(i,t,r)},p(i,r){r&1&&e!==(e=i[0].replace("/","").replace("/","")+"")&&W(t,e)},d(i){i&&P(t)}}}function $r(n){let e,t,i,r=n[0].replace("/","").replace("/","")+"",o,a,s,l,c,u=n[2]&&gi(n);return{c(){e=A("div"),t=A("div"),t.innerHTML='<svg viewBox="0 0 100 100" class="right-triangle svelte-1ufvm6z" width="8" height="8"><path fill="currentColor" stroke="currentColor" d="M94.9,20.8c-1.4-2.5-4.1-4.1-7.1-4.1H12.2c-3,0-5.7,1.6-7.1,4.1c-1.3,2.4-1.2,5.2,0.2,7.6L43.1,88c1.5,2.3,4,3.7,6.9,3.7 s5.4-1.4,6.9-3.7l37.8-59.6C96.1,26,96.2,23.2,94.9,20.8L94.9,20.8z"></path></svg>',i=F(),o=R(r),a=F(),u&&u.c(),s=ce(),b(t,"class","tree-item-icon collapse-icon svelte-1ufvm6z"),b(t,"style",""),b(e,"class","opener svelte-1ufvm6z"),re(e,"open",n[2])},m(f,p){N(f,e,p),S(e,t),S(e,i),S(e,o),N(f,a,p),u&&u.m(f,p),N(f,s,p),l||(c=Y(e,"click",n[3]),l=!0)},p(f,p){p&1&&r!==(r=f[0].replace("/","").replace("/","")+"")&&W(o,r),p&4&&re(e,"open",f[2]),f[2]?u?(u.p(f,p),p&4&&_(u,1)):(u=gi(f),u.c(),_(u,1),u.m(s.parentNode,s)):u&&(ae(),H(u,1,1,()=>{u=null}),se())},d(f){f&&P(e),f&&P(a),u&&u.d(f),f&&P(s),l=!1,c()}}}function gi(n){let e,t,i,r,o,a;return{c(){e=A("div"),t=A("audio"),i=A("source"),mt(i.src,r=n[1].startsWith("http")?n[1]:"https:"+n[1])||b(i,"src",r),b(i,"type","audio/mpeg"),t.controls=!0,b(t,"class","svelte-1ufvm6z")},m(s,l){N(s,e,l),S(e,t),S(t,i),a=!0},p(s,l){(!a||l&2&&!mt(i.src,r=s[1].startsWith("http")?s[1]:"https:"+s[1]))&&b(i,"src",r)},i(s){a||(s&&oe(()=>{o||(o=Pe(e,Fe,{duration:100},!0)),o.run(1)}),a=!0)},o(s){s&&(o||(o=Pe(e,Fe,{duration:100},!1)),o.run(0)),a=!1},d(s){s&&P(e),s&&o&&o.end()}}}function Gr(n){let e;function t(o,a){return o[1]?$r:Wr}let i=t(n,-1),r=i(n);return{c(){e=A("div"),r.c(),b(e,"class","main svelte-1ufvm6z")},m(o,a){N(o,e,a),r.m(e,null)},p(o,[a]){i===(i=t(o,a))&&r?r.p(o,a):(r.d(1),r=i(o),r&&(r.c(),r.m(e,null)))},i:T,o:T,d(o){o&&P(e),r.d()}}}function qr(n,e,t){let{text:i}=e,{audio:r}=e;addEventListener("dictionary-collapse",s=>t(2,o=s.detail.open));let o=!1,a=()=>t(2,o=!o);return n.$$set=s=>{"text"in s&&t(0,i=s.text),"audio"in s&&t(1,r=s.audio)},[i,r,o,a]}var vi=class extends K{constructor(e){super();Z(this,e,qr,Gr,Q,{text:0,audio:1},Br)}},xi=vi;var Pt=G(require("obsidian"));var Ci=G(Ot());function ao(n){X(n,"svelte-5jhpts",'@charset "UTF-8";.definition.svelte-5jhpts>p.svelte-5jhpts{user-select:text}.opener.svelte-5jhpts.svelte-5jhpts{display:flex}.opener.svelte-5jhpts .collapse-icon.svelte-5jhpts::after{content:"\xA0"}.opener.svelte-5jhpts svg.svelte-5jhpts{transform:rotate(-90deg)}.opener.open.svelte-5jhpts svg.svelte-5jhpts{transform:rotate(0)}.antonym.svelte-5jhpts.svelte-5jhpts,.synonym.svelte-5jhpts.svelte-5jhpts{transition:100ms}.antonym.svelte-5jhpts.svelte-5jhpts:hover,.synonym.svelte-5jhpts.svelte-5jhpts:hover{color:var(--interactive-accent);border-radius:2px}.main.svelte-5jhpts.svelte-5jhpts{background-color:var(--background-secondary);padding-left:0.6rem;padding-right:0.6rem;padding-top:0.3rem;padding-bottom:0.3rem;margin-bottom:0.3rem;border-radius:0.3rem}.main.svelte-5jhpts blockquote.svelte-5jhpts{font-style:italic;margin:0 0 1rem;padding-left:1rem;border-left:1px solid var(--background-modifier-border)}.main.svelte-5jhpts .mark{box-shadow:inset 0 -2px var(--text-faint)}.label.svelte-5jhpts.svelte-5jhpts{font-size:0.875em;font-weight:bold}.antonyms.svelte-5jhpts.svelte-5jhpts,.synonyms.svelte-5jhpts.svelte-5jhpts{padding-top:1rem}.antonyms.svelte-5jhpts>p.svelte-5jhpts,.synonyms.svelte-5jhpts>p.svelte-5jhpts,.definition.svelte-5jhpts>p.svelte-5jhpts{margin-top:0}.definition.svelte-5jhpts.svelte-5jhpts{padding:1.5rem 0;border-bottom:1px solid var(--background-modifier-border)}.definition.svelte-5jhpts.svelte-5jhpts:last-child{border-bottom:none}')}function ji(n,e,t){let i=n.slice();return i[8]=e[t],i[10]=t,i}function Ai(n,e,t){let i=n.slice();return i[11]=e[t],i[10]=t,i}function Di(n,e,t){let i=n.slice();return i[13]=e[t],i[10]=t,i}function Ii(n){let e,t,i,r=n[1],o=[];for(let a=0;a<r.length;a+=1)o[a]=Ti(ji(n,r,a));return{c(){e=A("div");for(let a=0;a<o.length;a+=1)o[a].c()},m(a,s){N(a,e,s);for(let l=0;l<o.length;l+=1)o[l].m(e,null);i=!0},p(a,s){if(s&19){r=a[1];let l;for(l=0;l<r.length;l+=1){let c=ji(a,r,l);o[l]?o[l].p(c,s):(o[l]=Ti(c),o[l].c(),o[l].m(e,null))}for(;l<o.length;l+=1)o[l].d(1);o.length=r.length}},i(a){i||(a&&oe(()=>{t||(t=Pe(e,Fe,{duration:150},!0)),t.run(1)}),i=!0)},o(a){a&&(t||(t=Pe(e,Fe,{duration:150},!1)),t.run(0)),i=!1},d(a){a&&P(e),ee(o,a),a&&t&&t.end()}}}function Mi(n){let e,t,i,r=n[8].definition+"",o;return{c(){e=A("div"),e.textContent=`${w("Definition:")}`,t=F(),i=A("p"),o=R(r),b(e,"class","label svelte-5jhpts"),b(i,"class","svelte-5jhpts")},m(a,s){N(a,e,s),N(a,t,s),N(a,i,s),S(i,o)},p(a,s){s&2&&r!==(r=a[8].definition+"")&&W(o,r)},d(a){a&&P(e),a&&P(t),a&&P(i)}}}function Oi(n){let e,t=n[8].example.replace(new RegExp(`(${n[0]})`,"gi"),'<i class="mark">$1</i>')+"";return{c(){e=A("blockquote"),b(e,"class","svelte-5jhpts")},m(i,r){N(i,e,r),e.innerHTML=t},p(i,r){r&3&&t!==(t=i[8].example.replace(new RegExp(`(${i[0]})`,"gi"),'<i class="mark">$1</i>')+"")&&(e.innerHTML=t)},d(i){i&&P(e)}}}function Pi(n){let e,t,i,r,o=n[8].synonyms,a=[];for(let s=0;s<o.length;s+=1)a[s]=Ei(Di(n,o,s));return{c(){e=A("div"),t=A("div"),t.textContent=`${w("Synonyms:")}`,i=F(),r=A("p");for(let s=0;s<a.length;s+=1)a[s].c();b(t,"class","label svelte-5jhpts"),b(r,"class","svelte-5jhpts"),b(e,"class","synonyms svelte-5jhpts")},m(s,l){N(s,e,l),S(e,t),S(e,i),S(e,r);for(let c=0;c<a.length;c+=1)a[c].m(r,null)},p(s,l){if(l&18){o=s[8].synonyms;let c;for(c=0;c<o.length;c+=1){let u=Di(s,o,c);a[c]?a[c].p(u,l):(a[c]=Ei(u),a[c].c(),a[c].m(r,null))}for(;c<a.length;c+=1)a[c].d(1);a.length=o.length}},d(s){s&&P(e),ee(a,s)}}}function Ni(n){let e=", ",t;return{c(){t=R(e)},m(i,r){N(i,t,r)},d(i){i&&P(t)}}}function Ei(n){let e,t=n[13]+"",i,r,o,a;function s(){return n[6](n[13])}let l=n[10]<n[8].synonyms.length-1&&Ni(n);return{c(){e=A("span"),i=R(t),l&&l.c(),r=ce(),b(e,"class","synonym svelte-5jhpts")},m(c,u){N(c,e,u),S(e,i),l&&l.m(c,u),N(c,r,u),o||(a=Y(e,"click",s),o=!0)},p(c,u){n=c,u&2&&t!==(t=n[13]+"")&&W(i,t),n[10]<n[8].synonyms.length-1?l||(l=Ni(n),l.c(),l.m(r.parentNode,r)):l&&(l.d(1),l=null)},d(c){c&&P(e),l&&l.d(c),c&&P(r),o=!1,a()}}}function Fi(n){let e,t,i,r,o=n[8].antonyms,a=[];for(let s=0;s<o.length;s+=1)a[s]=Li(Ai(n,o,s));return{c(){e=A("div"),t=A("div"),t.textContent=`${w("Antonyms:")}`,i=F(),r=A("p");for(let s=0;s<a.length;s+=1)a[s].c();b(t,"class","label svelte-5jhpts"),b(r,"class","svelte-5jhpts"),b(e,"class","antonyms svelte-5jhpts")},m(s,l){N(s,e,l),S(e,t),S(e,i),S(e,r);for(let c=0;c<a.length;c+=1)a[c].m(r,null)},p(s,l){if(l&18){o=s[8].antonyms;let c;for(c=0;c<o.length;c+=1){let u=Ai(s,o,c);a[c]?a[c].p(u,l):(a[c]=Li(u),a[c].c(),a[c].m(r,null))}for(;c<a.length;c+=1)a[c].d(1);a.length=o.length}},d(s){s&&P(e),ee(a,s)}}}function ki(n){let e=", ",t;return{c(){t=R(e)},m(i,r){N(i,t,r)},d(i){i&&P(t)}}}function Li(n){let e,t=n[11]+"",i,r,o,a;function s(){return n[7](n[11])}let l=n[10]<n[8].antonyms.length-1&&ki(n);return{c(){e=A("span"),i=R(t),l&&l.c(),r=ce(),b(e,"class","antonym svelte-5jhpts")},m(c,u){N(c,e,u),S(e,i),l&&l.m(c,u),N(c,r,u),o||(a=Y(e,"click",s),o=!0)},p(c,u){n=c,u&2&&t!==(t=n[11]+"")&&W(i,t),n[10]<n[8].antonyms.length-1?l||(l=ki(n),l.c(),l.m(r.parentNode,r)):l&&(l.d(1),l=null)},d(c){c&&P(e),l&&l.d(c),c&&P(r),o=!1,a()}}}function Ti(n){let e,t,i,r,o,a=n[8].definition&&Mi(n),s=n[8].example&&Oi(n),l=n[8].synonyms&&n[8].synonyms[n[10]]&&Pi(n),c=n[8].antonyms&&n[8].antonyms[n[10]]&&Fi(n);return{c(){e=A("div"),a&&a.c(),t=F(),s&&s.c(),i=F(),l&&l.c(),r=F(),c&&c.c(),o=F(),b(e,"class","definition svelte-5jhpts")},m(u,f){N(u,e,f),a&&a.m(e,null),S(e,t),s&&s.m(e,null),S(e,i),l&&l.m(e,null),S(e,r),c&&c.m(e,null),S(e,o)},p(u,f){u[8].definition?a?a.p(u,f):(a=Mi(u),a.c(),a.m(e,t)):a&&(a.d(1),a=null),u[8].example?s?s.p(u,f):(s=Oi(u),s.c(),s.m(e,i)):s&&(s.d(1),s=null),u[8].synonyms&&u[8].synonyms[u[10]]?l?l.p(u,f):(l=Pi(u),l.c(),l.m(e,r)):l&&(l.d(1),l=null),u[8].antonyms&&u[8].antonyms[u[10]]?c?c.p(u,f):(c=Fi(u),c.c(),c.m(e,o)):c&&(c.d(1),c=null)},d(u){u&&P(e),a&&a.d(),s&&s.d(),l&&l.d(),c&&c.d()}}}function so(n){var f;let e,t,i,r,o=((f=n[2])!=null?f:"")+"",a,s,l,c,u=n[3]&&Ii(n);return{c(){e=A("div"),t=A("div"),i=A("div"),i.innerHTML='<svg viewBox="0 0 100 100" class="right-triangle svelte-5jhpts" width="8" height="8"><path fill="currentColor" stroke="currentColor" d="M94.9,20.8c-1.4-2.5-4.1-4.1-7.1-4.1H12.2c-3,0-5.7,1.6-7.1,4.1c-1.3,2.4-1.2,5.2,0.2,7.6L43.1,88c1.5,2.3,4,3.7,6.9,3.7 s5.4-1.4,6.9-3.7l37.8-59.6C96.1,26,96.2,23.2,94.9,20.8L94.9,20.8z"></path></svg>',r=F(),a=R(o),s=F(),u&&u.c(),b(i,"class","tree-item-icon collapse-icon svelte-5jhpts"),b(i,"style",""),b(t,"class","opener svelte-5jhpts"),re(t,"open",n[3]),b(e,"class","main svelte-5jhpts")},m(p,y){N(p,e,y),S(e,t),S(t,i),S(t,r),S(t,a),S(e,s),u&&u.m(e,null),l||(c=Y(t,"click",n[5]),l=!0)},p(p,[y]){var g;y&4&&o!==(o=((g=p[2])!=null?g:"")+"")&&W(a,o),y&8&&re(t,"open",p[3]),p[3]?u?(u.p(p,y),y&8&&_(u,1)):(u=Ii(p),u.c(),_(u,1),u.m(e,null)):u&&(ae(),H(u,1,1,()=>{u=null}),se())},i(p){_(u)},o(p){H(u)},d(p){p&&P(e),u&&u.d(),l=!1,c()}}}function lo(n,e,t){let{word:i}=e,{definitions:r}=e,{partOfSpeech:o}=e,a=!1;addEventListener("dictionary-collapse",f=>{t(3,a=f.detail.open)});function s(f){(0,Ci.copy)(f,()=>new Pt.Notice(w('Copied "{{word}}" to clipboard').replace(/{{word}}/g,f)),p=>new Pt.Notice(p))}let l=()=>t(3,a=!a),c=f=>s(f),u=f=>s(f);return n.$$set=f=>{"word"in f&&t(0,i=f.word),"definitions"in f&&t(1,r=f.definitions),"partOfSpeech"in f&&t(2,o=f.partOfSpeech)},[i,r,o,a,s,l,c,u]}var _i=class extends K{constructor(e){super();Z(this,e,lo,so,Q,{word:0,definitions:1,partOfSpeech:2},ao)}},zi=_i;function co(n){X(n,"svelte-1lk7r5d",".error.svelte-1lk7r5d{text-align:center;width:100%;color:var(--text-muted)}.errorDescription.svelte-1lk7r5d{text-align:center;width:100%;font-size:0.9em;color:var(--text-faint)}")}function uo(n){var u;let e,t,i,r,o,a,s,l=((u=n[0])!=null?u:"")+"",c;return{c(){e=A("div"),t=A("p"),t.textContent="Something went wrong..",i=F(),r=A("p"),r.textContent=`${w("I can't find the word you are looking for or the server can't be reached. You can try again in a few minutes.")}`,o=F(),a=A("details"),s=A("summary"),s.textContent=`${w("View Error")}`,c=R(l),b(t,"class","error svelte-1lk7r5d"),b(r,"class","errorDescription svelte-1lk7r5d"),b(a,"class","errorDescription svelte-1lk7r5d"),b(e,"class","main")},m(f,p){N(f,e,p),S(e,t),S(e,i),S(e,r),S(e,o),S(e,a),S(a,s),S(a,c)},p(f,[p]){var y;p&1&&l!==(l=((y=f[0])!=null?y:"")+"")&&W(c,l)},i:T,o:T,d(f){f&&P(e)}}}function fo(n,e,t){let{error:i}=e;return Re(()=>{console.error(i)}),n.$$set=r=>{"error"in r&&t(0,i=r.error)},[i]}var Vi=class extends K{constructor(e){super();Z(this,e,fo,uo,Q,{error:0},co)}},Ri=Vi;function po(n){X(n,"svelte-v5d940",".main.svelte-v5d940{background-color:var(--background-secondary);padding-left:0.6rem;padding-right:0.6rem;padding-top:0.3rem;padding-bottom:0.3rem;margin-bottom:0.3rem;border-radius:0.3rem;user-select:text}")}function ho(n){let e,t=n[0].origin+"",i;return{c(){e=A("div"),i=R(t),b(e,"class","main svelte-v5d940")},m(r,o){N(r,e,o),S(e,i)},p(r,[o]){o&1&&t!==(t=r[0].origin+"")&&W(i,t)},i:T,o:T,d(r){r&&P(e)}}}function yo(n,e,t){let{data:i}=e;return n.$$set=r=>{"data"in r&&t(0,i=r.data)},[i]}var Ui=class extends K{constructor(e){super();Z(this,e,yo,ho,Q,{data:0},po)}},Hi=Ui;var we=G(require("obsidian"));function mo(n){X(n,"svelte-17ilbu5",'.contents.svelte-17ilbu5.svelte-17ilbu5{height:calc(100% - 5rem);overflow-y:auto}.results.svelte-17ilbu5.svelte-17ilbu5{display:flex;flex-wrap:wrap}.container.svelte-17ilbu5.svelte-17ilbu5{max-width:30vw;width:100%;margin:auto;background-color:var(--background-primary-alt);padding-left:0.5rem;padding-right:0.5rem;padding-top:0.3rem;padding-bottom:0.3rem;margin-top:0.5rem;border-radius:0.3rem}.container.svelte-17ilbu5>h3.svelte-17ilbu5{margin-top:0.3rem;margin-bottom:0.3rem;font-weight:normal}.center.svelte-17ilbu5.svelte-17ilbu5{margin:auto;width:100%;margin-top:2rem}@keyframes svelte-17ilbu5-spinner{0%{transform:translate3d(-50%, -50%, 0) rotate(0deg)}100%{transform:translate3d(-50%, -50%, 0) rotate(360deg)}}.spinner.svelte-17ilbu5.svelte-17ilbu5{height:3rem;opacity:1;position:relative;transition:opacity linear 0.1s}.spinner.svelte-17ilbu5.svelte-17ilbu5::before{animation:2s linear infinite svelte-17ilbu5-spinner;border:solid 3px var(--background-modifier-border);border-bottom-color:var(--interactive-accent);border-radius:50%;content:"";height:40px;left:50%;opacity:inherit;position:absolute;top:50%;transform:translate3d(-50%, -50%, 0);transform-origin:center;width:40px;will-change:transform}')}function Bi(n,e,t){let i=n.slice();return i[22]=e[t].definitions,i[23]=e[t].partOfSpeech,i}function Wi(n,e,t){let i=n.slice();return i[26]=e[t].text,i[27]=e[t].audio,i}function $i(n){let e,t,i,r;return{c(){e=A("div"),b(e,"class","search-input-clear-button"),b(e,"aria-label",t=w("Clear"))},m(o,a){N(o,e,a),i||(r=Y(e,"click",n[9]),i=!0)},p:T,d(o){o&&P(e),i=!1,r()}}}function go(n){let e;return{c(){e=A("div"),e.innerHTML='<div class="spinner svelte-17ilbu5"></div>',b(e,"class","center svelte-17ilbu5")},m(t,i){N(t,e,i)},p:T,i:T,o:T,d(t){t&&P(e)}}}function vo(n){let e,t,i,r={ctx:n,current:null,token:null,hasCatch:!0,pending:bo,then:wo,catch:xo,value:21,error:30,blocks:[,,,]};return At(t=n[2],r),{c(){e=ce(),r.block.c()},m(o,a){N(o,e,a),r.block.m(o,r.anchor=a),r.mount=()=>e.parentNode,r.anchor=e,i=!0},p(o,a){n=o,r.ctx=n,a&4&&t!==(t=n[2])&&At(t,r)||Fn(r,n,a)},i(o){i||(_(r.block),i=!0)},o(o){for(let a=0;a<3;a+=1){let s=r.blocks[a];H(s)}i=!1},d(o){o&&P(e),r.block.d(o),r.token=null,r=null}}}function xo(n){let e,t;return e=new Ri({props:{error:n[30]}}),{c(){We(e.$$.fragment)},m(i,r){Ne(e,i,r),t=!0},p(i,r){let o={};r&4&&(o.error=i[30]),e.$set(o)},i(i){t||(_(e.$$.fragment,i),t=!0)},o(i){H(e.$$.fragment,i),t=!1},d(i){Ce(e,i)}}}function wo(n){var g,v;let e,t=(v=(g=n[21].phonetics)==null?void 0:g.first())==null?void 0:v.text,i,r,o,a,s,l,c=t&&Gi(n),u=n[21].meanings,f=[];for(let m=0;m<u.length;m+=1)f[m]=Yi(Bi(n,u,m));let p=m=>H(f[m],1,1,()=>{f[m]=null}),y=n[21].origin&&Ji(n);return{c(){e=A("div"),c&&c.c(),i=F(),r=A("div"),o=A("h3"),o.textContent=`${w("Meanings")}`,a=F();for(let m=0;m<f.length;m+=1)f[m].c();s=F(),y&&y.c(),b(o,"class","svelte-17ilbu5"),b(r,"class","container svelte-17ilbu5"),b(e,"class","results svelte-17ilbu5")},m(m,x){N(m,e,x),c&&c.m(e,null),S(e,i),S(e,r),S(r,o),S(r,a);for(let C=0;C<f.length;C+=1)f[C].m(r,null);S(e,s),y&&y.m(e,null),l=!0},p(m,x){var C,E;if(x&4&&(t=(E=(C=m[21].phonetics)==null?void 0:C.first())==null?void 0:E.text),t?c?(c.p(m,x),x&4&&_(c,1)):(c=Gi(m),c.c(),_(c,1),c.m(e,i)):c&&(ae(),H(c,1,1,()=>{c=null}),se()),x&4){u=m[21].meanings;let D;for(D=0;D<u.length;D+=1){let j=Bi(m,u,D);f[D]?(f[D].p(j,x),_(f[D],1)):(f[D]=Yi(j),f[D].c(),_(f[D],1),f[D].m(r,null))}for(ae(),D=u.length;D<f.length;D+=1)p(D);se()}m[21].origin?y?(y.p(m,x),x&4&&_(y,1)):(y=Ji(m),y.c(),_(y,1),y.m(e,null)):y&&(ae(),H(y,1,1,()=>{y=null}),se())},i(m){if(!l){_(c);for(let x=0;x<u.length;x+=1)_(f[x]);_(y),l=!0}},o(m){H(c),f=f.filter(Boolean);for(let x=0;x<f.length;x+=1)H(f[x]);H(y),l=!1},d(m){m&&P(e),c&&c.d(),ee(f,m),y&&y.d()}}}function Gi(n){let e,t,i,r,o=n[21].phonetics,a=[];for(let l=0;l<o.length;l+=1)a[l]=qi(Wi(n,o,l));let s=l=>H(a[l],1,1,()=>{a[l]=null});return{c(){e=A("div"),t=A("h3"),t.textContent=`${w("Pronunciation")}`,i=F();for(let l=0;l<a.length;l+=1)a[l].c();b(t,"class","svelte-17ilbu5"),b(e,"class","container svelte-17ilbu5")},m(l,c){N(l,e,c),S(e,t),S(e,i);for(let u=0;u<a.length;u+=1)a[u].m(e,null);r=!0},p(l,c){if(c&4){o=l[21].phonetics;let u;for(u=0;u<o.length;u+=1){let f=Wi(l,o,u);a[u]?(a[u].p(f,c),_(a[u],1)):(a[u]=qi(f),a[u].c(),_(a[u],1),a[u].m(e,null))}for(ae(),u=o.length;u<a.length;u+=1)s(u);se()}},i(l){if(!r){for(let c=0;c<o.length;c+=1)_(a[c]);r=!0}},o(l){a=a.filter(Boolean);for(let c=0;c<a.length;c+=1)H(a[c]);r=!1},d(l){l&&P(e),ee(a,l)}}}function qi(n){let e,t;return e=new xi({props:{audio:n[27],text:n[26]}}),{c(){We(e.$$.fragment)},m(i,r){Ne(e,i,r),t=!0},p(i,r){let o={};r&4&&(o.audio=i[27]),r&4&&(o.text=i[26]),e.$set(o)},i(i){t||(_(e.$$.fragment,i),t=!0)},o(i){H(e.$$.fragment,i),t=!1},d(i){Ce(e,i)}}}function Yi(n){let e,t;return e=new zi({props:{word:n[21].word,partOfSpeech:n[23],definitions:n[22]}}),{c(){We(e.$$.fragment)},m(i,r){Ne(e,i,r),t=!0},p(i,r){let o={};r&4&&(o.word=i[21].word),r&4&&(o.partOfSpeech=i[23]),r&4&&(o.definitions=i[22]),e.$set(o)},i(i){t||(_(e.$$.fragment,i),t=!0)},o(i){H(e.$$.fragment,i),t=!1},d(i){Ce(e,i)}}}function Ji(n){let e,t,i,r,o;return r=new Hi({props:{data:n[21]}}),{c(){e=A("div"),t=A("h3"),t.textContent=`${w("Origin")}`,i=F(),We(r.$$.fragment),b(t,"class","svelte-17ilbu5"),b(e,"class","container svelte-17ilbu5")},m(a,s){N(a,e,s),S(e,t),S(e,i),Ne(r,e,null),o=!0},p(a,s){let l={};s&4&&(l.data=a[21]),r.$set(l)},i(a){o||(_(r.$$.fragment,a),o=!0)},o(a){H(r.$$.fragment,a),o=!1},d(a){a&&P(e),Ce(r)}}}function bo(n){let e;return{c(){e=A("div"),e.innerHTML='<div class="spinner svelte-17ilbu5"></div>',b(e,"class","center svelte-17ilbu5")},m(t,i){N(t,e,i)},p:T,i:T,o:T,d(t){t&&P(e)}}}function So(n){let e,t,i,r,o,a,s,l,c,u,f,p,y,g,v,m,x,C,E,D,j,k,I,O,B,ye,Ae,be,J=n[0]&&$i(n),De=[vo,go],$=[];function ue(L,q){return q&1&&(I=null),L[2]&&L[0]===L[1]?0:(I==null&&(I=!!L[0].trim()),I?1:-1)}return~(O=ue(n,-1))&&(B=$[O]=De[O](n)),{c(){e=A("div"),t=A("div"),r=F(),o=A("div"),s=F(),l=A("div"),u=F(),f=A("div"),y=F(),g=A("div"),m=F(),x=A("div"),C=A("input"),D=F(),J&&J.c(),j=F(),k=A("div"),B&&B.c(),b(t,"id","languageModal"),b(t,"class","nav-action-button"),b(t,"aria-label",i=w("Change Language")),b(o,"id","apiModal"),b(o,"class","nav-action-button"),b(o,"aria-label",a=w("Change Provider")),b(l,"id","openAndCloseAll"),b(l,"class","nav-action-button"),b(l,"aria-label",c=w("Collapse Results")),re(l,"is-active",n[5]),b(f,"id","matchCaseBtn"),b(f,"class","nav-action-button"),b(f,"aria-label",p=w("Match Case")),re(f,"is-active",n[4]),b(g,"id","localDictionaryBuilder"),b(g,"class","nav-action-button"),b(g,"aria-label",v=w("New Note")),b(e,"class","nav-buttons-container"),b(C,"id","dictionary-search-input"),b(C,"type","text"),b(C,"spellcheck","true"),b(C,"placeholder",E=w("Enter a word")),b(x,"class","search-input-container"),b(k,"class","contents svelte-17ilbu5")},m(L,q){N(L,e,q),S(e,t),n[13](t),S(e,r),S(e,o),n[14](o),S(e,s),S(e,l),n[15](l),S(e,u),S(e,f),n[16](f),S(e,y),S(e,g),n[18](g),N(L,m,q),N(L,x,q),S(x,C),xt(C,n[0]),S(x,D),J&&J.m(x,null),N(L,j,q),N(L,k,q),~O&&$[O].m(k,null),ye=!0,Ae||(be=[Y(t,"click",Co),Y(o,"click",jo),Y(l,"click",n[7]),Y(f,"click",n[17]),Y(g,"click",n[10]),Y(C,"input",n[19]),Y(C,"keydown",n[8]),Y(C,"keydown",n[6])],Ae=!0)},p(L,[q]){(!ye||q&32)&&re(l,"is-active",L[5]),(!ye||q&16)&&re(f,"is-active",L[4]),q&1&&C.value!==L[0]&&xt(C,L[0]),L[0]?J?J.p(L,q):(J=$i(L),J.c(),J.m(x,null)):J&&(J.d(1),J=null);let ie=O;O=ue(L,q),O===ie?~O&&$[O].p(L,q):(B&&(ae(),H($[ie],1,1,()=>{$[ie]=null}),se()),~O?(B=$[O],B?B.p(L,q):(B=$[O]=De[O](L),B.c()),_(B,1),B.m(k,null)):B=null)},i(L){ye||(_(B),ye=!0)},o(L){H(B),ye=!1},d(L){L&&P(e),n[13](null),n[14](null),n[15](null),n[16](null),n[18](null),L&&P(m),L&&P(x),J&&J.d(),L&&P(j),L&&P(k),~O&&$[O].d(),Ae=!1,de(be)}}}function Co(){dispatchEvent(new Event("dictionary-open-language-switcher"))}function jo(){dispatchEvent(new Event("dictionary-open-api-switcher"))}function Ao(n,e,t){let{manager:i}=e,{localDictionary:r}=e,{query:o=""}=e,a=null,s,l=[];Re(()=>setTimeout(()=>{(0,we.setIcon)(l[0],"languages",20),(0,we.setIcon)(l[1],"cloud",20),(0,we.setIcon)(l[2],"bullet-list",20),(0,we.setIcon)(l[3],"uppercase-lowercase-a",20),(0,we.setIcon)(l[4],"documents",20)},0));let c=(0,we.debounce)(f,800,!0),u=!0;function f(){o.trim()&&(t(1,a=o),t(2,s=i.requestDefinitions(u?o:o.toLowerCase())))}let p=!1;function y(){dispatchEvent(new CustomEvent("dictionary-collapse",{detail:{open:!p}})),t(5,p=!p)}function g(O){O.key==="Enter"&&f()}function v(){t(0,o=""),t(1,a=null),t(2,s=null),document.querySelector("#dictionary-search-input").focus()}function m(O){return hi(this,void 0,void 0,function*(){o.trim()&&s&&(yield r.newNote(yield s,!O.ctrlKey))})}addEventListener("obsidian-dictionary-plugin-search",O=>{t(0,o=O.detail.query),f()}),addEventListener("dictionary-focus-on-search",()=>{document.querySelector("#dictionary-search-input").focus()});function x(O){ve[O?"unshift":"push"](()=>{l[0]=O,t(3,l)})}function C(O){ve[O?"unshift":"push"](()=>{l[1]=O,t(3,l)})}function E(O){ve[O?"unshift":"push"](()=>{l[2]=O,t(3,l)})}function D(O){ve[O?"unshift":"push"](()=>{l[3]=O,t(3,l)})}let j=()=>t(4,u=!u);function k(O){ve[O?"unshift":"push"](()=>{l[4]=O,t(3,l)})}function I(){o=this.value,t(0,o)}return n.$$set=O=>{"manager"in O&&t(11,i=O.manager),"localDictionary"in O&&t(12,r=O.localDictionary),"query"in O&&t(0,o=O.query)},[o,a,s,l,u,p,c,y,g,v,m,i,r,x,C,E,D,j,k,I]}var Ki=class extends K{constructor(e){super();Z(this,e,Ao,So,Q,{manager:11,localDictionary:12,query:0},mo)}},Qi=Ki;var Xi=G(require("obsidian"));var ke=class extends Xi.FuzzySuggestModal{constructor(e,t){super(e);this.plugin=t,this.setPlaceholder(w("Choose a Language"))}getItems(){let e=[];for(let t in Se)e.push(t);return e}getItemText(e){return e==this.plugin.settings.defaultLanguage?Se[e]+" \u{1F5F8}":Se[e]}onChooseItem(e){return M(this,null,function*(){this.plugin.settings.defaultLanguage=e,this.plugin.settings.normalLang=e,yield this.plugin.saveSettings(),this.close()})}};var er=G(require("obsidian"));var Zi=G(require("obsidian"));var ot=class extends Zi.FuzzySuggestModal{constructor(e,t){super(e);this.available=[];this.plugin=t;for(let i=0;i<this.plugin.manager.synonymProvider.length;i++){let r=this.plugin.manager.synonymProvider[i];r.supportedLanguages.contains(this.plugin.settings.defaultLanguage)&&this.available.push(r.name)}this.setPlaceholder(w("Choose a Synonym Provider Service"))}onOpen(){var e;this.available.length<=1&&this.onChooseItem((e=this.available.first())!=null?e:null),super.onOpen()}getItems(){return this.available}getItemText(e){return e}onChooseItem(e){return M(this,null,function*(){let t=this.plugin.settings.defaultLanguage;this.plugin.settings.apiSettings[t].synonymApiName=e,yield this.plugin.saveSettings(),this.close()})}};var at=class extends er.FuzzySuggestModal{constructor(e,t){super(e);this.available=[];this.plugin=t;for(let i=0;i<this.plugin.manager.definitionProvider.length;i++){let r=this.plugin.manager.definitionProvider[i];r.supportedLanguages.contains(this.plugin.settings.defaultLanguage)&&this.available.push(r.name)}this.setPlaceholder(w("Choose a Definition Provider Service"))}onOpen(){var e;this.available.length<=1&&this.onChooseItem((e=this.available.first())!=null?e:null),super.onOpen()}getItems(){return this.available}getItemText(e){return e}onChooseItem(e){return M(this,null,function*(){let t=this.plugin.settings.defaultLanguage;this.plugin.settings.apiSettings[t].definitionApiName=e,yield this.plugin.saveSettings(),this.close(),new ot(this.app,this.plugin).open()})}};var st=class extends tr.ItemView{constructor(e,t){super(e);this.plugin=t}query(e){dispatchEvent(new CustomEvent("obsidian-dictionary-plugin-search",{detail:{query:e}}))}getViewType(){return ne}getDisplayText(){return mn}getIcon(){return gn}onClose(){return this._view.$destroy(),super.onClose()}onOpen(){return this._view=new Qi({target:this.contentEl,props:{manager:this.plugin.manager,localDictionary:this.plugin.localDictionary}}),this.contentEl.addClass("dictionary-view-content"),addEventListener("dictionary-open-language-switcher",()=>{new ke(this.app,this.plugin).open()}),addEventListener("dictionary-open-api-switcher",()=>{new at(this.app,this.plugin).open()}),super.onOpen()}};var lt=G(require("obsidian")),Nt=class{constructor(e){this.name="Offline Dictionary";this.supportedLanguages=["en_US","en_GB","cn"];this.offline=!0;this.manager=e}requestDefinitions(e,t){return M(this,null,function*(){let i=(yield this.getOfflineDictionary())[e.toLowerCase()];if(!i)return Promise.reject("Word doesnt exist in Offline Dictionary");let r=[];i.readings.forEach(s=>{r.push({text:s})});let o=[];return i.defs.forEach(s=>{var c,u,f,p,y;let l=[];l.push({definition:t==="cn"?s.def_cn:s.def_en,example:t==="cn"?(u=(c=s.ext)==null?void 0:c.first())==null?void 0:u.ext_cn:(y=(p=(f=s.ext)==null?void 0:f.first())==null?void 0:p.ext_en)!=null?y:""}),o.push({partOfSpeech:t==="cn"?s.pos_cn:s.pos_en,definitions:l})}),{word:e,phonetics:r,meanings:o}})}getOfflineDictionary(){return M(this,null,function*(){let{plugin:e}=this.manager,{adapter:t}=e.app.vault,i=(0,lt.normalizePath)(`${e.manifest.dir}/offlineDictionary.json`);if(!this.offlineDic){if(!(yield t.exists(i))){let r=yield(0,lt.request)({url:`https://github.com/phibr0/obsidian-dictionary/releases/download/${e.manifest.version}/dictionary.json`});yield t.write(i,r)}this.offlineDic=JSON.parse(yield t.read(i))}return this.offlineDic})}};var Et=G(require("obsidian"));var je;(function(r){r[r.Noun=0]="Noun",r[r.Verb=1]="Verb",r[r.Adjective=2]="Adjective",r[r.Adverb=3]="Adverb"})(je||(je={}));var nr;(function(h){h.Adj="ADJ",h.COMBInADJ="COMB in ADJ",h.Empty="",h.\u4E0D\u53EF\u6570\u540D\u8BCD="\u4E0D\u53EF\u6570\u540D\u8BCD",h.\u4E0D\u53EF\u6570\u540D\u8BCD\u590D\u6570\u540D\u8BCD="\u4E0D\u53EF\u6570\u540D\u8BCD\uFF1B\u590D\u6570\u540D\u8BCD",h.\u4E0D\u53EF\u6570\u540D\u8BCD\u611F\u53F9\u8BCD="\u4E0D\u53EF\u6570\u540D\u8BCD\uFF1B\u611F\u53F9\u8BCD",h.\u4E0D\u53EF\u6570\u96C6\u5408\u540D\u8BCD="\u4E0D\u53EF\u6570\u96C6\u5408\u540D\u8BCD",h.\u4E0D\u5B9A\u4EE3\u8BCD="\u4E0D\u5B9A\u4EE3\u8BCD",h.\u4E0D\u5B9A\u526F\u8BCD="\u4E0D\u5B9A\u526F\u8BCD",h.\u4E0E\u526F\u8BCD\u6784\u6210\u7684\u8BCD="\u4E0E\u526F\u8BCD\u6784\u6210\u7684\u8BCD",h.\u4E0E\u52A8\u8BCD\u6784\u6210\u7684\u8BCD="\u4E0E\u52A8\u8BCD\u6784\u6210\u7684\u8BCD",h.\u4E0E\u540D\u8BCD\u6784\u6210\u7684\u8BCD="\u4E0E\u540D\u8BCD\u6784\u6210\u7684\u8BCD",h.\u4E0E\u5F62\u5BB9\u8BCD\u53EF\u6570\u540D\u8BCD\u6784\u6210\u7684\u8BCD="\u4E0E\u5F62\u5BB9\u8BCD\uFF0C\u53EF\u6570\u540D\u8BCD\u6784\u6210\u7684\u8BCD",h.\u4E0E\u5F62\u5BB9\u8BCD\u540D\u8BCD\u6784\u6210\u7684\u8BCD="\u4E0E\u5F62\u5BB9\u8BCD\uFF0C\u540D\u8BCD\u6784\u6210\u7684\u8BCD",h.\u4E0E\u5F62\u5BB9\u8BCD\u6784\u6210\u7684\u8BCD="\u4E0E\u5F62\u5BB9\u8BCD\u6784\u6210\u7684\u8BCD",h.\u4E0E\u6570\u91CF\u8BCD\u6784\u6210\u7684\u8BCD="\u4E0E\u6570\u91CF\u8BCD\u6784\u6210\u7684\u8BCD",h.\u4E0E\u989C\u8272\u8BCD\u6784\u6210\u7684\u8BCD="\u4E0E\u989C\u8272\u8BCD\u6784\u6210\u7684\u8BCD",h.\u4E13\u6709\u540D\u8BCD="\u4E13\u6709\u540D\u8BCD",h.\u4E13\u6709\u540D\u8BCD\u53EF\u6570\u540D\u8BCD="\u4E13\u6709\u540D\u8BCD\uFF1B\u53EF\u6570\u540D\u8BCD",h.\u4E13\u6709\u540D\u8BCD\u79F0\u547C\u540D\u8BCD="\u4E13\u6709\u540D\u8BCD\uFF1B\u79F0\u547C\u540D\u8BCD",h.\u4E13\u6709\u590D\u6570\u540D\u8BCD="\u4E13\u6709\u590D\u6570\u540D\u8BCD",h.\u4E13\u6709\u96C6\u5408\u540D\u8BCD="\u4E13\u6709\u96C6\u5408\u540D\u8BCD",h.\u4ECB\u8BCD="\u4ECB\u8BCD",h.\u4EE3\u8BCD="\u4EE3\u8BCD",h.\u4F5C\u8005\u540D="\u4F5C\u8005\u540D",h.\u5173\u7CFB\u4EE3\u8BCD="\u5173\u7CFB\u4EE3\u8BCD",h.\u5206\u6570\u8BCD="\u5206\u6570\u8BCD",h.\u524D\u7F00="\u524D\u7F00",h.\u524D\u7F6E\u9650\u5B9A\u8BCD="\u524D\u7F6E\u9650\u5B9A\u8BCD",h.\u526F\u8BCD="\u526F\u8BCD",h.\u526F\u8BCD\u6700\u9AD8\u7EA7\u5F62\u5F0F="\u526F\u8BCD\u6700\u9AD8\u7EA7\u5F62\u5F0F",h.\u526F\u8BCD\u6BD4\u8F83\u7EA7\u5F62\u5F0F="\u526F\u8BCD\u6BD4\u8F83\u7EA7\u5F62\u5F0F",h.\u52A8\u8BCD="\u52A8\u8BCD",h.\u52A9\u52A8\u8BCD="\u52A9\u52A8\u8BCD",h.\u5355\u6570\u540D\u8BCD="\u5355\u6570\u540D\u8BCD",h.\u5355\u6570\u540D\u8BCD\u58F0\u97F3\u8BCD="\u5355\u6570\u540D\u8BCD\uFF1B\u58F0\u97F3\u8BCD",h.\u5355\u6570\u540D\u8BCD\u5934\u8854\u540D\u8BCD="\u5355\u6570\u540D\u8BCD\uFF1B\u5934\u8854\u540D\u8BCD",h.\u5355\u6570\u578B\u4EE3\u8BCD="\u5355\u6570\u578B\u4EE3\u8BCD",h.\u5355\u6570\u96C6\u5408\u540D\u8BCD="\u5355\u6570\u96C6\u5408\u540D\u8BCD",h.\u5355\u6570\u96C6\u5408\u540D\u8BCD\u4E13\u6709\u96C6\u5408\u540D\u8BCD="\u5355\u6570\u96C6\u5408\u540D\u8BCD\uFF1B\u4E13\u6709\u96C6\u5408\u540D\u8BCD",h.\u53CA\u7269\u4E0D\u53CA\u7269\u52A8\u8BCD="\u53CA\u7269/\u4E0D\u53CA\u7269\u52A8\u8BCD",h.\u53CD\u8EAB\u4EE3\u8BCD="\u53CD\u8EAB\u4EE3\u8BCD",h.\u53EF\u53D8\u540D\u8BCD="\u53EF\u53D8\u540D\u8BCD",h.\u53EF\u53D8\u540D\u8BCD\u540D\u79F0\u540D\u8BCD="\u53EF\u53D8\u540D\u8BCD\uFF1B\u540D\u79F0\u540D\u8BCD",h.\u53EF\u53D8\u96C6\u5408\u540D\u8BCD="\u53EF\u53D8\u96C6\u5408\u540D\u8BCD",h.\u53EF\u6570\u540D\u8BCD="\u53EF\u6570\u540D\u8BCD",h.\u53EF\u6570\u540D\u8BCD\u4E13\u6709\u540D\u8BCD="\u53EF\u6570\u540D\u8BCD\uFF1B\u4E13\u6709\u540D\u8BCD",h.\u53EF\u6570\u540D\u8BCD\u540D\u79F0\u540D\u8BCD="\u53EF\u6570\u540D\u8BCD\uFF1B\u540D\u79F0\u540D\u8BCD",h.\u53EF\u6570\u540D\u8BCD\u58F0\u97F3\u8BCD="\u53EF\u6570\u540D\u8BCD\uFF1B\u58F0\u97F3\u8BCD",h.\u53EF\u6570\u540D\u8BCD\u5934\u8854\u540D\u8BCD="\u53EF\u6570\u540D\u8BCD\uFF1B\u5934\u8854\u540D\u8BCD",h.\u53EF\u6570\u540D\u8BCD\u5934\u8854\u540D\u8BCD\u79F0\u547C\u540D\u8BCD="\u53EF\u6570\u540D\u8BCD\uFF1B\u5934\u8854\u540D\u8BCD\uFF1B\u79F0\u547C\u540D\u8BCD",h.\u53EF\u6570\u540D\u8BCD\u79F0\u547C\u540D\u8BCD="\u53EF\u6570\u540D\u8BCD\uFF1B\u79F0\u547C\u540D\u8BCD",h.\u53EF\u6570\u96C6\u5408\u540D\u8BCD="\u53EF\u6570\u96C6\u5408\u540D\u8BCD",h.\u53EF\u6570\u96C6\u5408\u540D\u8BCD\u540D\u79F0\u540D\u8BCD="\u53EF\u6570\u96C6\u5408\u540D\u8BCD\uFF1B\u540D\u79F0\u540D\u8BCD",h.\u540D\u79F0\u540D\u8BCD="\u540D\u79F0\u540D\u8BCD",h.\u540D\u79F0\u540D\u8BCD\u540D\u79F0\u540D\u8BCD="\u540D\u79F0\u540D\u8BCD\uFF1B\u540D\u79F0\u540D\u8BCD",h.\u540E\u7F00="\u540E\u7F00",h.\u5426\u5B9A\u4E0D\u5B9A\u4EE3\u8BCD="\u5426\u5B9A\u4E0D\u5B9A\u4EE3\u8BCD",h.\u5426\u5B9A\u4E0D\u5B9A\u526F\u8BCD="\u5426\u5B9A\u4E0D\u5B9A\u526F\u8BCD",h.\u5426\u5B9A\u526F\u8BCD="\u5426\u5B9A\u526F\u8BCD",h.\u5426\u5B9A\u8BCD="\u5426\u5B9A\u8BCD",h.\u5426\u5B9A\u9650\u5B9A\u8BCD="\u5426\u5B9A\u9650\u5B9A\u8BCD",h.\u58F0\u97F3\u8BCD="\u58F0\u97F3\u8BCD",h.\u590D\u6570\u540D\u8BCD="\u590D\u6570\u540D\u8BCD",h.\u590D\u6570\u540D\u8BCD\u540D\u79F0\u540D\u8BCD="\u590D\u6570\u540D\u8BCD\uFF1B\u540D\u79F0\u540D\u8BCD",h.\u590D\u6570\u578B\u4EE3\u8BCD="\u590D\u6570\u578B\u4EE3\u8BCD",h.\u590D\u6570\u6570\u91CF\u8BCD="\u590D\u6570\u6570\u91CF\u8BCD",h.\u5934\u8854\u540D\u8BCD="\u5934\u8854\u540D\u8BCD",h.\u5934\u8854\u540D\u8BCD\u53EF\u6570\u540D\u8BCD="\u5934\u8854\u540D\u8BCD\uFF1B\u53EF\u6570\u540D\u8BCD",h.\u5934\u8854\u540D\u8BCD\u53EF\u6570\u540D\u8BCD\u79F0\u547C\u540D\u8BCD="\u5934\u8854\u540D\u8BCD\uFF1B\u53EF\u6570\u540D\u8BCD\uFF1B\u79F0\u547C\u540D\u8BCD",h.\u5934\u8854\u540D\u8BCD\u79F0\u547C\u540D\u8BCD="\u5934\u8854\u540D\u8BCD\uFF1B\u79F0\u547C\u540D\u8BCD",h.\u5934\u8854\u540D\u8BCD\u79F0\u547C\u540D\u8BCD\u53EF\u6570\u540D\u8BCD="\u5934\u8854\u540D\u8BCD\uFF1B\u79F0\u547C\u540D\u8BCD\uFF1B\u53EF\u6570\u540D\u8BCD",h.\u5BB6\u5EAD\u6210\u5458\u540D\u8BCD="\u5BB6\u5EAD\u6210\u5458\u540D\u8BCD",h.\u5BB6\u5EAD\u6210\u5458\u540D\u8BCD\u5934\u8854\u540D\u8BCD="\u5BB6\u5EAD\u6210\u5458\u540D\u8BCD\uFF1B\u5934\u8854\u540D\u8BCD",h.\u5E7F\u4E49\u5426\u5B9A\u7ED3\u6784\u526F\u8BCD="\u5E7F\u4E49\u5426\u5B9A\u7ED3\u6784\u526F\u8BCD",h.\u5E8F\u6570\u8BCD="\u5E8F\u6570\u8BCD",h.\u5F3A\u8C03\u4EE3\u8BCD="\u5F3A\u8C03\u4EE3\u8BCD",h.\u5F3A\u8C03\u53CD\u8EAB\u4EE3\u8BCD="\u5F3A\u8C03\u53CD\u8EAB\u4EE3\u8BCD",h.\u5F62\u5BB9\u8BCD="\u5F62\u5BB9\u8BCD",h.\u5F62\u5BB9\u8BCD\u6700\u9AD8\u7EA7\u5F62\u5F0F="\u5F62\u5BB9\u8BCD\u6700\u9AD8\u7EA7\u5F62\u5F0F",h.\u5F62\u5BB9\u8BCD\u6BD4\u8F83\u7EA7\u5F62\u5F0F="\u5F62\u5BB9\u8BCD\u6BD4\u8F83\u7EA7\u5F62\u5F0F",h.\u5F62\u5BB9\u8BCD\u901A\u5E38\u7528\u4E8E\u540D\u8BCD\u524D="\u5F62\u5BB9\u8BCD  \u901A\u5E38\u7528\u4E8E\u540D\u8BCD\u524D",h.\u60C5\u6001\u52A8\u8BCD="\u60C5\u6001\u52A8\u8BCD",h.\u60C5\u6001\u52A8\u8BCD\u77ED\u8BED="\u60C5\u6001\u52A8\u8BCD\u77ED\u8BED",h.\u60EF\u7528\u8BED="\u60EF\u7528\u8BED",h.\u611F\u53F9\u8BCD\u4E0D\u53EF\u6570\u540D\u8BCD="\u611F\u53F9\u8BCD\uFF1B\u4E0D\u53EF\u6570\u540D\u8BCD",h.\u611F\u53F9\u8BED="\u611F\u53F9\u8BED",h.\u6240\u6709\u683C\u4EE3\u8BCD="\u6240\u6709\u683C\u4EE3\u8BCD",h.\u6240\u6709\u683C\u9650\u5B9A\u8BCD="\u6240\u6709\u683C\u9650\u5B9A\u8BCD",h.\u6570\u8BCD="\u6570\u8BCD",h.\u6570\u91CF\u8BCD="\u6570\u91CF\u8BCD",h.\u6784\u8BCD\u6210\u5206="\u6784\u8BCD\u6210\u5206",h.\u7269\u8D28\u540D\u8BCD="\u7269\u8D28\u540D\u8BCD",h.\u7591\u95EE\u8BCD="\u7591\u95EE\u8BCD",h.\u76F8\u4E92\u4EE3\u8BCD="\u76F8\u4E92\u4EE3\u8BCD",h.\u76F8\u4E92\u52A8\u8BCD="\u76F8\u4E92\u52A8\u8BCD",h.\u76F8\u4E92\u52A8\u8BCD\u4E60\u8BED="\u76F8\u4E92\u52A8\u8BCD\u4E60\u8BED",h.\u76F8\u4E92\u52A8\u8BCD\u77ED\u8BED="\u76F8\u4E92\u52A8\u8BCD\u77ED\u8BED",h.\u76F8\u4E92\u77ED\u8BED="\u76F8\u4E92\u77ED\u8BED",h.\u77ED\u8BED="\u77ED\u8BED",h.\u77ED\u8BED\u4ECB\u8BCD="\u77ED\u8BED\u4ECB\u8BCD",h.\u77ED\u8BED\u52A8\u8BCD="\u77ED\u8BED\u52A8\u8BCD",h.\u79F0\u547C\u540D\u8BCD="\u79F0\u547C\u540D\u8BCD",h.\u79F0\u547C\u540D\u8BCD\u4E13\u6709\u540D\u8BCD="\u79F0\u547C\u540D\u8BCD\uFF1B\u4E13\u6709\u540D\u8BCD",h.\u79F0\u547C\u540D\u8BCD\u5355\u6570\u540D\u8BCD="\u79F0\u547C\u540D\u8BCD\uFF1B\u5355\u6570\u540D\u8BCD",h.\u79F0\u547C\u540D\u8BCD\u53EF\u6570\u540D\u8BCD="\u79F0\u547C\u540D\u8BCD\uFF1B\u53EF\u6570\u540D\u8BCD",h.\u79F0\u547C\u540D\u8BCD\u590D\u6570\u540D\u8BCD="\u79F0\u547C\u540D\u8BCD\uFF1B\u590D\u6570\u540D\u8BCD",h.\u79F0\u547C\u540D\u8BCD\u5934\u8854\u540D\u8BCD\u53EF\u6570\u540D\u8BCD="\u79F0\u547C\u540D\u8BCD\uFF1B\u5934\u8854\u540D\u8BCD\uFF1B\u53EF\u6570\u540D\u8BCD",h.\u80FD\u88AB\u8868\u793A\u7A0B\u5EA6\u7684\u526F\u8BCD\u6216\u4ECB\u8BCD\u8BCD\u7EC4\u4FEE\u9970\u7684\u5F62\u5BB9\u8BCD="\u80FD\u88AB\u8868\u793A\u7A0B\u5EA6\u7684\u526F\u8BCD\u6216\u4ECB\u8BCD\u8BCD\u7EC4\u4FEE\u9970\u7684\u5F62\u5BB9\u8BCD",h.\u8868\u793A\u8BF4\u8BDD\u65F6\u7684\u8BED\u6C14\u6216\u611F\u60C5="\u8868\u793A\u8BF4\u8BDD\u65F6\u7684\u8BED\u6C14\u6216\u611F\u60C5",h.\u88AB\u52A8\u4EE3\u8BCD\u4E60\u8BED="\u88AB\u52A8\u4EE3\u8BCD\u4E60\u8BED",h.\u88AB\u52A8\u52A8\u8BCD="\u88AB\u52A8\u52A8\u8BCD",h.\u88AB\u52A8\u52A8\u8BCD\u77ED\u8BED="\u88AB\u52A8\u52A8\u8BCD\u77ED\u8BED",h.\u88AB\u52A8\u76F8\u4E92\u52A8\u8BCD="\u88AB\u52A8\u76F8\u4E92\u52A8\u8BCD",h.\u8AD6\u58C7="\u8AD6\u58C7",h.\u8FDE\u7CFB\u52A8\u8BCD="\u8FDE\u7CFB\u52A8\u8BCD",h.\u8FDE\u7CFB\u52A8\u8BCD\u53CA\u7269\u4E0D\u53CA\u7269="\u8FDE\u7CFB\u52A8\u8BCD\uFF08\u53CA\u7269/\u4E0D\u53CA\u7269\uFF09",h.\u8FDE\u7CFB\u52A8\u8BCD\u77ED\u8BED="\u8FDE\u7CFB\u52A8\u8BCD\u77ED\u8BED",h.\u8FDE\u8BCD="\u8FDE\u8BCD",h.\u9650\u5B9A\u8BCD="\u9650\u5B9A\u8BCD",h.\u989C\u8272\u8BCD="\u989C\u8272\u8BCD"})(nr||(nr={}));var ir;(function(d){d.ADJUsuADJN="ADJ usu ADJ n",d.ADVAsReply="ADV as reply",d.Adj="ADJ",d.AdjCompar="ADJ-COMPAR",d.AdjGraded="ADJ-GRADED",d.AdjSuperl="ADJ-SUPERL",d.AdjUngraded="ADJ-UNGRADED",d.Adv="ADV",d.AdvBrdNeg="ADV-BRD-NEG",d.AdvCompar="ADV-COMPAR",d.AdvGraded="ADV-GRADED",d.AdvIndef="ADV-INDEF",d.AdvIndefNeg="ADV-INDEF-NEG",d.AdvNeg="ADV-NEG",d.AdvSuperl="ADV-SUPERL",d.Author="AUTHOR",d.Aux="AUX",d.COMBInADJ="COMB in ADJ",d.COMBInADJAndN="COMB in ADJ and N",d.COMBInADJAndNCOUNT="COMB in ADJ and N-COUNT",d.COMBInADJGRADED="COMB in ADJ-GRADED",d.COMBInADV="COMB in ADV",d.COMBInCOLOUR="COMB in COLOUR",d.COMBInN="COMB in N",d.COMBInNCOUNT="COMB in N-COUNT",d.COMBInNCOUNTCOMBInADJ="COMB in N-COUNT, COMB in ADJ",d.COMBInNUNCOUNT="COMB in N-UNCOUNT",d.COMBInNUNCOUNTADJ="COMB in N-UNCOUNT, ADJ",d.COMBInQUANT="COMB in QUANT",d.COMBInVERB="COMB in VERB",d.Colour="COLOUR",d.Comb="COMB",d.ConjCoord="CONJ-COORD",d.ConjCoordNeg="CONJ-COORD-NEG",d.ConjSubord="CONJ-SUBORD",d.Convention="CONVENTION",d.Det="DET",d.DetNeg="DET-NEG",d.DetPoss="DET-POSS",d.Exclam="EXCLAM",d.ExclamNUncount="EXCLAM; N-UNCOUNT",d.Forum="FORUM",d.Fraction="FRACTION",d.Interj="INTERJ",d.Modal="MODAL",d.NCOUNTAlsoNINNAMES="N-COUNT; also N-IN-NAMES",d.NCOUNTAlsoNVOC="N-COUNT; also N-VOC",d.NCount="N-COUNT",d.NCountColl="N-COUNT-COLL",d.NCountCollNInNames="N-COUNT-COLL; N-IN-NAMES",d.NCountNInNames="N-COUNT; N-IN-NAMES",d.NCountNProper="N-COUNT; N-PROPER",d.NCountNTitle="N-COUNT; N-TITLE",d.NCountNTitleNVoc="N-COUNT; N-TITLE; N-VOC",d.NCountNVoc="N-COUNT; N-VOC",d.NCountSound="N-COUNT; SOUND",d.NFamily="N-FAMILY",d.NFamilyNTitle="N-FAMILY; N-TITLE",d.NInNames="N-IN-NAMES",d.NMass="N-MASS",d.NPROPERAlsoNCOUNT="N-PROPER; also N-COUNT",d.NPlural="N-PLURAL",d.NPluralNInNames="N-PLURAL; N-IN-NAMES",d.NProper="N-PROPER",d.NProperColl="N-PROPER-COLL",d.NProperNVoc="N-PROPER; N-VOC",d.NProperPlural="N-PROPER-PLURAL",d.NSing="N-SING",d.NSingColl="N-SING-COLL",d.NSingCollNProperColl="N-SING-COLL; N-PROPER-COLL",d.NSingNTitle="N-SING; N-TITLE",d.NSingSound="N-SING; SOUND",d.NTitle="N-TITLE",d.NTitleNCount="N-TITLE; N-COUNT",d.NTitleNCountNVoc="N-TITLE; N-COUNT; N-VOC",d.NTitleNVoc="N-TITLE; N-VOC",d.NTitleNVocNCount="N-TITLE; N-VOC; N-COUNT",d.NUNCOUNTAlsoEXCLAM="N-UNCOUNT, also EXCLAM",d.NUNCOUNTAlsoNPLURAL="N-UNCOUNT; also N-PLURAL",d.NUncount="N-UNCOUNT",d.NUncountColl="N-UNCOUNT-COLL",d.NUncountExclam="N-UNCOUNT; EXCLAM",d.NVARNum="N-VAR num",d.NVar="N-VAR",d.NVarColl="N-VAR-COLL",d.NVarNInNames="N-VAR; N-IN-NAMES",d.NVoc="N-VOC",d.NVocNCount="N-VOC; N-COUNT",d.NVocNPlural="N-VOC; N-PLURAL",d.NVocNProper="N-VOC; N-PROPER",d.NVocNSing="N-VOC; N-SING",d.NVocNTitleNCount="N-VOC; N-TITLE; N-COUNT",d.Neg="NEG",d.Num="NUM",d.Ord="ORD",d.PHRWithCl="PHR with cl",d.PhrConjCoord="PHR-CONJ-COORD",d.PhrConjSubord="PHR-CONJ-SUBORD",d.PhrErg="PHR-ERG",d.PhrModal="PHR-MODAL",d.PhrRecip="PHR-RECIP",d.PhrVLink="PHR-V-LINK",d.PhrVPassive="PHR-V-PASSIVE",d.PhrVRecip="PHR-V-RECIP",d.PhrasalVerb="PHRASAL VERB",d.PhrasalVerbErg="PHRASAL VERB-ERG",d.PhrasalVerbPassive="PHRASAL VERB-PASSIVE",d.PhrasalVerbRecip="PHRASAL VERB-RECIP",d.PhrasalVerbRecipErg="PHRASAL VERB-RECIP-ERG",d.Phrase="PHRASE",d.Predet="PREDET",d.Prefix="PREFIX",d.Prep="PREP",d.PrepPhrase="PREP-PHRASE",d.Pron="PRON",d.PronEmph="PRON-EMPH",d.PronIndef="PRON-INDEF",d.PronIndefNeg="PRON-INDEF-NEG",d.PronPlural="PRON-PLURAL",d.PronPoss="PRON-POSS",d.PronRecip="PRON-RECIP",d.PronRefl="PRON-REFL",d.PronReflEmph="PRON-REFL-EMPH",d.PronRel="PRON-REL",d.PronSing="PRON-SING",d.Quant="QUANT",d.QuantPlural="QUANT-PLURAL",d.Quest="QUEST",d.Sound="SOUND",d.Suffix="SUFFIX",d.ToInf="to inf",d.VERBNoPassive="VERB: no passive",d.VErg="V-ERG",d.VLink="V-LINK",d.VLinkErg="V-LINK-ERG",d.VLinkWorthAmount="v-link worth amount",d.VLinkWorthIng="v-link worth -ing",d.VLinkWorthNIng="v-link worth n/-ing",d.VPassive="V-PASSIVE",d.VPhr="V PHR",d.VRecip="V-RECIP",d.VRecipErg="V-RECIP-ERG",d.VRecipPassive="V-RECIP-PASSIVE",d.Verb="VERB"})(ir||(ir={}));var Ft=class{constructor(){this.API_END_POINT="https://api.dictionaryapi.dev/api/v2/entries/";this.name="Free Dictionary API";this.url="https://dictionaryapi.dev/";this.offline=!1;this.languageCodes={en_US:"en_US",hi:"hi",es:"es",fr:"fr",ja:"ja",ru:"ru",en_GB:"en_GB",de:"de",it:"it",ko:"ko",pt_BR:"pt-BR",ar:"ar",tr:"tr"}}constructRequest(e,t){return this.API_END_POINT+t+"/"+e}},kt=class extends Ft{constructor(){super(...arguments);this.supportedLanguages=["en_US","en_GB"]}requestDefinitions(e,t,i=!0){return M(this,null,function*(){let r;try{let a=this.constructRequest(encodeURIComponent(e),this.languageCodes[t]);r=yield(0,Et.request)({url:a})}catch(a){return Promise.reject(a)}let o=yield JSON.parse(r);return!o||o.title?Promise.reject(o.title):o.first()})}},Lt=class extends Ft{constructor(){super(...arguments);this.supportedLanguages=["en_US"]}getDoesPosMatch(e,t){switch(t){case je.Noun:return e.partOfSpeech.toLowerCase().contains("noun");case je.Verb:return e.partOfSpeech.toLowerCase().contains("verb");case je.Adjective:return e.partOfSpeech.toLowerCase().contains("adjective");case je.Adverb:return e.partOfSpeech.toLowerCase().contains("adverb")}}requestSynonyms(e,t,i){return M(this,null,function*(){let r;try{r=yield(0,Et.request)({url:this.constructRequest(e,this.languageCodes[t])})}catch(l){return Promise.reject(l)}if(!r)return Promise.reject("Word doesnt exist in this Dictionary");let o=(yield JSON.parse(r)).first().meanings,a=[],s=[];return o.forEach(l=>{if(Number.isNumber(i)&&!this.getDoesPosMatch(l,i)){l.definitions.forEach(c=>{c.synonyms&&c.synonyms.forEach(u=>{s.push({word:u})})});return}l.definitions.forEach(c=>{c.synonyms&&c.synonyms.forEach(u=>{a.push({word:u})})})}),a.concat(s)})}};var rr=G(require("obsidian")),Tt=class{constructor(){this.API_END_POINT="https://www.openthesaurus.de/synonyme/search?q=";this.name="OpenThesaurus";this.url="https://www.openthesaurus.de/";this.supportedLanguages=["de"];this.offline=!1}constructRequest(e){return this.API_END_POINT+e+"&format=application/json"}requestSynonyms(e){return M(this,null,function*(){let t;try{t=yield(0,rr.request)({url:this.constructRequest(e)})}catch(a){return Promise.reject(a)}if(!t)return Promise.reject("Word doesnt exist in this Dictionary");let i=yield JSON.parse(t);if(!i.synsets)return Promise.reject("Word doesnt exist in this Dictionary");if(i.synsets.length<=0)return Promise.reject("No Synonym found");let r=i.synsets[0].terms,o=[];return r.forEach(a=>{let s=a.term;e!=s&&o.push({word:s})}),o})}};var or=G(require("obsidian")),_t=class{constructor(){this.name="Altervista";this.url="http://thesaurus.altervista.org/";this.offline=!1;this.supportedLanguages=["es","it","fr","de"];this.languageCodes={es:"es_ES",it:"it_IT",fr:"fr_FR",de:"de_DE"};this.TOKEN="P4QAmqYIN1DY6XjlQJht"}requestSynonyms(e,t,i){return M(this,null,function*(){let r=[],o;try{o=yield(0,or.request)({url:this.constructRequest(encodeURIComponent(e),t)})}catch(s){return Promise.reject(s)}if(!o)return Promise.reject("Word doesnt exist in this Dictionary");let a=yield JSON.parse(o);for(let s of a.response)s.list.synonyms.split("|").forEach(c=>{r.push({word:c})});return r})}constructRequest(e,t){return`http://thesaurus.altervista.org/thesaurus/v1?word=${e}&key=${this.TOKEN}&language=${this.languageCodes[t]}&output=json`}};var ar=G(require("obsidian")),ct=class{constructor(){this.name="Google";this.offline=!1;this.supportedLanguages=["en_US","de","es","fr"]}};ct.LANGUAGES={en_US:"english",de:"deutsch",es:"Espa\xF1ol",fr:"Fran\xE7ais"};var Le=class extends ct{requestDefinitions(e,t){return M(this,null,function*(){var c,u,f,p,y,g;let i=yield(0,ar.requestUrl)({url:`https://www.google.com/search?q=define+${e.replace(/\s/g,"+")}+${Le.LANGUAGES[t]}`,headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36"}});console.log(i);let o=new DOMParser().parseFromString(i.text,"text/html").querySelector(`div[data-query-term=${e}]`);if(!o)throw"";let a={phonetics:[],meanings:[],word:(u=(c=o.querySelector('span[data-dobid="hdw"]'))==null?void 0:c.textContent)!=null?u:e},s=(f=o.querySelector(".LTKOO > span"))==null?void 0:f.textContent;s&&a.phonetics.push({text:s,audio:(y=(p=o.querySelector("audio > source"))==null?void 0:p.getAttribute("src"))!=null?y:void 0});let l=(g=o.querySelector(".vmod i"))==null?void 0:g.textContent;if(l){let v=m=>{let x=[],C=[];return o.querySelectorAll('.lr_container div[role="button"] span').forEach(D=>{var j;!((j=D.parentElement)==null?void 0:j.getAttribute("data-topic"))&&D.textContent&&C.push(D.textContent.trim())}),m.forEach((D,j)=>{var k;x.push({definition:D.textContent,example:(k=D.nextSibling)==null?void 0:k.textContent,synonyms:j?void 0:C})}),x};a.meanings.push({partOfSpeech:l,definitions:v(o.querySelectorAll('div[data-dobid="dfn"]'))})}return a})}},zt=class extends ct{constructor(){super();this.provider=new Le}requestSynonyms(e,t,i){return M(this,null,function*(){return(yield this.provider.requestDefinitions(e,t)).meanings.first().definitions.first().synonyms.map(r=>({word:r}))})}};var Vt=class{constructor(){this.name="Jisho";this.base_url="https://jisho.org/api/v1/search/words?keyword=";this.API_END_POINT="https://jisho.org/api/v1/search/words?keyword=";this.offline=!1;this.supportedLanguages=["ja"]}};Vt.LANGUAGES={ja:"Japanese"};var Rt=class extends Vt{constructor(){super(...arguments);this.requestDefinitions=(e,t)=>M(this,null,function*(){let r=(yield requestUrl({url:`${this.base_url}/search/words?keyword=${e}`})).json;console.log(r);let a=r.data.first();return{word:a.japanese[0].word,meanings:a.senses.map(l=>({partOfSpeech:l.parts_of_speech.join(`
`),definitions:l.english_definitions.map(c=>({definition:c}))})),phonetics:[...new Set(a.japanese.map(l=>`${l.word} \u300C${l.reading}\u300D`))].map(l=>({text:l}))}})}};var ut=class{constructor(e){this.definitionProvider=[new kt,new Nt(this),new Le,new Rt];this.synonymProvider=[new Lt,new Tt,new _t,new zt];this.partOfSpeechProvider=[];this.plugin=e}requestDefinitions(e){return M(this,null,function*(){let t=this.getDefinitionAPI(),{cache:i,settings:r}=this.plugin;if(r.useCaching&&!t.name.toLowerCase().contains("offline")){let o=i.cachedDefinitions.find(a=>a.content.word.toLowerCase()==e.toLowerCase()&&a.lang==r.defaultLanguage&&a.api==t.name);if(o)return new Promise(a=>a(o.content));{let a=t.requestDefinitions(e,r.defaultLanguage),s=yield a;return s&&(i.cachedDefinitions.push({content:s,api:t.name,lang:r.defaultLanguage}),yield this.plugin.saveCache()),a}}else return t.requestDefinitions(e,this.plugin.settings.defaultLanguage)})}requestSynonyms(e,t){return M(this,null,function*(){let i=this.getSynonymAPI();if(!i)throw"No Synonym API selected/available";let{cache:r,settings:o}=this.plugin;if(o.useCaching&&!i.name.toLowerCase().contains("offline")){let a=r.cachedSynonyms.find(s=>s.word.toLowerCase()==e.toLowerCase()&&s.lang==o.defaultLanguage&&s.api==i.name);if(a)return new Promise(s=>s(a.content));{let s=i.requestSynonyms(e,o.defaultLanguage),l=yield s;return l&&(r.cachedSynonyms.push({content:l,api:i.name,word:e,lang:o.defaultLanguage}),yield this.plugin.saveCache()),s}}else return i.requestSynonyms(e,this.plugin.settings.defaultLanguage,t)})}requestPartOfSpeech(e,t,i){var r;return(r=this.getPartOfSpeechAPI())==null?void 0:r.requestPartOfSpeech(e,t,i,this.plugin.settings.defaultLanguage)}getDefinitionAPI(){let e=this.plugin.settings.defaultLanguage;return this.definitionProvider.find(t=>t.name==this.plugin.settings.apiSettings[e].definitionApiName)}getSynonymAPI(){let e=this.plugin.settings.defaultLanguage;return this.synonymProvider.find(t=>t.name==this.plugin.settings.apiSettings[e].synonymApiName)}getPartOfSpeechAPI(){return this.plugin.settings.advancedSynonymAnalysis?this.partOfSpeechProvider.find(e=>e.name==this.plugin.settings.partOfSpeechApiName):null}};function Do(n){X(n,"svelte-1j3cg25",".dict-s-popover.svelte-1j3cg25.svelte-1j3cg25{min-width:210px;max-width:250px;max-height:200px;background-color:var(--background-primary);border:1px solid var(--background-modifier-border);position:absolute;z-index:var(--layer-popover);border-radius:5px;box-shadow:0px 15px 25px rgba(0, 0, 0, 0.2);font-size:14px;overflow-y:auto;overflow-x:hidden;line-height:1.4}.dict-s-popover__select-option.svelte-1j3cg25.svelte-1j3cg25{cursor:pointer;padding:10px;border-bottom:1px solid var(--background-modifier-border)}.dict-s-popover__select-option.svelte-1j3cg25.svelte-1j3cg25:hover{background-color:var(--background-secondary)}.dict-s-popover.svelte-1j3cg25>.dict-s-popover__select-option.svelte-1j3cg25:last-child{border-bottom:none}.dict-s-popover__select-label.svelte-1j3cg25.svelte-1j3cg25{display:flex;justify-content:space-between;align-items:center}.dict-s-popover__meta-description.svelte-1j3cg25.svelte-1j3cg25,.dict-s-popover__meta-pos.svelte-1j3cg25.svelte-1j3cg25{font-size:12px;color:var(--text-muted)}.dict-s-popover__meta-pos.svelte-1j3cg25.svelte-1j3cg25{display:inline-block;margin-left:10px}")}function sr(n,e,t){let i=n.slice();return i[6]=e[t],i}function lr(n){let e,t=n[6].partsOfSpeech.join(", ")+"",i;return{c(){e=A("div"),i=R(t),b(e,"class","dict-s-popover__meta-pos svelte-1j3cg25")},m(r,o){N(r,e,o),S(e,i)},p(r,o){o&2&&t!==(t=r[6].partsOfSpeech.join(", ")+"")&&W(i,t)},d(r){r&&P(e)}}}function cr(n){let e,t=n[6].description+"",i;return{c(){e=A("div"),i=R(t),b(e,"class","dict-s-popover__meta-description svelte-1j3cg25")},m(r,o){N(r,e,o),S(e,i)},p(r,o){o&2&&t!==(t=r[6].description+"")&&W(i,t)},d(r){r&&P(e)}}}function ur(n){var g;let e,t,i,r=n[6].word+"",o,a,s,l,c,u,f=!!((g=n[6].partsOfSpeech)==null?void 0:g.length)&&lr(n),p=n[6].description&&cr(n);function y(){return n[5](n[6])}return{c(){e=A("div"),t=A("div"),i=A("div"),o=R(r),a=F(),f&&f.c(),s=F(),p&&p.c(),l=F(),b(i,"class","dict-s-popover__term"),b(t,"class","dict-s-popover__select-label svelte-1j3cg25"),b(e,"class","dict-s-popover__select-option svelte-1j3cg25")},m(v,m){N(v,e,m),S(e,t),S(t,i),S(i,o),S(t,a),f&&f.m(t,null),S(e,s),p&&p.m(e,null),S(e,l),c||(u=Y(e,"click",y),c=!0)},p(v,m){var x;n=v,m&2&&r!==(r=n[6].word+"")&&W(o,r),((x=n[6].partsOfSpeech)==null?void 0:x.length)?f?f.p(n,m):(f=lr(n),f.c(),f.m(t,null)):f&&(f.d(1),f=null),n[6].description?p?p.p(n,m):(p=cr(n),p.c(),p.m(e,l)):p&&(p.d(1),p=null)},d(v){v&&P(e),f&&f.d(),p&&p.d(),c=!1,u()}}}function Io(n){let e,t,i,r,o,a=n[1],s=[];for(let l=0;l<a.length;l+=1)s[l]=ur(sr(n,a,l));return{c(){e=A("div");for(let l=0;l<s.length;l+=1)s[l].c();ze(e,"left",n[0].left+"px"),ze(e,"top",n[0].bottom+"px"),b(e,"class","dict-s-popover svelte-1j3cg25")},m(l,c){N(l,e,c);for(let u=0;u<s.length;u+=1)s[u].m(e,null);r||(o=bn(t=n[3].call(null,e)),r=!0)},p(l,[c]){if(c&6){a=l[1];let u;for(u=0;u<a.length;u+=1){let f=sr(l,a,u);s[u]?s[u].p(f,c):(s[u]=ur(f),s[u].c(),s[u].m(e,null))}for(;u<s.length;u+=1)s[u].d(1);s.length=a.length}c&1&&ze(e,"left",l[0].left+"px"),c&1&&ze(e,"top",l[0].bottom+"px")},i(l){i||oe(()=>{i=En(e,mi,{duration:50}),i.start()})},o:T,d(l){l&&P(e),ee(s,l),r=!1,o()}}}function Mo(n,e,t){let{coords:i}=e,{synonyms:r}=e,{onSelect:o}=e,{onClickOutside:a}=e;function s(c){let u=c.clientHeight,f=c.clientWidth;i.bottom+u>window.innerHeight&&c.style.setProperty("top",`${i.top-u}px`),i.left+f>window.innerWidth&&c.style.setProperty("left",`${window.innerWidth-f-15}px`);function p(y){c.contains(y.target)||(document.body.removeEventListener("pointerup",p),a())}document.body.addEventListener("pointerup",p)}let l=c=>o(c.word);return n.$$set=c=>{"coords"in c&&t(0,i=c.coords),"synonyms"in c&&t(1,r=c.synonyms),"onSelect"in c&&t(2,o=c.onSelect),"onClickOutside"in c&&t(4,a=c.onClickOutside)},[i,r,o,s,a,l]}var dr=class extends K{constructor(e){super();Z(this,e,Mo,Io,Q,{coords:0,synonyms:1,onSelect:2,onClickOutside:4},Do)}},fr=dr;var Ut=class{constructor(e){this.isDestroyed=!1;this.settings=e,this.openSynonymPopover()}destroy(){var e;(e=this._view)==null||e.$destroy(),this.isDestroyed=!0}openSynonymPopover(){return M(this,null,function*(){let{cursor:e,coords:t,line:i,selection:r,apiManager:o,onSelect:a}=this.settings,s=i.split(/[.!?]/g),l=0;for(let c of s){if(l<=e.ch&&e.ch<=l+c.length){let u=c.substring(0,e.ch-l),f=c.substring(e.ch-l+r.length),p;if(this.settings.advancedPoS)try{p=yield o.requestPartOfSpeech(r,u,f)}catch(g){console.error(`Error determining part of speech for word ${r}`,g)}let y;if(this.isDestroyed)return;try{y=yield o.requestSynonyms(r,p)}catch(g){console.error(`Error requesting synonyms for word ${r}`,g)}if(this.isDestroyed||!(y==null?void 0:y.length))return;this._view=new fr({intro:!0,target:document.body,props:{coords:t,synonyms:y,onSelect:g=>{a(g),this.destroy()},onClickOutside:()=>{this.destroy()}}});break}l+=c.length+1}})}};function Ht(n,e,t){if(!t.settings.shouldShowCustomContextMenu)return;let i=e.getSelection();i&&i.trim().split(" ").length===1&&(t.settings.shouldShowSynonymPopover||n.addItem(r=>{r.setTitle(w("Show Synonyms")).setIcon("synonyms").onClick(o=>M(this,null,function*(){t.handlePointerUp()}))}),n.addItem(r=>{r.setTitle(w("Look up")).setIcon("quote-glyph").onClick(o=>M(this,null,function*(){let a=t.app.workspace.getLeavesOfType(ne).first();a||(a=t.app.workspace.getRightLeaf(!1),yield a.setViewState({type:ne})),a.view.query(i.trim()),t.app.workspace.revealLeaf(a)}))}))}var pr=G(require("obsidian")),hr={copy:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" fill-opacity="0.0" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-copy"><rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect><path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path></svg>',synonyms:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" fill-opacity="0.0" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-book-open"><path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path><path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path></svg>'},yr=()=>{Object.keys(hr).forEach(n=>{(0,pr.addIcon)(n,hr[n])})};var he=G(require("obsidian")),dt=class{constructor(e){this.plugin=e,this.settings=e.settings}cap(e){return e&&e.split(" ").map(i=>i[0].toUpperCase()+i.substring(1)).join(" ")}newNote(e,t=!0){return M(this,null,function*(){let{plugin:i,settings:r}=this,o="";e.phonetics.forEach((p,y,g)=>{p.audio&&(o+="- "+(p.audio.startsWith("http")?p.audio:"https:"+p.audio),y!=g.length-1&&(o+=`
`))});let a="";e.phonetics.forEach((p,y,g)=>{p.text&&(a+="- "+(p.audio?`<details><summary>${p.text}</summary><audio controls><source src="${p.audio.startsWith("http")?p.audio:"https:"+p.audio}"></audio></details>`:p.text),y!=g.length-1&&(a+=`
`))});let s="";e.meanings.forEach((p,y)=>{var g;s+="### "+this.cap((g=p.partOfSpeech)!=null?g:w("Meaning {{i}}").replace(/{{i}}/g,(y+1).toString()))+`

`,p.definitions.forEach((v,m,x)=>{s+=v.definition+`

`,v.example&&(s+="> "+v.example+`

`),v.synonyms&&v.synonyms.length!=0&&(v.synonyms.forEach((C,E,D)=>{s+=C,E!=D.length-1&&(s+=", ")}),s+=`

`),m!=x.length-1&&(s+=`---

`)})});let l,c=Ie[r.defaultLanguage],u=`${r.folder?r.folder+"/":""}${r.languageSpecificSubFolders?c+"/":""}${r.prefix.replace(/{{lang}}/ig,c)}${r.capitalizedFileName?this.cap(e.word):e.word}${r.suffix.replace(/{{lang}}/ig,c)}.md`,f=r.template.replace(/{{notice}}/ig,w("Autogenerated by Obsidian Dictionary Plugin")).replace(/{{word}}/ig,r.capitalizedFileName?this.cap(e.word):e.word).replace(/{{pronunciationheader}}/ig,w("Pronunciation")).replace(/{{phoneticlist}}/ig,a).replace(/{{meaningheader}}/ig,w("Meanings")).replace(/{{meanings}}/ig,s).replace(/{{lang}}/ig,c).replace(/{{audioLinks}}/ig,o);e.origin?f=f.replace(/{{originHeader}}/ig,w("Origin")).replace(/{{origin}}/ig,e.origin):f=f.replace(/{{originHeader}}/ig,"").replace(/{{origin}}/ig,"");try{if((yield i.app.vault.adapter.exists((0,he.normalizePath)(`${r.folder?r.folder+"/":""}${r.languageSpecificSubFolders?c+"/":""}`)))||(yield i.app.vault.createFolder((0,he.normalizePath)(`${r.folder?r.folder+"/":""}${r.languageSpecificSubFolders?c+"/":""}`))),l=yield i.app.vault.create((0,he.normalizePath)(u),f),t){let p=i.app.workspace.splitActiveLeaf();yield p.openFile(l),i.app.workspace.setActiveLeaf(p)}}catch(p){new mr(this.plugin,(0,he.normalizePath)(u),f,t).open()}})}},mr=class extends he.Modal{constructor(e,t,i,r){super(e.app);this.path=t,this.content=i,this.openNote=r}onOpen(){this.contentEl.appendChild(createEl("p",{text:w("A existing File with the same Name was found, do you want to overwrite it?"),cls:"dictionarycenter"}));let e=this.contentEl.appendChild(createDiv({cls:"dictionarybuttons"}));e.appendChild(createEl("button",{text:w("Yes, overwrite the old File."),cls:"mod-cta"})).onClickEvent(()=>M(this,null,function*(){this.app.vault.modify(this.app.vault.getAbstractFileByPath(this.path),this.content);let t=!1;if(this.app.workspace.iterateAllLeaves(i=>{i.view instanceof he.MarkdownView&&i.getViewState().state.file.endsWith(this.path)&&(t=!0,this.app.workspace.setActiveLeaf(i))}),!t&&this.openNote){let i=this.app.workspace.splitActiveLeaf();yield i.openFile(this.app.vault.getAbstractFileByPath(this.path)),this.app.workspace.setActiveLeaf(i)}this.close()})),e.appendChild(createEl("button",{text:w("No, keep the old File."),cls:"mod-cta"})).onClickEvent(()=>{this.close()})}};var gr=G(Ot()),Bt=class extends le.Plugin{constructor(){super(...arguments);this.synonymPopover=null;this.handleContextMenuHelper=(e,t,i)=>{Ht(e,t,this)};this.handlePointerUp=(0,le.debounce)(()=>{var t,i,r;let e=this.app.workspace.activeLeaf;if((e==null?void 0:e.view)instanceof le.MarkdownView){let o=e.view;if(o.getMode()==="source"){let a=o.editor,s=a.getSelection();if(!s||/\s/.test(s))return;let l=a.getCursor("from"),c=a.getLine(l.line),u;if(a.cursorCoords)u=a.cursorCoords(!0,"window");else if(a.coordsAtPos){let f=a.posToOffset(l);u=(r=(i=(t=a.cm).coordsAtPos)==null?void 0:i.call(t,f))!=null?r:a.coordsAtPos(f)}else return;this.synonymPopover=new Ut({apiManager:this.manager,advancedPoS:this.settings.advancedSynonymAnalysis,coords:u,cursor:l,line:c,selection:s,onSelect:f=>{a.replaceSelection(Gt(f,s))}})}}},300,!0)}onload(){return M(this,null,function*(){console.log("loading dictionary"),yield Promise.all([this.loadSettings(),this.loadCache()]),yr(),this.addSettingTab(new tt(this.app,this)),this.manager=new ut(this),this.registerView(ne,e=>new st(e,this)),this.addCommand({id:"dictionary-open-view",name:w("Open Dictionary View"),callback:()=>M(this,null,function*(){this.app.workspace.getLeavesOfType(ne).length==0&&(yield this.app.workspace.getRightLeaf(!1).setViewState({type:ne})),this.app.workspace.revealLeaf(this.app.workspace.getLeavesOfType(ne).first()),dispatchEvent(new Event("dictionary-focus-on-search"))})}),this.addCommand({id:"dictionary-open-language-switcher",name:w("Open Language Switcher"),callback:()=>{new ke(this.app,this).open()}}),this.registerDomEvent(document.body,"pointerup",()=>{!this.settings.shouldShowSynonymPopover||this.handlePointerUp()}),this.registerDomEvent(window,"keydown",()=>{this.synonymPopover&&(this.synonymPopover.destroy(),this.synonymPopover=null)}),this.registerDomEvent(document.body,"contextmenu",e=>{var t,i;if(this.settings.shouldShowCustomContextMenu&&e.path.find(r=>{try{return r.hasClass("markdown-preview-view")}catch(o){return!1}})){let r=window.getSelection().toString();if(r&&((i=(t=this.app.workspace.activeLeaf)==null?void 0:t.getViewState())==null?void 0:i.state.mode)==="preview"){e.preventDefault();let o=new le.Menu(this.app);o.addItem(a=>{a.setTitle(w("Copy")).setIcon("copy").onClick(s=>{(0,gr.copy)(r)})}),r.trim().split(" ").length===1&&o.addItem(a=>{a.setTitle(w("Look up")).setIcon("quote-glyph").onClick(s=>M(this,null,function*(){let l=this.app.workspace.getLeavesOfType(ne).first();l||(l=this.app.workspace.getRightLeaf(!1),yield l.setViewState({type:ne})),l.view.query(r.trim()),this.app.workspace.revealLeaf(l)}))}),o.showAtPosition({x:e.clientX,y:e.clientY})}}}),this.localDictionary=new dt(this),this.registerEvent(this.app.workspace.on("editor-menu",this.handleContextMenuHelper)),this.registerEvent(this.app.workspace.on("file-open",e=>{var t,i,r,o;if(e&&this.settings.getLangFromFile){let a=(i=(t=this.app.metadataCache.getFileCache(e).frontmatter)==null?void 0:t.lang)!=null?i:null;a||(a=(o=(r=this.app.metadataCache.getFileCache(e).frontmatter)==null?void 0:r.language)!=null?o:null),a&&Object.values(Ie).contains(a)?this.settings.defaultLanguage=Object.keys(Ie)[Object.values(Ie).indexOf(a)]:this.settings.defaultLanguage=this.settings.normalLang,this.saveSettings()}}))})}onunload(){console.log("unloading dictionary")}loadSettings(){return M(this,null,function*(){this.settings=Object.assign({},Me,yield this.loadData())})}loadCache(){return M(this,null,function*(){this.cache=Object.assign({},vn,yield this.loadCacheFromDisk())})}loadCacheFromDisk(){return M(this,null,function*(){let e=(0,le.normalizePath)(`${this.manifest.dir}/cache.json`);return(yield this.app.vault.adapter.exists(e))||(yield this.app.vault.adapter.write(e,"{}")),JSON.parse(yield this.app.vault.adapter.read(e))})}saveCache(){return M(this,null,function*(){yield this.app.vault.adapter.write((0,le.normalizePath)(`${this.manifest.dir}/cache.json`),JSON.stringify(this.cache))})}saveSettings(){return M(this,null,function*(){yield this.saveData(this.settings)})}};
/*!
  Copyright (c) 2016 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/

/* nosourcemap */
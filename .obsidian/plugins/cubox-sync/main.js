/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var Kt=Object.defineProperty;var pn=Object.getOwnPropertyDescriptor;var yn=Object.getOwnPropertyNames;var Tn=Object.prototype.hasOwnProperty;var En=(s,e)=>{for(var t in e)Kt(s,t,{get:e[t],enumerable:!0})},wn=(s,e,t,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of yn(e))!Tn.call(s,n)&&n!==t&&Kt(s,n,{get:()=>e[n],enumerable:!(r=pn(e,n))||r.enumerable});return s};var Sn=s=>wn(Kt({},"__esModule",{value:!0}),s);var fa={};En(fa,{default:()=>jt});module.exports=Sn(fa);var H=require("obsidian");var Ps=require("obsidian");var ye=require("obsidian"),W="all_folders",vt=class extends ye.Modal{constructor(t,r,n,i){super(t);this.selectedFolders=new Set;this.folders=[];this.folders=r,n&&n.length>0&&n.forEach(a=>{a&&this.selectedFolders.add(a)}),this.onConfirm=i}async onOpen(){let{contentEl:t}=this;t.createEl("h2",{text:"Manage Cubox folders to be synced"}),t.addClass("cubox-modal"),this.listEl=t.createDiv({cls:"folder-list-container cubox-list-container"}),this.footerEl=t.createDiv({cls:"modal-footer"}),this.createFolderList(),this.footerEl.createEl("button",{text:"Cancel"}).addEventListener("click",()=>{this.close()}),this.footerEl.createEl("button",{text:"Done",cls:"mod-cta"}).addEventListener("click",()=>{if(this.selectedFolders.size===0){new ye.Notice("Please select at least one option.");return}let i=this.selectedFolders.has(W)?[W]:Array.from(this.selectedFolders);this.onConfirm(i),this.close()})}createFolderList(){this.listEl.empty();let t=new ye.Setting(this.listEl).setName("All items");this.selectedFolders.has(W)&&t.settingEl.addClass("is-selected"),t.settingEl.addEventListener("click",()=>{let r=this.selectedFolders.has(W);this.handleFolderToggle(W,!r),this.redraw()}),this.folders.forEach(r=>{let n=new ye.Setting(this.listEl).setName(r.nested_name);this.selectedFolders.has(W)?n.settingEl.addClass("is-disabled"):(this.selectedFolders.has(r.id)&&n.settingEl.addClass("is-selected"),n.settingEl.addEventListener("click",()=>{let i=this.selectedFolders.has(r.id);this.handleFolderToggle(r.id,!i),this.redraw()}))})}isFolderSelected(t){return this.selectedFolders.has(t)}handleFolderToggle(t,r){t===W?r?(this.selectedFolders.clear(),this.selectedFolders.add(W)):this.selectedFolders.delete(W):r?(this.selectedFolders.delete(W),this.selectedFolders.add(t)):this.selectedFolders.delete(t)}redraw(){this.createFolderList()}onClose(){let{contentEl:t}=this;t.empty()}};var le=require("obsidian"),A="all_items",Qt="",Ft=class extends le.Modal{constructor(t,r,n,i){super(t);this.selectedTags=new Set;this.tags=[];this.tags=r,n&&n.length>0&&n.forEach(a=>{this.selectedTags.add(a)}),this.onConfirm=i}async onOpen(){let{contentEl:t}=this;t.createEl("h2",{text:"Manage Cubox tags to be synced"}),t.addClass("cubox-modal"),this.listEl=t.createDiv({cls:"tag-list-container cubox-list-container"}),this.footerEl=t.createDiv({cls:"modal-footer"}),this.createTagList(),this.footerEl.createEl("button",{text:"Cancel"}).addEventListener("click",()=>{this.close()}),this.footerEl.createEl("button",{text:"Done",cls:"mod-cta"}).addEventListener("click",()=>{if(this.selectedTags.size===0){new le.Notice("Please select at least one option.");return}let i=this.selectedTags.has(A)?[A]:Array.from(this.selectedTags);this.onConfirm(i),this.close()})}createTagList(){this.listEl.empty();let t=new le.Setting(this.listEl).setName("All items");this.selectedTags.has(A)&&t.settingEl.addClass("is-selected"),t.settingEl.addEventListener("click",()=>{let n=this.selectedTags.has(A);this.handleTagToggle(A,!n),this.redraw()});let r=new le.Setting(this.listEl).setName("No tags");this.selectedTags.has(A)?r.settingEl.addClass("is-disabled"):(this.selectedTags.has(Qt)&&r.settingEl.addClass("is-selected"),r.settingEl.addEventListener("click",()=>{let n=this.selectedTags.has(Qt);this.handleTagToggle(Qt,!n),this.redraw()})),this.tags.forEach(n=>{let i=new le.Setting(this.listEl).setName(n.nested_name);this.selectedTags.has(A)?i.settingEl.addClass("is-disabled"):(this.selectedTags.has(n.id)&&i.settingEl.addClass("is-selected"),i.settingEl.addEventListener("click",()=>{let a=this.selectedTags.has(n.id);this.handleTagToggle(n.id,!a),this.redraw()}))})}isTagSelected(t){return this.selectedTags.has(t)}handleTagToggle(t,r){t===A?r?(this.selectedTags.clear(),this.selectedTags.add(A)):this.selectedTags.delete(A):r?(this.selectedTags.delete(A),this.selectedTags.add(t)):this.selectedTags.delete(t)}redraw(){this.createTagList()}onClose(){let{contentEl:t}=this;t.empty()}};var ve=require("obsidian"),ue="all",kt=class extends ve.Modal{constructor(t,r,n={},i){super(t);this.statuses=[{id:"all",name:"All items"},{id:"read",name:"Already read items only",value:!0},{id:"starred",name:"Starred items only",value:!0},{id:"annotated",name:"Annotated items only",value:!0}];this.selectedStatuses=new Set;this.statusValues=new Map;this.onSave=i,this.statuses.forEach(a=>{if(a.id!=="all"){let o=n[a.id]!==void 0?n[a.id]:a.value||!0;this.statusValues.set(a.id,o)}}),r&&r.length>0&&r.forEach(a=>{a&&this.selectedStatuses.add(a)})}async onOpen(){let{contentEl:t}=this;t.createEl("h2",{text:"Manage Cubox content status to be synced"}),t.addClass("cubox-modal"),this.listEl=t.createDiv({cls:"status-list-container cubox-list-container"}),this.footerEl=t.createDiv({cls:"modal-footer"}),this.createStatusList(),this.footerEl.createEl("button",{text:"Cancel"}).addEventListener("click",()=>{this.close()}),this.footerEl.createEl("button",{text:"Done",cls:"mod-cta"}).addEventListener("click",()=>{if(this.selectedStatuses.size===0){new ve.Notice("Please select at least one option.");return}let i=Array.from(this.selectedStatuses),a={};this.statusValues.forEach((o,l)=>{a[l]=o}),this.onSave(i,a),this.close()})}createStatusList(){this.listEl.empty(),this.statuses.forEach(t=>{let r=new ve.Setting(this.listEl).setName(t.name);t.id===ue?(this.selectedStatuses.has(t.id)&&r.settingEl.addClass("is-selected"),r.settingEl.addEventListener("click",()=>{let n=this.selectedStatuses.has(t.id);this.handleStatusToggle(t.id,!n),this.redraw()})):this.selectedStatuses.has(ue)?r.settingEl.addClass("is-disabled"):(this.selectedStatuses.has(t.id)&&r.settingEl.addClass("is-selected"),r.settingEl.addEventListener("click",()=>{let n=this.selectedStatuses.has(t.id);this.handleStatusToggle(t.id,!n),this.redraw()}))})}handleStatusToggle(t,r){t===ue?r?(this.selectedStatuses.clear(),this.selectedStatuses.add(ue),this.statuses.forEach(n=>{n.id!=="all"&&this.statusValues.set(n.id,!1)})):this.selectedStatuses.delete(ue):(r?(this.selectedStatuses.delete(ue),this.selectedStatuses.add(t)):this.selectedStatuses.delete(t),this.statusValues.set(t,r))}redraw(){this.createStatusList()}onClose(){let{contentEl:t}=this;t.empty()}};var bt=class{constructor(e,t){this.endpoint=`https://${e}`,this.apiKey=t}updateConfig(e,t){this.endpoint=`https://${e}`,this.apiKey=t}async request(e,t={}){let r=`${this.endpoint}${e}`,n={Authorization:`Bearer ${this.apiKey}`,"Content-Type":"application/json"};t.headers&&Object.entries(t.headers).forEach(([a,o])=>{typeof o=="string"&&(n[a]=o)});let i=await(0,Ps.requestUrl)({url:r,method:t.method||"GET",body:t.body,headers:n});if(i.status>=400)throw new Error(`API request failed: ${i.status}`);return JSON.parse(i.text)}async getArticles(e={lastCardId:null,lastCardUpdateTime:null}){var t;try{let r={limit:50},n=50;e.lastCardId!==null&&e.lastCardId.length>0&&e.lastCardUpdateTime!==null&&e.lastCardUpdateTime.length>0&&(r.last_card_id=e.lastCardId,r.last_card_update_time=e.lastCardUpdateTime),e.folderFilter&&e.folderFilter.length>0&&(e.folderFilter.includes(W)||(r.group_filters=e.folderFilter)),e.typeFilter&&e.typeFilter.length>0&&(r.type_filters=e.typeFilter),e.statusFilter&&e.statusFilter.length>0&&(e.statusFilter.includes(ue)||(e.isRead===!0&&(r.read=!0),e.isStarred===!0&&(r.starred=!0),e.isAnnotated===!0&&(r.annotated=!0))),e.tagsFilter&&e.tagsFilter.length>0&&(e.tagsFilter.includes(A)||(r.tag_filters=e.tagsFilter));let i="/c/api/third-party/card/filter",o=(t=(await this.request(i,{method:"POST",body:JSON.stringify(r)})).data)!=null?t:[],l=o&&o.length>=n;return{articles:o,hasMore:l}}catch(r){throw console.error("\u83B7\u53D6\u6587\u7AE0\u5217\u8868\u5931\u8D25:",r),r}}async getArticleDetail(e){try{let t=`/c/api/third-party/card/content?id=${e}`;return(await this.request(t)).data}catch(t){return console.error(`\u83B7\u53D6\u6587\u7AE0 ${e} \u8BE6\u60C5\u5931\u8D25:`,t),null}}async getFolders(){var e;try{let t="/c/api/third-party/group/list";return(e=(await this.request(t)).data)!=null?e:[]}catch(t){throw console.error("\u83B7\u53D6 Cubox \u6587\u4EF6\u5939\u5217\u8868\u5931\u8D25:",t),t}}async getTags(){var e;try{let t="/c/api/third-party/tag/list";return(e=(await this.request(t)).data)!=null?e:[]}catch(t){throw console.error("\u83B7\u53D6 Cubox \u6807\u7B7E\u5217\u8868\u5931\u8D25:",t),t}}};var ie=class extends Error{},It=class extends ie{constructor(e){super(`Invalid DateTime: ${e.toMessage()}`)}},Ot=class extends ie{constructor(e){super(`Invalid Interval: ${e.toMessage()}`)}},Ct=class extends ie{constructor(e){super(`Invalid Duration: ${e.toMessage()}`)}},G=class extends ie{},Fe=class extends ie{constructor(e){super(`Invalid unit ${e}`)}},D=class extends ie{},J=class extends ie{constructor(){super("Zone is an abstract class")}};var d="numeric",j="short",U="long",ce={year:d,month:d,day:d},ze={year:d,month:j,day:d},Xt={year:d,month:j,day:d,weekday:j},Be={year:d,month:U,day:d},Ye={year:d,month:U,day:d,weekday:U},Ge={hour:d,minute:d},Je={hour:d,minute:d,second:d},je={hour:d,minute:d,second:d,timeZoneName:j},Ke={hour:d,minute:d,second:d,timeZoneName:U},Qe={hour:d,minute:d,hourCycle:"h23"},Xe={hour:d,minute:d,second:d,hourCycle:"h23"},et={hour:d,minute:d,second:d,hourCycle:"h23",timeZoneName:j},tt={hour:d,minute:d,second:d,hourCycle:"h23",timeZoneName:U},st={year:d,month:d,day:d,hour:d,minute:d},rt={year:d,month:d,day:d,hour:d,minute:d,second:d},nt={year:d,month:j,day:d,hour:d,minute:d},it={year:d,month:j,day:d,hour:d,minute:d,second:d},es={year:d,month:j,day:d,weekday:j,hour:d,minute:d},at={year:d,month:U,day:d,hour:d,minute:d,timeZoneName:j},ot={year:d,month:U,day:d,hour:d,minute:d,second:d,timeZoneName:j},lt={year:d,month:U,day:d,weekday:U,hour:d,minute:d,timeZoneName:U},ut={year:d,month:U,day:d,weekday:U,hour:d,minute:d,second:d,timeZoneName:U};var R=class{get type(){throw new J}get name(){throw new J}get ianaName(){return this.name}get isUniversal(){throw new J}offsetName(e,t){throw new J}formatOffset(e,t){throw new J}offset(e){throw new J}equals(e){throw new J}get isValid(){throw new J}};var ts=null,ee=class extends R{static get instance(){return ts===null&&(ts=new ee),ts}get type(){return"system"}get name(){return new Intl.DateTimeFormat().resolvedOptions().timeZone}get isUniversal(){return!1}offsetName(e,{format:t,locale:r}){return Mt(e,t,r)}formatOffset(e,t){return de(this.offset(e),t)}offset(e){return-new Date(e).getTimezoneOffset()}equals(e){return e.type==="system"}get isValid(){return!0}};var _t={};function xn(s){return _t[s]||(_t[s]=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:s,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",era:"short"})),_t[s]}var vn={year:0,month:1,day:2,era:3,hour:4,minute:5,second:6};function Fn(s,e){let t=s.format(e).replace(/\u200E/g,""),r=/(\d+)\/(\d+)\/(\d+) (AD|BC),? (\d+):(\d+):(\d+)/.exec(t),[,n,i,a,o,l,u,c]=r;return[a,n,i,o,l,u,c]}function kn(s,e){let t=s.formatToParts(e),r=[];for(let n=0;n<t.length;n++){let{type:i,value:a}=t[n],o=vn[i];i==="era"?r[o]=a:m(o)||(r[o]=parseInt(a,10))}return r}var Nt={},N=class extends R{static create(e){return Nt[e]||(Nt[e]=new N(e)),Nt[e]}static resetCache(){Nt={},_t={}}static isValidSpecifier(e){return this.isValidZone(e)}static isValidZone(e){if(!e)return!1;try{return new Intl.DateTimeFormat("en-US",{timeZone:e}).format(),!0}catch(t){return!1}}constructor(e){super(),this.zoneName=e,this.valid=N.isValidZone(e)}get type(){return"iana"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(e,{format:t,locale:r}){return Mt(e,t,r,this.name)}formatOffset(e,t){return de(this.offset(e),t)}offset(e){let t=new Date(e);if(isNaN(t))return NaN;let r=xn(this.name),[n,i,a,o,l,u,c]=r.formatToParts?kn(r,t):Fn(r,t);o==="BC"&&(n=-Math.abs(n)+1);let y=ke({year:n,month:i,day:a,hour:l===24?0:l,minute:u,second:c,millisecond:0}),g=+t,k=g%1e3;return g-=k>=0?k:1e3+k,(y-g)/(60*1e3)}equals(e){return e.type==="iana"&&e.name===this.name}get isValid(){return this.valid}};var qs={};function bn(s,e={}){let t=JSON.stringify([s,e]),r=qs[t];return r||(r=new Intl.ListFormat(s,e),qs[t]=r),r}var ss={};function rs(s,e={}){let t=JSON.stringify([s,e]),r=ss[t];return r||(r=new Intl.DateTimeFormat(s,e),ss[t]=r),r}var ns={};function In(s,e={}){let t=JSON.stringify([s,e]),r=ns[t];return r||(r=new Intl.NumberFormat(s,e),ns[t]=r),r}var is={};function On(s,e={}){let{base:t,...r}=e,n=JSON.stringify([s,r]),i=is[n];return i||(i=new Intl.RelativeTimeFormat(s,e),is[n]=i),i}var ct=null;function Cn(){return ct||(ct=new Intl.DateTimeFormat().resolvedOptions().locale,ct)}var zs={};function Dn(s){let e=zs[s];if(!e){let t=new Intl.Locale(s);e="getWeekInfo"in t?t.getWeekInfo():t.weekInfo,zs[s]=e}return e}function Mn(s){let e=s.indexOf("-x-");e!==-1&&(s=s.substring(0,e));let t=s.indexOf("-u-");if(t===-1)return[s];{let r,n;try{r=rs(s).resolvedOptions(),n=s}catch(o){let l=s.substring(0,t);r=rs(l).resolvedOptions(),n=l}let{numberingSystem:i,calendar:a}=r;return[n,i,a]}}function Nn(s,e,t){return(t||e)&&(s.includes("-u-")||(s+="-u"),t&&(s+=`-ca-${t}`),e&&(s+=`-nu-${e}`)),s}function _n(s){let e=[];for(let t=1;t<=12;t++){let r=f.utc(2009,t,1);e.push(s(r))}return e}function An(s){let e=[];for(let t=1;t<=7;t++){let r=f.utc(2016,11,13+t);e.push(s(r))}return e}function At(s,e,t,r){let n=s.listingMode();return n==="error"?null:n==="en"?t(e):r(e)}function Ln(s){return s.numberingSystem&&s.numberingSystem!=="latn"?!1:s.numberingSystem==="latn"||!s.locale||s.locale.startsWith("en")||new Intl.DateTimeFormat(s.intl).resolvedOptions().numberingSystem==="latn"}var as=class{constructor(e,t,r){this.padTo=r.padTo||0,this.floor=r.floor||!1;let{padTo:n,floor:i,...a}=r;if(!t||Object.keys(a).length>0){let o={useGrouping:!1,...r};r.padTo>0&&(o.minimumIntegerDigits=r.padTo),this.inf=In(e,o)}}format(e){if(this.inf){let t=this.floor?Math.floor(e):e;return this.inf.format(t)}else{let t=this.floor?Math.floor(e):be(e,3);return I(t,this.padTo)}}},os=class{constructor(e,t,r){this.opts=r,this.originalZone=void 0;let n;if(this.opts.timeZone)this.dt=e;else if(e.zone.type==="fixed"){let a=-1*(e.offset/60),o=a>=0?`Etc/GMT+${a}`:`Etc/GMT${a}`;e.offset!==0&&N.create(o).valid?(n=o,this.dt=e):(n="UTC",this.dt=e.offset===0?e:e.setZone("UTC").plus({minutes:e.offset}),this.originalZone=e.zone)}else e.zone.type==="system"?this.dt=e:e.zone.type==="iana"?(this.dt=e,n=e.zone.name):(n="UTC",this.dt=e.setZone("UTC").plus({minutes:e.offset}),this.originalZone=e.zone);let i={...this.opts};i.timeZone=i.timeZone||n,this.dtf=rs(t,i)}format(){return this.originalZone?this.formatToParts().map(({value:e})=>e).join(""):this.dtf.format(this.dt.toJSDate())}formatToParts(){let e=this.dtf.formatToParts(this.dt.toJSDate());return this.originalZone?e.map(t=>{if(t.type==="timeZoneName"){let r=this.originalZone.offsetName(this.dt.ts,{locale:this.dt.locale,format:this.opts.timeZoneName});return{...t,value:r}}else return t}):e}resolvedOptions(){return this.dtf.resolvedOptions()}},ls=class{constructor(e,t,r){this.opts={style:"long",...r},!t&&Lt()&&(this.rtf=On(e,r))}format(e,t){return this.rtf?this.rtf.format(e,t):Bs(t,e,this.opts.numeric,this.opts.style!=="long")}formatToParts(e,t){return this.rtf?this.rtf.formatToParts(e,t):[]}},Vn={firstDay:1,minimalDays:4,weekend:[6,7]},T=class{static fromOpts(e){return T.create(e.locale,e.numberingSystem,e.outputCalendar,e.weekSettings,e.defaultToEN)}static create(e,t,r,n,i=!1){let a=e||E.defaultLocale,o=a||(i?"en-US":Cn()),l=t||E.defaultNumberingSystem,u=r||E.defaultOutputCalendar,c=dt(n)||E.defaultWeekSettings;return new T(o,l,u,c,a)}static resetCache(){ct=null,ss={},ns={},is={}}static fromObject({locale:e,numberingSystem:t,outputCalendar:r,weekSettings:n}={}){return T.create(e,t,r,n)}constructor(e,t,r,n,i){let[a,o,l]=Mn(e);this.locale=a,this.numberingSystem=t||o||null,this.outputCalendar=r||l||null,this.weekSettings=n,this.intl=Nn(this.locale,this.numberingSystem,this.outputCalendar),this.weekdaysCache={format:{},standalone:{}},this.monthsCache={format:{},standalone:{}},this.meridiemCache=null,this.eraCache={},this.specifiedLocale=i,this.fastNumbersCached=null}get fastNumbers(){return this.fastNumbersCached==null&&(this.fastNumbersCached=Ln(this)),this.fastNumbersCached}listingMode(){let e=this.isEnglish(),t=(this.numberingSystem===null||this.numberingSystem==="latn")&&(this.outputCalendar===null||this.outputCalendar==="gregory");return e&&t?"en":"intl"}clone(e){return!e||Object.getOwnPropertyNames(e).length===0?this:T.create(e.locale||this.specifiedLocale,e.numberingSystem||this.numberingSystem,e.outputCalendar||this.outputCalendar,dt(e.weekSettings)||this.weekSettings,e.defaultToEN||!1)}redefaultToEN(e={}){return this.clone({...e,defaultToEN:!0})}redefaultToSystem(e={}){return this.clone({...e,defaultToEN:!1})}months(e,t=!1){return At(this,e,us,()=>{let r=t?{month:e,day:"numeric"}:{month:e},n=t?"format":"standalone";return this.monthsCache[n][e]||(this.monthsCache[n][e]=_n(i=>this.extract(i,r,"month"))),this.monthsCache[n][e]})}weekdays(e,t=!1){return At(this,e,cs,()=>{let r=t?{weekday:e,year:"numeric",month:"long",day:"numeric"}:{weekday:e},n=t?"format":"standalone";return this.weekdaysCache[n][e]||(this.weekdaysCache[n][e]=An(i=>this.extract(i,r,"weekday"))),this.weekdaysCache[n][e]})}meridiems(){return At(this,void 0,()=>ds,()=>{if(!this.meridiemCache){let e={hour:"numeric",hourCycle:"h12"};this.meridiemCache=[f.utc(2016,11,13,9),f.utc(2016,11,13,19)].map(t=>this.extract(t,e,"dayperiod"))}return this.meridiemCache})}eras(e){return At(this,e,hs,()=>{let t={era:e};return this.eraCache[e]||(this.eraCache[e]=[f.utc(-40,1,1),f.utc(2017,1,1)].map(r=>this.extract(r,t,"era"))),this.eraCache[e]})}extract(e,t,r){let n=this.dtFormatter(e,t),i=n.formatToParts(),a=i.find(o=>o.type.toLowerCase()===r);return a?a.value:null}numberFormatter(e={}){return new as(this.intl,e.forceSimple||this.fastNumbers,e)}dtFormatter(e,t={}){return new os(e,this.intl,t)}relFormatter(e={}){return new ls(this.intl,this.isEnglish(),e)}listFormatter(e={}){return bn(this.intl,e)}isEnglish(){return this.locale==="en"||this.locale.toLowerCase()==="en-us"||new Intl.DateTimeFormat(this.intl).resolvedOptions().locale.startsWith("en-us")}getWeekSettings(){return this.weekSettings?this.weekSettings:Vt()?Dn(this.locale):Vn}getStartOfWeek(){return this.getWeekSettings().firstDay}getMinDaysInFirstWeek(){return this.getWeekSettings().minimalDays}getWeekendDays(){return this.getWeekSettings().weekend}equals(e){return this.locale===e.locale&&this.numberingSystem===e.numberingSystem&&this.outputCalendar===e.outputCalendar}toString(){return`Locale(${this.locale}, ${this.numberingSystem}, ${this.outputCalendar})`}};var ms=null,O=class extends R{static get utcInstance(){return ms===null&&(ms=new O(0)),ms}static instance(e){return e===0?O.utcInstance:new O(e)}static parseSpecifier(e){if(e){let t=e.match(/^utc(?:([+-]\d{1,2})(?::(\d{2}))?)?$/i);if(t)return new O(Te(t[1],t[2]))}return null}constructor(e){super(),this.fixed=e}get type(){return"fixed"}get name(){return this.fixed===0?"UTC":`UTC${de(this.fixed,"narrow")}`}get ianaName(){return this.fixed===0?"Etc/UTC":`Etc/GMT${de(-this.fixed,"narrow")}`}offsetName(){return this.name}formatOffset(e,t){return de(this.fixed,t)}get isUniversal(){return!0}offset(){return this.fixed}equals(e){return e.type==="fixed"&&e.fixed===this.fixed}get isValid(){return!0}};var Ie=class extends R{constructor(e){super(),this.zoneName=e}get type(){return"invalid"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(){return null}formatOffset(){return""}offset(){return NaN}equals(){return!1}get isValid(){return!1}};function K(s,e){let t;if(m(s)||s===null)return e;if(s instanceof R)return s;if(Ys(s)){let r=s.toLowerCase();return r==="default"?e:r==="local"||r==="system"?ee.instance:r==="utc"||r==="gmt"?O.utcInstance:O.parseSpecifier(r)||N.create(s)}else return Q(s)?O.instance(s):typeof s=="object"&&"offset"in s&&typeof s.offset=="function"?s:new Ie(s)}var gs={arab:"[\u0660-\u0669]",arabext:"[\u06F0-\u06F9]",bali:"[\u1B50-\u1B59]",beng:"[\u09E6-\u09EF]",deva:"[\u0966-\u096F]",fullwide:"[\uFF10-\uFF19]",gujr:"[\u0AE6-\u0AEF]",hanidec:"[\u3007|\u4E00|\u4E8C|\u4E09|\u56DB|\u4E94|\u516D|\u4E03|\u516B|\u4E5D]",khmr:"[\u17E0-\u17E9]",knda:"[\u0CE6-\u0CEF]",laoo:"[\u0ED0-\u0ED9]",limb:"[\u1946-\u194F]",mlym:"[\u0D66-\u0D6F]",mong:"[\u1810-\u1819]",mymr:"[\u1040-\u1049]",orya:"[\u0B66-\u0B6F]",tamldec:"[\u0BE6-\u0BEF]",telu:"[\u0C66-\u0C6F]",thai:"[\u0E50-\u0E59]",tibt:"[\u0F20-\u0F29]",latn:"\\d"},Gs={arab:[1632,1641],arabext:[1776,1785],bali:[6992,7001],beng:[2534,2543],deva:[2406,2415],fullwide:[65296,65303],gujr:[2790,2799],khmr:[6112,6121],knda:[3302,3311],laoo:[3792,3801],limb:[6470,6479],mlym:[3430,3439],mong:[6160,6169],mymr:[4160,4169],orya:[2918,2927],tamldec:[3046,3055],telu:[3174,3183],thai:[3664,3673],tibt:[3872,3881]},Wn=gs.hanidec.replace(/[\[|\]]/g,"").split("");function Js(s){let e=parseInt(s,10);if(isNaN(e)){e="";for(let t=0;t<s.length;t++){let r=s.charCodeAt(t);if(s[t].search(gs.hanidec)!==-1)e+=Wn.indexOf(s[t]);else for(let n in Gs){let[i,a]=Gs[n];r>=i&&r<=a&&(e+=r-i)}}return parseInt(e,10)}else return e}var Oe={};function js(){Oe={}}function P({numberingSystem:s},e=""){let t=s||"latn";return Oe[t]||(Oe[t]={}),Oe[t][e]||(Oe[t][e]=new RegExp(`${gs[t]}${e}`)),Oe[t][e]}var Ks=()=>Date.now(),Qs="system",Xs=null,er=null,tr=null,sr=60,rr,nr=null,E=class{static get now(){return Ks}static set now(e){Ks=e}static set defaultZone(e){Qs=e}static get defaultZone(){return K(Qs,ee.instance)}static get defaultLocale(){return Xs}static set defaultLocale(e){Xs=e}static get defaultNumberingSystem(){return er}static set defaultNumberingSystem(e){er=e}static get defaultOutputCalendar(){return tr}static set defaultOutputCalendar(e){tr=e}static get defaultWeekSettings(){return nr}static set defaultWeekSettings(e){nr=dt(e)}static get twoDigitCutoffYear(){return sr}static set twoDigitCutoffYear(e){sr=e%100}static get throwOnInvalid(){return rr}static set throwOnInvalid(e){rr=e}static resetCaches(){T.resetCache(),N.resetCache(),f.resetCache(),js()}};var _=class{constructor(e,t){this.reason=e,this.explanation=t}toMessage(){return this.explanation?`${this.reason}: ${this.explanation}`:this.reason}};var ir=[0,31,59,90,120,151,181,212,243,273,304,334],ar=[0,31,60,91,121,152,182,213,244,274,305,335];function q(s,e){return new _("unit out of range",`you specified ${e} (of type ${typeof e}) as a ${s}, which is invalid`)}function Wt(s,e,t){let r=new Date(Date.UTC(s,e-1,t));s<100&&s>=0&&r.setUTCFullYear(r.getUTCFullYear()-1900);let n=r.getUTCDay();return n===0?7:n}function or(s,e,t){return t+(we(s)?ar:ir)[e-1]}function lr(s,e){let t=we(s)?ar:ir,r=t.findIndex(i=>i<e),n=e-t[r];return{month:r+1,day:n}}function Rt(s,e){return(s-e+7)%7+1}function ht(s,e=4,t=1){let{year:r,month:n,day:i}=s,a=or(r,n,i),o=Rt(Wt(r,n,i),t),l=Math.floor((a-o+14-e)/7),u;return l<1?(u=r-1,l=Ee(u,e,t)):l>Ee(r,e,t)?(u=r+1,l=1):u=r,{weekYear:u,weekNumber:l,weekday:o,...mt(s)}}function ps(s,e=4,t=1){let{weekYear:r,weekNumber:n,weekday:i}=s,a=Rt(Wt(r,1,e),t),o=he(r),l=n*7+i-a-7+e,u;l<1?(u=r-1,l+=he(u)):l>o?(u=r+1,l-=he(r)):u=r;let{month:c,day:h}=lr(u,l);return{year:u,month:c,day:h,...mt(s)}}function Ht(s){let{year:e,month:t,day:r}=s,n=or(e,t,r);return{year:e,ordinal:n,...mt(s)}}function ys(s){let{year:e,ordinal:t}=s,{month:r,day:n}=lr(e,t);return{year:e,month:r,day:n,...mt(s)}}function Ts(s,e){if(!m(s.localWeekday)||!m(s.localWeekNumber)||!m(s.localWeekYear)){if(!m(s.weekday)||!m(s.weekNumber)||!m(s.weekYear))throw new G("Cannot mix locale-based week fields with ISO-based week fields");return m(s.localWeekday)||(s.weekday=s.localWeekday),m(s.localWeekNumber)||(s.weekNumber=s.localWeekNumber),m(s.localWeekYear)||(s.weekYear=s.localWeekYear),delete s.localWeekday,delete s.localWeekNumber,delete s.localWeekYear,{minDaysInFirstWeek:e.getMinDaysInFirstWeek(),startOfWeek:e.getStartOfWeek()}}else return{minDaysInFirstWeek:4,startOfWeek:1}}function ur(s,e=4,t=1){let r=ft(s.weekYear),n=$(s.weekNumber,1,Ee(s.weekYear,e,t)),i=$(s.weekday,1,7);return r?n?i?!1:q("weekday",s.weekday):q("week",s.weekNumber):q("weekYear",s.weekYear)}function cr(s){let e=ft(s.year),t=$(s.ordinal,1,he(s.year));return e?t?!1:q("ordinal",s.ordinal):q("year",s.year)}function Es(s){let e=ft(s.year),t=$(s.month,1,12),r=$(s.day,1,Ce(s.year,s.month));return e?t?r?!1:q("day",s.day):q("month",s.month):q("year",s.year)}function ws(s){let{hour:e,minute:t,second:r,millisecond:n}=s,i=$(e,0,23)||e===24&&t===0&&r===0&&n===0,a=$(t,0,59),o=$(r,0,59),l=$(n,0,999);return i?a?o?l?!1:q("millisecond",n):q("second",r):q("minute",t):q("hour",e)}function m(s){return typeof s=="undefined"}function Q(s){return typeof s=="number"}function ft(s){return typeof s=="number"&&s%1===0}function Ys(s){return typeof s=="string"}function hr(s){return Object.prototype.toString.call(s)==="[object Date]"}function Lt(){try{return typeof Intl!="undefined"&&!!Intl.RelativeTimeFormat}catch(s){return!1}}function Vt(){try{return typeof Intl!="undefined"&&!!Intl.Locale&&("weekInfo"in Intl.Locale.prototype||"getWeekInfo"in Intl.Locale.prototype)}catch(s){return!1}}function fr(s){return Array.isArray(s)?s:[s]}function Ss(s,e,t){if(s.length!==0)return s.reduce((r,n)=>{let i=[e(n),n];return r&&t(r[0],i[0])===r[0]?r:i},null)[1]}function mr(s,e){return e.reduce((t,r)=>(t[r]=s[r],t),{})}function fe(s,e){return Object.prototype.hasOwnProperty.call(s,e)}function dt(s){if(s==null)return null;if(typeof s!="object")throw new D("Week settings must be an object");if(!$(s.firstDay,1,7)||!$(s.minimalDays,1,7)||!Array.isArray(s.weekend)||s.weekend.some(e=>!$(e,1,7)))throw new D("Invalid week settings");return{firstDay:s.firstDay,minimalDays:s.minimalDays,weekend:Array.from(s.weekend)}}function $(s,e,t){return ft(s)&&s>=e&&s<=t}function Rn(s,e){return s-e*Math.floor(s/e)}function I(s,e=2){let t=s<0,r;return t?r="-"+(""+-s).padStart(e,"0"):r=(""+s).padStart(e,"0"),r}function ae(s){if(!(m(s)||s===null||s===""))return parseInt(s,10)}function me(s){if(!(m(s)||s===null||s===""))return parseFloat(s)}function gt(s){if(!(m(s)||s===null||s==="")){let e=parseFloat("0."+s)*1e3;return Math.floor(e)}}function be(s,e,t=!1){let r=10**e;return(t?Math.trunc:Math.round)(s*r)/r}function we(s){return s%4===0&&(s%100!==0||s%400===0)}function he(s){return we(s)?366:365}function Ce(s,e){let t=Rn(e-1,12)+1,r=s+(e-t)/12;return t===2?we(r)?29:28:[31,null,31,30,31,30,31,31,30,31,30,31][t-1]}function ke(s){let e=Date.UTC(s.year,s.month-1,s.day,s.hour,s.minute,s.second,s.millisecond);return s.year<100&&s.year>=0&&(e=new Date(e),e.setUTCFullYear(s.year,s.month-1,s.day)),+e}function dr(s,e,t){return-Rt(Wt(s,1,e),t)+e-1}function Ee(s,e=4,t=1){let r=dr(s,e,t),n=dr(s+1,e,t);return(he(s)-r+n)/7}function pt(s){return s>99?s:s>E.twoDigitCutoffYear?1900+s:2e3+s}function Mt(s,e,t,r=null){let n=new Date(s),i={hourCycle:"h23",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"};r&&(i.timeZone=r);let a={timeZoneName:e,...i},o=new Intl.DateTimeFormat(t,a).formatToParts(n).find(l=>l.type.toLowerCase()==="timezonename");return o?o.value:null}function Te(s,e){let t=parseInt(s,10);Number.isNaN(t)&&(t=0);let r=parseInt(e,10)||0,n=t<0||Object.is(t,-0)?-r:r;return t*60+n}function xs(s){let e=Number(s);if(typeof s=="boolean"||s===""||Number.isNaN(e))throw new D(`Invalid unit value ${s}`);return e}function De(s,e){let t={};for(let r in s)if(fe(s,r)){let n=s[r];if(n==null)continue;t[e(r)]=xs(n)}return t}function de(s,e){let t=Math.trunc(Math.abs(s/60)),r=Math.trunc(Math.abs(s%60)),n=s>=0?"+":"-";switch(e){case"short":return`${n}${I(t,2)}:${I(r,2)}`;case"narrow":return`${n}${t}${r>0?`:${r}`:""}`;case"techie":return`${n}${I(t,2)}${I(r,2)}`;default:throw new RangeError(`Value format ${e} is out of range for property format`)}}function mt(s){return mr(s,["hour","minute","second","millisecond"])}var Hn=["January","February","March","April","May","June","July","August","September","October","November","December"],vs=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],Un=["J","F","M","A","M","J","J","A","S","O","N","D"];function us(s){switch(s){case"narrow":return[...Un];case"short":return[...vs];case"long":return[...Hn];case"numeric":return["1","2","3","4","5","6","7","8","9","10","11","12"];case"2-digit":return["01","02","03","04","05","06","07","08","09","10","11","12"];default:return null}}var Fs=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],ks=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],$n=["M","T","W","T","F","S","S"];function cs(s){switch(s){case"narrow":return[...$n];case"short":return[...ks];case"long":return[...Fs];case"numeric":return["1","2","3","4","5","6","7"];default:return null}}var ds=["AM","PM"],Zn=["Before Christ","Anno Domini"],Pn=["BC","AD"],qn=["B","A"];function hs(s){switch(s){case"narrow":return[...qn];case"short":return[...Pn];case"long":return[...Zn];default:return null}}function gr(s){return ds[s.hour<12?0:1]}function pr(s,e){return cs(e)[s.weekday-1]}function yr(s,e){return us(e)[s.month-1]}function Tr(s,e){return hs(e)[s.year<0?0:1]}function Bs(s,e,t="always",r=!1){let n={years:["year","yr."],quarters:["quarter","qtr."],months:["month","mo."],weeks:["week","wk."],days:["day","day","days"],hours:["hour","hr."],minutes:["minute","min."],seconds:["second","sec."]},i=["hours","minutes","seconds"].indexOf(s)===-1;if(t==="auto"&&i){let h=s==="days";switch(e){case 1:return h?"tomorrow":`next ${n[s][0]}`;case-1:return h?"yesterday":`last ${n[s][0]}`;case 0:return h?"today":`this ${n[s][0]}`;default:}}let a=Object.is(e,-0)||e<0,o=Math.abs(e),l=o===1,u=n[s],c=r?l?u[1]:u[2]||u[1]:l?n[s][0]:s;return a?`${o} ${c} ago`:`in ${o} ${c}`}function Er(s,e){let t="";for(let r of s)r.literal?t+=r.val:t+=e(r.val);return t}var zn={D:ce,DD:ze,DDD:Be,DDDD:Ye,t:Ge,tt:Je,ttt:je,tttt:Ke,T:Qe,TT:Xe,TTT:et,TTTT:tt,f:st,ff:nt,fff:at,ffff:lt,F:rt,FF:it,FFF:ot,FFFF:ut},C=class{static create(e,t={}){return new C(e,t)}static parseFormat(e){let t=null,r="",n=!1,i=[];for(let a=0;a<e.length;a++){let o=e.charAt(a);o==="'"?(r.length>0&&i.push({literal:n||/^\s+$/.test(r),val:r}),t=null,r="",n=!n):n||o===t?r+=o:(r.length>0&&i.push({literal:/^\s+$/.test(r),val:r}),r=o,t=o)}return r.length>0&&i.push({literal:n||/^\s+$/.test(r),val:r}),i}static macroTokenToFormatOpts(e){return zn[e]}constructor(e,t){this.opts=t,this.loc=e,this.systemLoc=null}formatWithSystemDefault(e,t){return this.systemLoc===null&&(this.systemLoc=this.loc.redefaultToSystem()),this.systemLoc.dtFormatter(e,{...this.opts,...t}).format()}dtFormatter(e,t={}){return this.loc.dtFormatter(e,{...this.opts,...t})}formatDateTime(e,t){return this.dtFormatter(e,t).format()}formatDateTimeParts(e,t){return this.dtFormatter(e,t).formatToParts()}formatInterval(e,t){return this.dtFormatter(e.start,t).dtf.formatRange(e.start.toJSDate(),e.end.toJSDate())}resolvedOptions(e,t){return this.dtFormatter(e,t).resolvedOptions()}num(e,t=0){if(this.opts.forceSimple)return I(e,t);let r={...this.opts};return t>0&&(r.padTo=t),this.loc.numberFormatter(r).format(e)}formatDateTimeFromString(e,t){let r=this.loc.listingMode()==="en",n=this.loc.outputCalendar&&this.loc.outputCalendar!=="gregory",i=(g,k)=>this.loc.extract(e,g,k),a=g=>e.isOffsetFixed&&e.offset===0&&g.allowZ?"Z":e.isValid?e.zone.formatOffset(e.ts,g.format):"",o=()=>r?gr(e):i({hour:"numeric",hourCycle:"h12"},"dayperiod"),l=(g,k)=>r?yr(e,g):i(k?{month:g}:{month:g,day:"numeric"},"month"),u=(g,k)=>r?pr(e,g):i(k?{weekday:g}:{weekday:g,month:"long",day:"numeric"},"weekday"),c=g=>{let k=C.macroTokenToFormatOpts(g);return k?this.formatWithSystemDefault(e,k):g},h=g=>r?Tr(e,g):i({era:g},"era"),y=g=>{switch(g){case"S":return this.num(e.millisecond);case"u":case"SSS":return this.num(e.millisecond,3);case"s":return this.num(e.second);case"ss":return this.num(e.second,2);case"uu":return this.num(Math.floor(e.millisecond/10),2);case"uuu":return this.num(Math.floor(e.millisecond/100));case"m":return this.num(e.minute);case"mm":return this.num(e.minute,2);case"h":return this.num(e.hour%12===0?12:e.hour%12);case"hh":return this.num(e.hour%12===0?12:e.hour%12,2);case"H":return this.num(e.hour);case"HH":return this.num(e.hour,2);case"Z":return a({format:"narrow",allowZ:this.opts.allowZ});case"ZZ":return a({format:"short",allowZ:this.opts.allowZ});case"ZZZ":return a({format:"techie",allowZ:this.opts.allowZ});case"ZZZZ":return e.zone.offsetName(e.ts,{format:"short",locale:this.loc.locale});case"ZZZZZ":return e.zone.offsetName(e.ts,{format:"long",locale:this.loc.locale});case"z":return e.zoneName;case"a":return o();case"d":return n?i({day:"numeric"},"day"):this.num(e.day);case"dd":return n?i({day:"2-digit"},"day"):this.num(e.day,2);case"c":return this.num(e.weekday);case"ccc":return u("short",!0);case"cccc":return u("long",!0);case"ccccc":return u("narrow",!0);case"E":return this.num(e.weekday);case"EEE":return u("short",!1);case"EEEE":return u("long",!1);case"EEEEE":return u("narrow",!1);case"L":return n?i({month:"numeric",day:"numeric"},"month"):this.num(e.month);case"LL":return n?i({month:"2-digit",day:"numeric"},"month"):this.num(e.month,2);case"LLL":return l("short",!0);case"LLLL":return l("long",!0);case"LLLLL":return l("narrow",!0);case"M":return n?i({month:"numeric"},"month"):this.num(e.month);case"MM":return n?i({month:"2-digit"},"month"):this.num(e.month,2);case"MMM":return l("short",!1);case"MMMM":return l("long",!1);case"MMMMM":return l("narrow",!1);case"y":return n?i({year:"numeric"},"year"):this.num(e.year);case"yy":return n?i({year:"2-digit"},"year"):this.num(e.year.toString().slice(-2),2);case"yyyy":return n?i({year:"numeric"},"year"):this.num(e.year,4);case"yyyyyy":return n?i({year:"numeric"},"year"):this.num(e.year,6);case"G":return h("short");case"GG":return h("long");case"GGGGG":return h("narrow");case"kk":return this.num(e.weekYear.toString().slice(-2),2);case"kkkk":return this.num(e.weekYear,4);case"W":return this.num(e.weekNumber);case"WW":return this.num(e.weekNumber,2);case"n":return this.num(e.localWeekNumber);case"nn":return this.num(e.localWeekNumber,2);case"ii":return this.num(e.localWeekYear.toString().slice(-2),2);case"iiii":return this.num(e.localWeekYear,4);case"o":return this.num(e.ordinal);case"ooo":return this.num(e.ordinal,3);case"q":return this.num(e.quarter);case"qq":return this.num(e.quarter,2);case"X":return this.num(Math.floor(e.ts/1e3));case"x":return this.num(e.ts);default:return c(g)}};return Er(C.parseFormat(t),y)}formatDurationFromString(e,t){let r=l=>{switch(l[0]){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":return"hour";case"d":return"day";case"w":return"week";case"M":return"month";case"y":return"year";default:return null}},n=l=>u=>{let c=r(u);return c?this.num(l.get(c),u.length):u},i=C.parseFormat(t),a=i.reduce((l,{literal:u,val:c})=>u?l:l.concat(c),[]),o=e.shiftTo(...a.map(r).filter(l=>l));return Er(i,n(o))}};var Sr=/[A-Za-z_+-]{1,256}(?::?\/[A-Za-z0-9_+-]{1,256}(?:\/[A-Za-z0-9_+-]{1,256})?)?/;function Ne(...s){let e=s.reduce((t,r)=>t+r.source,"");return RegExp(`^${e}$`)}function _e(...s){return e=>s.reduce(([t,r,n],i)=>{let[a,o,l]=i(e,n);return[{...t,...a},o||r,l]},[{},null,1]).slice(0,2)}function Ae(s,...e){if(s==null)return[null,null];for(let[t,r]of e){let n=t.exec(s);if(n)return r(n)}return[null,null]}function xr(...s){return(e,t)=>{let r={},n;for(n=0;n<s.length;n++)r[s[n]]=ae(e[t+n]);return[r,null,t+n]}}var vr=/(?:(Z)|([+-]\d\d)(?::?(\d\d))?)/,Bn=`(?:${vr.source}?(?:\\[(${Sr.source})\\])?)?`,bs=/(\d\d)(?::?(\d\d)(?::?(\d\d)(?:[.,](\d{1,30}))?)?)?/,Fr=RegExp(`${bs.source}${Bn}`),Is=RegExp(`(?:T${Fr.source})?`),Yn=/([+-]\d{6}|\d{4})(?:-?(\d\d)(?:-?(\d\d))?)?/,Gn=/(\d{4})-?W(\d\d)(?:-?(\d))?/,Jn=/(\d{4})-?(\d{3})/,jn=xr("weekYear","weekNumber","weekDay"),Kn=xr("year","ordinal"),Qn=/(\d{4})-(\d\d)-(\d\d)/,kr=RegExp(`${bs.source} ?(?:${vr.source}|(${Sr.source}))?`),Xn=RegExp(`(?: ${kr.source})?`);function Me(s,e,t){let r=s[e];return m(r)?t:ae(r)}function ei(s,e){return[{year:Me(s,e),month:Me(s,e+1,1),day:Me(s,e+2,1)},null,e+3]}function Le(s,e){return[{hours:Me(s,e,0),minutes:Me(s,e+1,0),seconds:Me(s,e+2,0),milliseconds:gt(s[e+3])},null,e+4]}function yt(s,e){let t=!s[e]&&!s[e+1],r=Te(s[e+1],s[e+2]),n=t?null:O.instance(r);return[{},n,e+3]}function Tt(s,e){let t=s[e]?N.create(s[e]):null;return[{},t,e+1]}var ti=RegExp(`^T?${bs.source}$`),si=/^-?P(?:(?:(-?\d{1,20}(?:\.\d{1,20})?)Y)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20}(?:\.\d{1,20})?)W)?(?:(-?\d{1,20}(?:\.\d{1,20})?)D)?(?:T(?:(-?\d{1,20}(?:\.\d{1,20})?)H)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20})(?:[.,](-?\d{1,20}))?S)?)?)$/;function ri(s){let[e,t,r,n,i,a,o,l,u]=s,c=e[0]==="-",h=l&&l[0]==="-",y=(g,k=!1)=>g!==void 0&&(k||g&&c)?-g:g;return[{years:y(me(t)),months:y(me(r)),weeks:y(me(n)),days:y(me(i)),hours:y(me(a)),minutes:y(me(o)),seconds:y(me(l),l==="-0"),milliseconds:y(gt(u),h)}]}var ni={GMT:0,EDT:-4*60,EST:-5*60,CDT:-5*60,CST:-6*60,MDT:-6*60,MST:-7*60,PDT:-7*60,PST:-8*60};function Os(s,e,t,r,n,i,a){let o={year:e.length===2?pt(ae(e)):ae(e),month:vs.indexOf(t)+1,day:ae(r),hour:ae(n),minute:ae(i)};return a&&(o.second=ae(a)),s&&(o.weekday=s.length>3?Fs.indexOf(s)+1:ks.indexOf(s)+1),o}var ii=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\d\d)(\d\d)))$/;function ai(s){let[,e,t,r,n,i,a,o,l,u,c,h]=s,y=Os(e,n,r,t,i,a,o),g;return l?g=ni[l]:u?g=0:g=Te(c,h),[y,new O(g)]}function oi(s){return s.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").trim()}var li=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d\d):(\d\d):(\d\d) GMT$/,ui=/^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d\d) (\d\d):(\d\d):(\d\d) GMT$/,ci=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \d|\d\d) (\d\d):(\d\d):(\d\d) (\d{4})$/;function wr(s){let[,e,t,r,n,i,a,o]=s;return[Os(e,n,r,t,i,a,o),O.utcInstance]}function di(s){let[,e,t,r,n,i,a,o]=s;return[Os(e,o,t,r,n,i,a),O.utcInstance]}var hi=Ne(Yn,Is),fi=Ne(Gn,Is),mi=Ne(Jn,Is),gi=Ne(Fr),br=_e(ei,Le,yt,Tt),pi=_e(jn,Le,yt,Tt),yi=_e(Kn,Le,yt,Tt),Ti=_e(Le,yt,Tt);function Ir(s){return Ae(s,[hi,br],[fi,pi],[mi,yi],[gi,Ti])}function Or(s){return Ae(oi(s),[ii,ai])}function Cr(s){return Ae(s,[li,wr],[ui,wr],[ci,di])}function Dr(s){return Ae(s,[si,ri])}var Ei=_e(Le);function Mr(s){return Ae(s,[ti,Ei])}var wi=Ne(Qn,Xn),Si=Ne(kr),xi=_e(Le,yt,Tt);function Nr(s){return Ae(s,[wi,br],[Si,xi])}var _r="Invalid Duration",Lr={weeks:{days:7,hours:7*24,minutes:7*24*60,seconds:7*24*60*60,milliseconds:7*24*60*60*1e3},days:{hours:24,minutes:24*60,seconds:24*60*60,milliseconds:24*60*60*1e3},hours:{minutes:60,seconds:60*60,milliseconds:60*60*1e3},minutes:{seconds:60,milliseconds:60*1e3},seconds:{milliseconds:1e3}},vi={years:{quarters:4,months:12,weeks:52,days:365,hours:365*24,minutes:365*24*60,seconds:365*24*60*60,milliseconds:365*24*60*60*1e3},quarters:{months:3,weeks:13,days:91,hours:91*24,minutes:91*24*60,seconds:91*24*60*60,milliseconds:91*24*60*60*1e3},months:{weeks:4,days:30,hours:30*24,minutes:30*24*60,seconds:30*24*60*60,milliseconds:30*24*60*60*1e3},...Lr},z=146097/400,Ve=146097/4800,Fi={years:{quarters:4,months:12,weeks:z/7,days:z,hours:z*24,minutes:z*24*60,seconds:z*24*60*60,milliseconds:z*24*60*60*1e3},quarters:{months:3,weeks:z/28,days:z/4,hours:z*24/4,minutes:z*24*60/4,seconds:z*24*60*60/4,milliseconds:z*24*60*60*1e3/4},months:{weeks:Ve/7,days:Ve,hours:Ve*24,minutes:Ve*24*60,seconds:Ve*24*60*60,milliseconds:Ve*24*60*60*1e3},...Lr},Se=["years","quarters","months","weeks","days","hours","minutes","seconds","milliseconds"],ki=Se.slice(0).reverse();function ge(s,e,t=!1){let r={values:t?e.values:{...s.values,...e.values||{}},loc:s.loc.clone(e.loc),conversionAccuracy:e.conversionAccuracy||s.conversionAccuracy,matrix:e.matrix||s.matrix};return new p(r)}function Vr(s,e){var r;let t=(r=e.milliseconds)!=null?r:0;for(let n of ki.slice(1))e[n]&&(t+=e[n]*s[n].milliseconds);return t}function Ar(s,e){let t=Vr(s,e)<0?-1:1;Se.reduceRight((r,n)=>{if(m(e[n]))return r;if(r){let i=e[r]*t,a=s[n][r],o=Math.floor(i/a);e[n]+=o*t,e[r]-=o*a*t}return n},null),Se.reduce((r,n)=>{if(m(e[n]))return r;if(r){let i=e[r]%1;e[r]-=i,e[n]+=i*s[r][n]}return n},null)}function bi(s){let e={};for(let[t,r]of Object.entries(s))r!==0&&(e[t]=r);return e}var p=class{constructor(e){let t=e.conversionAccuracy==="longterm"||!1,r=t?Fi:vi;e.matrix&&(r=e.matrix),this.values=e.values,this.loc=e.loc||T.create(),this.conversionAccuracy=t?"longterm":"casual",this.invalid=e.invalid||null,this.matrix=r,this.isLuxonDuration=!0}static fromMillis(e,t){return p.fromObject({milliseconds:e},t)}static fromObject(e,t={}){if(e==null||typeof e!="object")throw new D(`Duration.fromObject: argument expected to be an object, got ${e===null?"null":typeof e}`);return new p({values:De(e,p.normalizeUnit),loc:T.fromObject(t),conversionAccuracy:t.conversionAccuracy,matrix:t.matrix})}static fromDurationLike(e){if(Q(e))return p.fromMillis(e);if(p.isDuration(e))return e;if(typeof e=="object")return p.fromObject(e);throw new D(`Unknown duration argument ${e} of type ${typeof e}`)}static fromISO(e,t){let[r]=Dr(e);return r?p.fromObject(r,t):p.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static fromISOTime(e,t){let[r]=Mr(e);return r?p.fromObject(r,t):p.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static invalid(e,t=null){if(!e)throw new D("need to specify a reason the Duration is invalid");let r=e instanceof _?e:new _(e,t);if(E.throwOnInvalid)throw new Ct(r);return new p({invalid:r})}static normalizeUnit(e){let t={year:"years",years:"years",quarter:"quarters",quarters:"quarters",month:"months",months:"months",week:"weeks",weeks:"weeks",day:"days",days:"days",hour:"hours",hours:"hours",minute:"minutes",minutes:"minutes",second:"seconds",seconds:"seconds",millisecond:"milliseconds",milliseconds:"milliseconds"}[e&&e.toLowerCase()];if(!t)throw new Fe(e);return t}static isDuration(e){return e&&e.isLuxonDuration||!1}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}toFormat(e,t={}){let r={...t,floor:t.round!==!1&&t.floor!==!1};return this.isValid?C.create(this.loc,r).formatDurationFromString(this,e):_r}toHuman(e={}){if(!this.isValid)return _r;let t=Se.map(r=>{let n=this.values[r];return m(n)?null:this.loc.numberFormatter({style:"unit",unitDisplay:"long",...e,unit:r.slice(0,-1)}).format(n)}).filter(r=>r);return this.loc.listFormatter({type:"conjunction",style:e.listStyle||"narrow",...e}).format(t)}toObject(){return this.isValid?{...this.values}:{}}toISO(){if(!this.isValid)return null;let e="P";return this.years!==0&&(e+=this.years+"Y"),(this.months!==0||this.quarters!==0)&&(e+=this.months+this.quarters*3+"M"),this.weeks!==0&&(e+=this.weeks+"W"),this.days!==0&&(e+=this.days+"D"),(this.hours!==0||this.minutes!==0||this.seconds!==0||this.milliseconds!==0)&&(e+="T"),this.hours!==0&&(e+=this.hours+"H"),this.minutes!==0&&(e+=this.minutes+"M"),(this.seconds!==0||this.milliseconds!==0)&&(e+=be(this.seconds+this.milliseconds/1e3,3)+"S"),e==="P"&&(e+="T0S"),e}toISOTime(e={}){if(!this.isValid)return null;let t=this.toMillis();return t<0||t>=864e5?null:(e={suppressMilliseconds:!1,suppressSeconds:!1,includePrefix:!1,format:"extended",...e,includeOffset:!1},f.fromMillis(t,{zone:"UTC"}).toISOTime(e))}toJSON(){return this.toISO()}toString(){return this.toISO()}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`Duration { values: ${JSON.stringify(this.values)} }`:`Duration { Invalid, reason: ${this.invalidReason} }`}toMillis(){return this.isValid?Vr(this.matrix,this.values):NaN}valueOf(){return this.toMillis()}plus(e){if(!this.isValid)return this;let t=p.fromDurationLike(e),r={};for(let n of Se)(fe(t.values,n)||fe(this.values,n))&&(r[n]=t.get(n)+this.get(n));return ge(this,{values:r},!0)}minus(e){if(!this.isValid)return this;let t=p.fromDurationLike(e);return this.plus(t.negate())}mapUnits(e){if(!this.isValid)return this;let t={};for(let r of Object.keys(this.values))t[r]=xs(e(this.values[r],r));return ge(this,{values:t},!0)}get(e){return this[p.normalizeUnit(e)]}set(e){if(!this.isValid)return this;let t={...this.values,...De(e,p.normalizeUnit)};return ge(this,{values:t})}reconfigure({locale:e,numberingSystem:t,conversionAccuracy:r,matrix:n}={}){let a={loc:this.loc.clone({locale:e,numberingSystem:t}),matrix:n,conversionAccuracy:r};return ge(this,a)}as(e){return this.isValid?this.shiftTo(e).get(e):NaN}normalize(){if(!this.isValid)return this;let e=this.toObject();return Ar(this.matrix,e),ge(this,{values:e},!0)}rescale(){if(!this.isValid)return this;let e=bi(this.normalize().shiftToAll().toObject());return ge(this,{values:e},!0)}shiftTo(...e){if(!this.isValid)return this;if(e.length===0)return this;e=e.map(a=>p.normalizeUnit(a));let t={},r={},n=this.toObject(),i;for(let a of Se)if(e.indexOf(a)>=0){i=a;let o=0;for(let u in r)o+=this.matrix[u][a]*r[u],r[u]=0;Q(n[a])&&(o+=n[a]);let l=Math.trunc(o);t[a]=l,r[a]=(o*1e3-l*1e3)/1e3}else Q(n[a])&&(r[a]=n[a]);for(let a in r)r[a]!==0&&(t[i]+=a===i?r[a]:r[a]/this.matrix[i][a]);return Ar(this.matrix,t),ge(this,{values:t},!0)}shiftToAll(){return this.isValid?this.shiftTo("years","months","weeks","days","hours","minutes","seconds","milliseconds"):this}negate(){if(!this.isValid)return this;let e={};for(let t of Object.keys(this.values))e[t]=this.values[t]===0?0:-this.values[t];return ge(this,{values:e},!0)}get years(){return this.isValid?this.values.years||0:NaN}get quarters(){return this.isValid?this.values.quarters||0:NaN}get months(){return this.isValid?this.values.months||0:NaN}get weeks(){return this.isValid?this.values.weeks||0:NaN}get days(){return this.isValid?this.values.days||0:NaN}get hours(){return this.isValid?this.values.hours||0:NaN}get minutes(){return this.isValid?this.values.minutes||0:NaN}get seconds(){return this.isValid?this.values.seconds||0:NaN}get milliseconds(){return this.isValid?this.values.milliseconds||0:NaN}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}equals(e){if(!this.isValid||!e.isValid||!this.loc.equals(e.loc))return!1;function t(r,n){return r===void 0||r===0?n===void 0||n===0:r===n}for(let r of Se)if(!t(this.values[r],e.values[r]))return!1;return!0}};var We="Invalid Interval";function Ii(s,e){return!s||!s.isValid?v.invalid("missing or invalid start"):!e||!e.isValid?v.invalid("missing or invalid end"):e<s?v.invalid("end before start",`The end of an interval must be after its start, but you had start=${s.toISO()} and end=${e.toISO()}`):null}var v=class{constructor(e){this.s=e.start,this.e=e.end,this.invalid=e.invalid||null,this.isLuxonInterval=!0}static invalid(e,t=null){if(!e)throw new D("need to specify a reason the Interval is invalid");let r=e instanceof _?e:new _(e,t);if(E.throwOnInvalid)throw new Ot(r);return new v({invalid:r})}static fromDateTimes(e,t){let r=Re(e),n=Re(t),i=Ii(r,n);return i==null?new v({start:r,end:n}):i}static after(e,t){let r=p.fromDurationLike(t),n=Re(e);return v.fromDateTimes(n,n.plus(r))}static before(e,t){let r=p.fromDurationLike(t),n=Re(e);return v.fromDateTimes(n.minus(r),n)}static fromISO(e,t){let[r,n]=(e||"").split("/",2);if(r&&n){let i,a;try{i=f.fromISO(r,t),a=i.isValid}catch(u){a=!1}let o,l;try{o=f.fromISO(n,t),l=o.isValid}catch(u){l=!1}if(a&&l)return v.fromDateTimes(i,o);if(a){let u=p.fromISO(n,t);if(u.isValid)return v.after(i,u)}else if(l){let u=p.fromISO(r,t);if(u.isValid)return v.before(o,u)}}return v.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static isInterval(e){return e&&e.isLuxonInterval||!1}get start(){return this.isValid?this.s:null}get end(){return this.isValid?this.e:null}get isValid(){return this.invalidReason===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}length(e="milliseconds"){return this.isValid?this.toDuration(e).get(e):NaN}count(e="milliseconds",t){if(!this.isValid)return NaN;let r=this.start.startOf(e,t),n;return t!=null&&t.useLocaleWeeks?n=this.end.reconfigure({locale:r.locale}):n=this.end,n=n.startOf(e,t),Math.floor(n.diff(r,e).get(e))+(n.valueOf()!==this.end.valueOf())}hasSame(e){return this.isValid?this.isEmpty()||this.e.minus(1).hasSame(this.s,e):!1}isEmpty(){return this.s.valueOf()===this.e.valueOf()}isAfter(e){return this.isValid?this.s>e:!1}isBefore(e){return this.isValid?this.e<=e:!1}contains(e){return this.isValid?this.s<=e&&this.e>e:!1}set({start:e,end:t}={}){return this.isValid?v.fromDateTimes(e||this.s,t||this.e):this}splitAt(...e){if(!this.isValid)return[];let t=e.map(Re).filter(a=>this.contains(a)).sort((a,o)=>a.toMillis()-o.toMillis()),r=[],{s:n}=this,i=0;for(;n<this.e;){let a=t[i]||this.e,o=+a>+this.e?this.e:a;r.push(v.fromDateTimes(n,o)),n=o,i+=1}return r}splitBy(e){let t=p.fromDurationLike(e);if(!this.isValid||!t.isValid||t.as("milliseconds")===0)return[];let{s:r}=this,n=1,i,a=[];for(;r<this.e;){let o=this.start.plus(t.mapUnits(l=>l*n));i=+o>+this.e?this.e:o,a.push(v.fromDateTimes(r,i)),r=i,n+=1}return a}divideEqually(e){return this.isValid?this.splitBy(this.length()/e).slice(0,e):[]}overlaps(e){return this.e>e.s&&this.s<e.e}abutsStart(e){return this.isValid?+this.e==+e.s:!1}abutsEnd(e){return this.isValid?+e.e==+this.s:!1}engulfs(e){return this.isValid?this.s<=e.s&&this.e>=e.e:!1}equals(e){return!this.isValid||!e.isValid?!1:this.s.equals(e.s)&&this.e.equals(e.e)}intersection(e){if(!this.isValid)return this;let t=this.s>e.s?this.s:e.s,r=this.e<e.e?this.e:e.e;return t>=r?null:v.fromDateTimes(t,r)}union(e){if(!this.isValid)return this;let t=this.s<e.s?this.s:e.s,r=this.e>e.e?this.e:e.e;return v.fromDateTimes(t,r)}static merge(e){let[t,r]=e.sort((n,i)=>n.s-i.s).reduce(([n,i],a)=>i?i.overlaps(a)||i.abutsStart(a)?[n,i.union(a)]:[n.concat([i]),a]:[n,a],[[],null]);return r&&t.push(r),t}static xor(e){let t=null,r=0,n=[],i=e.map(l=>[{time:l.s,type:"s"},{time:l.e,type:"e"}]),a=Array.prototype.concat(...i),o=a.sort((l,u)=>l.time-u.time);for(let l of o)r+=l.type==="s"?1:-1,r===1?t=l.time:(t&&+t!=+l.time&&n.push(v.fromDateTimes(t,l.time)),t=null);return v.merge(n)}difference(...e){return v.xor([this].concat(e)).map(t=>this.intersection(t)).filter(t=>t&&!t.isEmpty())}toString(){return this.isValid?`[${this.s.toISO()} \u2013 ${this.e.toISO()})`:We}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`Interval { start: ${this.s.toISO()}, end: ${this.e.toISO()} }`:`Interval { Invalid, reason: ${this.invalidReason} }`}toLocaleString(e=ce,t={}){return this.isValid?C.create(this.s.loc.clone(t),e).formatInterval(this):We}toISO(e){return this.isValid?`${this.s.toISO(e)}/${this.e.toISO(e)}`:We}toISODate(){return this.isValid?`${this.s.toISODate()}/${this.e.toISODate()}`:We}toISOTime(e){return this.isValid?`${this.s.toISOTime(e)}/${this.e.toISOTime(e)}`:We}toFormat(e,{separator:t=" \u2013 "}={}){return this.isValid?`${this.s.toFormat(e)}${t}${this.e.toFormat(e)}`:We}toDuration(e,t){return this.isValid?this.e.diff(this.s,e,t):p.invalid(this.invalidReason)}mapEndpoints(e){return v.fromDateTimes(e(this.s),e(this.e))}};var oe=class{static hasDST(e=E.defaultZone){let t=f.now().setZone(e).set({month:12});return!e.isUniversal&&t.offset!==t.set({month:6}).offset}static isValidIANAZone(e){return N.isValidZone(e)}static normalizeZone(e){return K(e,E.defaultZone)}static getStartOfWeek({locale:e=null,locObj:t=null}={}){return(t||T.create(e)).getStartOfWeek()}static getMinimumDaysInFirstWeek({locale:e=null,locObj:t=null}={}){return(t||T.create(e)).getMinDaysInFirstWeek()}static getWeekendWeekdays({locale:e=null,locObj:t=null}={}){return(t||T.create(e)).getWeekendDays().slice()}static months(e="long",{locale:t=null,numberingSystem:r=null,locObj:n=null,outputCalendar:i="gregory"}={}){return(n||T.create(t,r,i)).months(e)}static monthsFormat(e="long",{locale:t=null,numberingSystem:r=null,locObj:n=null,outputCalendar:i="gregory"}={}){return(n||T.create(t,r,i)).months(e,!0)}static weekdays(e="long",{locale:t=null,numberingSystem:r=null,locObj:n=null}={}){return(n||T.create(t,r,null)).weekdays(e)}static weekdaysFormat(e="long",{locale:t=null,numberingSystem:r=null,locObj:n=null}={}){return(n||T.create(t,r,null)).weekdays(e,!0)}static meridiems({locale:e=null}={}){return T.create(e).meridiems()}static eras(e="short",{locale:t=null}={}){return T.create(t,null,"gregory").eras(e)}static features(){return{relative:Lt(),localeWeek:Vt()}}};function Wr(s,e){let t=n=>n.toUTC(0,{keepLocalTime:!0}).startOf("day").valueOf(),r=t(e)-t(s);return Math.floor(p.fromMillis(r).as("days"))}function Oi(s,e,t){let r=[["years",(l,u)=>u.year-l.year],["quarters",(l,u)=>u.quarter-l.quarter+(u.year-l.year)*4],["months",(l,u)=>u.month-l.month+(u.year-l.year)*12],["weeks",(l,u)=>{let c=Wr(l,u);return(c-c%7)/7}],["days",Wr]],n={},i=s,a,o;for(let[l,u]of r)t.indexOf(l)>=0&&(a=l,n[l]=u(s,e),o=i.plus(n),o>e?(n[l]--,s=i.plus(n),s>e&&(o=s,n[l]--,s=i.plus(n))):s=o);return[s,n,o,a]}function Rr(s,e,t,r){let[n,i,a,o]=Oi(s,e,t),l=e-n,u=t.filter(h=>["hours","minutes","seconds","milliseconds"].indexOf(h)>=0);u.length===0&&(a<e&&(a=n.plus({[o]:1})),a!==n&&(i[o]=(i[o]||0)+l/(a-n)));let c=p.fromObject(i,r);return u.length>0?p.fromMillis(l,r).shiftTo(...u).plus(c):c}var Ci="missing Intl.DateTimeFormat.formatToParts support";function w(s,e=t=>t){return{regex:s,deser:([t])=>e(Js(t))}}var Di=String.fromCharCode(160),$r=`[ ${Di}]`,Zr=new RegExp($r,"g");function Mi(s){return s.replace(/\./g,"\\.?").replace(Zr,$r)}function Hr(s){return s.replace(/\./g,"").replace(Zr," ").toLowerCase()}function X(s,e){return s===null?null:{regex:RegExp(s.map(Mi).join("|")),deser:([t])=>s.findIndex(r=>Hr(t)===Hr(r))+e}}function Ur(s,e){return{regex:s,deser:([,t,r])=>Te(t,r),groups:e}}function Ut(s){return{regex:s,deser:([e])=>e}}function Ni(s){return s.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function _i(s,e){let t=P(e),r=P(e,"{2}"),n=P(e,"{3}"),i=P(e,"{4}"),a=P(e,"{6}"),o=P(e,"{1,2}"),l=P(e,"{1,3}"),u=P(e,"{1,6}"),c=P(e,"{1,9}"),h=P(e,"{2,4}"),y=P(e,"{4,6}"),g=F=>({regex:RegExp(Ni(F.val)),deser:([b])=>b,literal:!0}),S=(F=>{if(s.literal)return g(F);switch(F.val){case"G":return X(e.eras("short"),0);case"GG":return X(e.eras("long"),0);case"y":return w(u);case"yy":return w(h,pt);case"yyyy":return w(i);case"yyyyy":return w(y);case"yyyyyy":return w(a);case"M":return w(o);case"MM":return w(r);case"MMM":return X(e.months("short",!0),1);case"MMMM":return X(e.months("long",!0),1);case"L":return w(o);case"LL":return w(r);case"LLL":return X(e.months("short",!1),1);case"LLLL":return X(e.months("long",!1),1);case"d":return w(o);case"dd":return w(r);case"o":return w(l);case"ooo":return w(n);case"HH":return w(r);case"H":return w(o);case"hh":return w(r);case"h":return w(o);case"mm":return w(r);case"m":return w(o);case"q":return w(o);case"qq":return w(r);case"s":return w(o);case"ss":return w(r);case"S":return w(l);case"SSS":return w(n);case"u":return Ut(c);case"uu":return Ut(o);case"uuu":return w(t);case"a":return X(e.meridiems(),0);case"kkkk":return w(i);case"kk":return w(h,pt);case"W":return w(o);case"WW":return w(r);case"E":case"c":return w(t);case"EEE":return X(e.weekdays("short",!1),1);case"EEEE":return X(e.weekdays("long",!1),1);case"ccc":return X(e.weekdays("short",!0),1);case"cccc":return X(e.weekdays("long",!0),1);case"Z":case"ZZ":return Ur(new RegExp(`([+-]${o.source})(?::(${r.source}))?`),2);case"ZZZ":return Ur(new RegExp(`([+-]${o.source})(${r.source})?`),2);case"z":return Ut(/[a-z_+-/]{1,256}?/i);case" ":return Ut(/[^\S\n\r]/);default:return g(F)}})(s)||{invalidReason:Ci};return S.token=s,S}var Ai={year:{"2-digit":"yy",numeric:"yyyyy"},month:{numeric:"M","2-digit":"MM",short:"MMM",long:"MMMM"},day:{numeric:"d","2-digit":"dd"},weekday:{short:"EEE",long:"EEEE"},dayperiod:"a",dayPeriod:"a",hour12:{numeric:"h","2-digit":"hh"},hour24:{numeric:"H","2-digit":"HH"},minute:{numeric:"m","2-digit":"mm"},second:{numeric:"s","2-digit":"ss"},timeZoneName:{long:"ZZZZZ",short:"ZZZ"}};function Li(s,e,t){let{type:r,value:n}=s;if(r==="literal"){let l=/^\s+$/.test(n);return{literal:!l,val:l?" ":n}}let i=e[r],a=r;r==="hour"&&(e.hour12!=null?a=e.hour12?"hour12":"hour24":e.hourCycle!=null?e.hourCycle==="h11"||e.hourCycle==="h12"?a="hour12":a="hour24":a=t.hour12?"hour12":"hour24");let o=Ai[a];if(typeof o=="object"&&(o=o[i]),o)return{literal:!1,val:o}}function Vi(s){return[`^${s.map(t=>t.regex).reduce((t,r)=>`${t}(${r.source})`,"")}$`,s]}function Wi(s,e,t){let r=s.match(e);if(r){let n={},i=1;for(let a in t)if(fe(t,a)){let o=t[a],l=o.groups?o.groups+1:1;!o.literal&&o.token&&(n[o.token.val[0]]=o.deser(r.slice(i,i+l))),i+=l}return[r,n]}else return[r,{}]}function Ri(s){let e=i=>{switch(i){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":case"H":return"hour";case"d":return"day";case"o":return"ordinal";case"L":case"M":return"month";case"y":return"year";case"E":case"c":return"weekday";case"W":return"weekNumber";case"k":return"weekYear";case"q":return"quarter";default:return null}},t=null,r;return m(s.z)||(t=N.create(s.z)),m(s.Z)||(t||(t=new O(s.Z)),r=s.Z),m(s.q)||(s.M=(s.q-1)*3+1),m(s.h)||(s.h<12&&s.a===1?s.h+=12:s.h===12&&s.a===0&&(s.h=0)),s.G===0&&s.y&&(s.y=-s.y),m(s.u)||(s.S=gt(s.u)),[Object.keys(s).reduce((i,a)=>{let o=e(a);return o&&(i[o]=s[a]),i},{}),t,r]}var Cs=null;function Hi(){return Cs||(Cs=f.fromMillis(1555555555555)),Cs}function Ui(s,e){if(s.literal)return s;let t=C.macroTokenToFormatOpts(s.val),r=Ns(t,e);return r==null||r.includes(void 0)?s:r}function Ds(s,e){return Array.prototype.concat(...s.map(t=>Ui(t,e)))}var Et=class{constructor(e,t){if(this.locale=e,this.format=t,this.tokens=Ds(C.parseFormat(t),e),this.units=this.tokens.map(r=>_i(r,e)),this.disqualifyingUnit=this.units.find(r=>r.invalidReason),!this.disqualifyingUnit){let[r,n]=Vi(this.units);this.regex=RegExp(r,"i"),this.handlers=n}}explainFromTokens(e){if(this.isValid){let[t,r]=Wi(e,this.regex,this.handlers),[n,i,a]=r?Ri(r):[null,null,void 0];if(fe(r,"a")&&fe(r,"H"))throw new G("Can't include meridiem when specifying 24-hour format");return{input:e,tokens:this.tokens,regex:this.regex,rawMatches:t,matches:r,result:n,zone:i,specificOffset:a}}else return{input:e,tokens:this.tokens,invalidReason:this.invalidReason}}get isValid(){return!this.disqualifyingUnit}get invalidReason(){return this.disqualifyingUnit?this.disqualifyingUnit.invalidReason:null}};function Ms(s,e,t){return new Et(s,t).explainFromTokens(e)}function Pr(s,e,t){let{result:r,zone:n,specificOffset:i,invalidReason:a}=Ms(s,e,t);return[r,n,i,a]}function Ns(s,e){if(!s)return null;let r=C.create(e,s).dtFormatter(Hi()),n=r.formatToParts(),i=r.resolvedOptions();return n.map(a=>Li(a,s,i))}var _s="Invalid DateTime",qr=864e13;function wt(s){return new _("unsupported zone",`the zone "${s.name}" is not supported`)}function As(s){return s.weekData===null&&(s.weekData=ht(s.c)),s.weekData}function Ls(s){return s.localWeekData===null&&(s.localWeekData=ht(s.c,s.loc.getMinDaysInFirstWeek(),s.loc.getStartOfWeek())),s.localWeekData}function xe(s,e){let t={ts:s.ts,zone:s.zone,c:s.c,o:s.o,loc:s.loc,invalid:s.invalid};return new f({...t,...e,old:t})}function Kr(s,e,t){let r=s-e*60*1e3,n=t.offset(r);if(e===n)return[r,e];r-=(n-e)*60*1e3;let i=t.offset(r);return n===i?[r,n]:[s-Math.min(n,i)*60*1e3,Math.max(n,i)]}function $t(s,e){s+=e*60*1e3;let t=new Date(s);return{year:t.getUTCFullYear(),month:t.getUTCMonth()+1,day:t.getUTCDate(),hour:t.getUTCHours(),minute:t.getUTCMinutes(),second:t.getUTCSeconds(),millisecond:t.getUTCMilliseconds()}}function Pt(s,e,t){return Kr(ke(s),e,t)}function zr(s,e){let t=s.o,r=s.c.year+Math.trunc(e.years),n=s.c.month+Math.trunc(e.months)+Math.trunc(e.quarters)*3,i={...s.c,year:r,month:n,day:Math.min(s.c.day,Ce(r,n))+Math.trunc(e.days)+Math.trunc(e.weeks)*7},a=p.fromObject({years:e.years-Math.trunc(e.years),quarters:e.quarters-Math.trunc(e.quarters),months:e.months-Math.trunc(e.months),weeks:e.weeks-Math.trunc(e.weeks),days:e.days-Math.trunc(e.days),hours:e.hours,minutes:e.minutes,seconds:e.seconds,milliseconds:e.milliseconds}).as("milliseconds"),o=ke(i),[l,u]=Kr(o,t,s.zone);return a!==0&&(l+=a,u=s.zone.offset(l)),{ts:l,o:u}}function He(s,e,t,r,n,i){let{setZone:a,zone:o}=t;if(s&&Object.keys(s).length!==0||e){let l=e||o,u=f.fromObject(s,{...t,zone:l,specificOffset:i});return a?u:u.setZone(o)}else return f.invalid(new _("unparsable",`the input "${n}" can't be parsed as ${r}`))}function Zt(s,e,t=!0){return s.isValid?C.create(T.create("en-US"),{allowZ:t,forceSimple:!0}).formatDateTimeFromString(s,e):null}function Vs(s,e){let t=s.c.year>9999||s.c.year<0,r="";return t&&s.c.year>=0&&(r+="+"),r+=I(s.c.year,t?6:4),e?(r+="-",r+=I(s.c.month),r+="-",r+=I(s.c.day)):(r+=I(s.c.month),r+=I(s.c.day)),r}function Br(s,e,t,r,n,i){let a=I(s.c.hour);return e?(a+=":",a+=I(s.c.minute),(s.c.millisecond!==0||s.c.second!==0||!t)&&(a+=":")):a+=I(s.c.minute),(s.c.millisecond!==0||s.c.second!==0||!t)&&(a+=I(s.c.second),(s.c.millisecond!==0||!r)&&(a+=".",a+=I(s.c.millisecond,3))),n&&(s.isOffsetFixed&&s.offset===0&&!i?a+="Z":s.o<0?(a+="-",a+=I(Math.trunc(-s.o/60)),a+=":",a+=I(Math.trunc(-s.o%60))):(a+="+",a+=I(Math.trunc(s.o/60)),a+=":",a+=I(Math.trunc(s.o%60)))),i&&(a+="["+s.zone.ianaName+"]"),a}var Qr={month:1,day:1,hour:0,minute:0,second:0,millisecond:0},$i={weekNumber:1,weekday:1,hour:0,minute:0,second:0,millisecond:0},Zi={ordinal:1,hour:0,minute:0,second:0,millisecond:0},Xr=["year","month","day","hour","minute","second","millisecond"],Pi=["weekYear","weekNumber","weekday","hour","minute","second","millisecond"],qi=["year","ordinal","hour","minute","second","millisecond"];function zi(s){let e={year:"year",years:"year",month:"month",months:"month",day:"day",days:"day",hour:"hour",hours:"hour",minute:"minute",minutes:"minute",quarter:"quarter",quarters:"quarter",second:"second",seconds:"second",millisecond:"millisecond",milliseconds:"millisecond",weekday:"weekday",weekdays:"weekday",weeknumber:"weekNumber",weeksnumber:"weekNumber",weeknumbers:"weekNumber",weekyear:"weekYear",weekyears:"weekYear",ordinal:"ordinal"}[s.toLowerCase()];if(!e)throw new Fe(s);return e}function Yr(s){switch(s.toLowerCase()){case"localweekday":case"localweekdays":return"localWeekday";case"localweeknumber":case"localweeknumbers":return"localWeekNumber";case"localweekyear":case"localweekyears":return"localWeekYear";default:return zi(s)}}function Bi(s){return zt[s]||(qt===void 0&&(qt=E.now()),zt[s]=s.offset(qt)),zt[s]}function Gr(s,e){let t=K(e.zone,E.defaultZone);if(!t.isValid)return f.invalid(wt(t));let r=T.fromObject(e),n,i;if(m(s.year))n=E.now();else{for(let l of Xr)m(s[l])&&(s[l]=Qr[l]);let a=Es(s)||ws(s);if(a)return f.invalid(a);let o=Bi(t);[n,i]=Pt(s,o,t)}return new f({ts:n,zone:t,loc:r,o:i})}function Jr(s,e,t){let r=m(t.round)?!0:t.round,n=(a,o)=>(a=be(a,r||t.calendary?0:2,!0),e.loc.clone(t).relFormatter(t).format(a,o)),i=a=>t.calendary?e.hasSame(s,a)?0:e.startOf(a).diff(s.startOf(a),a).get(a):e.diff(s,a).get(a);if(t.unit)return n(i(t.unit),t.unit);for(let a of t.units){let o=i(a);if(Math.abs(o)>=1)return n(o,a)}return n(s>e?-0:0,t.units[t.units.length-1])}function jr(s){let e={},t;return s.length>0&&typeof s[s.length-1]=="object"?(e=s[s.length-1],t=Array.from(s).slice(0,s.length-1)):t=Array.from(s),[e,t]}var qt,zt={},f=class{constructor(e){let t=e.zone||E.defaultZone,r=e.invalid||(Number.isNaN(e.ts)?new _("invalid input"):null)||(t.isValid?null:wt(t));this.ts=m(e.ts)?E.now():e.ts;let n=null,i=null;if(!r)if(e.old&&e.old.ts===this.ts&&e.old.zone.equals(t))[n,i]=[e.old.c,e.old.o];else{let o=Q(e.o)&&!e.old?e.o:t.offset(this.ts);n=$t(this.ts,o),r=Number.isNaN(n.year)?new _("invalid input"):null,n=r?null:n,i=r?null:o}this._zone=t,this.loc=e.loc||T.create(),this.invalid=r,this.weekData=null,this.localWeekData=null,this.c=n,this.o=i,this.isLuxonDateTime=!0}static now(){return new f({})}static local(){let[e,t]=jr(arguments),[r,n,i,a,o,l,u]=t;return Gr({year:r,month:n,day:i,hour:a,minute:o,second:l,millisecond:u},e)}static utc(){let[e,t]=jr(arguments),[r,n,i,a,o,l,u]=t;return e.zone=O.utcInstance,Gr({year:r,month:n,day:i,hour:a,minute:o,second:l,millisecond:u},e)}static fromJSDate(e,t={}){let r=hr(e)?e.valueOf():NaN;if(Number.isNaN(r))return f.invalid("invalid input");let n=K(t.zone,E.defaultZone);return n.isValid?new f({ts:r,zone:n,loc:T.fromObject(t)}):f.invalid(wt(n))}static fromMillis(e,t={}){if(Q(e))return e<-qr||e>qr?f.invalid("Timestamp out of range"):new f({ts:e,zone:K(t.zone,E.defaultZone),loc:T.fromObject(t)});throw new D(`fromMillis requires a numerical input, but received a ${typeof e} with value ${e}`)}static fromSeconds(e,t={}){if(Q(e))return new f({ts:e*1e3,zone:K(t.zone,E.defaultZone),loc:T.fromObject(t)});throw new D("fromSeconds requires a numerical input")}static fromObject(e,t={}){e=e||{};let r=K(t.zone,E.defaultZone);if(!r.isValid)return f.invalid(wt(r));let n=T.fromObject(t),i=De(e,Yr),{minDaysInFirstWeek:a,startOfWeek:o}=Ts(i,n),l=E.now(),u=m(t.specificOffset)?r.offset(l):t.specificOffset,c=!m(i.ordinal),h=!m(i.year),y=!m(i.month)||!m(i.day),g=h||y,k=i.weekYear||i.weekNumber;if((g||c)&&k)throw new G("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(y&&c)throw new G("Can't mix ordinal dates with month/day");let S=k||i.weekday&&!g,F,b,M=$t(l,u);S?(F=Pi,b=$i,M=ht(M,a,o)):c?(F=qi,b=Zi,M=Ht(M)):(F=Xr,b=Qr);let Y=!1;for(let qe of F){let gn=i[qe];m(gn)?Y?i[qe]=b[qe]:i[qe]=M[qe]:Y=!0}let se=S?ur(i,a,o):c?cr(i):Es(i),Z=se||ws(i);if(Z)return f.invalid(Z);let re=S?ps(i,a,o):c?ys(i):i,[ne,V]=Pt(re,u,r),Pe=new f({ts:ne,zone:r,o:V,loc:n});return i.weekday&&g&&e.weekday!==Pe.weekday?f.invalid("mismatched weekday",`you can't specify both a weekday of ${i.weekday} and a date of ${Pe.toISO()}`):Pe.isValid?Pe:f.invalid(Pe.invalid)}static fromISO(e,t={}){let[r,n]=Ir(e);return He(r,n,t,"ISO 8601",e)}static fromRFC2822(e,t={}){let[r,n]=Or(e);return He(r,n,t,"RFC 2822",e)}static fromHTTP(e,t={}){let[r,n]=Cr(e);return He(r,n,t,"HTTP",t)}static fromFormat(e,t,r={}){if(m(e)||m(t))throw new D("fromFormat requires an input string and a format");let{locale:n=null,numberingSystem:i=null}=r,a=T.fromOpts({locale:n,numberingSystem:i,defaultToEN:!0}),[o,l,u,c]=Pr(a,e,t);return c?f.invalid(c):He(o,l,r,`format ${t}`,e,u)}static fromString(e,t,r={}){return f.fromFormat(e,t,r)}static fromSQL(e,t={}){let[r,n]=Nr(e);return He(r,n,t,"SQL",e)}static invalid(e,t=null){if(!e)throw new D("need to specify a reason the DateTime is invalid");let r=e instanceof _?e:new _(e,t);if(E.throwOnInvalid)throw new It(r);return new f({invalid:r})}static isDateTime(e){return e&&e.isLuxonDateTime||!1}static parseFormatForOpts(e,t={}){let r=Ns(e,T.fromObject(t));return r?r.map(n=>n?n.val:null).join(""):null}static expandFormat(e,t={}){return Ds(C.parseFormat(e),T.fromObject(t)).map(n=>n.val).join("")}static resetCache(){qt=void 0,zt={}}get(e){return this[e]}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}get outputCalendar(){return this.isValid?this.loc.outputCalendar:null}get zone(){return this._zone}get zoneName(){return this.isValid?this.zone.name:null}get year(){return this.isValid?this.c.year:NaN}get quarter(){return this.isValid?Math.ceil(this.c.month/3):NaN}get month(){return this.isValid?this.c.month:NaN}get day(){return this.isValid?this.c.day:NaN}get hour(){return this.isValid?this.c.hour:NaN}get minute(){return this.isValid?this.c.minute:NaN}get second(){return this.isValid?this.c.second:NaN}get millisecond(){return this.isValid?this.c.millisecond:NaN}get weekYear(){return this.isValid?As(this).weekYear:NaN}get weekNumber(){return this.isValid?As(this).weekNumber:NaN}get weekday(){return this.isValid?As(this).weekday:NaN}get isWeekend(){return this.isValid&&this.loc.getWeekendDays().includes(this.weekday)}get localWeekday(){return this.isValid?Ls(this).weekday:NaN}get localWeekNumber(){return this.isValid?Ls(this).weekNumber:NaN}get localWeekYear(){return this.isValid?Ls(this).weekYear:NaN}get ordinal(){return this.isValid?Ht(this.c).ordinal:NaN}get monthShort(){return this.isValid?oe.months("short",{locObj:this.loc})[this.month-1]:null}get monthLong(){return this.isValid?oe.months("long",{locObj:this.loc})[this.month-1]:null}get weekdayShort(){return this.isValid?oe.weekdays("short",{locObj:this.loc})[this.weekday-1]:null}get weekdayLong(){return this.isValid?oe.weekdays("long",{locObj:this.loc})[this.weekday-1]:null}get offset(){return this.isValid?+this.o:NaN}get offsetNameShort(){return this.isValid?this.zone.offsetName(this.ts,{format:"short",locale:this.locale}):null}get offsetNameLong(){return this.isValid?this.zone.offsetName(this.ts,{format:"long",locale:this.locale}):null}get isOffsetFixed(){return this.isValid?this.zone.isUniversal:null}get isInDST(){return this.isOffsetFixed?!1:this.offset>this.set({month:1,day:1}).offset||this.offset>this.set({month:5}).offset}getPossibleOffsets(){if(!this.isValid||this.isOffsetFixed)return[this];let e=864e5,t=6e4,r=ke(this.c),n=this.zone.offset(r-e),i=this.zone.offset(r+e),a=this.zone.offset(r-n*t),o=this.zone.offset(r-i*t);if(a===o)return[this];let l=r-a*t,u=r-o*t,c=$t(l,a),h=$t(u,o);return c.hour===h.hour&&c.minute===h.minute&&c.second===h.second&&c.millisecond===h.millisecond?[xe(this,{ts:l}),xe(this,{ts:u})]:[this]}get isInLeapYear(){return we(this.year)}get daysInMonth(){return Ce(this.year,this.month)}get daysInYear(){return this.isValid?he(this.year):NaN}get weeksInWeekYear(){return this.isValid?Ee(this.weekYear):NaN}get weeksInLocalWeekYear(){return this.isValid?Ee(this.localWeekYear,this.loc.getMinDaysInFirstWeek(),this.loc.getStartOfWeek()):NaN}resolvedLocaleOptions(e={}){let{locale:t,numberingSystem:r,calendar:n}=C.create(this.loc.clone(e),e).resolvedOptions(this);return{locale:t,numberingSystem:r,outputCalendar:n}}toUTC(e=0,t={}){return this.setZone(O.instance(e),t)}toLocal(){return this.setZone(E.defaultZone)}setZone(e,{keepLocalTime:t=!1,keepCalendarTime:r=!1}={}){if(e=K(e,E.defaultZone),e.equals(this.zone))return this;if(e.isValid){let n=this.ts;if(t||r){let i=e.offset(this.ts),a=this.toObject();[n]=Pt(a,i,e)}return xe(this,{ts:n,zone:e})}else return f.invalid(wt(e))}reconfigure({locale:e,numberingSystem:t,outputCalendar:r}={}){let n=this.loc.clone({locale:e,numberingSystem:t,outputCalendar:r});return xe(this,{loc:n})}setLocale(e){return this.reconfigure({locale:e})}set(e){if(!this.isValid)return this;let t=De(e,Yr),{minDaysInFirstWeek:r,startOfWeek:n}=Ts(t,this.loc),i=!m(t.weekYear)||!m(t.weekNumber)||!m(t.weekday),a=!m(t.ordinal),o=!m(t.year),l=!m(t.month)||!m(t.day),u=o||l,c=t.weekYear||t.weekNumber;if((u||a)&&c)throw new G("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(l&&a)throw new G("Can't mix ordinal dates with month/day");let h;i?h=ps({...ht(this.c,r,n),...t},r,n):m(t.ordinal)?(h={...this.toObject(),...t},m(t.day)&&(h.day=Math.min(Ce(h.year,h.month),h.day))):h=ys({...Ht(this.c),...t});let[y,g]=Pt(h,this.o,this.zone);return xe(this,{ts:y,o:g})}plus(e){if(!this.isValid)return this;let t=p.fromDurationLike(e);return xe(this,zr(this,t))}minus(e){if(!this.isValid)return this;let t=p.fromDurationLike(e).negate();return xe(this,zr(this,t))}startOf(e,{useLocaleWeeks:t=!1}={}){if(!this.isValid)return this;let r={},n=p.normalizeUnit(e);switch(n){case"years":r.month=1;case"quarters":case"months":r.day=1;case"weeks":case"days":r.hour=0;case"hours":r.minute=0;case"minutes":r.second=0;case"seconds":r.millisecond=0;break;case"milliseconds":break}if(n==="weeks")if(t){let i=this.loc.getStartOfWeek(),{weekday:a}=this;a<i&&(r.weekNumber=this.weekNumber-1),r.weekday=i}else r.weekday=1;if(n==="quarters"){let i=Math.ceil(this.month/3);r.month=(i-1)*3+1}return this.set(r)}endOf(e,t){return this.isValid?this.plus({[e]:1}).startOf(e,t).minus(1):this}toFormat(e,t={}){return this.isValid?C.create(this.loc.redefaultToEN(t)).formatDateTimeFromString(this,e):_s}toLocaleString(e=ce,t={}){return this.isValid?C.create(this.loc.clone(t),e).formatDateTime(this):_s}toLocaleParts(e={}){return this.isValid?C.create(this.loc.clone(e),e).formatDateTimeParts(this):[]}toISO({format:e="extended",suppressSeconds:t=!1,suppressMilliseconds:r=!1,includeOffset:n=!0,extendedZone:i=!1}={}){if(!this.isValid)return null;let a=e==="extended",o=Vs(this,a);return o+="T",o+=Br(this,a,t,r,n,i),o}toISODate({format:e="extended"}={}){return this.isValid?Vs(this,e==="extended"):null}toISOWeekDate(){return Zt(this,"kkkk-'W'WW-c")}toISOTime({suppressMilliseconds:e=!1,suppressSeconds:t=!1,includeOffset:r=!0,includePrefix:n=!1,extendedZone:i=!1,format:a="extended"}={}){return this.isValid?(n?"T":"")+Br(this,a==="extended",t,e,r,i):null}toRFC2822(){return Zt(this,"EEE, dd LLL yyyy HH:mm:ss ZZZ",!1)}toHTTP(){return Zt(this.toUTC(),"EEE, dd LLL yyyy HH:mm:ss 'GMT'")}toSQLDate(){return this.isValid?Vs(this,!0):null}toSQLTime({includeOffset:e=!0,includeZone:t=!1,includeOffsetSpace:r=!0}={}){let n="HH:mm:ss.SSS";return(t||e)&&(r&&(n+=" "),t?n+="z":e&&(n+="ZZ")),Zt(this,n,!0)}toSQL(e={}){return this.isValid?`${this.toSQLDate()} ${this.toSQLTime(e)}`:null}toString(){return this.isValid?this.toISO():_s}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`DateTime { ts: ${this.toISO()}, zone: ${this.zone.name}, locale: ${this.locale} }`:`DateTime { Invalid, reason: ${this.invalidReason} }`}valueOf(){return this.toMillis()}toMillis(){return this.isValid?this.ts:NaN}toSeconds(){return this.isValid?this.ts/1e3:NaN}toUnixInteger(){return this.isValid?Math.floor(this.ts/1e3):NaN}toJSON(){return this.toISO()}toBSON(){return this.toJSDate()}toObject(e={}){if(!this.isValid)return{};let t={...this.c};return e.includeConfig&&(t.outputCalendar=this.outputCalendar,t.numberingSystem=this.loc.numberingSystem,t.locale=this.loc.locale),t}toJSDate(){return new Date(this.isValid?this.ts:NaN)}diff(e,t="milliseconds",r={}){if(!this.isValid||!e.isValid)return p.invalid("created by diffing an invalid DateTime");let n={locale:this.locale,numberingSystem:this.numberingSystem,...r},i=fr(t).map(p.normalizeUnit),a=e.valueOf()>this.valueOf(),o=a?this:e,l=a?e:this,u=Rr(o,l,i,n);return a?u.negate():u}diffNow(e="milliseconds",t={}){return this.diff(f.now(),e,t)}until(e){return this.isValid?v.fromDateTimes(this,e):this}hasSame(e,t,r){if(!this.isValid)return!1;let n=e.valueOf(),i=this.setZone(e.zone,{keepLocalTime:!0});return i.startOf(t,r)<=n&&n<=i.endOf(t,r)}equals(e){return this.isValid&&e.isValid&&this.valueOf()===e.valueOf()&&this.zone.equals(e.zone)&&this.loc.equals(e.loc)}toRelative(e={}){if(!this.isValid)return null;let t=e.base||f.fromObject({},{zone:this.zone}),r=e.padding?this<t?-e.padding:e.padding:0,n=["years","months","days","hours","minutes","seconds"],i=e.unit;return Array.isArray(e.unit)&&(n=e.unit,i=void 0),Jr(t,this.plus(r),{...e,numeric:"always",units:n,unit:i})}toRelativeCalendar(e={}){return this.isValid?Jr(e.base||f.fromObject({},{zone:this.zone}),this,{...e,numeric:"auto",units:["years","months","days"],calendary:!0}):null}static min(...e){if(!e.every(f.isDateTime))throw new D("min requires all arguments be DateTimes");return Ss(e,t=>t.valueOf(),Math.min)}static max(...e){if(!e.every(f.isDateTime))throw new D("max requires all arguments be DateTimes");return Ss(e,t=>t.valueOf(),Math.max)}static fromFormatExplain(e,t,r={}){let{locale:n=null,numberingSystem:i=null}=r,a=T.fromOpts({locale:n,numberingSystem:i,defaultToEN:!0});return Ms(a,e,t)}static fromStringExplain(e,t,r={}){return f.fromFormatExplain(e,t,r)}static buildFormatParser(e,t={}){let{locale:r=null,numberingSystem:n=null}=t,i=T.fromOpts({locale:r,numberingSystem:n,defaultToEN:!0});return new Et(i,e)}static fromFormatParser(e,t,r={}){if(m(e)||m(t))throw new D("fromFormatParser requires an input string and a format parser");let{locale:n=null,numberingSystem:i=null}=r,a=T.fromOpts({locale:n,numberingSystem:i,defaultToEN:!0});if(!a.equals(t.locale))throw new D(`fromFormatParser called with a locale of ${a}, but the format parser was created for ${t.locale}`);let{result:o,zone:l,specificOffset:u,invalidReason:c}=t.explainFromTokens(e);return c?f.invalid(c):He(o,l,r,`format ${t.format}`,e,u)}static get DATE_SHORT(){return ce}static get DATE_MED(){return ze}static get DATE_MED_WITH_WEEKDAY(){return Xt}static get DATE_FULL(){return Be}static get DATE_HUGE(){return Ye}static get TIME_SIMPLE(){return Ge}static get TIME_WITH_SECONDS(){return Je}static get TIME_WITH_SHORT_OFFSET(){return je}static get TIME_WITH_LONG_OFFSET(){return Ke}static get TIME_24_SIMPLE(){return Qe}static get TIME_24_WITH_SECONDS(){return Xe}static get TIME_24_WITH_SHORT_OFFSET(){return et}static get TIME_24_WITH_LONG_OFFSET(){return tt}static get DATETIME_SHORT(){return st}static get DATETIME_SHORT_WITH_SECONDS(){return rt}static get DATETIME_MED(){return nt}static get DATETIME_MED_WITH_SECONDS(){return it}static get DATETIME_MED_WITH_WEEKDAY(){return es}static get DATETIME_FULL(){return at}static get DATETIME_FULL_WITH_SECONDS(){return ot}static get DATETIME_HUGE(){return lt}static get DATETIME_HUGE_WITH_SECONDS(){return ut}};function Re(s){if(f.isDateTime(s))return s;if(s&&s.valueOf&&Q(s.valueOf()))return f.fromJSDate(s);if(s&&typeof s=="object")return f.fromObject(s);throw new D(`Unknown datetime argument: ${s}, of type ${typeof s}`)}var en=require("obsidian"),Yi=/[<>:"/\\|?*\u0000-\u001F]/g;var Gi="-",Ji=s=>s.replace(Yi,Gi);var tn=s=>{let e=Ji(s).trim();return(0,en.normalizePath)(e)},te=(s,e="yyyy-MM-dd HH:mm:ss")=>{if(!s)return"";try{let t=f.fromISO(s);return t.isValid?t.toFormat(e):s}catch(t){return console.error("Error formatting date:",t),s}};var ji=Object.prototype.toString,$e=Array.isArray||function(e){return ji.call(e)==="[object Array]"};function Rs(s){return typeof s=="function"}function Ki(s){return $e(s)?"array":typeof s}function Ws(s){return s.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function sn(s,e){return s!=null&&typeof s=="object"&&e in s}function Qi(s,e){return s!=null&&typeof s!="object"&&s.hasOwnProperty&&s.hasOwnProperty(e)}var Xi=RegExp.prototype.test;function ea(s,e){return Xi.call(s,e)}var ta=/\S/;function sa(s){return!ea(ta,s)}var ra={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;","`":"&#x60;","=":"&#x3D;"};function na(s){return String(s).replace(/[&<>"'`=\/]/g,function(t){return ra[t]})}var ia=/\s*/,aa=/\s+/,rn=/\s*=/,oa=/\s*\}/,la=/#|\^|\/|>|\{|&|=|!/;function ua(s,e){if(!s)return[];var t=!1,r=[],n=[],i=[],a=!1,o=!1,l="",u=0;function c(){if(a&&!o)for(;i.length;)delete n[i.pop()];else i=[];a=!1,o=!1}var h,y,g;function k(V){if(typeof V=="string"&&(V=V.split(aa,2)),!$e(V)||V.length!==2)throw new Error("Invalid tags: "+V);h=new RegExp(Ws(V[0])+"\\s*"),y=new RegExp("\\s*"+Ws(V[1])),g=new RegExp("\\s*"+Ws("}"+V[1]))}k(e||B.tags);for(var S=new xt(s),F,b,M,Y,se,Z;!S.eos();){if(F=S.pos,M=S.scanUntil(h),M)for(var re=0,ne=M.length;re<ne;++re)Y=M.charAt(re),sa(Y)?(i.push(n.length),l+=Y):(o=!0,t=!0,l+=" "),n.push(["text",Y,F,F+1]),F+=1,Y===`
`&&(c(),l="",u=0,t=!1);if(!S.scan(h))break;if(a=!0,b=S.scan(la)||"name",S.scan(ia),b==="="?(M=S.scanUntil(rn),S.scan(rn),S.scanUntil(y)):b==="{"?(M=S.scanUntil(g),S.scan(oa),S.scanUntil(y),b="&"):M=S.scanUntil(y),!S.scan(y))throw new Error("Unclosed tag at "+S.pos);if(b==">"?se=[b,M,F,S.pos,l,u,t]:se=[b,M,F,S.pos],u++,n.push(se),b==="#"||b==="^")r.push(se);else if(b==="/"){if(Z=r.pop(),!Z)throw new Error('Unopened section "'+M+'" at '+F);if(Z[1]!==M)throw new Error('Unclosed section "'+Z[1]+'" at '+F)}else b==="name"||b==="{"||b==="&"?o=!0:b==="="&&k(M)}if(c(),Z=r.pop(),Z)throw new Error('Unclosed section "'+Z[1]+'" at '+S.pos);return da(ca(n))}function ca(s){for(var e=[],t,r,n=0,i=s.length;n<i;++n)t=s[n],t&&(t[0]==="text"&&r&&r[0]==="text"?(r[1]+=t[1],r[3]=t[3]):(e.push(t),r=t));return e}function da(s){for(var e=[],t=e,r=[],n,i,a=0,o=s.length;a<o;++a)switch(n=s[a],n[0]){case"#":case"^":t.push(n),r.push(n),t=n[4]=[];break;case"/":i=r.pop(),i[5]=n[2],t=r.length>0?r[r.length-1][4]:e;break;default:t.push(n)}return e}function xt(s){this.string=s,this.tail=s,this.pos=0}xt.prototype.eos=function(){return this.tail===""};xt.prototype.scan=function(e){var t=this.tail.match(e);if(!t||t.index!==0)return"";var r=t[0];return this.tail=this.tail.substring(r.length),this.pos+=r.length,r};xt.prototype.scanUntil=function(e){var t=this.tail.search(e),r;switch(t){case-1:r=this.tail,this.tail="";break;case 0:r="";break;default:r=this.tail.substring(0,t),this.tail=this.tail.substring(t)}return this.pos+=r.length,r};function Ue(s,e){this.view=s,this.cache={".":this.view},this.parent=e}Ue.prototype.push=function(e){return new Ue(e,this)};Ue.prototype.lookup=function(e){var t=this.cache,r;if(t.hasOwnProperty(e))r=t[e];else{for(var n=this,i,a,o,l=!1;n;){if(e.indexOf(".")>0)for(i=n.view,a=e.split("."),o=0;i!=null&&o<a.length;)o===a.length-1&&(l=sn(i,a[o])||Qi(i,a[o])),i=i[a[o++]];else i=n.view[e],l=sn(n.view,e);if(l){r=i;break}n=n.parent}t[e]=r}return Rs(r)&&(r=r.call(this.view)),r};function L(){this.templateCache={_cache:{},set:function(e,t){this._cache[e]=t},get:function(e){return this._cache[e]},clear:function(){this._cache={}}}}L.prototype.clearCache=function(){typeof this.templateCache!="undefined"&&this.templateCache.clear()};L.prototype.parse=function(e,t){var r=this.templateCache,n=e+":"+(t||B.tags).join(":"),i=typeof r!="undefined",a=i?r.get(n):void 0;return a==null&&(a=ua(e,t),i&&r.set(n,a)),a};L.prototype.render=function(e,t,r,n){var i=this.getConfigTags(n),a=this.parse(e,i),o=t instanceof Ue?t:new Ue(t,void 0);return this.renderTokens(a,o,r,e,n)};L.prototype.renderTokens=function(e,t,r,n,i){for(var a="",o,l,u,c=0,h=e.length;c<h;++c)u=void 0,o=e[c],l=o[0],l==="#"?u=this.renderSection(o,t,r,n,i):l==="^"?u=this.renderInverted(o,t,r,n,i):l===">"?u=this.renderPartial(o,t,r,i):l==="&"?u=this.unescapedValue(o,t):l==="name"?u=this.escapedValue(o,t,i):l==="text"&&(u=this.rawValue(o)),u!==void 0&&(a+=u);return a};L.prototype.renderSection=function(e,t,r,n,i){var a=this,o="",l=t.lookup(e[1]);function u(y){return a.render(y,t,r,i)}if(l){if($e(l))for(var c=0,h=l.length;c<h;++c)o+=this.renderTokens(e[4],t.push(l[c]),r,n,i);else if(typeof l=="object"||typeof l=="string"||typeof l=="number")o+=this.renderTokens(e[4],t.push(l),r,n,i);else if(Rs(l)){if(typeof n!="string")throw new Error("Cannot use higher-order sections without the original template");l=l.call(t.view,n.slice(e[3],e[5]),u),l!=null&&(o+=l)}else o+=this.renderTokens(e[4],t,r,n,i);return o}};L.prototype.renderInverted=function(e,t,r,n,i){var a=t.lookup(e[1]);if(!a||$e(a)&&a.length===0)return this.renderTokens(e[4],t,r,n,i)};L.prototype.indentPartial=function(e,t,r){for(var n=t.replace(/[^ \t]/g,""),i=e.split(`
`),a=0;a<i.length;a++)i[a].length&&(a>0||!r)&&(i[a]=n+i[a]);return i.join(`
`)};L.prototype.renderPartial=function(e,t,r,n){if(r){var i=this.getConfigTags(n),a=Rs(r)?r(e[1]):r[e[1]];if(a!=null){var o=e[6],l=e[5],u=e[4],c=a;l==0&&u&&(c=this.indentPartial(a,u,o));var h=this.parse(c,i);return this.renderTokens(h,t,r,c,n)}}};L.prototype.unescapedValue=function(e,t){var r=t.lookup(e[1]);if(r!=null)return r};L.prototype.escapedValue=function(e,t,r){var n=this.getConfigEscape(r)||B.escape,i=t.lookup(e[1]);if(i!=null)return typeof i=="number"&&n===B.escape?String(i):n(i)};L.prototype.rawValue=function(e){return e[1]};L.prototype.getConfigTags=function(e){return $e(e)?e:e&&typeof e=="object"?e.tags:void 0};L.prototype.getConfigEscape=function(e){if(e&&typeof e=="object"&&!$e(e))return e.escape};var B={name:"mustache.js",version:"4.2.0",tags:["{{","}}"],clearCache:void 0,escape:void 0,parse:void 0,render:void 0,Scanner:void 0,Context:void 0,Writer:void 0,set templateCache(s){St.templateCache=s},get templateCache(){return St.templateCache}},St=new L;B.clearCache=function(){return St.clearCache()};B.parse=function(e,t){return St.parse(e,t)};B.render=function(e,t,r,n){if(typeof e!="string")throw new TypeError('Invalid template! Template should be a "string" but "'+Ki(e)+'" was given as the first argument for mustache#render(template, view, partials)');return St.render(e,t,r,n)};B.escape=na;B.Scanner=xt;B.Context=Ue;B.Writer=L;var Hs=B;var Us=require("obsidian"),nn=["title","article_title","tags","create_time","update_time","domain","url","cubox_url","description","description","words_count","type","words_count","id"],Bt=class{constructor(){this.dateFormat="yyyy-MM-dd"}setDateFormat(e){this.dateFormat=e}processFilenameTemplate(e,t){if(!e)return t.title||"Untitled";let r={title:t.title||"",article_title:t.article_title||"",create_time:t.create_time?te(t.create_time,this.dateFormat):"",update_time:t.update_time?te(t.update_time,this.dateFormat):"",domain:t.domain||"",type:t.type||"",id:t.id||""},n="";try{n=Hs.render(e,r)}catch(a){console.error("\u6A21\u677F\u6E32\u67D3\u5931\u8D25:",a),n=t.title||"Untitled"}let i=100;return n.length>i&&(n=n.substring(0,i)),tn(n)}processFrontMatter(e,t){let r={id:t.id};if(e.length===0)return(0,Us.stringifyYaml)(r);for(let n of e){let i=n.split("::"),a=i[0],o=i.length>1?i[1]:a;if(a==="tags"&&t.tags&&t.tags.length>0){r[o]=t.tags;continue}if(a==="create_time"&&t.create_time){r[o]=te(t.create_time,this.dateFormat);continue}if(a==="update_time"&&t.update_time){r[o]=te(t.update_time,this.dateFormat);continue}let l=this.getArticleProperty(t,a);l&&(r[o]=l)}return(0,Us.stringifyYaml)(r)}processContentTemplate(e,t){if(!e){let n="";return t.content&&(n+=t.content+`

`),t.highlights&&t.highlights.length>0&&(n+=`## Highlights

`,t.highlights.forEach(i=>{n+=`- ${i.text}
`})),n}let r=this.createArticleView(t);return e.includes("content_highlighted")&&t.content&&t.highlights&&t.highlights.length>0&&(r.content_highlighted=this.generateHighlightedContent(t.content,t.highlights)),Hs.render(e,r)}createArticleView(e){var r;let t={...e,highlights:[],tags:e.tags||[],highlights_length:((r=e.highlights)==null?void 0:r.length)||0,create_time:e.create_time?te(e.create_time,this.dateFormat):"",update_time:e.update_time?te(e.update_time,this.dateFormat):""};return e.highlights&&e.highlights.length>0&&(t.highlights=e.highlights.map(n=>this.createHighlightView(n))),t.highlights.length===0?t.highlights_length=0:t.highlights_length=t.highlights.length,t}createHighlightView(e){let t=e.text||"";!t&&e.image_url?t=`![](${e.image_url})`:t.includes(`
`)&&(t=t.split(`
`).map((o,l)=>l===0?o:o.trim()?"> "+o:">").join(`
`));let r=e.note&&e.note.trim()?e.note.trim()+`
`:"",n={...e,text:t,note:r,create_time:e.create_time?te(e.create_time,this.dateFormat):""};return n.cubox_url||(n.cubox_url=""),n}generateHighlightedContent(e,t){if(!e||!t||t.length===0)return e||"";let r=e.length,n=new Array(r).fill(!1);for(let o of t){let l=o.text;if(!l)continue;let u=0,c;for(;(c=e.indexOf(l,u))!==-1;){for(let h=0;h<l.length;h++)n[c+h]=!0;u=c+1}}let i="",a=!1;for(let o=0;o<r;o++){let l=n[o];l&&!a&&(i+="==",a=!0),!l&&a&&(i+="==",a=!1),i+=e[o]}return a&&(i+="=="),i}getArticleProperty(e,t){return e[t]}needsArticleContent(e){return e?e.includes("{{content}}")||e.includes("{{{content}}}")||e.includes("{{content_highlighted}}")||e.includes("{{{content_highlighted}}}"):!0}};var x=require("obsidian");var $s=(s,e)=>{if(s==="cubox.pro")switch(e){case"filename":return"https://help.cubox.pro/share/obplugin/#%E6%96%87%E4%BB%B6%E5%90%8D%E6%A8%A1%E6%9D%BF";case"metadata":return"https://help.cubox.pro/share/obplugin/#%E5%85%83%E5%B1%9E%E6%80%A7";case"content":return"https://help.cubox.pro/share/obplugin/#%E5%86%85%E5%AE%B9%E6%A8%A1%E6%9D%BF";case"date":return"https://help.cubox.pro/share/obplugin/#%E6%97%A5%E6%9C%9F%E6%A0%BC%E5%BC%8F";default:return"#"}else if(s==="cubox.cc")switch(e){case"filename":return"https://help.cubox.cc/share/obplugin/#filename-template";case"metadata":return"https://help.cubox.cc/share/obplugin/#metadata";case"content":return"https://help.cubox.cc/share/obplugin/#content-template";case"date":return"https://help.cubox.cc/share/obplugin/#date-format";default:return"#"}return"#"},Yt=(s,e)=>s?`<div class="cubox-reference">For more, refer to <a href="${$s(s,e)}" class="reference-link" target="_blank">reference</a>.</div>`:'<div class="cubox-reference">For more, refer to <a href="#" class="reference-link">reference</a>.</div>',an=s=>Yt(s,"filename"),on=s=>Yt(s,"metadata"),ln=s=>Yt(s,"content"),un=s=>Yt(s,"date"),cn=`
Enter template for creating synced article file name.

<div class="cubox-variables-container">
    <div class="cubox-variables-title">Available variables</div>
    <ul class="cubox-variables-list">
        <li>{{{title}}}</li>
        <li>{{{article_title}}}</li>
        <li>{{{create_time}}}</li>
        <li>{{{update_time}}}</li>
        <li>{{{domain}}}</li>
        <li>{{{type}}}</li>
    </ul>
    <div class="cubox-reference domain-reference-filename"></div>
</div>
`,dn=`
Enter the metadata separated by comma. you can also use custom aliases in the format of metadata::alias. For syncing purposes, the id will always be included.

<div class="cubox-variables-container">
    <div class="cubox-variables-title">Available variables</div>
    <ul class="cubox-variables-list">
        <li>title</li>
        <li>article_title</li>
        <li>tags</li>
        <li>create_time</li>
        <li>update_time</li>
        <li>domain</li>
        <li>url</li>
        <li>cubox_url</li>
        <li>description</li>
        <li>words_count</li>
        <li>type</li>
    </ul>
    <div class="cubox-reference domain-reference-metadata"></div>
</div>
`,hn=`
Enter template for creating synced article content.

<div class="cubox-variables-container">
    <div class="cubox-variables-title">Available variables</div>
    <ul class="cubox-variables-list">
        <li>{{{id}}}</li>
        <li>{{{title}}}</li>
        <li>{{{description}}}</li>
        <li>{{{article_title}}}</li>
        <li>{{{content}}}</li>
        <li>{{{content_highlighted}}}</li>
        <li>{{{highlights}}}</li>
        <li class="highlight-item">
            <ul class="highlight-sublist">
                <li>{{{text}}}</li>
                <li>{{{image_url}}}</li>
                <li>{{{cubox_url}}}</li>
                <li>{{{note}}}</li>
                <li>{{{color}}}</li>
                <li>{{{create_time}}}</li>
            </ul>
        </li>
        <li>{{{tags}}}</li>
        <li>{{{create_time}}}</li>
        <li>{{{update_time}}}</li>
        <li>{{{domain}}}</li>
        <li>{{{url}}}</li>
        <li>{{{cubox_url}}}</li>
        <li>{{{words_count}}}</li>
    </ul>
    <div class="cubox-reference domain-reference-content"></div>
</div>
`,fn=`
If date is used on above templates, enter the format date.

<div class="cubox-variables-container">
    <div class="cubox-variables-title">Example</div>
    <ul class="cubox-variables-list">
        <li>yyyy-MM-dd</li>
        <li>MM/dd/yyyy</li>
        <li>dd.MM.yyyy</li>
        <li>yyyy-MM-dd HH:mm:ss</li>
    </ul>
    <div class="cubox-reference domain-reference-date"></div>
</div>
`;var Ze=require("obsidian"),Zs=["Article","Snippet","Memo","Image","Audio","Video","File"],Gt=class extends Ze.Modal{constructor(t,r=[],n){super(t);this.selectedTypes=new Set;this.onSave=n,r&&r.length>0&&r.forEach(i=>{i&&this.selectedTypes.add(i)})}onOpen(){let{contentEl:t}=this;t.empty(),t.createEl("h2",{text:"Manage Cubox content types to be synced"}),t.addClass("cubox-modal"),this.listEl=t.createDiv({cls:"type-list-container cubox-list-container"}),this.footerEl=t.createDiv({cls:"modal-footer"}),this.createTypeList(),this.footerEl.createEl("button",{text:"Cancel"}).addEventListener("click",()=>{this.close()}),this.footerEl.createEl("button",{text:"Done",cls:"mod-cta"}).addEventListener("click",()=>{if(this.selectedTypes.size===0){new Ze.Notice("Please select at least one option.");return}let i=Array.from(this.selectedTypes);this.onSave(i),this.close()})}createTypeList(){this.listEl.empty(),Zs.forEach(t=>{let r=new Ze.Setting(this.listEl).setName(t);this.selectedTypes.has(t)&&r.settingEl.addClass("is-selected"),r.settingEl.addEventListener("click",()=>{let n=this.selectedTypes.has(t);this.handleTypeToggle(t,!n),this.redraw()})})}handleTypeToggle(t,r){r?this.selectedTypes.add(t):this.selectedTypes.delete(t)}redraw(){this.createTypeList()}onClose(){let{contentEl:t}=this;t.empty()}};var mn=require("obsidian"),ha=`# {{{title}}}

{{{description}}}

[Read in Cubox]({{{cubox_url}}})  
[Read Original]({{{url}}})  

---

{{#highlights.length}}
## Annotations  

{{#highlights}}
> {{{text}}}  

{{#note}}
{{{note}}}
{{/note}}
[Link\uFE0F]({{{cubox_url}}})  

{{/highlights}}
{{/highlights.length}}`,pe={domain:"",apiKey:"",folderFilter:[],typeFilter:[],statusFilter:[],isRead:!0,isStarred:!0,isAnnotated:!0,tagsFilter:[],syncFrequency:30,targetFolder:"Cubox",filenameTemplate:"{{{title}}}-{{{create_time}}}",frontMatterVariables:["id","cubox_url","url","tags"],contentTemplate:ha,highlightInContent:!0,dateFormat:"yyyy-MM-dd",lastSyncTime:0,lastSyncCardId:null,lastCardUpdateTime:null,syncing:!1},Jt=class extends x.PluginSettingTab{constructor(t,r){super(t,r);this.plugin=r}display(){let{containerEl:t}=this;t.empty(),new x.Setting(t).setName("Cubox server domain").setDesc("Select the correct domain name of the Cubox you are using.").addDropdown(o=>o.addOption("","Choose region").addOption("cubox.cc","cubox.cc (international)").addOption("cubox.pro","cubox.pro").setValue(this.plugin.settings.domain).onChange(async l=>{this.plugin.settings.domain=l,await this.plugin.saveSettings(),this.updateApiKeySetting(),this.plugin.updateCuboxApiConfig(l,this.plugin.settings.apiKey),this.updateHelpLinks(l)})),this.apiKeySetting=new x.Setting(t).setName("Your Cubox API key").setDesc("Please select a region first").addText(o=>(o.setPlaceholder("Enter your API key").setValue(this.plugin.settings.apiKey).onChange(async l=>{let u=l.trim();if(u.includes("://"))try{let c=u.includes("://")?new URL(u):null;if(c){let h=c.pathname.split("/").filter(y=>y.length>0);h.length>0&&(u=h[h.length-1])}o.setValue(u)}catch(c){new x.Notice("Invalid API key")}this.plugin.settings.apiKey=u,await this.plugin.saveSettings(),this.plugin.updateCuboxApiConfig(this.plugin.settings.domain,u)}),this.plugin.settings.domain||(o.inputEl.disabled=!0),o)),this.updateApiKeySetting(),new x.Setting(t).setName("Filter").setHeading(),new x.Setting(t).setName("Folder filter").setDesc("Manage Cubox folders to be synced").addButton(o=>o.setButtonText(this.getFolderFilterButtonText()).setCta().onClick(async()=>{if(!this.plugin.settings.apiKey){new x.Notice("Please enter the API key first");return}o.setButtonText("Loading folders...");try{let l=await this.plugin.cuboxApi.getFolders();new vt(this.app,l,this.plugin.settings.folderFilter,async c=>{this.plugin.settings.folderFilter=c,await this.plugin.saveSettings(),o.setButtonText(this.getFolderFilterButtonText())}).open()}catch(l){console.error("\u83B7\u53D6\u6587\u4EF6\u5939\u5217\u8868\u5931\u8D25:",l),new x.Notice("Failed to get Cubox folders"),o.setButtonText(this.getFolderFilterButtonText())}o.setButtonText(this.getFolderFilterButtonText())})),new x.Setting(t).setName("Tag filter").setDesc("Manage Cubox tags to be synced").addButton(o=>o.setButtonText(this.getTagFilterButtonText()).setCta().onClick(async()=>{if(!this.plugin.settings.apiKey){new x.Notice("Please enter the API key first");return}o.setButtonText("Loading tags...");try{let l=await this.plugin.cuboxApi.getTags();new Ft(this.app,l,this.plugin.settings.tagsFilter,async c=>{this.plugin.settings.tagsFilter=c,await this.plugin.saveSettings(),o.setButtonText(this.getTagFilterButtonText())}).open()}catch(l){console.error("\u83B7\u53D6\u6807\u7B7E\u5217\u8868\u5931\u8D25:",l),new x.Notice("Failed to get Cubox tags"),o.setButtonText(this.getTagFilterButtonText())}o.setButtonText(this.getTagFilterButtonText())})),new x.Setting(t).setName("Type filter").setDesc("Manage Cubox content types to be synced").addButton(o=>o.setButtonText(this.getTypeFilterButtonText()).setCta().onClick(async()=>{if(!this.plugin.settings.apiKey){new x.Notice("Please enter the API key first");return}new Gt(this.app,this.plugin.settings.typeFilter,async u=>{this.plugin.settings.typeFilter=u,await this.plugin.saveSettings(),o.setButtonText(this.getTypeFilterButtonText())}).open()})),new x.Setting(t).setName("Status filter").setDesc("Manage Cubox content status to be synced").addButton(o=>o.setButtonText(this.getStatusFilterButtonText()).setCta().onClick(async()=>{if(!this.plugin.settings.apiKey){new x.Notice("Please enter the API key first");return}let l={read:this.plugin.settings.isRead,starred:this.plugin.settings.isStarred,annotated:this.plugin.settings.isAnnotated};new kt(this.app,this.plugin.settings.statusFilter,l,async(c,h)=>{this.plugin.settings.statusFilter=c,this.plugin.settings.isRead=h.read,this.plugin.settings.isStarred=h.starred,this.plugin.settings.isAnnotated=h.annotated,await this.plugin.saveSettings(),o.setButtonText(this.getStatusFilterButtonText())}).open()})),new x.Setting(t).setName("Sync").setHeading(),new x.Setting(t).setName("Sync interval").setDesc("Auto sync interval (in minutes). 0 means manual sync. Each item syncs only once. Subsequent updates won't be synced, and modifications in Obsidian won't affect Cubox. We recommend avoiding frequent updates.").addText(o=>o.setPlaceholder("Enter the interval").setValue(String(this.plugin.settings.syncFrequency)).onChange(async u=>{let c=parseInt(u);if(isNaN(c)){new x.Notice("Frequency must be a positive integer");return}c=Math.min(c,9999),this.plugin.settings.syncFrequency=c,await this.plugin.saveSettings(),this.plugin.setupAutoSync(),o.setValue(String(c))})),new x.Setting(t).setName("Folder").setDesc("Select the folder you'd like to sync to").addText(o=>o.setPlaceholder("Enter target folder path").setValue(this.plugin.settings.targetFolder).onChange(async l=>{this.plugin.settings.lastSyncCardId=null,this.plugin.settings.targetFolder=(0,mn.normalizePath)(l),await this.plugin.saveSettings()}));let r=document.createRange().createContextualFragment(cn);new x.Setting(t).setName("File name template").setDesc(r).addText(o=>o.setPlaceholder("Enter file name template").setValue(this.plugin.settings.filenameTemplate).onChange(async l=>{this.plugin.settings.filenameTemplate=l,await this.plugin.saveSettings()})).addExtraButton(o=>o.setIcon("reset").setTooltip("Reset to default").onClick(async()=>{var u,c;this.plugin.settings.filenameTemplate=pe.filenameTemplate,await this.plugin.saveSettings();let l=(c=(u=o.extraSettingsEl.parentElement)==null?void 0:u.parentElement)==null?void 0:c.querySelector("input");l&&(l.value=pe.filenameTemplate)}));let n=document.createRange().createContextualFragment(dn);new x.Setting(t).setName("Metadata variables").setDesc(n).addTextArea(o=>o.setPlaceholder("Enter front matter variables").setValue(this.plugin.settings.frontMatterVariables.join(",")).onChange(async l=>{this.plugin.settings.frontMatterVariables=l.split(",").map(u=>u.trim()).filter((u,c,h)=>nn.includes(u.split("::")[0])&&h.indexOf(u)===c),await this.plugin.saveSettings()}).then(l=>{l.inputEl.rows=20,l.inputEl.cols=30})).addExtraButton(o=>{o.setIcon("reset").setTooltip("Reset to default").onClick(async()=>{var u,c;this.plugin.settings.frontMatterVariables=pe.frontMatterVariables,await this.plugin.saveSettings();let l=(c=(u=o.extraSettingsEl.parentElement)==null?void 0:u.parentElement)==null?void 0:c.querySelector("textarea");l&&(l.value=pe.frontMatterVariables.join(","))})});let i=document.createRange().createContextualFragment(hn);new x.Setting(t).setName("Content template").setDesc(i).addTextArea(o=>o.setPlaceholder("Enter content template").setValue(this.plugin.settings.contentTemplate).onChange(async l=>{this.plugin.settings.contentTemplate=l,await this.plugin.saveSettings()}).then(l=>{l.inputEl.rows=24,l.inputEl.cols=30})).addExtraButton(o=>{o.setIcon("reset").setTooltip("Reset to default").onClick(async()=>{var u,c;this.plugin.settings.contentTemplate=pe.contentTemplate,await this.plugin.saveSettings();let l=(c=(u=o.extraSettingsEl.parentElement)==null?void 0:u.parentElement)==null?void 0:c.querySelector("textarea");l&&(l.value=pe.contentTemplate)})});let a=document.createRange().createContextualFragment(fn);new x.Setting(t).setName("Date format").setDesc(a).addText(o=>o.setPlaceholder("Enter date format").setValue(this.plugin.settings.dateFormat).onChange(async l=>{this.plugin.settings.dateFormat=l,await this.plugin.saveSettings(),this.plugin.updateTemplateProcessorDateFormat(l)})),new x.Setting(t).setName("Status").setHeading(),new x.Setting(t).setDesc(`Last sync: ${this.plugin.formatLastSyncTime()}`),setTimeout(()=>{this.updateHelpLinks(this.plugin.settings.domain)},100)}updateApiKeySetting(){let t=this.plugin.settings.domain,r=this.apiKeySetting.components[0];if(!t)this.apiKeySetting.setDesc("Please select a region first"),r.inputEl.disabled=!0;else{let n=`https://${t}/my/settings/extensions`,i=this.apiKeySetting.descEl;i.empty(),i.appendChild(createSpan({text:"You can create a key in the "})),i.appendChild(createEl("a",{text:"Extension settings",href:n,attr:{target:"_blank",rel:"noopener"}})),i.appendChild(createSpan({text:" of Cubox web app."})),r.inputEl.disabled=!1}}updateHelpLinks(t){let r=(n,i)=>{let a=document.querySelector(n);if(a){a.empty();let o=a.createSpan({text:"For more, refer to "}),l=t?$s(t,n.includes("filename")?"filename":n.includes("metadata")?"metadata":n.includes("content")?"content":"date"):"#",u=o.createEl("a",{text:"reference",cls:"reference-link",href:l});t&&u.setAttribute("target","_blank"),o.createSpan({text:"."})}};r(".domain-reference-filename",an),r(".domain-reference-metadata",on),r(".domain-reference-content",ln),r(".domain-reference-date",un)}getFolderFilterButtonText(){let t=this.plugin.settings.folderFilter;return!t||t.length===0?"Select...":t.includes(W)?"All folders":`${t.length} selected`}getTypeFilterButtonText(){let t=this.plugin.settings.typeFilter;return!t||t.length===0?"Select...":t.length===Zs.length?"All types":`${t.length} selected`}getStatusFilterButtonText(){let t=this.plugin.settings.statusFilter;return!t||t.length===0?"Select...":t.includes("all")?"All items":`${t.length} selected`}getTagFilterButtonText(){let t=this.plugin.settings.tagsFilter;return!t||t.length===0?"Select...":t.includes(A)?"All items":t.includes("")?"No tags":`${t.length} selected`}};var jt=class extends H.Plugin{async onload(){await this.loadSettings(),this.cuboxApi=new bt(this.settings.domain,this.settings.apiKey),this.templateProcessor=new Bt,this.templateProcessor.setDateFormat(this.settings.dateFormat);let t="Cubox";(0,H.addIcon)(t,'<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_812_101)"><path d="M20.0858 18.3243C19.9877 24.0475 16.415 23.8861 11.8868 23.8861C7.35861 23.8861 5.01758 22.9138 5.01758 17.9719C5.01758 12.8223 7.35861 9 11.8868 9C16.415 9 20.0858 13.1746 20.0858 18.3243Z" stroke="currentColor" stroke-width="2"/><rect x="2" y="2" width="20" height="20" rx="10" stroke="currentColor" stroke-width="2"/></g><ellipse cx="9" cy="15.15" rx="1" ry="1.15" fill="currentColor"/><ellipse cx="12" cy="15.15" rx="1" ry="1.15" fill="currentColor"/><defs> <clipPath id="clip0_812_101"><rect x="1" y="1" width="22" height="22" rx="11" fill="white"/></clipPath></defs></svg>'),this.addRibbonIcon(t,t,async n=>{new H.Notice("Syncing your Cubox\u2026"),await this.syncCubox()}).addClass("cubox-sync-ribbon-class"),this.addCommand({id:"sync-cubox-data",name:"Sync now",callback:async()=>{await this.syncCubox()}}),this.addSettingTab(new Jt(this.app,this)),this.setupAutoSync()}onunload(){this.syncIntervalId&&window.clearInterval(this.syncIntervalId)}async loadSettings(){this.settings=Object.assign({},pe,await this.loadData())}async saveSettings(){await this.saveData(this.settings)}async setupAutoSync(){this.syncIntervalId&&window.clearInterval(this.syncIntervalId),this.settings.syncFrequency>0&&(this.syncIntervalId=window.setInterval(async()=>await this.syncCubox(),this.settings.syncFrequency*60*1e3),this.registerInterval(this.syncIntervalId))}async syncCubox(){var t,r;if(this.settings.syncing){new H.Notice("Sync is in progress, please wait.");return}if(this.settings.folderFilter.length===0||this.settings.typeFilter.length===0||this.settings.statusFilter.length===0||this.settings.tagsFilter.length===0){new H.Notice("Please select the filter first");return}try{this.settings.syncing=!0,await this.saveSettings(),await this.ensureTargetFolder();let n=this.settings.lastSyncCardId,i=this.settings.lastCardUpdateTime,a=!0,o=0,l=0,u=0;for(;a;){let h=await this.cuboxApi.getArticles({lastCardId:n,lastCardUpdateTime:i,folderFilter:this.settings.folderFilter,typeFilter:this.settings.typeFilter,statusFilter:this.settings.statusFilter,tagsFilter:this.settings.tagsFilter,isRead:this.settings.isRead,isStarred:this.settings.isStarred,isAnnotated:this.settings.isAnnotated}),{articles:y,hasMore:g}=h,k=this.templateProcessor.needsArticleContent(this.settings.contentTemplate);if(y.length===0)break;for(let S of y)try{let F={...S};if(k){let ne=await this.cuboxApi.getArticleDetail(S.id);if(ne===null)continue;F.content=ne}let b=this.templateProcessor.processFilenameTemplate(this.settings.filenameTemplate,F),M=`${this.settings.targetFolder}/${b}.md`,Y=this.app.vault.getAbstractFileByPath(M);if(Y instanceof H.TFile){let ne=!1,V=(r=(t=this.app.metadataCache.getFileCache(Y))==null?void 0:t.frontmatter)==null?void 0:r.id;if(V&&V===S.id&&(ne=!0),ne){u++;continue}}let se=this.templateProcessor.processFrontMatter(this.settings.frontMatterVariables,F),Z=this.templateProcessor.processContentTemplate(this.settings.contentTemplate,F),re="";se.length>0&&(re=`---
${se}
---
`),re+=Z,await this.app.vault.create(M,re),o++}catch(F){l++,console.error("\u540C\u6B65 Cubox \u6570\u636E\u5931\u8D25:",F)}a=g,y.length>0&&(n=y[y.length-1].id,i=y[y.length-1].update_time,this.settings.lastSyncCardId=n,this.settings.lastCardUpdateTime=i,await this.saveSettings())}this.settings.lastSyncTime=Date.now(),this.settings.syncing=!1,await this.saveSettings();let c=`Cubox sync completed: ${o} new items${u>0?`, ${u} skipped`:""}${l>0?`, ${l} errors`:""}`;new H.Notice(c)}catch(n){console.error("\u540C\u6B65 Cubox \u6570\u636E\u5931\u8D25:",n),new H.Notice("Cubox sync failed. Please check settings or network.")}finally{this.settings.syncing=!1,await this.saveSettings()}}async ensureTargetFolder(){let t=this.settings.targetFolder;this.app.vault.getAbstractFileByPath(t)instanceof H.TFolder||await this.app.vault.createFolder(t)}formatLastSyncTime(){return this.settings.lastSyncTime?te(new Date(this.settings.lastSyncTime).toISOString(),"yyyy-MM-dd HH:mm"):"never"}updateCuboxApiConfig(t,r){this.cuboxApi.updateConfig(t,r)}updateTemplateProcessorDateFormat(t){this.templateProcessor.setDateFormat(t)}};
/*! Bundled license information:

mustache/mustache.mjs:
  (*!
   * mustache.js - Logic-less {{mustache}} templates with JavaScript
   * http://github.com/janl/mustache.js
   *)
*/

/* nosourcemap */
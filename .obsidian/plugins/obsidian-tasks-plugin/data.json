{"presets": {"this_file": "path includes {{query.file.path}}", "this_folder": "folder includes {{query.file.folder}}", "this_folder_only": "filter by function task.file.folder === query.file.folder", "this_root": "root includes {{query.file.root}}", "hide_date_fields": "# Hide any values for all date fields\nhide due date\nhide scheduled date\nhide start date\nhide created date\nhide done date\nhide cancelled date", "hide_non_date_fields": "# Hide all the non-date fields, but not tags\nhide id\nhide depends on\nhide recurrence rule\nhide on completion\nhide priority", "hide_query_elements": "# Hide postpone, edit and backinks\nhide postpone button\nhide edit button\nhide backlinks", "hide_everything": "# Hide everything except description and any tags\npreset hide_date_fields\npreset hide_non_date_fields\npreset hide_query_elements"}, "globalQuery": "", "globalFilter": "", "removeGlobalFilter": false, "taskFormat": "tasksPluginEmoji", "setCreatedDate": false, "setDoneDate": true, "setCancelledDate": true, "autoSuggestInEditor": true, "autoSuggestMinMatch": 0, "autoSuggestMaxItems": 20, "provideAccessKeys": true, "useFilenameAsScheduledDate": false, "filenameAsScheduledDateFormat": "", "filenameAsDateFolders": [], "recurrenceOnNextLine": false, "removeScheduledDateOnRecurrence": false, "statusSettings": {"coreStatuses": [{"symbol": " ", "name": "Todo", "nextStatusSymbol": "x", "availableAsCommand": true, "type": "TODO"}, {"symbol": "x", "name": "Done", "nextStatusSymbol": " ", "availableAsCommand": true, "type": "DONE"}], "customStatuses": [{"symbol": "/", "name": "In Progress", "nextStatusSymbol": "x", "availableAsCommand": true, "type": "IN_PROGRESS"}, {"symbol": "-", "name": "Cancelled", "nextStatusSymbol": " ", "availableAsCommand": true, "type": "CANCELLED"}]}, "features": {"INTERNAL_TESTING_ENABLED_BY_DEFAULT": true}, "generalSettings": {}, "headingOpened": {"核心状态": true, "自定义状态": true}, "debugSettings": {"ignoreSortInstructions": false, "showTaskHiddenData": false, "recordTimings": false}, "loggingOptions": {"minLevels": {"": "info", "tasks": "info", "tasks.Cache": "info", "tasks.Events": "info", "tasks.File": "info", "tasks.Query": "info", "tasks.Task": "info"}}}
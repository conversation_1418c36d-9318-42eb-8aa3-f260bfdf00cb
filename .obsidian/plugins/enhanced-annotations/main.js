/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
var __accessCheck = (obj, member, msg) => {
  if (!member.has(obj))
    throw TypeError("Cannot " + msg);
};
var __privateGet = (obj, member, getter) => {
  __accessCheck(obj, member, "read from private field");
  return getter ? getter.call(obj) : member.get(obj);
};
var __privateAdd = (obj, member, value) => {
  if (member.has(obj))
    throw TypeError("Cannot add the same private member more than once");
  member instanceof WeakSet ? member.add(obj) : member.set(obj, value);
};
var __privateSet = (obj, member, value, setter) => {
  __accessCheck(obj, member, "write to private field");
  setter ? setter.call(obj, value) : member.set(obj, value);
  return value;
};

// src/main.ts
var main_exports = {};
__export(main_exports, {
  default: () => LabeledAnnotations
});
module.exports = __toCommonJS(main_exports);
var import_obsidian20 = require("obsidian");

// src/lang/lang.ts
var l = {
  OUTLINE_FILTER_ANNOTATIONS: "Filter annotations",
  OUTLINE_SEARCH_ANNOTATIONS: "Search annotations",
  OUTLINE_FILTER_ANNOTATIONS_CLEAR: "Clear",
  OUTLINE_INCREASE_FONT_SIZE: "Increase font size",
  OUTLINE_DECREASE_FONT_SIZE: "Decrease font size",
  OUTLINE_TOGGLE_STYLES_SETTINGS: "Annotations styles",
  OUTLINE_SETTINGS: "Outline settings",
  OUTLINE_SHOW_ALL_CONTROLS: "More options",
  OUTLINE_COPY_ANNOTATIONS_TO_CLIPBOARD: "Copy annotations to clipboard",
  OUTLINE_FILTER_ANNOTATIONS_PLACEHOLDER: "Filter annotations...",
  OUTLINE_NO_ANNOTATIONS_FOUND: "No annotations found.",
  OUTLINE_NOTICE_COPIED_TO_CLIPBOARD: "Copied to clipboard",
  OUTLINE_NOTICE_COULD_NOT_COPY: "Could not copy text",
  OUTLINE_READ_ANNOTATIONS: "Read annotations",
  OUTLINE_PLUGIN_IS_IDLE: " plugin is idle",
  OUTLINE_ENABLE_PLUGIN: "Enable",
  PLUGIN_NAME: "Enhanced Annotations",
  SETTINGS_AUTO_SUGGEST_TITLE: "Autocomplete",
  SETTINGS_AUTO_SUGGEST_ENABLE: "Enable autocomplete popup",
  SETTINGS_AUTO_SUGGEST_ENABLE_DESC: "The popup is used to insert new labeled comments (e.g, type `//todo` to insert `<!--todo: -->`)",
  SETTINGS_AUTO_SUGGEST_TRIGGER_PHRASE: "Trigger phrase",
  SETTINGS_AUTO_SUGGEST_TRIGGER_PHRASE_DESC: "Typing this will trigger the popup",
  SETTINGS_AUTO_SUGGEST_AUTO_REGISTER: "Automatically create a style for new labels",
  SETTINGS_AUTO_SUGGEST_AUTO_REGISTER_DESC: "When a new label such as `<!--todo: -->` is used, a new style is automatically created for it",
  SETTINGS_TTS_TITLE: "Text to speech",
  SETTINGS_TTS_VOICE: "Voice",
  SETTINGS_TTS_VOLUME: "Volume",
  SETTINGS_TTS_PITCH: "Pitch",
  SETTINGS_TTS_RATE: "Rate",
  SETTINGS_TTS_RESTORE_DEFAULTS: "Restore defaults",
  SETTINGS_TTS_FOCUS_ANNOTATION_IN_EDITOR: "Highlight active annotation in the editor",
  SETTINGS_TTS_FOCUS_ANNOTATION_IN_EDITOR_DESC: "Highlight the annotation that is being read by TTS in the editor",
  SETTINGS_CLIPBOARD_TITLE: "Clipboard",
  SETTINGS_CLIPBOARD_TEMPLATE: "template",
  SETTINGS_NOTE_CREATION_TITLE: "Notes",
  SETTINGS_NOTE_CREATION_FOLDER: "Default folder of created notes",
  SETTINGS_NOTE_CREATION_FOLDER_MODE: "Default location of created notes",
  SETTINGS_NOTE_CREATION_FOLDER_PLACEHOLDER: "folder",
  SETTINGS_NOTE_CREATION_NAME: "Default name of created notes",
  SETTINGS_NOTE_CREATION_OPEN: "Open created notes",
  SETTINGS_NOTE_CREATION_OPEN_DESC: "Open newly created notes in a new tab",
  SETTINGS_NOTE_CREATION_TEMPLATE: "Template of created notes",
  SETTINGS_NOTE_TRUNCATE_FILE_NAME: "Truncate the name of created notes",
  SETTINGS_NOTE_TRUNCATE_FILE_NAME_DESC: "Limit the name to 100 characters",
  SETTINGS_TEMPLATE_desc: "Available variables: ",
  SETTINGS_NOTE_CREATION_INSERT: "Insert a link to the created note",
  SETTINGS_NOTE_CREATION_INSERT_DESC: "A link `[[note-name|\u2197]]` is added at the end of the annotation block",
  SETTINGS_STYLES_WRAPPERS_STYLE: "Style of comment wrappers",
  SETTINGS_STYLES_WRAPPERS_STYLE_DESC: "Defines how `<!-- -->` and `%%` are styled",
  SETTINGS_LABELS_STYLES_TITLE: "Styling",
  SETTINGS_LABELS_STYLES_NEW: "New style",
  SETTINGS_LABELS_STYLES_NAME_PLACE_HOLDER: "label",
  SETTINGS_LABELS_STYLES_ENABLE_STYLE: "Enable style",
  SETTINGS_LABELS_STYLES_TOGGLE_CASE: "Toggle Case",
  SETTINGS_LABELS_STYLES_DELETE_STYLE: "Delete style",
  SETTINGS_COMMANDS_TITLE: "Commands",
  SETTINGS_COMMANDS_ASSIGN_HOTKEYS: "Assign default hotkeys to commands",
  SETTINGS_COMMANDS_ASSIGN_HOTKEYS_DESC: "Assigns keys `F5` `F6` `F7` to comment insertion commands",
  EXPAND: "Expand",
  COLLAPSE: "Collapse",
  COMMANDS_JUMP_TO_NEW_LINE: "Jump to a new line",
  COMMANDS_INSERT_COMMENT: "Insert a comment",
  COMMANDS_INSERT_COMMENT_AFTER_EMPTY_LINE: "Insert a comment after an empty line",
  COMMANDS_INSERT_COMMENT_WITH_PREVIOUS_LABEL: "Insert a comment with the most recently used label",
  COMMANDS_INSERT_COMMENT_WITH_SECOND_PREVIOUS_LABEL: "Insert a comment with the second most recently used label",
  COMMANDS_INSERT_COMMENT_WITH_PREVIOUS_LABEL_AFTER_EMPTY_LINE: "Insert a comment with the most recently used label after an empty line",
  COMMANDS_INSERT_COMMENT_WITH_SECOND_PREVIOUS_LABEL_AFTER_EMPTY_LINE: "Insert a comment with the second most recently used label after an empty line",
  COMMANDS_COULD_NOT_CREATE_FILE: "Could not create note: ",
  OUTLINE_EDITOR_CREATE_NOTE_FROM_COMMENT: "Create a note from this comment",
  OUTLINE_EDITOR_CREATE_NOTE_FROM_HIGHLIGHT: "Create a note from this highlight",
  SETTINGS_AUTO_SUGGEST_COMMENT_TYPE: "Comment format",
  SETTINGS_AUTO_SUGGEST_COMMENT_TYPE_DESC: "The format of the inserted comment: `<!-- HTML comment -->` or `%% Obsidian comment %%`",
  SETTINGS_DEFAULT_PALETTE: "Default color palette",
  SETTINGS_DEFAULT_PALETTE_DESC: "The default color palette used to assign colors to new styles",
  OUTLINE_NO_ANNOTATIONS_MATCH_FILTER: "No annotations match the applied filter.",
  DISABLE_DECORATION: "Disable decoration",
  ENABLE_DECORATION: "Enable decoration"
};

// src/commands/helpers/slugify.ts
var slugify = (inputString) => {
  return inputString.toLowerCase().replace(/[^a-z0-9]/g, "-").replace(/-+/g, "-").replace(/^-|-$/g, "");
};

// src/commands/helpers/insert-new-line.ts
var insertNewLine = ({ doc }) => {
  const cursor = doc.getCursor();
  const lineText = doc.getLine(cursor.line);
  doc.replaceRange("\n", {
    line: cursor.line,
    ch: lineText.length
  });
  doc.setCursor({
    line: cursor.line + 1,
    ch: 0
  });
};

// src/commands/helpers/wrap-text.ts
var wrapText = ({
  text: text2,
  type,
  format,
  label = ""
}) => {
  if (label)
    label = label + ": ";
  if (type === "highlight") {
    return `==${label}${text2}==`;
  } else if (type) {
    if (format === "html") {
      return `<!--${label}${text2}-->`;
    } else
      return `%%${label}${text2}%%`;
  } else
    return text2;
};

// src/commands/helpers/cursor-position.ts
var cursorPosition = (text2, commentType, type) => {
  return text2.length - (type ? commentType === "html" ? 3 : 2 : 0);
};

// src/editor-plugin/helpers/decorate-annotations/helpers/parse-annotations/parse-annotations.ts
var pairs = {
  "<!--": "-->",
  "==": "==",
  "%%": "%%"
};
var startPattern = /(<!--|%%|==)/g;
var endPattern = /(-->|%%|==)/g;
var blockRegex = /```/g;
var inlineCodeRegex = /`([^`]+)`/g;
var parseAnnotations = (text2, lineNumber = 0, from = 0, includeEmptyAnnotations = false) => {
  const lines = text2.split("\n");
  const annotations = [];
  const state = {
    multiLineAnnotation: null,
    line: lineNumber,
    from,
    multiLineStart: "",
    multiAnnotationLine: false,
    isInsideBlock: false
  };
  for (let i = 0; i < lines.length; i++) {
    let line = lines[i];
    if (blockRegex.test(line)) {
      state.isInsideBlock = !state.isInsideBlock;
    }
    if (inlineCodeRegex.test(line)) {
      line = line.replace(/`([^`]*?)==([^`]*?)`/g, "`$1++$2`");
      inlineCodeRegex.lastIndex = 0;
    }
    if (!state.isInsideBlock) {
      if (state.multiAnnotationLine) {
        state.multiAnnotationLine = false;
      } else {
        startPattern.lastIndex = 0;
        endPattern.lastIndex = 0;
      }
      let startRegex = startPattern.exec(line);
      let endRegex = void 0;
      if (startRegex || state.multiLineAnnotation) {
        if (!state.multiLineAnnotation)
          endPattern.lastIndex = startPattern.lastIndex;
        endRegex = endPattern.exec(line);
      }
      if (startRegex && endRegex) {
        const start = startRegex[1];
        const end = endRegex[1];
        if (pairs[start] === end) {
          const from2 = startRegex.index;
          const beforeTo = endRegex.index;
          const afterFrom = from2 + start.length;
          const to = beforeTo + end.length;
          if (from2 < beforeTo) {
            const annotation = line.substring(afterFrom, beforeTo);
            const labelRegex = /^([^:\s]+): ?(.+)$/g.exec(
              annotation
            );
            const text3 = (labelRegex ? labelRegex[2] : annotation).trim();
            const label = (labelRegex ? labelRegex[1] : "").trim();
            if (text3 || label || includeEmptyAnnotations) {
              annotations.push({
                text: text3,
                label,
                isHighlight: start === "==",
                position: {
                  from: state.from + from2,
                  to: state.from + to,
                  afterFrom: state.from + afterFrom,
                  beforeTo: state.from + beforeTo
                },
                range: {
                  from: {
                    line: state.line,
                    ch: from2
                  },
                  to: {
                    line: state.line,
                    ch: to
                  }
                }
              });
            }
            if (startPattern.exec(line) !== null) {
              state.multiAnnotationLine = true;
              startPattern.lastIndex = endPattern.lastIndex;
              i = i - 1;
              continue;
            }
            state.multiLineAnnotation = null;
          } else if (from2 === beforeTo) {
            startRegex = null;
          }
        }
      }
      if (startRegex && !endRegex) {
        const start = startRegex[1];
        const from2 = state.from + startRegex.index;
        const afterFrom = startRegex.index + start.length;
        const annotation = line.substring(afterFrom);
        const labelRegex = /^([^:\s]+): ?(.+)$/g.exec(annotation);
        const text3 = labelRegex ? labelRegex[2] : annotation;
        const label = (labelRegex ? labelRegex[1] : "").trim();
        if (text3 || label) {
          state.multiLineAnnotation = {
            label,
            text: [text3],
            isHighlight: start === "==",
            position: {
              from: from2,
              afterFrom: state.from + afterFrom,
              beforeTo: -1,
              to: -1
            },
            range: {
              from: { line: state.line, ch: startRegex.index }
            }
          };
          state.multiLineStart = start;
        }
      } else if (endRegex && state.multiLineAnnotation) {
        const end = endRegex[1];
        if (pairs[state.multiLineStart] === end) {
          const beforeTo = endRegex.index;
          const to = beforeTo + end.length;
          state.multiLineAnnotation.text.push(
            line.substring(0, beforeTo)
          );
          state.multiLineAnnotation.position.to = state.from + to;
          state.multiLineAnnotation.position.beforeTo = state.from + beforeTo;
          state.multiLineAnnotation.range.to = {
            line: state.line,
            ch: to
          };
          const allText = state.multiLineAnnotation.text.map((t) => t.trim()).filter(Boolean).join(" ");
          if (allText || includeEmptyAnnotations)
            annotations.push({
              ...state.multiLineAnnotation,
              text: allText
            });
          state.multiLineAnnotation = null;
          state.multiLineStart = null;
          if (startRegex) {
            startPattern.lastIndex = endPattern.lastIndex;
            i = i - 1;
            continue;
          }
        }
      } else if (state.multiLineAnnotation) {
        state.multiLineAnnotation.text.push(line);
      }
    }
    state.line++;
    state.from += line.length + 1;
  }
  return annotations;
};

// src/commands/helpers/wrap-selected-text-in-an-annotation.ts
var wrapSelectedTextInAnAnnotation = ({
  selection,
  format,
  type,
  label = "",
  doc
}) => {
  if (type)
    selection = wrapText({
      type,
      format,
      text: selection,
      label
    });
  const cursor = doc.getCursor();
  doc.replaceSelection(selection);
  doc.setCursor({
    line: cursor.line,
    ch: cursor.ch + cursorPosition(selection, format, type)
  });
};

// src/commands/helpers/insert-annotation.ts
var insertAnnotation = ({
  plugin,
  type,
  label,
  emptyLines = 0,
  editor
}) => {
  const format = plugin.settings.getValue().editorSuggest.commentFormat;
  const doc = editor.getDoc();
  const selection = doc.getSelection();
  if (selection) {
    wrapSelectedTextInAnAnnotation({
      type,
      format,
      label,
      doc,
      selection
    });
  } else {
    const cursor = doc.getCursor();
    const currentLineText = doc.getLine(cursor.line);
    const currentLineAnnotation = parseAnnotations(
      currentLineText,
      0,
      0,
      true
    )[0];
    let startingLine = 0;
    if (currentLineText.trim()) {
      startingLine = startingLine + 1;
    }
    const text2 = wrapText({
      text: "",
      label: label || currentLineAnnotation?.label,
      type: type || (currentLineAnnotation ? currentLineAnnotation.isHighlight ? "highlight" : "comment" : void 0),
      format
    });
    doc.replaceRange("".padStart(emptyLines + startingLine, "\n") + text2, {
      line: cursor.line,
      ch: currentLineText.length
    });
    doc.setCursor({
      line: cursor.line + emptyLines + startingLine,
      ch: cursorPosition(text2, format, type)
    });
  }
};

// src/commands/commands.ts
var addInsertCommentCommands = (plugin) => {
  const commands = [
    {
      name: l.ENABLE_DECORATION,
      editorCallback: () => {
        plugin.decorationSettings.enabled = true;
      }
    },
    {
      name: l.DISABLE_DECORATION,
      editorCallback: () => {
        plugin.decorationSettings.enabled = false;
      }
    },
    {
      name: l.COMMANDS_JUMP_TO_NEW_LINE,
      hotkeys: [{ key: "F5", modifiers: [] }],
      editorCallback: async (editor) => {
        const doc = editor.getDoc();
        insertNewLine({ doc });
      }
    },
    {
      name: l.COMMANDS_INSERT_COMMENT,
      hotkeys: [{ key: "F7", modifiers: [] }],
      editorCallback: async (editor) => {
        insertAnnotation({ editor, plugin, type: "comment" });
      }
    },
    {
      name: l.COMMANDS_INSERT_COMMENT_AFTER_EMPTY_LINE,
      hotkeys: [{ key: "F7", modifiers: ["Shift"] }],
      editorCallback: async (editor) => {
        insertAnnotation({
          editor,
          plugin,
          type: "comment",
          emptyLines: 1
        });
      }
    },
    {
      name: l.COMMANDS_INSERT_COMMENT_WITH_PREVIOUS_LABEL,
      hotkeys: [{ key: "F6", modifiers: [] }],
      editorCallback: async (editor) => {
        const label = plugin.editorSuggest.useMostRecentSuggestion();
        insertAnnotation({ editor, plugin, type: "comment", label });
      }
    },
    {
      name: l.COMMANDS_INSERT_COMMENT_WITH_PREVIOUS_LABEL_AFTER_EMPTY_LINE,
      hotkeys: [{ key: "F6", modifiers: ["Shift"] }],
      editorCallback: async (editor) => {
        const label = plugin.editorSuggest.useMostRecentSuggestion();
        insertAnnotation({
          editor,
          plugin,
          type: "comment",
          label,
          emptyLines: 1
        });
      }
    },
    {
      name: l.COMMANDS_INSERT_COMMENT_WITH_SECOND_PREVIOUS_LABEL,
      hotkeys: [{ key: "F6", modifiers: ["Alt"] }],
      editorCallback: async (editor) => {
        const label = plugin.editorSuggest.useSecondMostRecentSuggestion();
        insertAnnotation({ editor, plugin, type: "comment", label });
      }
    },
    {
      name: l.COMMANDS_INSERT_COMMENT_WITH_SECOND_PREVIOUS_LABEL_AFTER_EMPTY_LINE,
      hotkeys: [{ key: "F6", modifiers: ["Shift", "Alt"] }],
      editorCallback: async (editor) => {
        const label = plugin.editorSuggest.useSecondMostRecentSuggestion();
        insertAnnotation({
          editor,
          plugin,
          type: "comment",
          label,
          emptyLines: 1
        });
      }
    }
  ];
  for (const { name, editorCallback } of commands) {
    plugin.addCommand({
      id: slugify(name),
      editorCallback,
      name
    });
  }
};

// src/sidebar-outline/sidebar-outline-view.ts
var import_obsidian4 = require("obsidian");

// node_modules/svelte/src/runtime/internal/utils.js
function noop() {
}
function assign(tar, src) {
  for (const k in src)
    tar[k] = src[k];
  return (
    /** @type {T & S} */
    tar
  );
}
function run(fn) {
  return fn();
}
function blank_object() {
  return /* @__PURE__ */ Object.create(null);
}
function run_all(fns) {
  fns.forEach(run);
}
function is_function(thing) {
  return typeof thing === "function";
}
function safe_not_equal(a, b) {
  return a != a ? b == b : a !== b || a && typeof a === "object" || typeof a === "function";
}
function is_empty(obj) {
  return Object.keys(obj).length === 0;
}
function subscribe(store, ...callbacks) {
  if (store == null) {
    for (const callback of callbacks) {
      callback(void 0);
    }
    return noop;
  }
  const unsub = store.subscribe(...callbacks);
  return unsub.unsubscribe ? () => unsub.unsubscribe() : unsub;
}
function get_store_value(store) {
  let value;
  subscribe(store, (_) => value = _)();
  return value;
}
function component_subscribe(component, store, callback) {
  component.$$.on_destroy.push(subscribe(store, callback));
}
function create_slot(definition, ctx, $$scope, fn) {
  if (definition) {
    const slot_ctx = get_slot_context(definition, ctx, $$scope, fn);
    return definition[0](slot_ctx);
  }
}
function get_slot_context(definition, ctx, $$scope, fn) {
  return definition[1] && fn ? assign($$scope.ctx.slice(), definition[1](fn(ctx))) : $$scope.ctx;
}
function get_slot_changes(definition, $$scope, dirty, fn) {
  if (definition[2] && fn) {
    const lets = definition[2](fn(dirty));
    if ($$scope.dirty === void 0) {
      return lets;
    }
    if (typeof lets === "object") {
      const merged = [];
      const len = Math.max($$scope.dirty.length, lets.length);
      for (let i = 0; i < len; i += 1) {
        merged[i] = $$scope.dirty[i] | lets[i];
      }
      return merged;
    }
    return $$scope.dirty | lets;
  }
  return $$scope.dirty;
}
function update_slot_base(slot, slot_definition, ctx, $$scope, slot_changes, get_slot_context_fn) {
  if (slot_changes) {
    const slot_context = get_slot_context(slot_definition, ctx, $$scope, get_slot_context_fn);
    slot.p(slot_context, slot_changes);
  }
}
function get_all_dirty_from_scope($$scope) {
  if ($$scope.ctx.length > 32) {
    const dirty = [];
    const length = $$scope.ctx.length / 32;
    for (let i = 0; i < length; i++) {
      dirty[i] = -1;
    }
    return dirty;
  }
  return -1;
}
function exclude_internal_props(props) {
  const result = {};
  for (const k in props)
    if (k[0] !== "$")
      result[k] = props[k];
  return result;
}
function compute_rest_props(props, keys) {
  const rest = {};
  keys = new Set(keys);
  for (const k in props)
    if (!keys.has(k) && k[0] !== "$")
      rest[k] = props[k];
  return rest;
}
function null_to_empty(value) {
  return value == null ? "" : value;
}

// node_modules/svelte/src/runtime/internal/globals.js
var globals = typeof window !== "undefined" ? window : typeof globalThis !== "undefined" ? globalThis : (
  // @ts-ignore Node typings have this
  global
);

// node_modules/svelte/src/runtime/internal/ResizeObserverSingleton.js
var ResizeObserverSingleton = class {
  /** @param {ResizeObserverOptions} options */
  constructor(options2) {
    /**
     * @private
     * @readonly
     * @type {WeakMap<Element, import('./private.js').Listener>}
     */
    __publicField(this, "_listeners", "WeakMap" in globals ? /* @__PURE__ */ new WeakMap() : void 0);
    /**
     * @private
     * @type {ResizeObserver}
     */
    __publicField(this, "_observer");
    /** @type {ResizeObserverOptions} */
    __publicField(this, "options");
    this.options = options2;
  }
  /**
   * @param {Element} element
   * @param {import('./private.js').Listener} listener
   * @returns {() => void}
   */
  observe(element2, listener) {
    this._listeners.set(element2, listener);
    this._getObserver().observe(element2, this.options);
    return () => {
      this._listeners.delete(element2);
      this._observer.unobserve(element2);
    };
  }
  /**
   * @private
   */
  _getObserver() {
    return this._observer ?? (this._observer = new ResizeObserver((entries) => {
      for (const entry of entries) {
        ResizeObserverSingleton.entries.set(entry.target, entry);
        this._listeners.get(entry.target)?.(entry);
      }
    }));
  }
};
ResizeObserverSingleton.entries = "WeakMap" in globals ? /* @__PURE__ */ new WeakMap() : void 0;

// node_modules/svelte/src/runtime/internal/dom.js
var is_hydrating = false;
function start_hydrating() {
  is_hydrating = true;
}
function end_hydrating() {
  is_hydrating = false;
}
function upper_bound(low, high, key, value) {
  while (low < high) {
    const mid = low + (high - low >> 1);
    if (key(mid) <= value) {
      low = mid + 1;
    } else {
      high = mid;
    }
  }
  return low;
}
function init_hydrate(target) {
  if (target.hydrate_init)
    return;
  target.hydrate_init = true;
  let children2 = (
    /** @type {ArrayLike<NodeEx2>} */
    target.childNodes
  );
  if (target.nodeName === "HEAD") {
    const my_children = [];
    for (let i = 0; i < children2.length; i++) {
      const node = children2[i];
      if (node.claim_order !== void 0) {
        my_children.push(node);
      }
    }
    children2 = my_children;
  }
  const m = new Int32Array(children2.length + 1);
  const p = new Int32Array(children2.length);
  m[0] = -1;
  let longest = 0;
  for (let i = 0; i < children2.length; i++) {
    const current = children2[i].claim_order;
    const seq_len = (longest > 0 && children2[m[longest]].claim_order <= current ? longest + 1 : upper_bound(1, longest, (idx) => children2[m[idx]].claim_order, current)) - 1;
    p[i] = m[seq_len] + 1;
    const new_len = seq_len + 1;
    m[new_len] = i;
    longest = Math.max(new_len, longest);
  }
  const lis = [];
  const to_move = [];
  let last = children2.length - 1;
  for (let cur = m[longest] + 1; cur != 0; cur = p[cur - 1]) {
    lis.push(children2[cur - 1]);
    for (; last >= cur; last--) {
      to_move.push(children2[last]);
    }
    last--;
  }
  for (; last >= 0; last--) {
    to_move.push(children2[last]);
  }
  lis.reverse();
  to_move.sort((a, b) => a.claim_order - b.claim_order);
  for (let i = 0, j = 0; i < to_move.length; i++) {
    while (j < lis.length && to_move[i].claim_order >= lis[j].claim_order) {
      j++;
    }
    const anchor = j < lis.length ? lis[j] : null;
    target.insertBefore(to_move[i], anchor);
  }
}
function append(target, node) {
  target.appendChild(node);
}
function append_styles(target, style_sheet_id, styles) {
  const append_styles_to = get_root_for_style(target);
  if (!append_styles_to.getElementById(style_sheet_id)) {
    const style = element("style");
    style.id = style_sheet_id;
    style.textContent = styles;
    append_stylesheet(append_styles_to, style);
  }
}
function get_root_for_style(node) {
  if (!node)
    return document;
  const root = node.getRootNode ? node.getRootNode() : node.ownerDocument;
  if (root && /** @type {ShadowRoot} */
  root.host) {
    return (
      /** @type {ShadowRoot} */
      root
    );
  }
  return node.ownerDocument;
}
function append_stylesheet(node, style) {
  append(
    /** @type {Document} */
    node.head || node,
    style
  );
  return style.sheet;
}
function append_hydration(target, node) {
  if (is_hydrating) {
    init_hydrate(target);
    if (target.actual_end_child === void 0 || target.actual_end_child !== null && target.actual_end_child.parentNode !== target) {
      target.actual_end_child = target.firstChild;
    }
    while (target.actual_end_child !== null && target.actual_end_child.claim_order === void 0) {
      target.actual_end_child = target.actual_end_child.nextSibling;
    }
    if (node !== target.actual_end_child) {
      if (node.claim_order !== void 0 || node.parentNode !== target) {
        target.insertBefore(node, target.actual_end_child);
      }
    } else {
      target.actual_end_child = node.nextSibling;
    }
  } else if (node.parentNode !== target || node.nextSibling !== null) {
    target.appendChild(node);
  }
}
function insert(target, node, anchor) {
  target.insertBefore(node, anchor || null);
}
function insert_hydration(target, node, anchor) {
  if (is_hydrating && !anchor) {
    append_hydration(target, node);
  } else if (node.parentNode !== target || node.nextSibling != anchor) {
    target.insertBefore(node, anchor || null);
  }
}
function detach(node) {
  if (node.parentNode) {
    node.parentNode.removeChild(node);
  }
}
function destroy_each(iterations, detaching) {
  for (let i = 0; i < iterations.length; i += 1) {
    if (iterations[i])
      iterations[i].d(detaching);
  }
}
function element(name) {
  return document.createElement(name);
}
function svg_element(name) {
  return document.createElementNS("http://www.w3.org/2000/svg", name);
}
function text(data) {
  return document.createTextNode(data);
}
function space() {
  return text(" ");
}
function empty() {
  return text("");
}
function listen(node, event, handler, options2) {
  node.addEventListener(event, handler, options2);
  return () => node.removeEventListener(event, handler, options2);
}
function attr(node, attribute, value) {
  if (value == null)
    node.removeAttribute(attribute);
  else if (node.getAttribute(attribute) !== value)
    node.setAttribute(attribute, value);
}
function set_svg_attributes(node, attributes) {
  for (const key in attributes) {
    attr(node, key, attributes[key]);
  }
}
function children(element2) {
  return Array.from(element2.childNodes);
}
function init_claim_info(nodes) {
  if (nodes.claim_info === void 0) {
    nodes.claim_info = { last_index: 0, total_claimed: 0 };
  }
}
function claim_node(nodes, predicate, process_node, create_node, dont_update_last_index = false) {
  init_claim_info(nodes);
  const result_node = (() => {
    for (let i = nodes.claim_info.last_index; i < nodes.length; i++) {
      const node = nodes[i];
      if (predicate(node)) {
        const replacement = process_node(node);
        if (replacement === void 0) {
          nodes.splice(i, 1);
        } else {
          nodes[i] = replacement;
        }
        if (!dont_update_last_index) {
          nodes.claim_info.last_index = i;
        }
        return node;
      }
    }
    for (let i = nodes.claim_info.last_index - 1; i >= 0; i--) {
      const node = nodes[i];
      if (predicate(node)) {
        const replacement = process_node(node);
        if (replacement === void 0) {
          nodes.splice(i, 1);
        } else {
          nodes[i] = replacement;
        }
        if (!dont_update_last_index) {
          nodes.claim_info.last_index = i;
        } else if (replacement === void 0) {
          nodes.claim_info.last_index--;
        }
        return node;
      }
    }
    return create_node();
  })();
  result_node.claim_order = nodes.claim_info.total_claimed;
  nodes.claim_info.total_claimed += 1;
  return result_node;
}
function claim_element_base(nodes, name, attributes, create_element) {
  return claim_node(
    nodes,
    /** @returns {node is Element | SVGElement} */
    (node) => node.nodeName === name,
    /** @param {Element} node */
    (node) => {
      const remove = [];
      for (let j = 0; j < node.attributes.length; j++) {
        const attribute = node.attributes[j];
        if (!attributes[attribute.name]) {
          remove.push(attribute.name);
        }
      }
      remove.forEach((v) => node.removeAttribute(v));
      return void 0;
    },
    () => create_element(name)
  );
}
function claim_svg_element(nodes, name, attributes) {
  return claim_element_base(nodes, name, attributes, svg_element);
}
function set_data(text2, data) {
  data = "" + data;
  if (text2.data === data)
    return;
  text2.data = /** @type {string} */
  data;
}
function set_input_value(input, value) {
  input.value = value == null ? "" : value;
}
function set_style(node, key, value, important) {
  if (value == null) {
    node.style.removeProperty(key);
  } else {
    node.style.setProperty(key, value, important ? "important" : "");
  }
}
var HtmlTag = class {
  constructor(is_svg = false) {
    /**
     * @private
     * @default false
     */
    __publicField(this, "is_svg", false);
    /** parent for creating node */
    __publicField(this, "e");
    /** html tag nodes */
    __publicField(this, "n");
    /** target */
    __publicField(this, "t");
    /** anchor */
    __publicField(this, "a");
    this.is_svg = is_svg;
    this.e = this.n = null;
  }
  /**
   * @param {string} html
   * @returns {void}
   */
  c(html) {
    this.h(html);
  }
  /**
   * @param {string} html
   * @param {HTMLElement | SVGElement} target
   * @param {HTMLElement | SVGElement} anchor
   * @returns {void}
   */
  m(html, target, anchor = null) {
    if (!this.e) {
      if (this.is_svg)
        this.e = svg_element(
          /** @type {keyof SVGElementTagNameMap} */
          target.nodeName
        );
      else
        this.e = element(
          /** @type {keyof HTMLElementTagNameMap} */
          target.nodeType === 11 ? "TEMPLATE" : target.nodeName
        );
      this.t = target.tagName !== "TEMPLATE" ? target : (
        /** @type {HTMLTemplateElement} */
        target.content
      );
      this.c(html);
    }
    this.i(anchor);
  }
  /**
   * @param {string} html
   * @returns {void}
   */
  h(html) {
    this.e.innerHTML = html;
    this.n = Array.from(
      this.e.nodeName === "TEMPLATE" ? this.e.content.childNodes : this.e.childNodes
    );
  }
  /**
   * @returns {void} */
  i(anchor) {
    for (let i = 0; i < this.n.length; i += 1) {
      insert(this.t, this.n[i], anchor);
    }
  }
  /**
   * @param {string} html
   * @returns {void}
   */
  p(html) {
    this.d();
    this.h(html);
    this.i(this.a);
  }
  /**
   * @returns {void} */
  d() {
    this.n.forEach(detach);
  }
};
function get_custom_elements_slots(element2) {
  const result = {};
  element2.childNodes.forEach(
    /** @param {Element} node */
    (node) => {
      result[node.slot || "default"] = true;
    }
  );
  return result;
}
function construct_svelte_component(component, props) {
  return new component(props);
}

// node_modules/svelte/src/runtime/internal/lifecycle.js
var current_component;
function set_current_component(component) {
  current_component = component;
}
function get_current_component() {
  if (!current_component)
    throw new Error("Function called outside component initialization");
  return current_component;
}
function onDestroy(fn) {
  get_current_component().$$.on_destroy.push(fn);
}

// node_modules/svelte/src/runtime/internal/scheduler.js
var dirty_components = [];
var binding_callbacks = [];
var render_callbacks = [];
var flush_callbacks = [];
var resolved_promise = /* @__PURE__ */ Promise.resolve();
var update_scheduled = false;
function schedule_update() {
  if (!update_scheduled) {
    update_scheduled = true;
    resolved_promise.then(flush);
  }
}
function add_render_callback(fn) {
  render_callbacks.push(fn);
}
var seen_callbacks = /* @__PURE__ */ new Set();
var flushidx = 0;
function flush() {
  if (flushidx !== 0) {
    return;
  }
  const saved_component = current_component;
  do {
    try {
      while (flushidx < dirty_components.length) {
        const component = dirty_components[flushidx];
        flushidx++;
        set_current_component(component);
        update(component.$$);
      }
    } catch (e) {
      dirty_components.length = 0;
      flushidx = 0;
      throw e;
    }
    set_current_component(null);
    dirty_components.length = 0;
    flushidx = 0;
    while (binding_callbacks.length)
      binding_callbacks.pop()();
    for (let i = 0; i < render_callbacks.length; i += 1) {
      const callback = render_callbacks[i];
      if (!seen_callbacks.has(callback)) {
        seen_callbacks.add(callback);
        callback();
      }
    }
    render_callbacks.length = 0;
  } while (dirty_components.length);
  while (flush_callbacks.length) {
    flush_callbacks.pop()();
  }
  update_scheduled = false;
  seen_callbacks.clear();
  set_current_component(saved_component);
}
function update($$) {
  if ($$.fragment !== null) {
    $$.update();
    run_all($$.before_update);
    const dirty = $$.dirty;
    $$.dirty = [-1];
    $$.fragment && $$.fragment.p($$.ctx, dirty);
    $$.after_update.forEach(add_render_callback);
  }
}
function flush_render_callbacks(fns) {
  const filtered = [];
  const targets = [];
  render_callbacks.forEach((c) => fns.indexOf(c) === -1 ? filtered.push(c) : targets.push(c));
  targets.forEach((c) => c());
  render_callbacks = filtered;
}

// node_modules/svelte/src/runtime/internal/transitions.js
var outroing = /* @__PURE__ */ new Set();
var outros;
function group_outros() {
  outros = {
    r: 0,
    c: [],
    p: outros
    // parent group
  };
}
function check_outros() {
  if (!outros.r) {
    run_all(outros.c);
  }
  outros = outros.p;
}
function transition_in(block, local) {
  if (block && block.i) {
    outroing.delete(block);
    block.i(local);
  }
}
function transition_out(block, local, detach2, callback) {
  if (block && block.o) {
    if (outroing.has(block))
      return;
    outroing.add(block);
    outros.c.push(() => {
      outroing.delete(block);
      if (callback) {
        if (detach2)
          block.d(1);
        callback();
      }
    });
    block.o(local);
  } else if (callback) {
    callback();
  }
}

// node_modules/svelte/src/runtime/internal/each.js
function ensure_array_like(array_like_or_iterator) {
  return array_like_or_iterator?.length !== void 0 ? array_like_or_iterator : Array.from(array_like_or_iterator);
}
function outro_and_destroy_block(block, lookup) {
  transition_out(block, 1, 1, () => {
    lookup.delete(block.key);
  });
}
function update_keyed_each(old_blocks, dirty, get_key, dynamic, ctx, list, lookup, node, destroy, create_each_block5, next, get_context) {
  let o = old_blocks.length;
  let n = list.length;
  let i = o;
  const old_indexes = {};
  while (i--)
    old_indexes[old_blocks[i].key] = i;
  const new_blocks = [];
  const new_lookup = /* @__PURE__ */ new Map();
  const deltas = /* @__PURE__ */ new Map();
  const updates = [];
  i = n;
  while (i--) {
    const child_ctx = get_context(ctx, list, i);
    const key = get_key(child_ctx);
    let block = lookup.get(key);
    if (!block) {
      block = create_each_block5(key, child_ctx);
      block.c();
    } else if (dynamic) {
      updates.push(() => block.p(child_ctx, dirty));
    }
    new_lookup.set(key, new_blocks[i] = block);
    if (key in old_indexes)
      deltas.set(key, Math.abs(i - old_indexes[key]));
  }
  const will_move = /* @__PURE__ */ new Set();
  const did_move = /* @__PURE__ */ new Set();
  function insert2(block) {
    transition_in(block, 1);
    block.m(node, next);
    lookup.set(block.key, block);
    next = block.first;
    n--;
  }
  while (o && n) {
    const new_block = new_blocks[n - 1];
    const old_block = old_blocks[o - 1];
    const new_key = new_block.key;
    const old_key = old_block.key;
    if (new_block === old_block) {
      next = new_block.first;
      o--;
      n--;
    } else if (!new_lookup.has(old_key)) {
      destroy(old_block, lookup);
      o--;
    } else if (!lookup.has(new_key) || will_move.has(new_key)) {
      insert2(new_block);
    } else if (did_move.has(old_key)) {
      o--;
    } else if (deltas.get(new_key) > deltas.get(old_key)) {
      did_move.add(new_key);
      insert2(new_block);
    } else {
      will_move.add(old_key);
      o--;
    }
  }
  while (o--) {
    const old_block = old_blocks[o];
    if (!new_lookup.has(old_block.key))
      destroy(old_block, lookup);
  }
  while (n)
    insert2(new_blocks[n - 1]);
  run_all(updates);
  return new_blocks;
}

// node_modules/svelte/src/runtime/internal/spread.js
function get_spread_update(levels, updates) {
  const update2 = {};
  const to_null_out = {};
  const accounted_for = { $$scope: 1 };
  let i = levels.length;
  while (i--) {
    const o = levels[i];
    const n = updates[i];
    if (n) {
      for (const key in o) {
        if (!(key in n))
          to_null_out[key] = 1;
      }
      for (const key in n) {
        if (!accounted_for[key]) {
          update2[key] = n[key];
          accounted_for[key] = 1;
        }
      }
      levels[i] = n;
    } else {
      for (const key in o) {
        accounted_for[key] = 1;
      }
    }
  }
  for (const key in to_null_out) {
    if (!(key in update2))
      update2[key] = void 0;
  }
  return update2;
}
function get_spread_object(spread_props) {
  return typeof spread_props === "object" && spread_props !== null ? spread_props : {};
}

// node_modules/svelte/src/shared/boolean_attributes.js
var _boolean_attributes = (
  /** @type {const} */
  [
    "allowfullscreen",
    "allowpaymentrequest",
    "async",
    "autofocus",
    "autoplay",
    "checked",
    "controls",
    "default",
    "defer",
    "disabled",
    "formnovalidate",
    "hidden",
    "inert",
    "ismap",
    "loop",
    "multiple",
    "muted",
    "nomodule",
    "novalidate",
    "open",
    "playsinline",
    "readonly",
    "required",
    "reversed",
    "selected"
  ]
);
var boolean_attributes = /* @__PURE__ */ new Set([..._boolean_attributes]);

// node_modules/svelte/src/runtime/internal/Component.js
function create_component(block) {
  block && block.c();
}
function claim_component(block, parent_nodes) {
  block && block.l(parent_nodes);
}
function mount_component(component, target, anchor) {
  const { fragment, after_update } = component.$$;
  fragment && fragment.m(target, anchor);
  add_render_callback(() => {
    const new_on_destroy = component.$$.on_mount.map(run).filter(is_function);
    if (component.$$.on_destroy) {
      component.$$.on_destroy.push(...new_on_destroy);
    } else {
      run_all(new_on_destroy);
    }
    component.$$.on_mount = [];
  });
  after_update.forEach(add_render_callback);
}
function destroy_component(component, detaching) {
  const $$ = component.$$;
  if ($$.fragment !== null) {
    flush_render_callbacks($$.after_update);
    run_all($$.on_destroy);
    $$.fragment && $$.fragment.d(detaching);
    $$.on_destroy = $$.fragment = null;
    $$.ctx = [];
  }
}
function make_dirty(component, i) {
  if (component.$$.dirty[0] === -1) {
    dirty_components.push(component);
    schedule_update();
    component.$$.dirty.fill(0);
  }
  component.$$.dirty[i / 31 | 0] |= 1 << i % 31;
}
function init(component, options2, instance36, create_fragment36, not_equal, props, append_styles2 = null, dirty = [-1]) {
  const parent_component = current_component;
  set_current_component(component);
  const $$ = component.$$ = {
    fragment: null,
    ctx: [],
    // state
    props,
    update: noop,
    not_equal,
    bound: blank_object(),
    // lifecycle
    on_mount: [],
    on_destroy: [],
    on_disconnect: [],
    before_update: [],
    after_update: [],
    context: new Map(options2.context || (parent_component ? parent_component.$$.context : [])),
    // everything else
    callbacks: blank_object(),
    dirty,
    skip_bound: false,
    root: options2.target || parent_component.$$.root
  };
  append_styles2 && append_styles2($$.root);
  let ready = false;
  $$.ctx = instance36 ? instance36(component, options2.props || {}, (i, ret, ...rest) => {
    const value = rest.length ? rest[0] : ret;
    if ($$.ctx && not_equal($$.ctx[i], $$.ctx[i] = value)) {
      if (!$$.skip_bound && $$.bound[i])
        $$.bound[i](value);
      if (ready)
        make_dirty(component, i);
    }
    return ret;
  }) : [];
  $$.update();
  ready = true;
  run_all($$.before_update);
  $$.fragment = create_fragment36 ? create_fragment36($$.ctx) : false;
  if (options2.target) {
    if (options2.hydrate) {
      start_hydrating();
      const nodes = children(options2.target);
      $$.fragment && $$.fragment.l(nodes);
      nodes.forEach(detach);
    } else {
      $$.fragment && $$.fragment.c();
    }
    if (options2.intro)
      transition_in(component.$$.fragment);
    mount_component(component, options2.target, options2.anchor);
    end_hydrating();
    flush();
  }
  set_current_component(parent_component);
}
var SvelteElement;
if (typeof HTMLElement === "function") {
  SvelteElement = class extends HTMLElement {
    constructor($$componentCtor, $$slots, use_shadow_dom) {
      super();
      /** The Svelte component constructor */
      __publicField(this, "$$ctor");
      /** Slots */
      __publicField(this, "$$s");
      /** The Svelte component instance */
      __publicField(this, "$$c");
      /** Whether or not the custom element is connected */
      __publicField(this, "$$cn", false);
      /** Component props data */
      __publicField(this, "$$d", {});
      /** `true` if currently in the process of reflecting component props back to attributes */
      __publicField(this, "$$r", false);
      /** @type {Record<string, CustomElementPropDefinition>} Props definition (name, reflected, type etc) */
      __publicField(this, "$$p_d", {});
      /** @type {Record<string, Function[]>} Event listeners */
      __publicField(this, "$$l", {});
      /** @type {Map<Function, Function>} Event listener unsubscribe functions */
      __publicField(this, "$$l_u", /* @__PURE__ */ new Map());
      this.$$ctor = $$componentCtor;
      this.$$s = $$slots;
      if (use_shadow_dom) {
        this.attachShadow({ mode: "open" });
      }
    }
    addEventListener(type, listener, options2) {
      this.$$l[type] = this.$$l[type] || [];
      this.$$l[type].push(listener);
      if (this.$$c) {
        const unsub = this.$$c.$on(type, listener);
        this.$$l_u.set(listener, unsub);
      }
      super.addEventListener(type, listener, options2);
    }
    removeEventListener(type, listener, options2) {
      super.removeEventListener(type, listener, options2);
      if (this.$$c) {
        const unsub = this.$$l_u.get(listener);
        if (unsub) {
          unsub();
          this.$$l_u.delete(listener);
        }
      }
    }
    async connectedCallback() {
      this.$$cn = true;
      if (!this.$$c) {
        let create_slot2 = function(name) {
          return () => {
            let node;
            const obj = {
              c: function create() {
                node = element("slot");
                if (name !== "default") {
                  attr(node, "name", name);
                }
              },
              /**
               * @param {HTMLElement} target
               * @param {HTMLElement} [anchor]
               */
              m: function mount(target, anchor) {
                insert(target, node, anchor);
              },
              d: function destroy(detaching) {
                if (detaching) {
                  detach(node);
                }
              }
            };
            return obj;
          };
        };
        await Promise.resolve();
        if (!this.$$cn) {
          return;
        }
        const $$slots = {};
        const existing_slots = get_custom_elements_slots(this);
        for (const name of this.$$s) {
          if (name in existing_slots) {
            $$slots[name] = [create_slot2(name)];
          }
        }
        for (const attribute of this.attributes) {
          const name = this.$$g_p(attribute.name);
          if (!(name in this.$$d)) {
            this.$$d[name] = get_custom_element_value(name, attribute.value, this.$$p_d, "toProp");
          }
        }
        this.$$c = new this.$$ctor({
          target: this.shadowRoot || this,
          props: {
            ...this.$$d,
            $$slots,
            $$scope: {
              ctx: []
            }
          }
        });
        const reflect_attributes = () => {
          this.$$r = true;
          for (const key in this.$$p_d) {
            this.$$d[key] = this.$$c.$$.ctx[this.$$c.$$.props[key]];
            if (this.$$p_d[key].reflect) {
              const attribute_value = get_custom_element_value(
                key,
                this.$$d[key],
                this.$$p_d,
                "toAttribute"
              );
              if (attribute_value == null) {
                this.removeAttribute(this.$$p_d[key].attribute || key);
              } else {
                this.setAttribute(this.$$p_d[key].attribute || key, attribute_value);
              }
            }
          }
          this.$$r = false;
        };
        this.$$c.$$.after_update.push(reflect_attributes);
        reflect_attributes();
        for (const type in this.$$l) {
          for (const listener of this.$$l[type]) {
            const unsub = this.$$c.$on(type, listener);
            this.$$l_u.set(listener, unsub);
          }
        }
        this.$$l = {};
      }
    }
    // We don't need this when working within Svelte code, but for compatibility of people using this outside of Svelte
    // and setting attributes through setAttribute etc, this is helpful
    attributeChangedCallback(attr2, _oldValue, newValue) {
      if (this.$$r)
        return;
      attr2 = this.$$g_p(attr2);
      this.$$d[attr2] = get_custom_element_value(attr2, newValue, this.$$p_d, "toProp");
      this.$$c?.$set({ [attr2]: this.$$d[attr2] });
    }
    disconnectedCallback() {
      this.$$cn = false;
      Promise.resolve().then(() => {
        if (!this.$$cn) {
          this.$$c.$destroy();
          this.$$c = void 0;
        }
      });
    }
    $$g_p(attribute_name) {
      return Object.keys(this.$$p_d).find(
        (key) => this.$$p_d[key].attribute === attribute_name || !this.$$p_d[key].attribute && key.toLowerCase() === attribute_name
      ) || attribute_name;
    }
  };
}
function get_custom_element_value(prop, value, props_definition, transform) {
  const type = props_definition[prop]?.type;
  value = type === "Boolean" && typeof value !== "boolean" ? value != null : value;
  if (!transform || !props_definition[prop]) {
    return value;
  } else if (transform === "toAttribute") {
    switch (type) {
      case "Object":
      case "Array":
        return value == null ? null : JSON.stringify(value);
      case "Boolean":
        return value ? "" : null;
      case "Number":
        return value == null ? null : value;
      default:
        return value;
    }
  } else {
    switch (type) {
      case "Object":
      case "Array":
        return value && JSON.parse(value);
      case "Boolean":
        return value;
      case "Number":
        return value != null ? +value : value;
      default:
        return value;
    }
  }
}
var SvelteComponent = class {
  constructor() {
    /**
     * ### PRIVATE API
     *
     * Do not use, may change at any time
     *
     * @type {any}
     */
    __publicField(this, "$$");
    /**
     * ### PRIVATE API
     *
     * Do not use, may change at any time
     *
     * @type {any}
     */
    __publicField(this, "$$set");
  }
  /** @returns {void} */
  $destroy() {
    destroy_component(this, 1);
    this.$destroy = noop;
  }
  /**
   * @template {Extract<keyof Events, string>} K
   * @param {K} type
   * @param {((e: Events[K]) => void) | null | undefined} callback
   * @returns {() => void}
   */
  $on(type, callback) {
    if (!is_function(callback)) {
      return noop;
    }
    const callbacks = this.$$.callbacks[type] || (this.$$.callbacks[type] = []);
    callbacks.push(callback);
    return () => {
      const index = callbacks.indexOf(callback);
      if (index !== -1)
        callbacks.splice(index, 1);
    };
  }
  /**
   * @param {Partial<Props>} props
   * @returns {void}
   */
  $set(props) {
    if (this.$$set && !is_empty(props)) {
      this.$$.skip_bound = true;
      this.$$set(props);
      this.$$.skip_bound = false;
    }
  }
};

// node_modules/svelte/src/shared/version.js
var PUBLIC_VERSION = "4";

// node_modules/svelte/src/runtime/internal/disclose-version/index.js
if (typeof window !== "undefined")
  (window.__svelte || (window.__svelte = { v: /* @__PURE__ */ new Set() })).v.add(PUBLIC_VERSION);

// node_modules/svelte/src/runtime/store/index.js
var subscriber_queue = [];
function readable(value, start) {
  return {
    subscribe: writable(value, start).subscribe
  };
}
function writable(value, start = noop) {
  let stop;
  const subscribers = /* @__PURE__ */ new Set();
  function set(new_value) {
    if (safe_not_equal(value, new_value)) {
      value = new_value;
      if (stop) {
        const run_queue = !subscriber_queue.length;
        for (const subscriber of subscribers) {
          subscriber[1]();
          subscriber_queue.push(subscriber, value);
        }
        if (run_queue) {
          for (let i = 0; i < subscriber_queue.length; i += 2) {
            subscriber_queue[i][0](subscriber_queue[i + 1]);
          }
          subscriber_queue.length = 0;
        }
      }
    }
  }
  function update2(fn) {
    set(fn(value));
  }
  function subscribe2(run2, invalidate = noop) {
    const subscriber = [run2, invalidate];
    subscribers.add(subscriber);
    if (subscribers.size === 1) {
      stop = start(set, update2) || noop;
    }
    run2(value);
    return () => {
      subscribers.delete(subscriber);
      if (subscribers.size === 0 && stop) {
        stop();
        stop = null;
      }
    };
  }
  return { set, update: update2, subscribe: subscribe2 };
}
function derived(stores, fn, initial_value) {
  const single = !Array.isArray(stores);
  const stores_array = single ? [stores] : stores;
  if (!stores_array.every(Boolean)) {
    throw new Error("derived() expects stores as input, got a falsy value");
  }
  const auto = fn.length < 2;
  return readable(initial_value, (set, update2) => {
    let started = false;
    const values = [];
    let pending = 0;
    let cleanup = noop;
    const sync = () => {
      if (pending) {
        return;
      }
      cleanup();
      const result = fn(single ? values[0] : values, set, update2);
      if (auto) {
        set(result);
      } else {
        cleanup = is_function(result) ? result : noop;
      }
    };
    const unsubscribers = stores_array.map(
      (store, i) => subscribe(
        store,
        (value) => {
          values[i] = value;
          pending &= ~(1 << i);
          if (started) {
            sync();
          }
        },
        () => {
          pending |= 1 << i;
        }
      )
    );
    started = true;
    sync();
    return function stop() {
      run_all(unsubscribers);
      cleanup();
      started = false;
    };
  });
}

// src/sidebar-outline/components/components/controls-bar/components/search-input.store.ts
var searchTerm = writable("");

// src/sidebar-outline/components/components/annotations-list/annotations-list.store.ts
var labelSettings = writable({});
var fileAnnotations = writable({ labels: {} });
var hiddenTypes = writable(/* @__PURE__ */ new Set());
var hiddenLabels = writable(/* @__PURE__ */ new Set());
var activeAnnotationIndex = writable(-1);
var filteredBySearch = derived(
  [searchTerm, fileAnnotations],
  ([$term, $items]) => {
    const term = $term.toLowerCase();
    if (!term)
      return $items;
    const filteredDictionary = { labels: {} };
    for (const [label, annotations] of Object.entries($items.labels)) {
      const filteredAnnotations = annotations.filter((v) => {
        return v.text.toLowerCase().includes(term);
      });
      if (filteredAnnotations.length) {
        filteredDictionary.labels[label] = filteredAnnotations;
      }
    }
    return filteredDictionary;
  }
);
var filteredBySearchAndCategory = derived(
  [filteredBySearch, hiddenTypes],
  ([$items, categories]) => {
    if (!categories.size)
      return $items;
    const filteredDictionary = { labels: {} };
    for (const [label, annotations] of Object.entries($items.labels)) {
      const filteredAnnotations = annotations.filter((v) => {
        return !categories.has(v.isHighlight ? "highlight" : "comment");
      });
      if (filteredAnnotations.length) {
        filteredDictionary.labels[label] = filteredAnnotations;
      }
    }
    return filteredDictionary;
  }
);
var filteredBySearchAndCategoryAndLabel = derived(
  [filteredBySearchAndCategory, hiddenLabels],
  ([filteredAnnotations, hiddenLabels2]) => {
    const visibleAnnotations = [];
    for (const [label, annotations] of Object.entries(
      filteredAnnotations.labels
    )) {
      if (!hiddenLabels2.has(label)) {
        visibleAnnotations.push(...annotations);
      }
    }
    return visibleAnnotations.sort(
      (a, b) => a.range.from.line - b.range.from.line
    );
  }
);
var filteredHiddenLabels = derived(
  [hiddenLabels, filteredBySearchAndCategory],
  ([hiddenLabels2, filteredAnnotations]) => {
    const existingLabels = new Set(Object.keys(filteredAnnotations.labels));
    return new Set([...hiddenLabels2].filter((l2) => existingLabels.has(l2)));
  }
);
var filteredHiddenCategories = derived(
  [hiddenTypes, filteredBySearch],
  ([hiddenCategories, filteredAnnotations]) => {
    const existing = /* @__PURE__ */ new Set();
    const annotations = Object.values(filteredAnnotations.labels).flat();
    if (annotations.find((c) => !c.isHighlight))
      existing.add("comment");
    if (annotations.find((c) => c.isHighlight))
      existing.add("highlight");
    return new Set([...hiddenCategories].filter((l2) => existing.has(l2)));
  }
);

// node_modules/lucide-svelte/dist/esm/defaultAttributes.js
var defaultAttributes = {
  xmlns: "http://www.w3.org/2000/svg",
  width: 24,
  height: 24,
  viewBox: "0 0 24 24",
  fill: "none",
  stroke: "currentColor",
  "stroke-width": 2,
  "stroke-linecap": "round",
  "stroke-linejoin": "round"
};

// node_modules/lucide-svelte/dist/esm/Icon.svelte.js
function get_each_context(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[10] = list[i][0];
  child_ctx[11] = list[i][1];
  return child_ctx;
}
function create_dynamic_element(ctx) {
  let svelte_element;
  let svelte_element_levels = [
    /*attrs*/
    ctx[11]
  ];
  let svelte_element_data = {};
  for (let i = 0; i < svelte_element_levels.length; i += 1) {
    svelte_element_data = assign(svelte_element_data, svelte_element_levels[i]);
  }
  return {
    c() {
      svelte_element = svg_element(
        /*tag*/
        ctx[10]
      );
      this.h();
    },
    l(nodes) {
      svelte_element = claim_svg_element(
        nodes,
        /*tag*/
        ctx[10],
        {}
      );
      children(svelte_element).forEach(detach);
      this.h();
    },
    h() {
      set_svg_attributes(svelte_element, svelte_element_data);
    },
    m(target, anchor) {
      insert_hydration(target, svelte_element, anchor);
    },
    p(ctx2, dirty) {
      set_svg_attributes(svelte_element, svelte_element_data = get_spread_update(svelte_element_levels, [dirty & /*iconNode*/
      32 && /*attrs*/
      ctx2[11]]));
    },
    d(detaching) {
      if (detaching) {
        detach(svelte_element);
      }
    }
  };
}
function create_each_block(ctx) {
  let previous_tag = (
    /*tag*/
    ctx[10]
  );
  let svelte_element_anchor;
  let svelte_element = (
    /*tag*/
    ctx[10] && create_dynamic_element(ctx)
  );
  return {
    c() {
      if (svelte_element)
        svelte_element.c();
      svelte_element_anchor = empty();
    },
    l(nodes) {
      if (svelte_element)
        svelte_element.l(nodes);
      svelte_element_anchor = empty();
    },
    m(target, anchor) {
      if (svelte_element)
        svelte_element.m(target, anchor);
      insert_hydration(target, svelte_element_anchor, anchor);
    },
    p(ctx2, dirty) {
      if (
        /*tag*/
        ctx2[10]
      ) {
        if (!previous_tag) {
          svelte_element = create_dynamic_element(ctx2);
          previous_tag = /*tag*/
          ctx2[10];
          svelte_element.c();
          svelte_element.m(svelte_element_anchor.parentNode, svelte_element_anchor);
        } else if (safe_not_equal(
          previous_tag,
          /*tag*/
          ctx2[10]
        )) {
          svelte_element.d(1);
          svelte_element = create_dynamic_element(ctx2);
          previous_tag = /*tag*/
          ctx2[10];
          svelte_element.c();
          svelte_element.m(svelte_element_anchor.parentNode, svelte_element_anchor);
        } else {
          svelte_element.p(ctx2, dirty);
        }
      } else if (previous_tag) {
        svelte_element.d(1);
        svelte_element = null;
        previous_tag = /*tag*/
        ctx2[10];
      }
    },
    d(detaching) {
      if (detaching) {
        detach(svelte_element_anchor);
      }
      if (svelte_element)
        svelte_element.d(detaching);
    }
  };
}
function create_fragment(ctx) {
  let svg;
  let each_1_anchor;
  let svg_stroke_width_value;
  let svg_class_value;
  let current;
  let each_value = ensure_array_like(
    /*iconNode*/
    ctx[5]
  );
  let each_blocks = [];
  for (let i = 0; i < each_value.length; i += 1) {
    each_blocks[i] = create_each_block(get_each_context(ctx, each_value, i));
  }
  const default_slot_template = (
    /*#slots*/
    ctx[9].default
  );
  const default_slot = create_slot(
    default_slot_template,
    ctx,
    /*$$scope*/
    ctx[8],
    null
  );
  let svg_levels = [
    defaultAttributes,
    /*$$restProps*/
    ctx[6],
    { width: (
      /*size*/
      ctx[2]
    ) },
    { height: (
      /*size*/
      ctx[2]
    ) },
    { stroke: (
      /*color*/
      ctx[1]
    ) },
    {
      "stroke-width": svg_stroke_width_value = /*absoluteStrokeWidth*/
      ctx[4] ? Number(
        /*strokeWidth*/
        ctx[3]
      ) * 24 / Number(
        /*size*/
        ctx[2]
      ) : (
        /*strokeWidth*/
        ctx[3]
      )
    },
    {
      class: svg_class_value = `lucide-icon lucide lucide-${/*name*/
      ctx[0]} ${/*$$props*/
      ctx[7].class ?? ""}`
    }
  ];
  let svg_data = {};
  for (let i = 0; i < svg_levels.length; i += 1) {
    svg_data = assign(svg_data, svg_levels[i]);
  }
  return {
    c() {
      svg = svg_element("svg");
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      each_1_anchor = empty();
      if (default_slot)
        default_slot.c();
      this.h();
    },
    l(nodes) {
      svg = claim_svg_element(nodes, "svg", {
        width: true,
        height: true,
        stroke: true,
        "stroke-width": true,
        class: true
      });
      var svg_nodes = children(svg);
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].l(svg_nodes);
      }
      each_1_anchor = empty();
      if (default_slot)
        default_slot.l(svg_nodes);
      svg_nodes.forEach(detach);
      this.h();
    },
    h() {
      set_svg_attributes(svg, svg_data);
    },
    m(target, anchor) {
      insert_hydration(target, svg, anchor);
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(svg, null);
        }
      }
      append_hydration(svg, each_1_anchor);
      if (default_slot) {
        default_slot.m(svg, null);
      }
      current = true;
    },
    p(ctx2, [dirty]) {
      if (dirty & /*iconNode*/
      32) {
        each_value = ensure_array_like(
          /*iconNode*/
          ctx2[5]
        );
        let i;
        for (i = 0; i < each_value.length; i += 1) {
          const child_ctx = get_each_context(ctx2, each_value, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(svg, each_1_anchor);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value.length;
      }
      if (default_slot) {
        if (default_slot.p && (!current || dirty & /*$$scope*/
        256)) {
          update_slot_base(
            default_slot,
            default_slot_template,
            ctx2,
            /*$$scope*/
            ctx2[8],
            !current ? get_all_dirty_from_scope(
              /*$$scope*/
              ctx2[8]
            ) : get_slot_changes(
              default_slot_template,
              /*$$scope*/
              ctx2[8],
              dirty,
              null
            ),
            null
          );
        }
      }
      set_svg_attributes(svg, svg_data = get_spread_update(svg_levels, [
        defaultAttributes,
        dirty & /*$$restProps*/
        64 && /*$$restProps*/
        ctx2[6],
        (!current || dirty & /*size*/
        4) && { width: (
          /*size*/
          ctx2[2]
        ) },
        (!current || dirty & /*size*/
        4) && { height: (
          /*size*/
          ctx2[2]
        ) },
        (!current || dirty & /*color*/
        2) && { stroke: (
          /*color*/
          ctx2[1]
        ) },
        (!current || dirty & /*absoluteStrokeWidth, strokeWidth, size*/
        28 && svg_stroke_width_value !== (svg_stroke_width_value = /*absoluteStrokeWidth*/
        ctx2[4] ? Number(
          /*strokeWidth*/
          ctx2[3]
        ) * 24 / Number(
          /*size*/
          ctx2[2]
        ) : (
          /*strokeWidth*/
          ctx2[3]
        ))) && { "stroke-width": svg_stroke_width_value },
        (!current || dirty & /*name, $$props*/
        129 && svg_class_value !== (svg_class_value = `lucide-icon lucide lucide-${/*name*/
        ctx2[0]} ${/*$$props*/
        ctx2[7].class ?? ""}`)) && { class: svg_class_value }
      ]));
    },
    i(local) {
      if (current)
        return;
      transition_in(default_slot, local);
      current = true;
    },
    o(local) {
      transition_out(default_slot, local);
      current = false;
    },
    d(detaching) {
      if (detaching) {
        detach(svg);
      }
      destroy_each(each_blocks, detaching);
      if (default_slot)
        default_slot.d(detaching);
    }
  };
}
function instance($$self, $$props, $$invalidate) {
  const omit_props_names = ["name", "color", "size", "strokeWidth", "absoluteStrokeWidth", "iconNode"];
  let $$restProps = compute_rest_props($$props, omit_props_names);
  let { $$slots: slots = {}, $$scope } = $$props;
  let { name } = $$props;
  let { color = "currentColor" } = $$props;
  let { size = 24 } = $$props;
  let { strokeWidth = 2 } = $$props;
  let { absoluteStrokeWidth = false } = $$props;
  let { iconNode } = $$props;
  $$self.$$set = ($$new_props) => {
    $$invalidate(7, $$props = assign(assign({}, $$props), exclude_internal_props($$new_props)));
    $$invalidate(6, $$restProps = compute_rest_props($$props, omit_props_names));
    if ("name" in $$new_props)
      $$invalidate(0, name = $$new_props.name);
    if ("color" in $$new_props)
      $$invalidate(1, color = $$new_props.color);
    if ("size" in $$new_props)
      $$invalidate(2, size = $$new_props.size);
    if ("strokeWidth" in $$new_props)
      $$invalidate(3, strokeWidth = $$new_props.strokeWidth);
    if ("absoluteStrokeWidth" in $$new_props)
      $$invalidate(4, absoluteStrokeWidth = $$new_props.absoluteStrokeWidth);
    if ("iconNode" in $$new_props)
      $$invalidate(5, iconNode = $$new_props.iconNode);
    if ("$$scope" in $$new_props)
      $$invalidate(8, $$scope = $$new_props.$$scope);
  };
  $$props = exclude_internal_props($$props);
  return [
    name,
    color,
    size,
    strokeWidth,
    absoluteStrokeWidth,
    iconNode,
    $$restProps,
    $$props,
    $$scope,
    slots
  ];
}
var Icon = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance, create_fragment, safe_not_equal, {
      name: 0,
      color: 1,
      size: 2,
      strokeWidth: 3,
      absoluteStrokeWidth: 4,
      iconNode: 5
    });
  }
};
var Icon$1 = Icon;

// node_modules/lucide-svelte/dist/esm/icons/case-lower.svelte.js
function create_default_slot(ctx) {
  let current;
  const default_slot_template = (
    /*#slots*/
    ctx[2].default
  );
  const default_slot = create_slot(
    default_slot_template,
    ctx,
    /*$$scope*/
    ctx[3],
    null
  );
  return {
    c() {
      if (default_slot)
        default_slot.c();
    },
    l(nodes) {
      if (default_slot)
        default_slot.l(nodes);
    },
    m(target, anchor) {
      if (default_slot) {
        default_slot.m(target, anchor);
      }
      current = true;
    },
    p(ctx2, dirty) {
      if (default_slot) {
        if (default_slot.p && (!current || dirty & /*$$scope*/
        8)) {
          update_slot_base(
            default_slot,
            default_slot_template,
            ctx2,
            /*$$scope*/
            ctx2[3],
            !current ? get_all_dirty_from_scope(
              /*$$scope*/
              ctx2[3]
            ) : get_slot_changes(
              default_slot_template,
              /*$$scope*/
              ctx2[3],
              dirty,
              null
            ),
            null
          );
        }
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(default_slot, local);
      current = true;
    },
    o(local) {
      transition_out(default_slot, local);
      current = false;
    },
    d(detaching) {
      if (default_slot)
        default_slot.d(detaching);
    }
  };
}
function create_fragment2(ctx) {
  let icon;
  let current;
  const icon_spread_levels = [
    { name: "case-lower" },
    /*$$props*/
    ctx[1],
    { iconNode: (
      /*iconNode*/
      ctx[0]
    ) }
  ];
  let icon_props = {
    $$slots: { default: [create_default_slot] },
    $$scope: { ctx }
  };
  for (let i = 0; i < icon_spread_levels.length; i += 1) {
    icon_props = assign(icon_props, icon_spread_levels[i]);
  }
  icon = new Icon$1({ props: icon_props });
  return {
    c() {
      create_component(icon.$$.fragment);
    },
    l(nodes) {
      claim_component(icon.$$.fragment, nodes);
    },
    m(target, anchor) {
      mount_component(icon, target, anchor);
      current = true;
    },
    p(ctx2, [dirty]) {
      const icon_changes = dirty & /*$$props, iconNode*/
      3 ? get_spread_update(icon_spread_levels, [
        icon_spread_levels[0],
        dirty & /*$$props*/
        2 && get_spread_object(
          /*$$props*/
          ctx2[1]
        ),
        dirty & /*iconNode*/
        1 && { iconNode: (
          /*iconNode*/
          ctx2[0]
        ) }
      ]) : {};
      if (dirty & /*$$scope*/
      8) {
        icon_changes.$$scope = { dirty, ctx: ctx2 };
      }
      icon.$set(icon_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(icon.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(icon.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(icon, detaching);
    }
  };
}
function instance2($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  const iconNode = [
    ["circle", { "cx": "7", "cy": "12", "r": "3" }],
    ["path", { "d": "M10 9v6" }],
    ["circle", { "cx": "17", "cy": "12", "r": "3" }],
    ["path", { "d": "M14 7v8" }]
  ];
  $$self.$$set = ($$new_props) => {
    $$invalidate(1, $$props = assign(assign({}, $$props), exclude_internal_props($$new_props)));
    if ("$$scope" in $$new_props)
      $$invalidate(3, $$scope = $$new_props.$$scope);
  };
  $$props = exclude_internal_props($$props);
  return [iconNode, $$props, slots, $$scope];
}
var Case_lower = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance2, create_fragment2, safe_not_equal, {});
  }
};
var Case_lower$1 = Case_lower;

// node_modules/lucide-svelte/dist/esm/icons/case-sensitive.svelte.js
function create_default_slot2(ctx) {
  let current;
  const default_slot_template = (
    /*#slots*/
    ctx[2].default
  );
  const default_slot = create_slot(
    default_slot_template,
    ctx,
    /*$$scope*/
    ctx[3],
    null
  );
  return {
    c() {
      if (default_slot)
        default_slot.c();
    },
    l(nodes) {
      if (default_slot)
        default_slot.l(nodes);
    },
    m(target, anchor) {
      if (default_slot) {
        default_slot.m(target, anchor);
      }
      current = true;
    },
    p(ctx2, dirty) {
      if (default_slot) {
        if (default_slot.p && (!current || dirty & /*$$scope*/
        8)) {
          update_slot_base(
            default_slot,
            default_slot_template,
            ctx2,
            /*$$scope*/
            ctx2[3],
            !current ? get_all_dirty_from_scope(
              /*$$scope*/
              ctx2[3]
            ) : get_slot_changes(
              default_slot_template,
              /*$$scope*/
              ctx2[3],
              dirty,
              null
            ),
            null
          );
        }
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(default_slot, local);
      current = true;
    },
    o(local) {
      transition_out(default_slot, local);
      current = false;
    },
    d(detaching) {
      if (default_slot)
        default_slot.d(detaching);
    }
  };
}
function create_fragment3(ctx) {
  let icon;
  let current;
  const icon_spread_levels = [
    { name: "case-sensitive" },
    /*$$props*/
    ctx[1],
    { iconNode: (
      /*iconNode*/
      ctx[0]
    ) }
  ];
  let icon_props = {
    $$slots: { default: [create_default_slot2] },
    $$scope: { ctx }
  };
  for (let i = 0; i < icon_spread_levels.length; i += 1) {
    icon_props = assign(icon_props, icon_spread_levels[i]);
  }
  icon = new Icon$1({ props: icon_props });
  return {
    c() {
      create_component(icon.$$.fragment);
    },
    l(nodes) {
      claim_component(icon.$$.fragment, nodes);
    },
    m(target, anchor) {
      mount_component(icon, target, anchor);
      current = true;
    },
    p(ctx2, [dirty]) {
      const icon_changes = dirty & /*$$props, iconNode*/
      3 ? get_spread_update(icon_spread_levels, [
        icon_spread_levels[0],
        dirty & /*$$props*/
        2 && get_spread_object(
          /*$$props*/
          ctx2[1]
        ),
        dirty & /*iconNode*/
        1 && { iconNode: (
          /*iconNode*/
          ctx2[0]
        ) }
      ]) : {};
      if (dirty & /*$$scope*/
      8) {
        icon_changes.$$scope = { dirty, ctx: ctx2 };
      }
      icon.$set(icon_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(icon.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(icon.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(icon, detaching);
    }
  };
}
function instance3($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  const iconNode = [
    ["path", { "d": "m3 15 4-8 4 8" }],
    ["path", { "d": "M4 13h6" }],
    ["circle", { "cx": "18", "cy": "12", "r": "3" }],
    ["path", { "d": "M21 9v6" }]
  ];
  $$self.$$set = ($$new_props) => {
    $$invalidate(1, $$props = assign(assign({}, $$props), exclude_internal_props($$new_props)));
    if ("$$scope" in $$new_props)
      $$invalidate(3, $$scope = $$new_props.$$scope);
  };
  $$props = exclude_internal_props($$props);
  return [iconNode, $$props, slots, $$scope];
}
var Case_sensitive = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance3, create_fragment3, safe_not_equal, {});
  }
};
var Case_sensitive$1 = Case_sensitive;

// node_modules/lucide-svelte/dist/esm/icons/case-upper.svelte.js
function create_default_slot3(ctx) {
  let current;
  const default_slot_template = (
    /*#slots*/
    ctx[2].default
  );
  const default_slot = create_slot(
    default_slot_template,
    ctx,
    /*$$scope*/
    ctx[3],
    null
  );
  return {
    c() {
      if (default_slot)
        default_slot.c();
    },
    l(nodes) {
      if (default_slot)
        default_slot.l(nodes);
    },
    m(target, anchor) {
      if (default_slot) {
        default_slot.m(target, anchor);
      }
      current = true;
    },
    p(ctx2, dirty) {
      if (default_slot) {
        if (default_slot.p && (!current || dirty & /*$$scope*/
        8)) {
          update_slot_base(
            default_slot,
            default_slot_template,
            ctx2,
            /*$$scope*/
            ctx2[3],
            !current ? get_all_dirty_from_scope(
              /*$$scope*/
              ctx2[3]
            ) : get_slot_changes(
              default_slot_template,
              /*$$scope*/
              ctx2[3],
              dirty,
              null
            ),
            null
          );
        }
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(default_slot, local);
      current = true;
    },
    o(local) {
      transition_out(default_slot, local);
      current = false;
    },
    d(detaching) {
      if (default_slot)
        default_slot.d(detaching);
    }
  };
}
function create_fragment4(ctx) {
  let icon;
  let current;
  const icon_spread_levels = [
    { name: "case-upper" },
    /*$$props*/
    ctx[1],
    { iconNode: (
      /*iconNode*/
      ctx[0]
    ) }
  ];
  let icon_props = {
    $$slots: { default: [create_default_slot3] },
    $$scope: { ctx }
  };
  for (let i = 0; i < icon_spread_levels.length; i += 1) {
    icon_props = assign(icon_props, icon_spread_levels[i]);
  }
  icon = new Icon$1({ props: icon_props });
  return {
    c() {
      create_component(icon.$$.fragment);
    },
    l(nodes) {
      claim_component(icon.$$.fragment, nodes);
    },
    m(target, anchor) {
      mount_component(icon, target, anchor);
      current = true;
    },
    p(ctx2, [dirty]) {
      const icon_changes = dirty & /*$$props, iconNode*/
      3 ? get_spread_update(icon_spread_levels, [
        icon_spread_levels[0],
        dirty & /*$$props*/
        2 && get_spread_object(
          /*$$props*/
          ctx2[1]
        ),
        dirty & /*iconNode*/
        1 && { iconNode: (
          /*iconNode*/
          ctx2[0]
        ) }
      ]) : {};
      if (dirty & /*$$scope*/
      8) {
        icon_changes.$$scope = { dirty, ctx: ctx2 };
      }
      icon.$set(icon_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(icon.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(icon.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(icon, detaching);
    }
  };
}
function instance4($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  const iconNode = [
    ["path", { "d": "m3 15 4-8 4 8" }],
    ["path", { "d": "M4 13h6" }],
    [
      "path",
      {
        "d": "M15 11h4.5a2 2 0 0 1 0 4H15V7h4a2 2 0 0 1 0 4"
      }
    ]
  ];
  $$self.$$set = ($$new_props) => {
    $$invalidate(1, $$props = assign(assign({}, $$props), exclude_internal_props($$new_props)));
    if ("$$scope" in $$new_props)
      $$invalidate(3, $$scope = $$new_props.$$scope);
  };
  $$props = exclude_internal_props($$props);
  return [iconNode, $$props, slots, $$scope];
}
var Case_upper = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance4, create_fragment4, safe_not_equal, {});
  }
};
var Case_upper$1 = Case_upper;

// node_modules/lucide-svelte/dist/esm/icons/chevron-down.svelte.js
function create_default_slot4(ctx) {
  let current;
  const default_slot_template = (
    /*#slots*/
    ctx[2].default
  );
  const default_slot = create_slot(
    default_slot_template,
    ctx,
    /*$$scope*/
    ctx[3],
    null
  );
  return {
    c() {
      if (default_slot)
        default_slot.c();
    },
    l(nodes) {
      if (default_slot)
        default_slot.l(nodes);
    },
    m(target, anchor) {
      if (default_slot) {
        default_slot.m(target, anchor);
      }
      current = true;
    },
    p(ctx2, dirty) {
      if (default_slot) {
        if (default_slot.p && (!current || dirty & /*$$scope*/
        8)) {
          update_slot_base(
            default_slot,
            default_slot_template,
            ctx2,
            /*$$scope*/
            ctx2[3],
            !current ? get_all_dirty_from_scope(
              /*$$scope*/
              ctx2[3]
            ) : get_slot_changes(
              default_slot_template,
              /*$$scope*/
              ctx2[3],
              dirty,
              null
            ),
            null
          );
        }
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(default_slot, local);
      current = true;
    },
    o(local) {
      transition_out(default_slot, local);
      current = false;
    },
    d(detaching) {
      if (default_slot)
        default_slot.d(detaching);
    }
  };
}
function create_fragment5(ctx) {
  let icon;
  let current;
  const icon_spread_levels = [
    { name: "chevron-down" },
    /*$$props*/
    ctx[1],
    { iconNode: (
      /*iconNode*/
      ctx[0]
    ) }
  ];
  let icon_props = {
    $$slots: { default: [create_default_slot4] },
    $$scope: { ctx }
  };
  for (let i = 0; i < icon_spread_levels.length; i += 1) {
    icon_props = assign(icon_props, icon_spread_levels[i]);
  }
  icon = new Icon$1({ props: icon_props });
  return {
    c() {
      create_component(icon.$$.fragment);
    },
    l(nodes) {
      claim_component(icon.$$.fragment, nodes);
    },
    m(target, anchor) {
      mount_component(icon, target, anchor);
      current = true;
    },
    p(ctx2, [dirty]) {
      const icon_changes = dirty & /*$$props, iconNode*/
      3 ? get_spread_update(icon_spread_levels, [
        icon_spread_levels[0],
        dirty & /*$$props*/
        2 && get_spread_object(
          /*$$props*/
          ctx2[1]
        ),
        dirty & /*iconNode*/
        1 && { iconNode: (
          /*iconNode*/
          ctx2[0]
        ) }
      ]) : {};
      if (dirty & /*$$scope*/
      8) {
        icon_changes.$$scope = { dirty, ctx: ctx2 };
      }
      icon.$set(icon_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(icon.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(icon.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(icon, detaching);
    }
  };
}
function instance5($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  const iconNode = [["path", { "d": "m6 9 6 6 6-6" }]];
  $$self.$$set = ($$new_props) => {
    $$invalidate(1, $$props = assign(assign({}, $$props), exclude_internal_props($$new_props)));
    if ("$$scope" in $$new_props)
      $$invalidate(3, $$scope = $$new_props.$$scope);
  };
  $$props = exclude_internal_props($$props);
  return [iconNode, $$props, slots, $$scope];
}
var Chevron_down = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance5, create_fragment5, safe_not_equal, {});
  }
};
var Chevron_down$1 = Chevron_down;

// node_modules/lucide-svelte/dist/esm/icons/chevron-up.svelte.js
function create_default_slot5(ctx) {
  let current;
  const default_slot_template = (
    /*#slots*/
    ctx[2].default
  );
  const default_slot = create_slot(
    default_slot_template,
    ctx,
    /*$$scope*/
    ctx[3],
    null
  );
  return {
    c() {
      if (default_slot)
        default_slot.c();
    },
    l(nodes) {
      if (default_slot)
        default_slot.l(nodes);
    },
    m(target, anchor) {
      if (default_slot) {
        default_slot.m(target, anchor);
      }
      current = true;
    },
    p(ctx2, dirty) {
      if (default_slot) {
        if (default_slot.p && (!current || dirty & /*$$scope*/
        8)) {
          update_slot_base(
            default_slot,
            default_slot_template,
            ctx2,
            /*$$scope*/
            ctx2[3],
            !current ? get_all_dirty_from_scope(
              /*$$scope*/
              ctx2[3]
            ) : get_slot_changes(
              default_slot_template,
              /*$$scope*/
              ctx2[3],
              dirty,
              null
            ),
            null
          );
        }
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(default_slot, local);
      current = true;
    },
    o(local) {
      transition_out(default_slot, local);
      current = false;
    },
    d(detaching) {
      if (default_slot)
        default_slot.d(detaching);
    }
  };
}
function create_fragment6(ctx) {
  let icon;
  let current;
  const icon_spread_levels = [
    { name: "chevron-up" },
    /*$$props*/
    ctx[1],
    { iconNode: (
      /*iconNode*/
      ctx[0]
    ) }
  ];
  let icon_props = {
    $$slots: { default: [create_default_slot5] },
    $$scope: { ctx }
  };
  for (let i = 0; i < icon_spread_levels.length; i += 1) {
    icon_props = assign(icon_props, icon_spread_levels[i]);
  }
  icon = new Icon$1({ props: icon_props });
  return {
    c() {
      create_component(icon.$$.fragment);
    },
    l(nodes) {
      claim_component(icon.$$.fragment, nodes);
    },
    m(target, anchor) {
      mount_component(icon, target, anchor);
      current = true;
    },
    p(ctx2, [dirty]) {
      const icon_changes = dirty & /*$$props, iconNode*/
      3 ? get_spread_update(icon_spread_levels, [
        icon_spread_levels[0],
        dirty & /*$$props*/
        2 && get_spread_object(
          /*$$props*/
          ctx2[1]
        ),
        dirty & /*iconNode*/
        1 && { iconNode: (
          /*iconNode*/
          ctx2[0]
        ) }
      ]) : {};
      if (dirty & /*$$scope*/
      8) {
        icon_changes.$$scope = { dirty, ctx: ctx2 };
      }
      icon.$set(icon_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(icon.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(icon.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(icon, detaching);
    }
  };
}
function instance6($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  const iconNode = [["path", { "d": "m18 15-6-6-6 6" }]];
  $$self.$$set = ($$new_props) => {
    $$invalidate(1, $$props = assign(assign({}, $$props), exclude_internal_props($$new_props)));
    if ("$$scope" in $$new_props)
      $$invalidate(3, $$scope = $$new_props.$$scope);
  };
  $$props = exclude_internal_props($$props);
  return [iconNode, $$props, slots, $$scope];
}
var Chevron_up = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance6, create_fragment6, safe_not_equal, {});
  }
};
var Chevron_up$1 = Chevron_up;

// node_modules/lucide-svelte/dist/esm/icons/highlighter.svelte.js
function create_default_slot6(ctx) {
  let current;
  const default_slot_template = (
    /*#slots*/
    ctx[2].default
  );
  const default_slot = create_slot(
    default_slot_template,
    ctx,
    /*$$scope*/
    ctx[3],
    null
  );
  return {
    c() {
      if (default_slot)
        default_slot.c();
    },
    l(nodes) {
      if (default_slot)
        default_slot.l(nodes);
    },
    m(target, anchor) {
      if (default_slot) {
        default_slot.m(target, anchor);
      }
      current = true;
    },
    p(ctx2, dirty) {
      if (default_slot) {
        if (default_slot.p && (!current || dirty & /*$$scope*/
        8)) {
          update_slot_base(
            default_slot,
            default_slot_template,
            ctx2,
            /*$$scope*/
            ctx2[3],
            !current ? get_all_dirty_from_scope(
              /*$$scope*/
              ctx2[3]
            ) : get_slot_changes(
              default_slot_template,
              /*$$scope*/
              ctx2[3],
              dirty,
              null
            ),
            null
          );
        }
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(default_slot, local);
      current = true;
    },
    o(local) {
      transition_out(default_slot, local);
      current = false;
    },
    d(detaching) {
      if (default_slot)
        default_slot.d(detaching);
    }
  };
}
function create_fragment7(ctx) {
  let icon;
  let current;
  const icon_spread_levels = [
    { name: "highlighter" },
    /*$$props*/
    ctx[1],
    { iconNode: (
      /*iconNode*/
      ctx[0]
    ) }
  ];
  let icon_props = {
    $$slots: { default: [create_default_slot6] },
    $$scope: { ctx }
  };
  for (let i = 0; i < icon_spread_levels.length; i += 1) {
    icon_props = assign(icon_props, icon_spread_levels[i]);
  }
  icon = new Icon$1({ props: icon_props });
  return {
    c() {
      create_component(icon.$$.fragment);
    },
    l(nodes) {
      claim_component(icon.$$.fragment, nodes);
    },
    m(target, anchor) {
      mount_component(icon, target, anchor);
      current = true;
    },
    p(ctx2, [dirty]) {
      const icon_changes = dirty & /*$$props, iconNode*/
      3 ? get_spread_update(icon_spread_levels, [
        icon_spread_levels[0],
        dirty & /*$$props*/
        2 && get_spread_object(
          /*$$props*/
          ctx2[1]
        ),
        dirty & /*iconNode*/
        1 && { iconNode: (
          /*iconNode*/
          ctx2[0]
        ) }
      ]) : {};
      if (dirty & /*$$scope*/
      8) {
        icon_changes.$$scope = { dirty, ctx: ctx2 };
      }
      icon.$set(icon_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(icon.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(icon.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(icon, detaching);
    }
  };
}
function instance7($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  const iconNode = [
    ["path", { "d": "m9 11-6 6v3h9l3-3" }],
    [
      "path",
      {
        "d": "m22 12-4.6 4.6a2 2 0 0 1-2.8 0l-5.2-5.2a2 2 0 0 1 0-2.8L14 4"
      }
    ]
  ];
  $$self.$$set = ($$new_props) => {
    $$invalidate(1, $$props = assign(assign({}, $$props), exclude_internal_props($$new_props)));
    if ("$$scope" in $$new_props)
      $$invalidate(3, $$scope = $$new_props.$$scope);
  };
  $$props = exclude_internal_props($$props);
  return [iconNode, $$props, slots, $$scope];
}
var Highlighter = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance7, create_fragment7, safe_not_equal, {});
  }
};
var Highlighter$1 = Highlighter;

// node_modules/lucide-svelte/dist/esm/icons/italic.svelte.js
function create_default_slot7(ctx) {
  let current;
  const default_slot_template = (
    /*#slots*/
    ctx[2].default
  );
  const default_slot = create_slot(
    default_slot_template,
    ctx,
    /*$$scope*/
    ctx[3],
    null
  );
  return {
    c() {
      if (default_slot)
        default_slot.c();
    },
    l(nodes) {
      if (default_slot)
        default_slot.l(nodes);
    },
    m(target, anchor) {
      if (default_slot) {
        default_slot.m(target, anchor);
      }
      current = true;
    },
    p(ctx2, dirty) {
      if (default_slot) {
        if (default_slot.p && (!current || dirty & /*$$scope*/
        8)) {
          update_slot_base(
            default_slot,
            default_slot_template,
            ctx2,
            /*$$scope*/
            ctx2[3],
            !current ? get_all_dirty_from_scope(
              /*$$scope*/
              ctx2[3]
            ) : get_slot_changes(
              default_slot_template,
              /*$$scope*/
              ctx2[3],
              dirty,
              null
            ),
            null
          );
        }
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(default_slot, local);
      current = true;
    },
    o(local) {
      transition_out(default_slot, local);
      current = false;
    },
    d(detaching) {
      if (default_slot)
        default_slot.d(detaching);
    }
  };
}
function create_fragment8(ctx) {
  let icon;
  let current;
  const icon_spread_levels = [
    { name: "italic" },
    /*$$props*/
    ctx[1],
    { iconNode: (
      /*iconNode*/
      ctx[0]
    ) }
  ];
  let icon_props = {
    $$slots: { default: [create_default_slot7] },
    $$scope: { ctx }
  };
  for (let i = 0; i < icon_spread_levels.length; i += 1) {
    icon_props = assign(icon_props, icon_spread_levels[i]);
  }
  icon = new Icon$1({ props: icon_props });
  return {
    c() {
      create_component(icon.$$.fragment);
    },
    l(nodes) {
      claim_component(icon.$$.fragment, nodes);
    },
    m(target, anchor) {
      mount_component(icon, target, anchor);
      current = true;
    },
    p(ctx2, [dirty]) {
      const icon_changes = dirty & /*$$props, iconNode*/
      3 ? get_spread_update(icon_spread_levels, [
        icon_spread_levels[0],
        dirty & /*$$props*/
        2 && get_spread_object(
          /*$$props*/
          ctx2[1]
        ),
        dirty & /*iconNode*/
        1 && { iconNode: (
          /*iconNode*/
          ctx2[0]
        ) }
      ]) : {};
      if (dirty & /*$$scope*/
      8) {
        icon_changes.$$scope = { dirty, ctx: ctx2 };
      }
      icon.$set(icon_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(icon.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(icon.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(icon, detaching);
    }
  };
}
function instance8($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  const iconNode = [
    [
      "line",
      {
        "x1": "19",
        "x2": "10",
        "y1": "4",
        "y2": "4"
      }
    ],
    [
      "line",
      {
        "x1": "14",
        "x2": "5",
        "y1": "20",
        "y2": "20"
      }
    ],
    [
      "line",
      {
        "x1": "15",
        "x2": "9",
        "y1": "4",
        "y2": "20"
      }
    ]
  ];
  $$self.$$set = ($$new_props) => {
    $$invalidate(1, $$props = assign(assign({}, $$props), exclude_internal_props($$new_props)));
    if ("$$scope" in $$new_props)
      $$invalidate(3, $$scope = $$new_props.$$scope);
  };
  $$props = exclude_internal_props($$props);
  return [iconNode, $$props, slots, $$scope];
}
var Italic = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance8, create_fragment8, safe_not_equal, {});
  }
};
var Italic$1 = Italic;

// node_modules/lucide-svelte/dist/esm/icons/list-filter.svelte.js
function create_default_slot8(ctx) {
  let current;
  const default_slot_template = (
    /*#slots*/
    ctx[2].default
  );
  const default_slot = create_slot(
    default_slot_template,
    ctx,
    /*$$scope*/
    ctx[3],
    null
  );
  return {
    c() {
      if (default_slot)
        default_slot.c();
    },
    l(nodes) {
      if (default_slot)
        default_slot.l(nodes);
    },
    m(target, anchor) {
      if (default_slot) {
        default_slot.m(target, anchor);
      }
      current = true;
    },
    p(ctx2, dirty) {
      if (default_slot) {
        if (default_slot.p && (!current || dirty & /*$$scope*/
        8)) {
          update_slot_base(
            default_slot,
            default_slot_template,
            ctx2,
            /*$$scope*/
            ctx2[3],
            !current ? get_all_dirty_from_scope(
              /*$$scope*/
              ctx2[3]
            ) : get_slot_changes(
              default_slot_template,
              /*$$scope*/
              ctx2[3],
              dirty,
              null
            ),
            null
          );
        }
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(default_slot, local);
      current = true;
    },
    o(local) {
      transition_out(default_slot, local);
      current = false;
    },
    d(detaching) {
      if (default_slot)
        default_slot.d(detaching);
    }
  };
}
function create_fragment9(ctx) {
  let icon;
  let current;
  const icon_spread_levels = [
    { name: "list-filter" },
    /*$$props*/
    ctx[1],
    { iconNode: (
      /*iconNode*/
      ctx[0]
    ) }
  ];
  let icon_props = {
    $$slots: { default: [create_default_slot8] },
    $$scope: { ctx }
  };
  for (let i = 0; i < icon_spread_levels.length; i += 1) {
    icon_props = assign(icon_props, icon_spread_levels[i]);
  }
  icon = new Icon$1({ props: icon_props });
  return {
    c() {
      create_component(icon.$$.fragment);
    },
    l(nodes) {
      claim_component(icon.$$.fragment, nodes);
    },
    m(target, anchor) {
      mount_component(icon, target, anchor);
      current = true;
    },
    p(ctx2, [dirty]) {
      const icon_changes = dirty & /*$$props, iconNode*/
      3 ? get_spread_update(icon_spread_levels, [
        icon_spread_levels[0],
        dirty & /*$$props*/
        2 && get_spread_object(
          /*$$props*/
          ctx2[1]
        ),
        dirty & /*iconNode*/
        1 && { iconNode: (
          /*iconNode*/
          ctx2[0]
        ) }
      ]) : {};
      if (dirty & /*$$scope*/
      8) {
        icon_changes.$$scope = { dirty, ctx: ctx2 };
      }
      icon.$set(icon_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(icon.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(icon.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(icon, detaching);
    }
  };
}
function instance9($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  const iconNode = [
    ["path", { "d": "M3 6h18" }],
    ["path", { "d": "M7 12h10" }],
    ["path", { "d": "M10 18h4" }]
  ];
  $$self.$$set = ($$new_props) => {
    $$invalidate(1, $$props = assign(assign({}, $$props), exclude_internal_props($$new_props)));
    if ("$$scope" in $$new_props)
      $$invalidate(3, $$scope = $$new_props.$$scope);
  };
  $$props = exclude_internal_props($$props);
  return [iconNode, $$props, slots, $$scope];
}
var List_filter = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance9, create_fragment9, safe_not_equal, {});
  }
};
var List_filter$1 = List_filter;

// node_modules/lucide-svelte/dist/esm/icons/message-square.svelte.js
function create_default_slot9(ctx) {
  let current;
  const default_slot_template = (
    /*#slots*/
    ctx[2].default
  );
  const default_slot = create_slot(
    default_slot_template,
    ctx,
    /*$$scope*/
    ctx[3],
    null
  );
  return {
    c() {
      if (default_slot)
        default_slot.c();
    },
    l(nodes) {
      if (default_slot)
        default_slot.l(nodes);
    },
    m(target, anchor) {
      if (default_slot) {
        default_slot.m(target, anchor);
      }
      current = true;
    },
    p(ctx2, dirty) {
      if (default_slot) {
        if (default_slot.p && (!current || dirty & /*$$scope*/
        8)) {
          update_slot_base(
            default_slot,
            default_slot_template,
            ctx2,
            /*$$scope*/
            ctx2[3],
            !current ? get_all_dirty_from_scope(
              /*$$scope*/
              ctx2[3]
            ) : get_slot_changes(
              default_slot_template,
              /*$$scope*/
              ctx2[3],
              dirty,
              null
            ),
            null
          );
        }
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(default_slot, local);
      current = true;
    },
    o(local) {
      transition_out(default_slot, local);
      current = false;
    },
    d(detaching) {
      if (default_slot)
        default_slot.d(detaching);
    }
  };
}
function create_fragment10(ctx) {
  let icon;
  let current;
  const icon_spread_levels = [
    { name: "message-square" },
    /*$$props*/
    ctx[1],
    { iconNode: (
      /*iconNode*/
      ctx[0]
    ) }
  ];
  let icon_props = {
    $$slots: { default: [create_default_slot9] },
    $$scope: { ctx }
  };
  for (let i = 0; i < icon_spread_levels.length; i += 1) {
    icon_props = assign(icon_props, icon_spread_levels[i]);
  }
  icon = new Icon$1({ props: icon_props });
  return {
    c() {
      create_component(icon.$$.fragment);
    },
    l(nodes) {
      claim_component(icon.$$.fragment, nodes);
    },
    m(target, anchor) {
      mount_component(icon, target, anchor);
      current = true;
    },
    p(ctx2, [dirty]) {
      const icon_changes = dirty & /*$$props, iconNode*/
      3 ? get_spread_update(icon_spread_levels, [
        icon_spread_levels[0],
        dirty & /*$$props*/
        2 && get_spread_object(
          /*$$props*/
          ctx2[1]
        ),
        dirty & /*iconNode*/
        1 && { iconNode: (
          /*iconNode*/
          ctx2[0]
        ) }
      ]) : {};
      if (dirty & /*$$scope*/
      8) {
        icon_changes.$$scope = { dirty, ctx: ctx2 };
      }
      icon.$set(icon_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(icon.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(icon.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(icon, detaching);
    }
  };
}
function instance10($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  const iconNode = [
    [
      "path",
      {
        "d": "M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"
      }
    ]
  ];
  $$self.$$set = ($$new_props) => {
    $$invalidate(1, $$props = assign(assign({}, $$props), exclude_internal_props($$new_props)));
    if ("$$scope" in $$new_props)
      $$invalidate(3, $$scope = $$new_props.$$scope);
  };
  $$props = exclude_internal_props($$props);
  return [iconNode, $$props, slots, $$scope];
}
var Message_square = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance10, create_fragment10, safe_not_equal, {});
  }
};
var Message_square$1 = Message_square;

// node_modules/lucide-svelte/dist/esm/icons/more-horizontal.svelte.js
function create_default_slot10(ctx) {
  let current;
  const default_slot_template = (
    /*#slots*/
    ctx[2].default
  );
  const default_slot = create_slot(
    default_slot_template,
    ctx,
    /*$$scope*/
    ctx[3],
    null
  );
  return {
    c() {
      if (default_slot)
        default_slot.c();
    },
    l(nodes) {
      if (default_slot)
        default_slot.l(nodes);
    },
    m(target, anchor) {
      if (default_slot) {
        default_slot.m(target, anchor);
      }
      current = true;
    },
    p(ctx2, dirty) {
      if (default_slot) {
        if (default_slot.p && (!current || dirty & /*$$scope*/
        8)) {
          update_slot_base(
            default_slot,
            default_slot_template,
            ctx2,
            /*$$scope*/
            ctx2[3],
            !current ? get_all_dirty_from_scope(
              /*$$scope*/
              ctx2[3]
            ) : get_slot_changes(
              default_slot_template,
              /*$$scope*/
              ctx2[3],
              dirty,
              null
            ),
            null
          );
        }
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(default_slot, local);
      current = true;
    },
    o(local) {
      transition_out(default_slot, local);
      current = false;
    },
    d(detaching) {
      if (default_slot)
        default_slot.d(detaching);
    }
  };
}
function create_fragment11(ctx) {
  let icon;
  let current;
  const icon_spread_levels = [
    { name: "more-horizontal" },
    /*$$props*/
    ctx[1],
    { iconNode: (
      /*iconNode*/
      ctx[0]
    ) }
  ];
  let icon_props = {
    $$slots: { default: [create_default_slot10] },
    $$scope: { ctx }
  };
  for (let i = 0; i < icon_spread_levels.length; i += 1) {
    icon_props = assign(icon_props, icon_spread_levels[i]);
  }
  icon = new Icon$1({ props: icon_props });
  return {
    c() {
      create_component(icon.$$.fragment);
    },
    l(nodes) {
      claim_component(icon.$$.fragment, nodes);
    },
    m(target, anchor) {
      mount_component(icon, target, anchor);
      current = true;
    },
    p(ctx2, [dirty]) {
      const icon_changes = dirty & /*$$props, iconNode*/
      3 ? get_spread_update(icon_spread_levels, [
        icon_spread_levels[0],
        dirty & /*$$props*/
        2 && get_spread_object(
          /*$$props*/
          ctx2[1]
        ),
        dirty & /*iconNode*/
        1 && { iconNode: (
          /*iconNode*/
          ctx2[0]
        ) }
      ]) : {};
      if (dirty & /*$$scope*/
      8) {
        icon_changes.$$scope = { dirty, ctx: ctx2 };
      }
      icon.$set(icon_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(icon.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(icon.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(icon, detaching);
    }
  };
}
function instance11($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  const iconNode = [
    ["circle", { "cx": "12", "cy": "12", "r": "1" }],
    ["circle", { "cx": "19", "cy": "12", "r": "1" }],
    ["circle", { "cx": "5", "cy": "12", "r": "1" }]
  ];
  $$self.$$set = ($$new_props) => {
    $$invalidate(1, $$props = assign(assign({}, $$props), exclude_internal_props($$new_props)));
    if ("$$scope" in $$new_props)
      $$invalidate(3, $$scope = $$new_props.$$scope);
  };
  $$props = exclude_internal_props($$props);
  return [iconNode, $$props, slots, $$scope];
}
var More_horizontal = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance11, create_fragment11, safe_not_equal, {});
  }
};
var More_horizontal$1 = More_horizontal;

// node_modules/lucide-svelte/dist/esm/icons/paintbrush.svelte.js
function create_default_slot11(ctx) {
  let current;
  const default_slot_template = (
    /*#slots*/
    ctx[2].default
  );
  const default_slot = create_slot(
    default_slot_template,
    ctx,
    /*$$scope*/
    ctx[3],
    null
  );
  return {
    c() {
      if (default_slot)
        default_slot.c();
    },
    l(nodes) {
      if (default_slot)
        default_slot.l(nodes);
    },
    m(target, anchor) {
      if (default_slot) {
        default_slot.m(target, anchor);
      }
      current = true;
    },
    p(ctx2, dirty) {
      if (default_slot) {
        if (default_slot.p && (!current || dirty & /*$$scope*/
        8)) {
          update_slot_base(
            default_slot,
            default_slot_template,
            ctx2,
            /*$$scope*/
            ctx2[3],
            !current ? get_all_dirty_from_scope(
              /*$$scope*/
              ctx2[3]
            ) : get_slot_changes(
              default_slot_template,
              /*$$scope*/
              ctx2[3],
              dirty,
              null
            ),
            null
          );
        }
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(default_slot, local);
      current = true;
    },
    o(local) {
      transition_out(default_slot, local);
      current = false;
    },
    d(detaching) {
      if (default_slot)
        default_slot.d(detaching);
    }
  };
}
function create_fragment12(ctx) {
  let icon;
  let current;
  const icon_spread_levels = [
    { name: "paintbrush" },
    /*$$props*/
    ctx[1],
    { iconNode: (
      /*iconNode*/
      ctx[0]
    ) }
  ];
  let icon_props = {
    $$slots: { default: [create_default_slot11] },
    $$scope: { ctx }
  };
  for (let i = 0; i < icon_spread_levels.length; i += 1) {
    icon_props = assign(icon_props, icon_spread_levels[i]);
  }
  icon = new Icon$1({ props: icon_props });
  return {
    c() {
      create_component(icon.$$.fragment);
    },
    l(nodes) {
      claim_component(icon.$$.fragment, nodes);
    },
    m(target, anchor) {
      mount_component(icon, target, anchor);
      current = true;
    },
    p(ctx2, [dirty]) {
      const icon_changes = dirty & /*$$props, iconNode*/
      3 ? get_spread_update(icon_spread_levels, [
        icon_spread_levels[0],
        dirty & /*$$props*/
        2 && get_spread_object(
          /*$$props*/
          ctx2[1]
        ),
        dirty & /*iconNode*/
        1 && { iconNode: (
          /*iconNode*/
          ctx2[0]
        ) }
      ]) : {};
      if (dirty & /*$$scope*/
      8) {
        icon_changes.$$scope = { dirty, ctx: ctx2 };
      }
      icon.$set(icon_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(icon.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(icon.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(icon, detaching);
    }
  };
}
function instance12($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  const iconNode = [
    [
      "path",
      {
        "d": "M18.37 2.63 14 7l-1.59-1.59a2 2 0 0 0-2.82 0L8 7l9 9 1.59-1.59a2 2 0 0 0 0-2.82L17 10l4.37-4.37a2.12 2.12 0 1 0-3-3Z"
      }
    ],
    [
      "path",
      {
        "d": "M9 8c-2 3-4 3.5-7 4l8 10c2-1 6-5 6-7"
      }
    ],
    ["path", { "d": "M14.5 17.5 4.5 15" }]
  ];
  $$self.$$set = ($$new_props) => {
    $$invalidate(1, $$props = assign(assign({}, $$props), exclude_internal_props($$new_props)));
    if ("$$scope" in $$new_props)
      $$invalidate(3, $$scope = $$new_props.$$scope);
  };
  $$props = exclude_internal_props($$props);
  return [iconNode, $$props, slots, $$scope];
}
var Paintbrush = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance12, create_fragment12, safe_not_equal, {});
  }
};
var Paintbrush$1 = Paintbrush;

// node_modules/lucide-svelte/dist/esm/icons/plus.svelte.js
function create_default_slot12(ctx) {
  let current;
  const default_slot_template = (
    /*#slots*/
    ctx[2].default
  );
  const default_slot = create_slot(
    default_slot_template,
    ctx,
    /*$$scope*/
    ctx[3],
    null
  );
  return {
    c() {
      if (default_slot)
        default_slot.c();
    },
    l(nodes) {
      if (default_slot)
        default_slot.l(nodes);
    },
    m(target, anchor) {
      if (default_slot) {
        default_slot.m(target, anchor);
      }
      current = true;
    },
    p(ctx2, dirty) {
      if (default_slot) {
        if (default_slot.p && (!current || dirty & /*$$scope*/
        8)) {
          update_slot_base(
            default_slot,
            default_slot_template,
            ctx2,
            /*$$scope*/
            ctx2[3],
            !current ? get_all_dirty_from_scope(
              /*$$scope*/
              ctx2[3]
            ) : get_slot_changes(
              default_slot_template,
              /*$$scope*/
              ctx2[3],
              dirty,
              null
            ),
            null
          );
        }
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(default_slot, local);
      current = true;
    },
    o(local) {
      transition_out(default_slot, local);
      current = false;
    },
    d(detaching) {
      if (default_slot)
        default_slot.d(detaching);
    }
  };
}
function create_fragment13(ctx) {
  let icon;
  let current;
  const icon_spread_levels = [
    { name: "plus" },
    /*$$props*/
    ctx[1],
    { iconNode: (
      /*iconNode*/
      ctx[0]
    ) }
  ];
  let icon_props = {
    $$slots: { default: [create_default_slot12] },
    $$scope: { ctx }
  };
  for (let i = 0; i < icon_spread_levels.length; i += 1) {
    icon_props = assign(icon_props, icon_spread_levels[i]);
  }
  icon = new Icon$1({ props: icon_props });
  return {
    c() {
      create_component(icon.$$.fragment);
    },
    l(nodes) {
      claim_component(icon.$$.fragment, nodes);
    },
    m(target, anchor) {
      mount_component(icon, target, anchor);
      current = true;
    },
    p(ctx2, [dirty]) {
      const icon_changes = dirty & /*$$props, iconNode*/
      3 ? get_spread_update(icon_spread_levels, [
        icon_spread_levels[0],
        dirty & /*$$props*/
        2 && get_spread_object(
          /*$$props*/
          ctx2[1]
        ),
        dirty & /*iconNode*/
        1 && { iconNode: (
          /*iconNode*/
          ctx2[0]
        ) }
      ]) : {};
      if (dirty & /*$$scope*/
      8) {
        icon_changes.$$scope = { dirty, ctx: ctx2 };
      }
      icon.$set(icon_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(icon.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(icon.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(icon, detaching);
    }
  };
}
function instance13($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  const iconNode = [["path", { "d": "M5 12h14" }], ["path", { "d": "M12 5v14" }]];
  $$self.$$set = ($$new_props) => {
    $$invalidate(1, $$props = assign(assign({}, $$props), exclude_internal_props($$new_props)));
    if ("$$scope" in $$new_props)
      $$invalidate(3, $$scope = $$new_props.$$scope);
  };
  $$props = exclude_internal_props($$props);
  return [iconNode, $$props, slots, $$scope];
}
var Plus = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance13, create_fragment13, safe_not_equal, {});
  }
};
var Plus$1 = Plus;

// node_modules/lucide-svelte/dist/esm/icons/search.svelte.js
function create_default_slot13(ctx) {
  let current;
  const default_slot_template = (
    /*#slots*/
    ctx[2].default
  );
  const default_slot = create_slot(
    default_slot_template,
    ctx,
    /*$$scope*/
    ctx[3],
    null
  );
  return {
    c() {
      if (default_slot)
        default_slot.c();
    },
    l(nodes) {
      if (default_slot)
        default_slot.l(nodes);
    },
    m(target, anchor) {
      if (default_slot) {
        default_slot.m(target, anchor);
      }
      current = true;
    },
    p(ctx2, dirty) {
      if (default_slot) {
        if (default_slot.p && (!current || dirty & /*$$scope*/
        8)) {
          update_slot_base(
            default_slot,
            default_slot_template,
            ctx2,
            /*$$scope*/
            ctx2[3],
            !current ? get_all_dirty_from_scope(
              /*$$scope*/
              ctx2[3]
            ) : get_slot_changes(
              default_slot_template,
              /*$$scope*/
              ctx2[3],
              dirty,
              null
            ),
            null
          );
        }
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(default_slot, local);
      current = true;
    },
    o(local) {
      transition_out(default_slot, local);
      current = false;
    },
    d(detaching) {
      if (default_slot)
        default_slot.d(detaching);
    }
  };
}
function create_fragment14(ctx) {
  let icon;
  let current;
  const icon_spread_levels = [
    { name: "search" },
    /*$$props*/
    ctx[1],
    { iconNode: (
      /*iconNode*/
      ctx[0]
    ) }
  ];
  let icon_props = {
    $$slots: { default: [create_default_slot13] },
    $$scope: { ctx }
  };
  for (let i = 0; i < icon_spread_levels.length; i += 1) {
    icon_props = assign(icon_props, icon_spread_levels[i]);
  }
  icon = new Icon$1({ props: icon_props });
  return {
    c() {
      create_component(icon.$$.fragment);
    },
    l(nodes) {
      claim_component(icon.$$.fragment, nodes);
    },
    m(target, anchor) {
      mount_component(icon, target, anchor);
      current = true;
    },
    p(ctx2, [dirty]) {
      const icon_changes = dirty & /*$$props, iconNode*/
      3 ? get_spread_update(icon_spread_levels, [
        icon_spread_levels[0],
        dirty & /*$$props*/
        2 && get_spread_object(
          /*$$props*/
          ctx2[1]
        ),
        dirty & /*iconNode*/
        1 && { iconNode: (
          /*iconNode*/
          ctx2[0]
        ) }
      ]) : {};
      if (dirty & /*$$scope*/
      8) {
        icon_changes.$$scope = { dirty, ctx: ctx2 };
      }
      icon.$set(icon_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(icon.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(icon.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(icon, detaching);
    }
  };
}
function instance14($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  const iconNode = [
    ["circle", { "cx": "11", "cy": "11", "r": "8" }],
    ["path", { "d": "m21 21-4.3-4.3" }]
  ];
  $$self.$$set = ($$new_props) => {
    $$invalidate(1, $$props = assign(assign({}, $$props), exclude_internal_props($$new_props)));
    if ("$$scope" in $$new_props)
      $$invalidate(3, $$scope = $$new_props.$$scope);
  };
  $$props = exclude_internal_props($$props);
  return [iconNode, $$props, slots, $$scope];
}
var Search = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance14, create_fragment14, safe_not_equal, {});
  }
};
var Search$1 = Search;

// node_modules/lucide-svelte/dist/esm/icons/settings.svelte.js
function create_default_slot14(ctx) {
  let current;
  const default_slot_template = (
    /*#slots*/
    ctx[2].default
  );
  const default_slot = create_slot(
    default_slot_template,
    ctx,
    /*$$scope*/
    ctx[3],
    null
  );
  return {
    c() {
      if (default_slot)
        default_slot.c();
    },
    l(nodes) {
      if (default_slot)
        default_slot.l(nodes);
    },
    m(target, anchor) {
      if (default_slot) {
        default_slot.m(target, anchor);
      }
      current = true;
    },
    p(ctx2, dirty) {
      if (default_slot) {
        if (default_slot.p && (!current || dirty & /*$$scope*/
        8)) {
          update_slot_base(
            default_slot,
            default_slot_template,
            ctx2,
            /*$$scope*/
            ctx2[3],
            !current ? get_all_dirty_from_scope(
              /*$$scope*/
              ctx2[3]
            ) : get_slot_changes(
              default_slot_template,
              /*$$scope*/
              ctx2[3],
              dirty,
              null
            ),
            null
          );
        }
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(default_slot, local);
      current = true;
    },
    o(local) {
      transition_out(default_slot, local);
      current = false;
    },
    d(detaching) {
      if (default_slot)
        default_slot.d(detaching);
    }
  };
}
function create_fragment15(ctx) {
  let icon;
  let current;
  const icon_spread_levels = [
    { name: "settings" },
    /*$$props*/
    ctx[1],
    { iconNode: (
      /*iconNode*/
      ctx[0]
    ) }
  ];
  let icon_props = {
    $$slots: { default: [create_default_slot14] },
    $$scope: { ctx }
  };
  for (let i = 0; i < icon_spread_levels.length; i += 1) {
    icon_props = assign(icon_props, icon_spread_levels[i]);
  }
  icon = new Icon$1({ props: icon_props });
  return {
    c() {
      create_component(icon.$$.fragment);
    },
    l(nodes) {
      claim_component(icon.$$.fragment, nodes);
    },
    m(target, anchor) {
      mount_component(icon, target, anchor);
      current = true;
    },
    p(ctx2, [dirty]) {
      const icon_changes = dirty & /*$$props, iconNode*/
      3 ? get_spread_update(icon_spread_levels, [
        icon_spread_levels[0],
        dirty & /*$$props*/
        2 && get_spread_object(
          /*$$props*/
          ctx2[1]
        ),
        dirty & /*iconNode*/
        1 && { iconNode: (
          /*iconNode*/
          ctx2[0]
        ) }
      ]) : {};
      if (dirty & /*$$scope*/
      8) {
        icon_changes.$$scope = { dirty, ctx: ctx2 };
      }
      icon.$set(icon_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(icon.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(icon.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(icon, detaching);
    }
  };
}
function instance15($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  const iconNode = [
    [
      "path",
      {
        "d": "M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"
      }
    ],
    ["circle", { "cx": "12", "cy": "12", "r": "3" }]
  ];
  $$self.$$set = ($$new_props) => {
    $$invalidate(1, $$props = assign(assign({}, $$props), exclude_internal_props($$new_props)));
    if ("$$scope" in $$new_props)
      $$invalidate(3, $$scope = $$new_props.$$scope);
  };
  $$props = exclude_internal_props($$props);
  return [iconNode, $$props, slots, $$scope];
}
var Settings = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance15, create_fragment15, safe_not_equal, {});
  }
};
var Settings$1 = Settings;

// node_modules/lucide-svelte/dist/esm/icons/trash-2.svelte.js
function create_default_slot15(ctx) {
  let current;
  const default_slot_template = (
    /*#slots*/
    ctx[2].default
  );
  const default_slot = create_slot(
    default_slot_template,
    ctx,
    /*$$scope*/
    ctx[3],
    null
  );
  return {
    c() {
      if (default_slot)
        default_slot.c();
    },
    l(nodes) {
      if (default_slot)
        default_slot.l(nodes);
    },
    m(target, anchor) {
      if (default_slot) {
        default_slot.m(target, anchor);
      }
      current = true;
    },
    p(ctx2, dirty) {
      if (default_slot) {
        if (default_slot.p && (!current || dirty & /*$$scope*/
        8)) {
          update_slot_base(
            default_slot,
            default_slot_template,
            ctx2,
            /*$$scope*/
            ctx2[3],
            !current ? get_all_dirty_from_scope(
              /*$$scope*/
              ctx2[3]
            ) : get_slot_changes(
              default_slot_template,
              /*$$scope*/
              ctx2[3],
              dirty,
              null
            ),
            null
          );
        }
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(default_slot, local);
      current = true;
    },
    o(local) {
      transition_out(default_slot, local);
      current = false;
    },
    d(detaching) {
      if (default_slot)
        default_slot.d(detaching);
    }
  };
}
function create_fragment16(ctx) {
  let icon;
  let current;
  const icon_spread_levels = [
    { name: "trash-2" },
    /*$$props*/
    ctx[1],
    { iconNode: (
      /*iconNode*/
      ctx[0]
    ) }
  ];
  let icon_props = {
    $$slots: { default: [create_default_slot15] },
    $$scope: { ctx }
  };
  for (let i = 0; i < icon_spread_levels.length; i += 1) {
    icon_props = assign(icon_props, icon_spread_levels[i]);
  }
  icon = new Icon$1({ props: icon_props });
  return {
    c() {
      create_component(icon.$$.fragment);
    },
    l(nodes) {
      claim_component(icon.$$.fragment, nodes);
    },
    m(target, anchor) {
      mount_component(icon, target, anchor);
      current = true;
    },
    p(ctx2, [dirty]) {
      const icon_changes = dirty & /*$$props, iconNode*/
      3 ? get_spread_update(icon_spread_levels, [
        icon_spread_levels[0],
        dirty & /*$$props*/
        2 && get_spread_object(
          /*$$props*/
          ctx2[1]
        ),
        dirty & /*iconNode*/
        1 && { iconNode: (
          /*iconNode*/
          ctx2[0]
        ) }
      ]) : {};
      if (dirty & /*$$scope*/
      8) {
        icon_changes.$$scope = { dirty, ctx: ctx2 };
      }
      icon.$set(icon_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(icon.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(icon.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(icon, detaching);
    }
  };
}
function instance16($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  const iconNode = [
    ["path", { "d": "M3 6h18" }],
    [
      "path",
      {
        "d": "M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"
      }
    ],
    [
      "path",
      {
        "d": "M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"
      }
    ],
    [
      "line",
      {
        "x1": "10",
        "x2": "10",
        "y1": "11",
        "y2": "17"
      }
    ],
    [
      "line",
      {
        "x1": "14",
        "x2": "14",
        "y1": "11",
        "y2": "17"
      }
    ]
  ];
  $$self.$$set = ($$new_props) => {
    $$invalidate(1, $$props = assign(assign({}, $$props), exclude_internal_props($$new_props)));
    if ("$$scope" in $$new_props)
      $$invalidate(3, $$scope = $$new_props.$$scope);
  };
  $$props = exclude_internal_props($$props);
  return [iconNode, $$props, slots, $$scope];
}
var Trash_2 = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance16, create_fragment16, safe_not_equal, {});
  }
};
var Trash_2$1 = Trash_2;

// node_modules/lucide-svelte/dist/esm/icons/underline.svelte.js
function create_default_slot16(ctx) {
  let current;
  const default_slot_template = (
    /*#slots*/
    ctx[2].default
  );
  const default_slot = create_slot(
    default_slot_template,
    ctx,
    /*$$scope*/
    ctx[3],
    null
  );
  return {
    c() {
      if (default_slot)
        default_slot.c();
    },
    l(nodes) {
      if (default_slot)
        default_slot.l(nodes);
    },
    m(target, anchor) {
      if (default_slot) {
        default_slot.m(target, anchor);
      }
      current = true;
    },
    p(ctx2, dirty) {
      if (default_slot) {
        if (default_slot.p && (!current || dirty & /*$$scope*/
        8)) {
          update_slot_base(
            default_slot,
            default_slot_template,
            ctx2,
            /*$$scope*/
            ctx2[3],
            !current ? get_all_dirty_from_scope(
              /*$$scope*/
              ctx2[3]
            ) : get_slot_changes(
              default_slot_template,
              /*$$scope*/
              ctx2[3],
              dirty,
              null
            ),
            null
          );
        }
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(default_slot, local);
      current = true;
    },
    o(local) {
      transition_out(default_slot, local);
      current = false;
    },
    d(detaching) {
      if (default_slot)
        default_slot.d(detaching);
    }
  };
}
function create_fragment17(ctx) {
  let icon;
  let current;
  const icon_spread_levels = [
    { name: "underline" },
    /*$$props*/
    ctx[1],
    { iconNode: (
      /*iconNode*/
      ctx[0]
    ) }
  ];
  let icon_props = {
    $$slots: { default: [create_default_slot16] },
    $$scope: { ctx }
  };
  for (let i = 0; i < icon_spread_levels.length; i += 1) {
    icon_props = assign(icon_props, icon_spread_levels[i]);
  }
  icon = new Icon$1({ props: icon_props });
  return {
    c() {
      create_component(icon.$$.fragment);
    },
    l(nodes) {
      claim_component(icon.$$.fragment, nodes);
    },
    m(target, anchor) {
      mount_component(icon, target, anchor);
      current = true;
    },
    p(ctx2, [dirty]) {
      const icon_changes = dirty & /*$$props, iconNode*/
      3 ? get_spread_update(icon_spread_levels, [
        icon_spread_levels[0],
        dirty & /*$$props*/
        2 && get_spread_object(
          /*$$props*/
          ctx2[1]
        ),
        dirty & /*iconNode*/
        1 && { iconNode: (
          /*iconNode*/
          ctx2[0]
        ) }
      ]) : {};
      if (dirty & /*$$scope*/
      8) {
        icon_changes.$$scope = { dirty, ctx: ctx2 };
      }
      icon.$set(icon_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(icon.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(icon.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(icon, detaching);
    }
  };
}
function instance17($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  const iconNode = [
    ["path", { "d": "M6 4v6a6 6 0 0 0 12 0V4" }],
    [
      "line",
      {
        "x1": "4",
        "x2": "20",
        "y1": "20",
        "y2": "20"
      }
    ]
  ];
  $$self.$$set = ($$new_props) => {
    $$invalidate(1, $$props = assign(assign({}, $$props), exclude_internal_props($$new_props)));
    if ("$$scope" in $$new_props)
      $$invalidate(3, $$scope = $$new_props.$$scope);
  };
  $$props = exclude_internal_props($$props);
  return [iconNode, $$props, slots, $$scope];
}
var Underline = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance17, create_fragment17, safe_not_equal, {});
  }
};
var Underline$1 = Underline;

// src/sidebar-outline/components/components/controls-bar/components/tabs-filter.svelte
function add_css(target) {
  append_styles(target, "svelte-lueosl", ".tab-badge.svelte-lueosl{position:absolute;right:-6px;top:2px;font-size:10px;width:15px;height:15px;opacity:50%}.tabs-container.svelte-lueosl{display:flex;align-items:center;gap:5px;padding:5px;max-width:100%;flex-wrap:wrap;border-bottom:1px solid var(--tab-outline-color)\n	}.tab.svelte-lueosl{padding:5px 10px;cursor:pointer;border-radius:3px;position:relative;background-color:var(--nav-item-background-active);color:var(--nav-item-color-active);opacity:1}.tab.svelte-lueosl:hover{background-color:var(--nav-item-background-hover);color:var(--nav-item-color-hover);opacity:1}.tab.is-hidden.svelte-lueosl{background-color:transparent;color:var(--nav-item-color);opacity:0.3}");
}
function get_each_context2(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[11] = list[i][0];
  child_ctx[12] = list[i][1];
  return child_ctx;
}
function create_if_block(ctx) {
  let div;
  let each_value = ensure_array_like(Object.entries(
    /*$filteredBySearchAndCategory*/
    ctx[0].labels
  ));
  let each_blocks = [];
  for (let i = 0; i < each_value.length; i += 1) {
    each_blocks[i] = create_each_block2(get_each_context2(ctx, each_value, i));
  }
  return {
    c() {
      div = element("div");
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      attr(div, "class", "tabs-container svelte-lueosl");
    },
    m(target, anchor) {
      insert(target, div, anchor);
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(div, null);
        }
      }
    },
    p(ctx2, dirty) {
      if (dirty & /*$hiddenLabels, Object, $filteredBySearchAndCategory, toggleTab*/
      25) {
        each_value = ensure_array_like(Object.entries(
          /*$filteredBySearchAndCategory*/
          ctx2[0].labels
        ));
        let i;
        for (i = 0; i < each_value.length; i += 1) {
          const child_ctx = get_each_context2(ctx2, each_value, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block2(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(div, null);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value.length;
      }
    },
    d(detaching) {
      if (detaching) {
        detach(div);
      }
      destroy_each(each_blocks, detaching);
    }
  };
}
function create_each_block2(ctx) {
  let div;
  let span0;
  let t0_value = (
    /*label*/
    ctx[11] + ""
  );
  let t0;
  let t1;
  let span1;
  let t2_value = (
    /*group*/
    ctx[12].length + ""
  );
  let t2;
  let t3;
  let div_class_value;
  let mounted;
  let dispose;
  function click_handler_2(...args) {
    return (
      /*click_handler_2*/
      ctx[9](
        /*label*/
        ctx[11],
        ...args
      )
    );
  }
  return {
    c() {
      div = element("div");
      span0 = element("span");
      t0 = text(t0_value);
      t1 = space();
      span1 = element("span");
      t2 = text(t2_value);
      t3 = space();
      attr(span1, "class", "tab-badge svelte-lueosl");
      attr(div, "class", div_class_value = null_to_empty(`tab ${/*$hiddenLabels*/
      ctx[3].has(
        /*label*/
        ctx[11]
      ) ? "is-hidden" : ""}`) + " svelte-lueosl");
    },
    m(target, anchor) {
      insert(target, div, anchor);
      append(div, span0);
      append(span0, t0);
      append(div, t1);
      append(div, span1);
      append(span1, t2);
      append(div, t3);
      if (!mounted) {
        dispose = listen(div, "click", click_handler_2);
        mounted = true;
      }
    },
    p(new_ctx, dirty) {
      ctx = new_ctx;
      if (dirty & /*$filteredBySearchAndCategory*/
      1 && t0_value !== (t0_value = /*label*/
      ctx[11] + ""))
        set_data(t0, t0_value);
      if (dirty & /*$filteredBySearchAndCategory*/
      1 && t2_value !== (t2_value = /*group*/
      ctx[12].length + ""))
        set_data(t2, t2_value);
      if (dirty & /*$hiddenLabels, $filteredBySearchAndCategory*/
      9 && div_class_value !== (div_class_value = null_to_empty(`tab ${/*$hiddenLabels*/
      ctx[3].has(
        /*label*/
        ctx[11]
      ) ? "is-hidden" : ""}`) + " svelte-lueosl")) {
        attr(div, "class", div_class_value);
      }
    },
    d(detaching) {
      if (detaching) {
        detach(div);
      }
      mounted = false;
      dispose();
    }
  };
}
function create_fragment18(ctx) {
  let div2;
  let div0;
  let span0;
  let messagesquare;
  let t0;
  let span1;
  let t1_value = (
    /*stats*/
    ctx[1].comments + ""
  );
  let t1;
  let div0_class_value;
  let t2;
  let div1;
  let span2;
  let highlighter;
  let t3;
  let span3;
  let t4_value = (
    /*stats*/
    ctx[1].highlights + ""
  );
  let t4;
  let div1_class_value;
  let t5;
  let show_if = Object.keys(
    /*$filteredBySearchAndCategory*/
    ctx[0].labels
  ).length;
  let if_block_anchor;
  let current;
  let mounted;
  let dispose;
  messagesquare = new Message_square$1({ props: { size: "13px" } });
  highlighter = new Highlighter$1({ props: { size: "13px" } });
  let if_block = show_if && create_if_block(ctx);
  return {
    c() {
      div2 = element("div");
      div0 = element("div");
      span0 = element("span");
      create_component(messagesquare.$$.fragment);
      t0 = space();
      span1 = element("span");
      t1 = text(t1_value);
      t2 = space();
      div1 = element("div");
      span2 = element("span");
      create_component(highlighter.$$.fragment);
      t3 = space();
      span3 = element("span");
      t4 = text(t4_value);
      t5 = space();
      if (if_block)
        if_block.c();
      if_block_anchor = empty();
      attr(span1, "class", "tab-badge svelte-lueosl");
      attr(div0, "class", div0_class_value = null_to_empty(`tab ${/*$hiddenTypes*/
      ctx[2].has("comment") ? "is-hidden" : ""}`) + " svelte-lueosl");
      attr(span3, "class", "tab-badge svelte-lueosl");
      attr(div1, "class", div1_class_value = null_to_empty(`tab ${/*$hiddenTypes*/
      ctx[2].has("highlight") ? "is-hidden" : ""}`) + " svelte-lueosl");
      attr(div2, "class", "tabs-container svelte-lueosl");
    },
    m(target, anchor) {
      insert(target, div2, anchor);
      append(div2, div0);
      append(div0, span0);
      mount_component(messagesquare, span0, null);
      append(div0, t0);
      append(div0, span1);
      append(span1, t1);
      append(div2, t2);
      append(div2, div1);
      append(div1, span2);
      mount_component(highlighter, span2, null);
      append(div1, t3);
      append(div1, span3);
      append(span3, t4);
      insert(target, t5, anchor);
      if (if_block)
        if_block.m(target, anchor);
      insert(target, if_block_anchor, anchor);
      current = true;
      if (!mounted) {
        dispose = [
          listen(
            div0,
            "click",
            /*click_handler*/
            ctx[7]
          ),
          listen(
            div1,
            "click",
            /*click_handler_1*/
            ctx[8]
          )
        ];
        mounted = true;
      }
    },
    p(ctx2, [dirty]) {
      if ((!current || dirty & /*stats*/
      2) && t1_value !== (t1_value = /*stats*/
      ctx2[1].comments + ""))
        set_data(t1, t1_value);
      if (!current || dirty & /*$hiddenTypes*/
      4 && div0_class_value !== (div0_class_value = null_to_empty(`tab ${/*$hiddenTypes*/
      ctx2[2].has("comment") ? "is-hidden" : ""}`) + " svelte-lueosl")) {
        attr(div0, "class", div0_class_value);
      }
      if ((!current || dirty & /*stats*/
      2) && t4_value !== (t4_value = /*stats*/
      ctx2[1].highlights + ""))
        set_data(t4, t4_value);
      if (!current || dirty & /*$hiddenTypes*/
      4 && div1_class_value !== (div1_class_value = null_to_empty(`tab ${/*$hiddenTypes*/
      ctx2[2].has("highlight") ? "is-hidden" : ""}`) + " svelte-lueosl")) {
        attr(div1, "class", div1_class_value);
      }
      if (dirty & /*$filteredBySearchAndCategory*/
      1)
        show_if = Object.keys(
          /*$filteredBySearchAndCategory*/
          ctx2[0].labels
        ).length;
      if (show_if) {
        if (if_block) {
          if_block.p(ctx2, dirty);
        } else {
          if_block = create_if_block(ctx2);
          if_block.c();
          if_block.m(if_block_anchor.parentNode, if_block_anchor);
        }
      } else if (if_block) {
        if_block.d(1);
        if_block = null;
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(messagesquare.$$.fragment, local);
      transition_in(highlighter.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(messagesquare.$$.fragment, local);
      transition_out(highlighter.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      if (detaching) {
        detach(div2);
        detach(t5);
        detach(if_block_anchor);
      }
      destroy_component(messagesquare);
      destroy_component(highlighter);
      if (if_block)
        if_block.d(detaching);
      mounted = false;
      run_all(dispose);
    }
  };
}
function instance18($$self, $$props, $$invalidate) {
  let $filteredBySearch;
  let $filteredBySearchAndCategory;
  let $hiddenTypes;
  let $hiddenLabels;
  component_subscribe($$self, filteredBySearch, ($$value) => $$invalidate(6, $filteredBySearch = $$value));
  component_subscribe($$self, filteredBySearchAndCategory, ($$value) => $$invalidate(0, $filteredBySearchAndCategory = $$value));
  component_subscribe($$self, hiddenTypes, ($$value) => $$invalidate(2, $hiddenTypes = $$value));
  component_subscribe($$self, hiddenLabels, ($$value) => $$invalidate(3, $hiddenLabels = $$value));
  let allLabels;
  let stats;
  const toggleTab = (label, inverted) => {
    hiddenLabels.update((hidden) => {
      if (inverted) {
        const notHidden = allLabels.filter((l2) => !hidden.has(l2));
        hidden.clear();
        notHidden.forEach((l2) => hidden.add(l2));
      } else
        hidden.has(label) ? hidden.delete(label) : hidden.add(label);
      return hidden;
    });
  };
  const toggleCategory = (category) => {
    hiddenTypes.update((c) => {
      if (c.has(category)) {
        c.delete(category);
      } else
        c.add(category);
      return c;
    });
  };
  const click_handler = () => toggleCategory("comment");
  const click_handler_1 = () => toggleCategory("highlight");
  const click_handler_2 = (label, e) => toggleTab(label, e.shiftKey);
  $$self.$$.update = () => {
    if ($$self.$$.dirty & /*$filteredBySearchAndCategory*/
    1) {
      $:
        allLabels = Object.keys($filteredBySearchAndCategory.labels);
    }
    if ($$self.$$.dirty & /*$filteredBySearch*/
    64) {
      $:
        $$invalidate(1, stats = Object.values($filteredBySearch.labels).flat().reduce(
          (acc, v) => {
            if (v.isHighlight)
              acc.highlights++;
            else
              acc.comments++;
            return acc;
          },
          { comments: 0, highlights: 0 }
        ));
    }
  };
  return [
    $filteredBySearchAndCategory,
    stats,
    $hiddenTypes,
    $hiddenLabels,
    toggleTab,
    toggleCategory,
    $filteredBySearch,
    click_handler,
    click_handler_1,
    click_handler_2
  ];
}
var Tabs_filter = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance18, create_fragment18, safe_not_equal, {}, add_css);
  }
};
var tabs_filter_default = Tabs_filter;

// src/sidebar-outline/components/components/controls-bar/components/search-input.svelte
function create_fragment19(ctx) {
  let div1;
  let input;
  let input_placeholder_value;
  let t;
  let div0;
  let div0_aria_label_value;
  let mounted;
  let dispose;
  return {
    c() {
      div1 = element("div");
      input = element("input");
      t = space();
      div0 = element("div");
      attr(input, "enterkeyhint", "search");
      attr(input, "placeholder", input_placeholder_value = l.OUTLINE_SEARCH_ANNOTATIONS);
      attr(input, "spellcheck", "false");
      attr(input, "type", "search");
      attr(div0, "aria-label", div0_aria_label_value = l.OUTLINE_FILTER_COMMENTS_CLEAR);
      attr(div0, "class", "search-input-clear-button");
      attr(div1, "class", "search-input-container");
      set_style(div1, "width", "90%");
    },
    m(target, anchor) {
      insert(target, div1, anchor);
      append(div1, input);
      set_input_value(
        input,
        /*term*/
        ctx[0]
      );
      append(div1, t);
      append(div1, div0);
      if (!mounted) {
        dispose = [
          listen(
            input,
            "input",
            /*input_input_handler*/
            ctx[1]
          ),
          listen(
            div0,
            "click",
            /*click_handler*/
            ctx[2]
          )
        ];
        mounted = true;
      }
    },
    p(ctx2, [dirty]) {
      if (dirty & /*term*/
      1 && input.value !== /*term*/
      ctx2[0]) {
        set_input_value(
          input,
          /*term*/
          ctx2[0]
        );
      }
    },
    i: noop,
    o: noop,
    d(detaching) {
      if (detaching) {
        detach(div1);
      }
      mounted = false;
      run_all(dispose);
    }
  };
}
function instance19($$self, $$props, $$invalidate) {
  let $searchTerm;
  component_subscribe($$self, searchTerm, ($$value) => $$invalidate(3, $searchTerm = $$value));
  let term = $searchTerm;
  function input_input_handler() {
    term = this.value;
    $$invalidate(0, term);
  }
  const click_handler = () => {
    $$invalidate(0, term = "");
  };
  $$self.$$.update = () => {
    if ($$self.$$.dirty & /*term*/
    1) {
      $:
        searchTerm.set(term);
    }
  };
  return [term, input_input_handler, click_handler];
}
var Search_input = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance19, create_fragment19, safe_not_equal, {});
  }
};
var search_input_default = Search_input;

// src/helpers/store.ts
var Store = class {
  constructor(initialValue, reducer2) {
    this.subscribers = /* @__PURE__ */ new Set();
    this.reducer = () => this.value;
    this.value = initialValue;
    if (reducer2)
      this.reducer = reducer2;
  }
  getValue() {
    return this.value;
  }
  dispatch(action) {
    this.value = this.reducer(this.value, action);
    this.notifySubscribers(action);
  }
  set(value) {
    this.value = value;
    this.notifySubscribers();
  }
  subscribe(run2, invalidate) {
    this.subscribers.add(run2);
    run2(this.value);
    return () => {
      this.subscribers.delete(run2);
    };
  }
  update(updater) {
    this.value = updater(this.value);
    this.notifySubscribers();
  }
  notifySubscribers(action) {
    for (const subscriber of this.subscribers) {
      subscriber(this.value, action);
    }
  }
};

// src/sidebar-outline/components/components/controls-bar/controls-bar.store.ts
var POSSIBLE_FONT_SIZES = [10, 12, 14, 16, 18, 20, 22, 24];
var fontSize = writable(12);
var isReading = writable(false);
var pluginIdle = writable(false);
var updateState = (store, action) => {
  if (action.type === "TOGGLE_SEARCH_INPUT") {
    store.showSearchInput = !store.showSearchInput;
    if (store.showSearchInput)
      store.showStylesSettings = false;
  } else if (action.type === "TOGGLE_LABELS_FILTERS") {
    store.showLabelsFilter = !store.showLabelsFilter;
    if (store.showLabelsFilter)
      store.showStylesSettings = false;
  } else if (action.type === "TOGGLE_EXTRA_BUTTONS") {
    store.showExtraButtons = !store.showExtraButtons;
    if (!store.showExtraButtons)
      store.showStylesSettings = false;
  } else if (action.type === "TOGGLE_STYLES_SETTINGS") {
    store.showStylesSettings = !store.showStylesSettings;
    if (store.showStylesSettings) {
      store.showSearchInput = false;
      store.showLabelsFilter = false;
      store.showOutlineSettings = false;
    }
  } else if (action.type === "TOGGLE_OUTLINE_SETTINGS") {
    store.showOutlineSettings = !store.showOutlineSettings;
    if (store.showOutlineSettings)
      store.showStylesSettings = false;
  }
};
var reducer = (store, action) => {
  updateState(store, action);
  return store;
};
var controls = new Store(
  {
    showLabelsFilter: false,
    showSearchInput: false,
    showExtraButtons: false,
    showStylesSettings: false,
    showOutlineSettings: false
  },
  reducer
);

// src/sidebar-outline/components/components/controls-bar/components/clickable-icon.svelte
function add_css2(target) {
  append_styles(target, "svelte-vmnm9", ".asterisk.svelte-vmnm9{position:absolute;top:1px;right:1px;opacity:0.8}");
}
function create_if_block2(ctx) {
  let span;
  return {
    c() {
      span = element("span");
      span.textContent = "*";
      attr(span, "class", "asterisk svelte-vmnm9");
    },
    m(target, anchor) {
      insert(target, span, anchor);
    },
    d(detaching) {
      if (detaching) {
        detach(span);
      }
    }
  };
}
function create_fragment20(ctx) {
  let button;
  let t;
  let button_class_value;
  let current;
  let mounted;
  let dispose;
  let if_block = (
    /*hasEnabledItems*/
    ctx[3] && create_if_block2(ctx)
  );
  const default_slot_template = (
    /*#slots*/
    ctx[6].default
  );
  const default_slot = create_slot(
    default_slot_template,
    ctx,
    /*$$scope*/
    ctx[5],
    null
  );
  return {
    c() {
      button = element("button");
      if (if_block)
        if_block.c();
      t = space();
      if (default_slot)
        default_slot.c();
      attr(
        button,
        "aria-label",
        /*label*/
        ctx[1]
      );
      attr(button, "class", button_class_value = null_to_empty("clickable-icon nav-action-button " + /*isActive*/
      (ctx[2] ? "is-active " : "")) + " svelte-vmnm9");
      button.disabled = /*disabled*/
      ctx[4];
      set_style(button, "position", "relative");
    },
    m(target, anchor) {
      insert(target, button, anchor);
      if (if_block)
        if_block.m(button, null);
      append(button, t);
      if (default_slot) {
        default_slot.m(button, null);
      }
      current = true;
      if (!mounted) {
        dispose = listen(button, "click", function() {
          if (is_function(
            /*onClick*/
            ctx[0]
          ))
            ctx[0].apply(this, arguments);
        });
        mounted = true;
      }
    },
    p(new_ctx, [dirty]) {
      ctx = new_ctx;
      if (
        /*hasEnabledItems*/
        ctx[3]
      ) {
        if (if_block) {
        } else {
          if_block = create_if_block2(ctx);
          if_block.c();
          if_block.m(button, t);
        }
      } else if (if_block) {
        if_block.d(1);
        if_block = null;
      }
      if (default_slot) {
        if (default_slot.p && (!current || dirty & /*$$scope*/
        32)) {
          update_slot_base(
            default_slot,
            default_slot_template,
            ctx,
            /*$$scope*/
            ctx[5],
            !current ? get_all_dirty_from_scope(
              /*$$scope*/
              ctx[5]
            ) : get_slot_changes(
              default_slot_template,
              /*$$scope*/
              ctx[5],
              dirty,
              null
            ),
            null
          );
        }
      }
      if (!current || dirty & /*label*/
      2) {
        attr(
          button,
          "aria-label",
          /*label*/
          ctx[1]
        );
      }
      if (!current || dirty & /*isActive*/
      4 && button_class_value !== (button_class_value = null_to_empty("clickable-icon nav-action-button " + /*isActive*/
      (ctx[2] ? "is-active " : "")) + " svelte-vmnm9")) {
        attr(button, "class", button_class_value);
      }
      if (!current || dirty & /*disabled*/
      16) {
        button.disabled = /*disabled*/
        ctx[4];
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(default_slot, local);
      current = true;
    },
    o(local) {
      transition_out(default_slot, local);
      current = false;
    },
    d(detaching) {
      if (detaching) {
        detach(button);
      }
      if (if_block)
        if_block.d();
      if (default_slot)
        default_slot.d(detaching);
      mounted = false;
      dispose();
    }
  };
}
function instance20($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  let { onClick } = $$props;
  let { label } = $$props;
  let { isActive = false } = $$props;
  let { hasEnabledItems = false } = $$props;
  let { disabled = false } = $$props;
  $$self.$$set = ($$props2) => {
    if ("onClick" in $$props2)
      $$invalidate(0, onClick = $$props2.onClick);
    if ("label" in $$props2)
      $$invalidate(1, label = $$props2.label);
    if ("isActive" in $$props2)
      $$invalidate(2, isActive = $$props2.isActive);
    if ("hasEnabledItems" in $$props2)
      $$invalidate(3, hasEnabledItems = $$props2.hasEnabledItems);
    if ("disabled" in $$props2)
      $$invalidate(4, disabled = $$props2.disabled);
    if ("$$scope" in $$props2)
      $$invalidate(5, $$scope = $$props2.$$scope);
  };
  return [onClick, label, isActive, hasEnabledItems, disabled, $$scope, slots];
}
var Clickable_icon = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(
      this,
      options2,
      instance20,
      create_fragment20,
      safe_not_equal,
      {
        onClick: 0,
        label: 1,
        isActive: 2,
        hasEnabledItems: 3,
        disabled: 4
      },
      add_css2
    );
  }
};
var clickable_icon_default = Clickable_icon;

// src/sidebar-outline/components/components/annotations-list/helpers/focus-text.ts
var selectText = (annotation, plugin) => {
  const view = plugin.outline.getValue().view;
  if (view) {
    const editor = view.editor;
    const selection = {
      from: annotation.range.from,
      to: annotation.range.to
    };
    editor.setCursor({ line: annotation.range.from.line - 1, ch: 0 });
    editor.transaction({
      selection
    });
    editor.scrollIntoView(selection, true);
  }
};

// src/sidebar-outline/components/components/controls-bar/helpers/tts.ts
var _isReading;
var _TTS = class {
  constructor() {
    __privateAdd(this, _isReading, void 0);
    this.subscribers = /* @__PURE__ */ new Set();
    this.fileHasChanged = false;
    this.setPlugin = (value) => {
      this.plugin = value;
      this.plugin.outline.subscribe((v) => {
        if (v.view?.file !== this.activeTTSFile) {
          this.fileHasChanged = true;
        } else {
          this.fileHasChanged = false;
        }
      });
    };
    this.updateState = () => {
      __privateSet(this, _isReading, window.speechSynthesis.speaking || window.speechSynthesis.pending || window.speechSynthesis.paused);
      if (!__privateGet(this, _isReading))
        activeAnnotationIndex.set(-1);
      this.invokeSubscribers();
    };
    this.onLoad();
  }
  get isReading() {
    return __privateGet(this, _isReading);
  }
  static getInstance() {
    if (!_TTS.instance) {
      _TTS.instance = new _TTS();
    }
    return _TTS.instance;
  }
  read() {
    const dict = this.annotations.reduce(
      (acc, v, i) => {
        acc.annotations[acc.c] = v;
        acc.c += v.text.length + 2;
        return acc;
      },
      { annotations: {}, c: 0 }
    );
    this.activeTTSFile = this.plugin.outline.getValue().view?.file;
    this.utterance.text = this.annotations.map((c) => c.text).join(".\n");
    const settings = this.plugin.settings.getValue().tts;
    this.utterance.volume = settings.volume;
    this.utterance.rate = settings.rate;
    this.utterance.pitch = settings.pitch;
    this.utterance.voice = window.speechSynthesis.getVoices().filter((otherVoice) => otherVoice.name === settings.voice)[0];
    window.speechSynthesis.cancel();
    this.fileHasChanged = false;
    window.speechSynthesis.speak(this.utterance);
    this.updateState();
    let annotationIndex = 0;
    this.utterance.onboundary = (e) => {
      if (e.name === "sentence") {
        const annotation = dict.annotations[e.charIndex];
        if (annotation) {
          if (!this.fileHasChanged) {
            if (settings.focusAnnotationInEditor)
              selectText(annotation, this.plugin);
            activeAnnotationIndex.set(annotationIndex);
          } else {
            activeAnnotationIndex.set(-1);
          }
          annotationIndex = annotationIndex + 1;
        }
      }
    };
  }
  stop() {
    window.speechSynthesis.cancel();
    this.updateState();
    activeAnnotationIndex.set(-1);
  }
  subscribe(callback) {
    this.subscribers.add(callback);
    callback(this.isReading);
    return () => {
      this.subscribers.delete(callback);
    };
  }
  onLoad() {
    filteredBySearchAndCategoryAndLabel.subscribe((v) => {
      this.annotations = v;
    });
    this.utterance = new SpeechSynthesisUtterance();
    this.utterance.onpause = this.updateState;
    this.utterance.onend = this.updateState;
    this.utterance.onstart = this.updateState;
    this.utterance.onresume = this.updateState;
  }
  invokeSubscribers() {
    this.subscribers.forEach((subscriber) => {
      subscriber(this.isReading);
    });
  }
};
var TTS = _TTS;
_isReading = new WeakMap();
var tts = TTS.getInstance();

// src/clipboard/helpers/apply-variables-to-template.ts
var applyVariablesToTemplate = ({
  template,
  variables
}) => {
  for (const [key, value] of Object.entries(variables)) {
    template = template.replace(`{{${String(key)}}}`, value);
  }
  return template;
};

// src/helpers/date-utils.ts
var formattedDate = () => {
  const date = new Date();
  return date.toISOString().split("T")[0];
};
var formattedTime = () => {
  const date = new Date();
  const [hh, mm] = date.toISOString().split("T")[1].split(":");
  return `${hh}:${mm}`;
};
var timeTag = () => {
  const [date, time] = new Date().toISOString().split("T");
  const [yyyy, mm_, dd] = date.split("-");
  const [hh, mm] = time.split(":");
  return `#${[yyyy.slice(2), mm_, dd, hh, mm].join("/")}`;
};

// src/clipboard/helpers/annotations-to-text.ts
var copiedAnnotationsVariables = {
  front: ["root_folder", "date", "time", "time_tag"],
  header: ["note_folder", "note_name"],
  highlight: ["highlight_text", "highlight_label"],
  comment: ["comment_text", "comment_label"]
};
var copiedAnnotationsTemplates = {
  front: "",
  header: `# {{note_name}}`,
  highlight: `- {{highlight_text}}`,
  comment: `- _{{comment_text}}_`
};
var annotationsToText = (annotations, templates, rootFolder) => {
  const frontVariables = {
    time: formattedTime(),
    date: formattedDate(),
    time_tag: timeTag(),
    root_folder: rootFolder
  };
  const front = applyVariablesToTemplate({
    template: templates.front,
    variables: frontVariables
  });
  const annotationsText = [];
  if (front.trim())
    annotationsText.push(front);
  for (const {
    annotations: fileAnnotations2,
    basename,
    folder
  } of annotations) {
    const fileText = [];
    const header = applyVariablesToTemplate({
      template: templates.header,
      variables: { note_folder: folder, note_name: basename }
    });
    for (const annotation of fileAnnotations2) {
      if (annotation.text) {
        const text2 = applyVariablesToTemplate({
          template: annotation.isHighlight ? templates.highlight : templates.comment,
          variables: {
            [annotation.isHighlight ? "highlight_text" : "comment_text"]: annotation.text.trim(),
            [annotation.isHighlight ? "highlight_label" : "comment_label"]: annotation.label.trim()
          }
        });
        if (text2.trim())
          fileText.push(text2);
      }
    }
    if (fileText.length)
      annotationsText.push(header + "\n" + fileText.join("\n"));
  }
  return annotationsText.join("\n\n");
};

// src/sidebar-outline/components/components/controls-bar/components/extra-buttons.svelte
var import_electron = require("electron");
var import_obsidian = require("obsidian");
function add_css3(target) {
  append_styles(target, "svelte-bdrni0", ".nav-buttons-container.svelte-bdrni0{display:flex;align-items:center;justify-content:center}");
}
function create_else_block(ctx) {
  let svg;
  let path0;
  let polyline;
  let path1;
  let path2;
  let path3;
  return {
    c() {
      svg = svg_element("svg");
      path0 = svg_element("path");
      polyline = svg_element("polyline");
      path1 = svg_element("path");
      path2 = svg_element("path");
      path3 = svg_element("path");
      attr(path0, "d", "M17.5 22h.5c.5 0 1-.2 1.4-.6.4-.4.6-.9.6-1.4V7.5L14.5 2H6c-.5 0-1 .2-1.4.6C4.2 3 4 3.5 4 4v3");
      attr(polyline, "points", "14 2 14 8 20 8");
      attr(path1, "d", "M10 20v-1a2 2 0 1 1 4 0v1a2 2 0 1 1-4 0Z");
      attr(path2, "d", "M6 20v-1a2 2 0 1 0-4 0v1a2 2 0 1 0 4 0Z");
      attr(path3, "d", "M2 19v-3a6 6 0 0 1 12 0v3");
      attr(svg, "class", "svg-icon");
      attr(svg, "xmlns", "http://www.w3.org/2000/svg");
      attr(svg, "width", "24");
      attr(svg, "height", "24");
      attr(svg, "viewBox", "0 0 24 24");
      attr(svg, "fill", "none");
      attr(svg, "stroke", "currentColor");
      attr(svg, "stroke-width", "2");
      attr(svg, "stroke-linecap", "round");
      attr(svg, "stroke-linejoin", "round");
    },
    m(target, anchor) {
      insert(target, svg, anchor);
      append(svg, path0);
      append(svg, polyline);
      append(svg, path1);
      append(svg, path2);
      append(svg, path3);
    },
    d(detaching) {
      if (detaching) {
        detach(svg);
      }
    }
  };
}
function create_if_block3(ctx) {
  let svg;
  let circle;
  let rect;
  return {
    c() {
      svg = svg_element("svg");
      circle = svg_element("circle");
      rect = svg_element("rect");
      attr(circle, "cx", "12");
      attr(circle, "cy", "12");
      attr(circle, "r", "10");
      attr(rect, "width", "6");
      attr(rect, "height", "6");
      attr(rect, "x", "9");
      attr(rect, "y", "9");
      attr(svg, "class", "svg-icon");
      attr(svg, "xmlns", "http://www.w3.org/2000/svg");
      attr(svg, "width", "24");
      attr(svg, "height", "24");
      attr(svg, "viewBox", "0 0 24 24");
      attr(svg, "fill", "none");
      attr(svg, "stroke", "currentColor");
      attr(svg, "stroke-width", "2");
      attr(svg, "stroke-linecap", "round");
      attr(svg, "stroke-linejoin", "round");
    },
    m(target, anchor) {
      insert(target, svg, anchor);
      append(svg, circle);
      append(svg, rect);
    },
    d(detaching) {
      if (detaching) {
        detach(svg);
      }
    }
  };
}
function create_default_slot_3(ctx) {
  let if_block_anchor;
  function select_block_type(ctx2, dirty) {
    if (
      /*$isReading*/
      ctx2[1]
    )
      return create_if_block3;
    return create_else_block;
  }
  let current_block_type = select_block_type(ctx, -1);
  let if_block = current_block_type(ctx);
  return {
    c() {
      if_block.c();
      if_block_anchor = empty();
    },
    m(target, anchor) {
      if_block.m(target, anchor);
      insert(target, if_block_anchor, anchor);
    },
    p(ctx2, dirty) {
      if (current_block_type !== (current_block_type = select_block_type(ctx2, dirty))) {
        if_block.d(1);
        if_block = current_block_type(ctx2);
        if (if_block) {
          if_block.c();
          if_block.m(if_block_anchor.parentNode, if_block_anchor);
        }
      }
    },
    d(detaching) {
      if (detaching) {
        detach(if_block_anchor);
      }
      if_block.d(detaching);
    }
  };
}
function create_default_slot_2(ctx) {
  let svg;
  let rect;
  let path0;
  let path1;
  let path2;
  let path3;
  return {
    c() {
      svg = svg_element("svg");
      rect = svg_element("rect");
      path0 = svg_element("path");
      path1 = svg_element("path");
      path2 = svg_element("path");
      path3 = svg_element("path");
      attr(rect, "height", "4");
      attr(rect, "rx", "1");
      attr(rect, "ry", "1");
      attr(rect, "width", "8");
      attr(rect, "x", "8");
      attr(rect, "y", "2");
      attr(path0, "d", "M8 4H6a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-2");
      attr(path1, "d", "M16 4h2a2 2 0 0 1 2 2v4");
      attr(path2, "d", "M21 14H11");
      attr(path3, "d", "m15 10-4 4 4 4");
      attr(svg, "class", "svg-icon lucide lucide-clipboard-copy");
      attr(svg, "fill", "none");
      attr(svg, "height", "18");
      attr(svg, "stroke", "currentColor");
      attr(svg, "stroke-linecap", "round");
      attr(svg, "stroke-linejoin", "round");
      attr(svg, "stroke-width", "2");
      attr(svg, "viewBox", "0 0 24 24");
      attr(svg, "width", "18");
      attr(svg, "xmlns", "http://www.w3.org/2000/svg");
    },
    m(target, anchor) {
      insert(target, svg, anchor);
      append(svg, rect);
      append(svg, path0);
      append(svg, path1);
      append(svg, path2);
      append(svg, path3);
    },
    p: noop,
    d(detaching) {
      if (detaching) {
        detach(svg);
      }
    }
  };
}
function create_default_slot_1(ctx) {
  let paintbrush;
  let current;
  paintbrush = new Paintbrush$1({ props: { class: "svg-icon" } });
  return {
    c() {
      create_component(paintbrush.$$.fragment);
    },
    m(target, anchor) {
      mount_component(paintbrush, target, anchor);
      current = true;
    },
    p: noop,
    i(local) {
      if (current)
        return;
      transition_in(paintbrush.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(paintbrush.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(paintbrush, detaching);
    }
  };
}
function create_default_slot17(ctx) {
  let settings;
  let current;
  settings = new Settings$1({ props: { class: "svg-icon" } });
  return {
    c() {
      create_component(settings.$$.fragment);
    },
    m(target, anchor) {
      mount_component(settings, target, anchor);
      current = true;
    },
    p: noop,
    i(local) {
      if (current)
        return;
      transition_in(settings.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(settings.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(settings, detaching);
    }
  };
}
function create_fragment21(ctx) {
  let div;
  let navbutton0;
  let t0;
  let navbutton1;
  let t1;
  let navbutton2;
  let t2;
  let navbutton3;
  let current;
  navbutton0 = new clickable_icon_default({
    props: {
      isActive: (
        /*$isReading*/
        ctx[1]
      ),
      label: l.OUTLINE_READ_ANNOTATIONS,
      onClick: (
        /*read*/
        ctx[3]
      ),
      $$slots: { default: [create_default_slot_3] },
      $$scope: { ctx }
    }
  });
  navbutton1 = new clickable_icon_default({
    props: {
      label: l.OUTLINE_COPY_ANNOTATIONS_TO_CLIPBOARD,
      onClick: (
        /*copyAnnotationsToClipboard*/
        ctx[0]
      ),
      $$slots: { default: [create_default_slot_2] },
      $$scope: { ctx }
    }
  });
  navbutton2 = new clickable_icon_default({
    props: {
      isActive: (
        /*$controls*/
        ctx[2].showStylesSettings
      ),
      label: l.OUTLINE_TOGGLE_STYLES_SETTINGS,
      onClick: (
        /*toggleShowStylesSettings*/
        ctx[4]
      ),
      $$slots: { default: [create_default_slot_1] },
      $$scope: { ctx }
    }
  });
  navbutton3 = new clickable_icon_default({
    props: {
      isActive: (
        /*$controls*/
        ctx[2].showOutlineSettings
      ),
      label: l.OUTLINE_SETTINGS,
      onClick: (
        /*func*/
        ctx[6]
      ),
      $$slots: { default: [create_default_slot17] },
      $$scope: { ctx }
    }
  });
  return {
    c() {
      div = element("div");
      create_component(navbutton0.$$.fragment);
      t0 = space();
      create_component(navbutton1.$$.fragment);
      t1 = space();
      create_component(navbutton2.$$.fragment);
      t2 = space();
      create_component(navbutton3.$$.fragment);
      attr(div, "class", "nav-buttons-container svelte-bdrni0");
    },
    m(target, anchor) {
      insert(target, div, anchor);
      mount_component(navbutton0, div, null);
      append(div, t0);
      mount_component(navbutton1, div, null);
      append(div, t1);
      mount_component(navbutton2, div, null);
      append(div, t2);
      mount_component(navbutton3, div, null);
      current = true;
    },
    p(ctx2, [dirty]) {
      const navbutton0_changes = {};
      if (dirty & /*$isReading*/
      2)
        navbutton0_changes.isActive = /*$isReading*/
        ctx2[1];
      if (dirty & /*$$scope, $isReading*/
      258) {
        navbutton0_changes.$$scope = { dirty, ctx: ctx2 };
      }
      navbutton0.$set(navbutton0_changes);
      const navbutton1_changes = {};
      if (dirty & /*$$scope*/
      256) {
        navbutton1_changes.$$scope = { dirty, ctx: ctx2 };
      }
      navbutton1.$set(navbutton1_changes);
      const navbutton2_changes = {};
      if (dirty & /*$controls*/
      4)
        navbutton2_changes.isActive = /*$controls*/
        ctx2[2].showStylesSettings;
      if (dirty & /*$$scope*/
      256) {
        navbutton2_changes.$$scope = { dirty, ctx: ctx2 };
      }
      navbutton2.$set(navbutton2_changes);
      const navbutton3_changes = {};
      if (dirty & /*$controls*/
      4)
        navbutton3_changes.isActive = /*$controls*/
        ctx2[2].showOutlineSettings;
      if (dirty & /*$$scope*/
      256) {
        navbutton3_changes.$$scope = { dirty, ctx: ctx2 };
      }
      navbutton3.$set(navbutton3_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(navbutton0.$$.fragment, local);
      transition_in(navbutton1.$$.fragment, local);
      transition_in(navbutton2.$$.fragment, local);
      transition_in(navbutton3.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(navbutton0.$$.fragment, local);
      transition_out(navbutton1.$$.fragment, local);
      transition_out(navbutton2.$$.fragment, local);
      transition_out(navbutton3.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      if (detaching) {
        detach(div);
      }
      destroy_component(navbutton0);
      destroy_component(navbutton1);
      destroy_component(navbutton2);
      destroy_component(navbutton3);
    }
  };
}
function instance21($$self, $$props, $$invalidate) {
  let $isReading;
  let $controls;
  component_subscribe($$self, isReading, ($$value) => $$invalidate(1, $isReading = $$value));
  component_subscribe($$self, controls, ($$value) => $$invalidate(2, $controls = $$value));
  let { plugin } = $$props;
  const copyAnnotationsToClipboard2 = () => {
    var _a, _b;
    const annotations = Object.values(get_store_value(filteredBySearchAndCategory).labels).flat().sort((a, b) => a.position.from - b.position.from);
    const outline = plugin.outline;
    const f = (_a = outline.getValue().view) === null || _a === void 0 ? void 0 : _a.file;
    if (f) {
      const folder = (_b = f.parent) === null || _b === void 0 ? void 0 : _b.path;
      const basename = f.basename;
      const text2 = annotationsToText([{ folder, basename, annotations }], plugin.settings.getValue().clipboard.templates, folder);
      import_electron.clipboard.writeText(text2);
      new import_obsidian.Notice(l.OUTLINE_NOTICE_COPIED_TO_CLIPBOARD);
    } else {
      new import_obsidian.Notice(l.OUTLINE_NOTICE_COULD_NOT_COPY);
    }
  };
  const read = () => {
    if (!tts.isReading) {
      tts.read();
    } else {
      tts.stop();
    }
  };
  const toggleShowStylesSettings = () => {
    controls.dispatch({ type: "TOGGLE_STYLES_SETTINGS" });
  };
  const unsub = tts.subscribe((value) => isReading.set(value));
  onDestroy(unsub);
  const func = () => controls.dispatch({ type: "TOGGLE_OUTLINE_SETTINGS" });
  $$self.$$set = ($$props2) => {
    if ("plugin" in $$props2)
      $$invalidate(5, plugin = $$props2.plugin);
  };
  return [
    copyAnnotationsToClipboard2,
    $isReading,
    $controls,
    read,
    toggleShowStylesSettings,
    plugin,
    func
  ];
}
var Extra_buttons = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance21, create_fragment21, safe_not_equal, { plugin: 5, copyAnnotationsToClipboard: 0 }, add_css3);
  }
  get copyAnnotationsToClipboard() {
    return this.$$.ctx[0];
  }
};
var extra_buttons_default = Extra_buttons;

// src/sidebar-outline/components/components/controls-bar/components/outline-settings.svelte
function create_default_slot_12(ctx) {
  let svg;
  let path0;
  let path1;
  let circle;
  let path2;
  let rect;
  return {
    c() {
      svg = svg_element("svg");
      path0 = svg_element("path");
      path1 = svg_element("path");
      circle = svg_element("circle");
      path2 = svg_element("path");
      rect = svg_element("rect");
      attr(path0, "d", "m 3,17 4,-8 4,8");
      attr(path0, "id", "path1");
      attr(path1, "d", "m 4,15 h 6");
      attr(path1, "id", "path2");
      attr(circle, "cx", "18");
      attr(circle, "cy", "14");
      attr(circle, "id", "circle2");
      attr(circle, "r", "3");
      attr(path2, "d", "m 21,11 v 6");
      attr(path2, "id", "path3");
      attr(rect, "height", "3.880337");
      set_style(rect, "stroke-width", "1.58341");
      attr(rect, "transform", "rotate(90)");
      attr(rect, "width", "0.064672284");
      attr(rect, "x", "5.3965225");
      attr(rect, "y", "-20.387436");
      attr(svg, "class", "svg-icon lucide lucide-case-sensitive");
      attr(svg, "fill", "none");
      attr(svg, "height", "24");
      attr(svg, "stroke", "currentColor");
      attr(svg, "stroke-linecap", "round");
      attr(svg, "stroke-linejoin", "round");
      attr(svg, "stroke-width", "2");
      attr(svg, "viewBox", "0 0 24 24");
      attr(svg, "width", "24");
      attr(svg, "xmlns", "http://www.w3.org/2000/svg");
    },
    m(target, anchor) {
      insert(target, svg, anchor);
      append(svg, path0);
      append(svg, path1);
      append(svg, circle);
      append(svg, path2);
      append(svg, rect);
    },
    p: noop,
    d(detaching) {
      if (detaching) {
        detach(svg);
      }
    }
  };
}
function create_default_slot18(ctx) {
  let svg;
  let path0;
  let path1;
  let circle;
  let path2;
  let rect0;
  let rect1;
  return {
    c() {
      svg = svg_element("svg");
      path0 = svg_element("path");
      path1 = svg_element("path");
      circle = svg_element("circle");
      path2 = svg_element("path");
      rect0 = svg_element("rect");
      rect1 = svg_element("rect");
      attr(path0, "d", "m 3,17 4,-8 4,8");
      attr(path0, "id", "path1");
      attr(path1, "d", "m 4,15 h 6");
      attr(path1, "id", "path2");
      attr(circle, "cx", "18");
      attr(circle, "cy", "14");
      attr(circle, "id", "circle2");
      attr(circle, "r", "3");
      attr(path2, "d", "m 21,11 v 6");
      attr(path2, "id", "path3");
      attr(rect0, "height", "3.880337");
      set_style(rect0, "stroke-width", "1.58341");
      attr(rect0, "transform", "rotate(90)");
      attr(rect0, "width", "0.064672284");
      attr(rect0, "x", "5.3965225");
      attr(rect0, "y", "-20.387436");
      attr(rect1, "height", "3.880337");
      set_style(rect1, "stroke-width", "1.58341");
      attr(rect1, "transform", "scale(-1)");
      attr(rect1, "width", "0.064672284");
      attr(rect1, "x", "-18.479605");
      attr(rect1, "y", "-7.3690276");
      attr(svg, "class", "svg-icon lucide lucide-case-sensitive");
      attr(svg, "fill", "none");
      attr(svg, "height", "24");
      attr(svg, "stroke", "currentColor");
      attr(svg, "stroke-linecap", "round");
      attr(svg, "stroke-linejoin", "round");
      attr(svg, "stroke-width", "2");
      attr(svg, "viewBox", "0 0 24 24");
      attr(svg, "width", "24");
      attr(svg, "xmlns", "http://www.w3.org/2000/svg");
    },
    m(target, anchor) {
      insert(target, svg, anchor);
      append(svg, path0);
      append(svg, path1);
      append(svg, circle);
      append(svg, path2);
      append(svg, rect0);
      append(svg, rect1);
    },
    p: noop,
    d(detaching) {
      if (detaching) {
        detach(svg);
      }
    }
  };
}
function create_fragment22(ctx) {
  let div;
  let navbutton0;
  let t;
  let navbutton1;
  let current;
  navbutton0 = new clickable_icon_default({
    props: {
      disabled: POSSIBLE_FONT_SIZES.indexOf(
        /*$fontSize*/
        ctx[0]
      ) === 0,
      label: l.OUTLINE_DECREASE_FONT_SIZE,
      onClick: (
        /*decreaseFontSize*/
        ctx[2]
      ),
      $$slots: { default: [create_default_slot_12] },
      $$scope: { ctx }
    }
  });
  navbutton1 = new clickable_icon_default({
    props: {
      disabled: POSSIBLE_FONT_SIZES.indexOf(
        /*$fontSize*/
        ctx[0]
      ) === POSSIBLE_FONT_SIZES.length - 1,
      label: l.OUTLINE_INCREASE_FONT_SIZE,
      onClick: (
        /*increaseFontSize*/
        ctx[1]
      ),
      $$slots: { default: [create_default_slot18] },
      $$scope: { ctx }
    }
  });
  return {
    c() {
      div = element("div");
      create_component(navbutton0.$$.fragment);
      t = space();
      create_component(navbutton1.$$.fragment);
      set_style(div, "display", "flex");
    },
    m(target, anchor) {
      insert(target, div, anchor);
      mount_component(navbutton0, div, null);
      append(div, t);
      mount_component(navbutton1, div, null);
      current = true;
    },
    p(ctx2, [dirty]) {
      const navbutton0_changes = {};
      if (dirty & /*$fontSize*/
      1)
        navbutton0_changes.disabled = POSSIBLE_FONT_SIZES.indexOf(
          /*$fontSize*/
          ctx2[0]
        ) === 0;
      if (dirty & /*$$scope*/
      8) {
        navbutton0_changes.$$scope = { dirty, ctx: ctx2 };
      }
      navbutton0.$set(navbutton0_changes);
      const navbutton1_changes = {};
      if (dirty & /*$fontSize*/
      1)
        navbutton1_changes.disabled = POSSIBLE_FONT_SIZES.indexOf(
          /*$fontSize*/
          ctx2[0]
        ) === POSSIBLE_FONT_SIZES.length - 1;
      if (dirty & /*$$scope*/
      8) {
        navbutton1_changes.$$scope = { dirty, ctx: ctx2 };
      }
      navbutton1.$set(navbutton1_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(navbutton0.$$.fragment, local);
      transition_in(navbutton1.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(navbutton0.$$.fragment, local);
      transition_out(navbutton1.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      if (detaching) {
        detach(div);
      }
      destroy_component(navbutton0);
      destroy_component(navbutton1);
    }
  };
}
function instance22($$self, $$props, $$invalidate) {
  let $fontSize;
  component_subscribe($$self, fontSize, ($$value) => $$invalidate(0, $fontSize = $$value));
  const increaseFontSize = () => {
    const i = (POSSIBLE_FONT_SIZES.indexOf($fontSize) || 0) + 1;
    fontSize.set(POSSIBLE_FONT_SIZES[Math.min(i, POSSIBLE_FONT_SIZES.length - 1)]);
  };
  const decreaseFontSize = () => {
    const i = (POSSIBLE_FONT_SIZES.indexOf($fontSize) || 0) - 1;
    fontSize.set(POSSIBLE_FONT_SIZES[Math.max(i, 0)]);
  };
  return [$fontSize, increaseFontSize, decreaseFontSize];
}
var Outline_settings = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance22, create_fragment22, safe_not_equal, {});
  }
};
var outline_settings_default = Outline_settings;

// src/sidebar-outline/components/components/controls-bar/controls-bar.svelte
function add_css4(target) {
  append_styles(target, "svelte-1sux4uf", ".outline-controls.svelte-1sux4uf{display:flex;flex-direction:column;align-items:center;justify-content:center;width:100%;gap:10px;align-self:center;justify-self:center;box-sizing:border-box;height:auto}.nav-buttons-container.svelte-1sux4uf{display:flex;align-items:center;justify-content:center}");
}
function create_default_slot_22(ctx) {
  let search;
  let current;
  search = new Search$1({ props: { class: "svg-icon" } });
  return {
    c() {
      create_component(search.$$.fragment);
    },
    m(target, anchor) {
      mount_component(search, target, anchor);
      current = true;
    },
    p: noop,
    i(local) {
      if (current)
        return;
      transition_in(search.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(search.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(search, detaching);
    }
  };
}
function create_default_slot_13(ctx) {
  let listfilter;
  let current;
  listfilter = new List_filter$1({ props: { class: "svg-icon" } });
  return {
    c() {
      create_component(listfilter.$$.fragment);
    },
    m(target, anchor) {
      mount_component(listfilter, target, anchor);
      current = true;
    },
    p: noop,
    i(local) {
      if (current)
        return;
      transition_in(listfilter.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(listfilter.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(listfilter, detaching);
    }
  };
}
function create_default_slot19(ctx) {
  let morehorizontal;
  let current;
  morehorizontal = new More_horizontal$1({ props: { class: "svg-icon" } });
  return {
    c() {
      create_component(morehorizontal.$$.fragment);
    },
    m(target, anchor) {
      mount_component(morehorizontal, target, anchor);
      current = true;
    },
    p: noop,
    i(local) {
      if (current)
        return;
      transition_in(morehorizontal.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(morehorizontal.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(morehorizontal, detaching);
    }
  };
}
function create_if_block_3(ctx) {
  let secondarycontrolsbar;
  let current;
  secondarycontrolsbar = new extra_buttons_default({ props: { plugin: (
    /*plugin*/
    ctx[0]
  ) } });
  return {
    c() {
      create_component(secondarycontrolsbar.$$.fragment);
    },
    m(target, anchor) {
      mount_component(secondarycontrolsbar, target, anchor);
      current = true;
    },
    p(ctx2, dirty) {
      const secondarycontrolsbar_changes = {};
      if (dirty & /*plugin*/
      1)
        secondarycontrolsbar_changes.plugin = /*plugin*/
        ctx2[0];
      secondarycontrolsbar.$set(secondarycontrolsbar_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(secondarycontrolsbar.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(secondarycontrolsbar.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(secondarycontrolsbar, detaching);
    }
  };
}
function create_if_block_2(ctx) {
  let searchinput;
  let current;
  searchinput = new search_input_default({});
  return {
    c() {
      create_component(searchinput.$$.fragment);
    },
    m(target, anchor) {
      mount_component(searchinput, target, anchor);
      current = true;
    },
    i(local) {
      if (current)
        return;
      transition_in(searchinput.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(searchinput.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(searchinput, detaching);
    }
  };
}
function create_if_block_1(ctx) {
  let tabsfilter;
  let current;
  tabsfilter = new tabs_filter_default({});
  return {
    c() {
      create_component(tabsfilter.$$.fragment);
    },
    m(target, anchor) {
      mount_component(tabsfilter, target, anchor);
      current = true;
    },
    i(local) {
      if (current)
        return;
      transition_in(tabsfilter.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(tabsfilter.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(tabsfilter, detaching);
    }
  };
}
function create_if_block4(ctx) {
  let outlinesettings;
  let current;
  outlinesettings = new outline_settings_default({});
  return {
    c() {
      create_component(outlinesettings.$$.fragment);
    },
    m(target, anchor) {
      mount_component(outlinesettings, target, anchor);
      current = true;
    },
    i(local) {
      if (current)
        return;
      transition_in(outlinesettings.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(outlinesettings.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(outlinesettings, detaching);
    }
  };
}
function create_fragment23(ctx) {
  let div1;
  let div0;
  let navbutton0;
  let t0;
  let navbutton1;
  let t1;
  let navbutton2;
  let t2;
  let t3;
  let t4;
  let t5;
  let current;
  navbutton0 = new clickable_icon_default({
    props: {
      hasEnabledItems: !!/*$searchTerm*/
      ctx[1],
      isActive: (
        /*$controls*/
        ctx[2].showSearchInput
      ),
      label: l.OUTLINE_SEARCH_ANNOTATIONS,
      onClick: (
        /*toggleShowSearchInput*/
        ctx[6]
      ),
      $$slots: { default: [create_default_slot_22] },
      $$scope: { ctx }
    }
  });
  navbutton1 = new clickable_icon_default({
    props: {
      hasEnabledItems: (
        /*$filteredHiddenLabels*/
        ctx[3].size > 0 || /*$filteredHiddenCategories*/
        ctx[4].size > 0
      ),
      isActive: (
        /*$controls*/
        ctx[2].showLabelsFilter
      ),
      label: l.OUTLINE_FILTER_ANNOTATIONS,
      onClick: (
        /*toggleLabelsFilter*/
        ctx[5]
      ),
      $$slots: { default: [create_default_slot_13] },
      $$scope: { ctx }
    }
  });
  navbutton2 = new clickable_icon_default({
    props: {
      isActive: (
        /*$controls*/
        ctx[2].showExtraButtons
      ),
      label: l.OUTLINE_SHOW_ALL_CONTROLS,
      onClick: (
        /*toggleSecondaryControlsBar*/
        ctx[7]
      ),
      $$slots: { default: [create_default_slot19] },
      $$scope: { ctx }
    }
  });
  let if_block0 = (
    /*$controls*/
    ctx[2].showExtraButtons && create_if_block_3(ctx)
  );
  let if_block1 = (
    /*$controls*/
    ctx[2].showSearchInput && create_if_block_2(ctx)
  );
  let if_block2 = (
    /*$controls*/
    ctx[2].showLabelsFilter && create_if_block_1(ctx)
  );
  let if_block3 = (
    /*$controls*/
    ctx[2].showOutlineSettings && create_if_block4(ctx)
  );
  return {
    c() {
      div1 = element("div");
      div0 = element("div");
      create_component(navbutton0.$$.fragment);
      t0 = space();
      create_component(navbutton1.$$.fragment);
      t1 = space();
      create_component(navbutton2.$$.fragment);
      t2 = space();
      if (if_block0)
        if_block0.c();
      t3 = space();
      if (if_block1)
        if_block1.c();
      t4 = space();
      if (if_block2)
        if_block2.c();
      t5 = space();
      if (if_block3)
        if_block3.c();
      attr(div0, "class", "nav-buttons-container svelte-1sux4uf");
      attr(div1, "class", "outline-controls svelte-1sux4uf");
    },
    m(target, anchor) {
      insert(target, div1, anchor);
      append(div1, div0);
      mount_component(navbutton0, div0, null);
      append(div0, t0);
      mount_component(navbutton1, div0, null);
      append(div0, t1);
      mount_component(navbutton2, div0, null);
      append(div1, t2);
      if (if_block0)
        if_block0.m(div1, null);
      append(div1, t3);
      if (if_block1)
        if_block1.m(div1, null);
      append(div1, t4);
      if (if_block2)
        if_block2.m(div1, null);
      append(div1, t5);
      if (if_block3)
        if_block3.m(div1, null);
      current = true;
    },
    p(ctx2, [dirty]) {
      const navbutton0_changes = {};
      if (dirty & /*$searchTerm*/
      2)
        navbutton0_changes.hasEnabledItems = !!/*$searchTerm*/
        ctx2[1];
      if (dirty & /*$controls*/
      4)
        navbutton0_changes.isActive = /*$controls*/
        ctx2[2].showSearchInput;
      if (dirty & /*$$scope*/
      256) {
        navbutton0_changes.$$scope = { dirty, ctx: ctx2 };
      }
      navbutton0.$set(navbutton0_changes);
      const navbutton1_changes = {};
      if (dirty & /*$filteredHiddenLabels, $filteredHiddenCategories*/
      24)
        navbutton1_changes.hasEnabledItems = /*$filteredHiddenLabels*/
        ctx2[3].size > 0 || /*$filteredHiddenCategories*/
        ctx2[4].size > 0;
      if (dirty & /*$controls*/
      4)
        navbutton1_changes.isActive = /*$controls*/
        ctx2[2].showLabelsFilter;
      if (dirty & /*$$scope*/
      256) {
        navbutton1_changes.$$scope = { dirty, ctx: ctx2 };
      }
      navbutton1.$set(navbutton1_changes);
      const navbutton2_changes = {};
      if (dirty & /*$controls*/
      4)
        navbutton2_changes.isActive = /*$controls*/
        ctx2[2].showExtraButtons;
      if (dirty & /*$$scope*/
      256) {
        navbutton2_changes.$$scope = { dirty, ctx: ctx2 };
      }
      navbutton2.$set(navbutton2_changes);
      if (
        /*$controls*/
        ctx2[2].showExtraButtons
      ) {
        if (if_block0) {
          if_block0.p(ctx2, dirty);
          if (dirty & /*$controls*/
          4) {
            transition_in(if_block0, 1);
          }
        } else {
          if_block0 = create_if_block_3(ctx2);
          if_block0.c();
          transition_in(if_block0, 1);
          if_block0.m(div1, t3);
        }
      } else if (if_block0) {
        group_outros();
        transition_out(if_block0, 1, 1, () => {
          if_block0 = null;
        });
        check_outros();
      }
      if (
        /*$controls*/
        ctx2[2].showSearchInput
      ) {
        if (if_block1) {
          if (dirty & /*$controls*/
          4) {
            transition_in(if_block1, 1);
          }
        } else {
          if_block1 = create_if_block_2(ctx2);
          if_block1.c();
          transition_in(if_block1, 1);
          if_block1.m(div1, t4);
        }
      } else if (if_block1) {
        group_outros();
        transition_out(if_block1, 1, 1, () => {
          if_block1 = null;
        });
        check_outros();
      }
      if (
        /*$controls*/
        ctx2[2].showLabelsFilter
      ) {
        if (if_block2) {
          if (dirty & /*$controls*/
          4) {
            transition_in(if_block2, 1);
          }
        } else {
          if_block2 = create_if_block_1(ctx2);
          if_block2.c();
          transition_in(if_block2, 1);
          if_block2.m(div1, t5);
        }
      } else if (if_block2) {
        group_outros();
        transition_out(if_block2, 1, 1, () => {
          if_block2 = null;
        });
        check_outros();
      }
      if (
        /*$controls*/
        ctx2[2].showOutlineSettings
      ) {
        if (if_block3) {
          if (dirty & /*$controls*/
          4) {
            transition_in(if_block3, 1);
          }
        } else {
          if_block3 = create_if_block4(ctx2);
          if_block3.c();
          transition_in(if_block3, 1);
          if_block3.m(div1, null);
        }
      } else if (if_block3) {
        group_outros();
        transition_out(if_block3, 1, 1, () => {
          if_block3 = null;
        });
        check_outros();
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(navbutton0.$$.fragment, local);
      transition_in(navbutton1.$$.fragment, local);
      transition_in(navbutton2.$$.fragment, local);
      transition_in(if_block0);
      transition_in(if_block1);
      transition_in(if_block2);
      transition_in(if_block3);
      current = true;
    },
    o(local) {
      transition_out(navbutton0.$$.fragment, local);
      transition_out(navbutton1.$$.fragment, local);
      transition_out(navbutton2.$$.fragment, local);
      transition_out(if_block0);
      transition_out(if_block1);
      transition_out(if_block2);
      transition_out(if_block3);
      current = false;
    },
    d(detaching) {
      if (detaching) {
        detach(div1);
      }
      destroy_component(navbutton0);
      destroy_component(navbutton1);
      destroy_component(navbutton2);
      if (if_block0)
        if_block0.d();
      if (if_block1)
        if_block1.d();
      if (if_block2)
        if_block2.d();
      if (if_block3)
        if_block3.d();
    }
  };
}
function instance23($$self, $$props, $$invalidate) {
  let $searchTerm;
  let $controls;
  let $filteredHiddenLabels;
  let $filteredHiddenCategories;
  component_subscribe($$self, searchTerm, ($$value) => $$invalidate(1, $searchTerm = $$value));
  component_subscribe($$self, controls, ($$value) => $$invalidate(2, $controls = $$value));
  component_subscribe($$self, filteredHiddenLabels, ($$value) => $$invalidate(3, $filteredHiddenLabels = $$value));
  component_subscribe($$self, filteredHiddenCategories, ($$value) => $$invalidate(4, $filteredHiddenCategories = $$value));
  let { plugin } = $$props;
  const toggleLabelsFilter = () => {
    controls.dispatch({ type: "TOGGLE_LABELS_FILTERS" });
  };
  const toggleShowSearchInput = () => {
    controls.dispatch({ type: "TOGGLE_SEARCH_INPUT" });
  };
  const toggleSecondaryControlsBar = () => {
    controls.dispatch({ type: "TOGGLE_EXTRA_BUTTONS" });
  };
  $$self.$$set = ($$props2) => {
    if ("plugin" in $$props2)
      $$invalidate(0, plugin = $$props2.plugin);
  };
  return [
    plugin,
    $searchTerm,
    $controls,
    $filteredHiddenLabels,
    $filteredHiddenCategories,
    toggleLabelsFilter,
    toggleShowSearchInput,
    toggleSecondaryControlsBar
  ];
}
var Controls_bar = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance23, create_fragment23, safe_not_equal, { plugin: 0 }, add_css4);
  }
};
var controls_bar_default = Controls_bar;

// src/sidebar-outline/components/components/no-annotations.svelte
function create_fragment24(ctx) {
  let div;
  let t_value = (
    /*variant*/
    (ctx[0] === "filter-applied" ? l.OUTLINE_NO_ANNOTATIONS_MATCH_FILTER : l.OUTLINE_NO_ANNOTATIONS_FOUND) + ""
  );
  let t;
  return {
    c() {
      div = element("div");
      t = text(t_value);
      attr(div, "class", "pane-empty");
    },
    m(target, anchor) {
      insert(target, div, anchor);
      append(div, t);
    },
    p(ctx2, [dirty]) {
      if (dirty & /*variant*/
      1 && t_value !== (t_value = /*variant*/
      (ctx2[0] === "filter-applied" ? l.OUTLINE_NO_ANNOTATIONS_MATCH_FILTER : l.OUTLINE_NO_ANNOTATIONS_FOUND) + ""))
        set_data(t, t_value);
    },
    i: noop,
    o: noop,
    d(detaching) {
      if (detaching) {
        detach(div);
      }
    }
  };
}
function instance24($$self, $$props, $$invalidate) {
  let { variant = void 0 } = $$props;
  $$self.$$set = ($$props2) => {
    if ("variant" in $$props2)
      $$invalidate(0, variant = $$props2.variant);
  };
  return [variant];
}
var No_annotations = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance24, create_fragment24, safe_not_equal, { variant: 0 });
  }
};
var no_annotations_default = No_annotations;

// src/sidebar-outline/components/components/annotations-list/annotation.svelte
function add_css5(target) {
  append_styles(target, "svelte-3ld1qy", ".annotation.svelte-3ld1qy{position:relative;display:flex;cursor:pointer;align-items:center;box-sizing:border-box;height:fit-content;color:var(--nav-item-color);border-radius:var(--radius-s);font-size:var(--nav-item-size);line-height:var(--line-height-tight);font-weight:var(--nav-item-weight);margin-bottom:var(--size-2-1);padding:var(--size-4-2)}.annotation.svelte-3ld1qy:hover{color:var(--nav-item-color-hover);background-color:var(--nav-item-background-hover)}mark.svelte-3ld1qy{color:var(--nav-item-color);background-color:var(--nav-item-background-active);font-style:italic}.annotation-badge.svelte-3ld1qy{position:absolute;right:4px;bottom:2px;top:2px;font-size:10px;opacity:0.5;display:flex;flex-direction:column;justify-content:center;align-items:end}.active.svelte-3ld1qy{color:var(--nav-item-color-highlighted);background-color:var(--nav-item-background-active)}");
}
function create_else_block2(ctx) {
  let t_value = (
    /*annotation*/
    ctx[0].text + ""
  );
  let t;
  return {
    c() {
      t = text(t_value);
    },
    m(target, anchor) {
      insert(target, t, anchor);
    },
    p(ctx2, dirty) {
      if (dirty & /*annotation*/
      1 && t_value !== (t_value = /*annotation*/
      ctx2[0].text + ""))
        set_data(t, t_value);
    },
    d(detaching) {
      if (detaching) {
        detach(t);
      }
    }
  };
}
function create_if_block5(ctx) {
  let mark;
  let t_value = (
    /*annotation*/
    ctx[0].text + ""
  );
  let t;
  let mark_class_value;
  return {
    c() {
      mark = element("mark");
      t = text(t_value);
      attr(mark, "class", mark_class_value = null_to_empty(
        /*isActive*/
        ctx[1] ? "active" : ""
      ) + " svelte-3ld1qy");
    },
    m(target, anchor) {
      insert(target, mark, anchor);
      append(mark, t);
    },
    p(ctx2, dirty) {
      if (dirty & /*annotation*/
      1 && t_value !== (t_value = /*annotation*/
      ctx2[0].text + ""))
        set_data(t, t_value);
      if (dirty & /*isActive*/
      2 && mark_class_value !== (mark_class_value = null_to_empty(
        /*isActive*/
        ctx2[1] ? "active" : ""
      ) + " svelte-3ld1qy")) {
        attr(mark, "class", mark_class_value);
      }
    },
    d(detaching) {
      if (detaching) {
        detach(mark);
      }
    }
  };
}
function create_fragment25(ctx) {
  let div;
  let span0;
  let span0_class_value;
  let span0_style_value;
  let t0;
  let span3;
  let span1;
  let t1_value = (
    /*annotation*/
    ctx[0].range.from.line + 1 + ""
  );
  let t1;
  let t2;
  let span2;
  let t3_value = (
    /*annotation*/
    ctx[0].label + ""
  );
  let t3;
  let div_class_value;
  let mounted;
  let dispose;
  function select_block_type(ctx2, dirty) {
    if (
      /*annotation*/
      ctx2[0].isHighlight
    )
      return create_if_block5;
    return create_else_block2;
  }
  let current_block_type = select_block_type(ctx, -1);
  let if_block = current_block_type(ctx);
  return {
    c() {
      div = element("div");
      span0 = element("span");
      if_block.c();
      t0 = space();
      span3 = element("span");
      span1 = element("span");
      t1 = text(t1_value);
      t2 = space();
      span2 = element("span");
      t3 = text(t3_value);
      attr(span0, "class", span0_class_value = "annotation-text");
      attr(span0, "style", span0_style_value = `font-size:${/*$fontSize*/
      ctx[2]}px;`);
      set_style(span2, "position", "absolute");
      set_style(span2, "bottom", "0");
      set_style(span2, "white-space", "nowrap");
      attr(span3, "class", "annotation-badge svelte-3ld1qy");
      attr(div, "class", div_class_value = null_to_empty("annotation " + /*isActive*/
      (ctx[1] ? "active" : "")) + " svelte-3ld1qy");
      attr(div, "role", "button");
      attr(div, "tabindex", "0");
    },
    m(target, anchor) {
      insert(target, div, anchor);
      append(div, span0);
      if_block.m(span0, null);
      append(div, t0);
      append(div, span3);
      append(span3, span1);
      append(span1, t1);
      append(span3, t2);
      append(span3, span2);
      append(span2, t3);
      if (!mounted) {
        dispose = [
          listen(
            div,
            "click",
            /*onClick*/
            ctx[3]
          ),
          listen(
            div,
            "keyup",
            /*onClick*/
            ctx[3]
          )
        ];
        mounted = true;
      }
    },
    p(ctx2, [dirty]) {
      if (current_block_type === (current_block_type = select_block_type(ctx2, dirty)) && if_block) {
        if_block.p(ctx2, dirty);
      } else {
        if_block.d(1);
        if_block = current_block_type(ctx2);
        if (if_block) {
          if_block.c();
          if_block.m(span0, null);
        }
      }
      if (dirty & /*$fontSize*/
      4 && span0_style_value !== (span0_style_value = `font-size:${/*$fontSize*/
      ctx2[2]}px;`)) {
        attr(span0, "style", span0_style_value);
      }
      if (dirty & /*annotation*/
      1 && t1_value !== (t1_value = /*annotation*/
      ctx2[0].range.from.line + 1 + ""))
        set_data(t1, t1_value);
      if (dirty & /*annotation*/
      1 && t3_value !== (t3_value = /*annotation*/
      ctx2[0].label + ""))
        set_data(t3, t3_value);
      if (dirty & /*isActive*/
      2 && div_class_value !== (div_class_value = null_to_empty("annotation " + /*isActive*/
      (ctx2[1] ? "active" : "")) + " svelte-3ld1qy")) {
        attr(div, "class", div_class_value);
      }
    },
    i: noop,
    o: noop,
    d(detaching) {
      if (detaching) {
        detach(div);
      }
      if_block.d();
      mounted = false;
      run_all(dispose);
    }
  };
}
function instance25($$self, $$props, $$invalidate) {
  let $fontSize;
  component_subscribe($$self, fontSize, ($$value) => $$invalidate(2, $fontSize = $$value));
  let { plugin } = $$props;
  let { annotation } = $$props;
  let { isActive } = $$props;
  const onClick = () => {
    selectText(annotation, plugin);
  };
  $$self.$$set = ($$props2) => {
    if ("plugin" in $$props2)
      $$invalidate(4, plugin = $$props2.plugin);
    if ("annotation" in $$props2)
      $$invalidate(0, annotation = $$props2.annotation);
    if ("isActive" in $$props2)
      $$invalidate(1, isActive = $$props2.isActive);
  };
  return [annotation, isActive, $fontSize, onClick, plugin];
}
var Annotation = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance25, create_fragment25, safe_not_equal, { plugin: 4, annotation: 0, isActive: 1 }, add_css5);
  }
};
var annotation_default = Annotation;

// src/sidebar-outline/components/components/annotations-list/annotations-list.svelte
function add_css6(target) {
  append_styles(target, "svelte-1vh0obk", ".annotations-container.svelte-1vh0obk{display:flex;flex-direction:column;width:100%;gap:5px;overflow-y:auto;box-sizing:border-box;padding:0 10px}");
}
function get_each_context3(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[6] = list[i];
  child_ctx[8] = i;
  return child_ctx;
}
function create_else_block3(ctx) {
  let noannotations;
  let current;
  noannotations = new no_annotations_default({ props: { variant: "filter-applied" } });
  return {
    c() {
      create_component(noannotations.$$.fragment);
    },
    m(target, anchor) {
      mount_component(noannotations, target, anchor);
      current = true;
    },
    p: noop,
    i(local) {
      if (current)
        return;
      transition_in(noannotations.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(noannotations.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(noannotations, detaching);
    }
  };
}
function create_if_block6(ctx) {
  let div;
  let current;
  let each_value = ensure_array_like(
    /*$filteredBySearchAndCategoryAndLabel*/
    ctx[2]
  );
  let each_blocks = [];
  for (let i = 0; i < each_value.length; i += 1) {
    each_blocks[i] = create_each_block3(get_each_context3(ctx, each_value, i));
  }
  const out = (i) => transition_out(each_blocks[i], 1, 1, () => {
    each_blocks[i] = null;
  });
  return {
    c() {
      div = element("div");
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      attr(div, "class", "annotations-container svelte-1vh0obk");
    },
    m(target, anchor) {
      insert(target, div, anchor);
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(div, null);
        }
      }
      ctx[4](div);
      current = true;
    },
    p(ctx2, dirty) {
      if (dirty & /*$filteredBySearchAndCategoryAndLabel, plugin, $activeAnnotationIndex*/
      13) {
        each_value = ensure_array_like(
          /*$filteredBySearchAndCategoryAndLabel*/
          ctx2[2]
        );
        let i;
        for (i = 0; i < each_value.length; i += 1) {
          const child_ctx = get_each_context3(ctx2, each_value, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
            transition_in(each_blocks[i], 1);
          } else {
            each_blocks[i] = create_each_block3(child_ctx);
            each_blocks[i].c();
            transition_in(each_blocks[i], 1);
            each_blocks[i].m(div, null);
          }
        }
        group_outros();
        for (i = each_value.length; i < each_blocks.length; i += 1) {
          out(i);
        }
        check_outros();
      }
    },
    i(local) {
      if (current)
        return;
      for (let i = 0; i < each_value.length; i += 1) {
        transition_in(each_blocks[i]);
      }
      current = true;
    },
    o(local) {
      each_blocks = each_blocks.filter(Boolean);
      for (let i = 0; i < each_blocks.length; i += 1) {
        transition_out(each_blocks[i]);
      }
      current = false;
    },
    d(detaching) {
      if (detaching) {
        detach(div);
      }
      destroy_each(each_blocks, detaching);
      ctx[4](null);
    }
  };
}
function create_each_block3(ctx) {
  let annotation_1;
  let current;
  annotation_1 = new annotation_default({
    props: {
      annotation: (
        /*annotation*/
        ctx[6]
      ),
      plugin: (
        /*plugin*/
        ctx[0]
      ),
      isActive: (
        /*$activeAnnotationIndex*/
        ctx[3] === /*index*/
        ctx[8]
      )
    }
  });
  return {
    c() {
      create_component(annotation_1.$$.fragment);
    },
    m(target, anchor) {
      mount_component(annotation_1, target, anchor);
      current = true;
    },
    p(ctx2, dirty) {
      const annotation_1_changes = {};
      if (dirty & /*$filteredBySearchAndCategoryAndLabel*/
      4)
        annotation_1_changes.annotation = /*annotation*/
        ctx2[6];
      if (dirty & /*plugin*/
      1)
        annotation_1_changes.plugin = /*plugin*/
        ctx2[0];
      if (dirty & /*$activeAnnotationIndex*/
      8)
        annotation_1_changes.isActive = /*$activeAnnotationIndex*/
        ctx2[3] === /*index*/
        ctx2[8];
      annotation_1.$set(annotation_1_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(annotation_1.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(annotation_1.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(annotation_1, detaching);
    }
  };
}
function create_fragment26(ctx) {
  let current_block_type_index;
  let if_block;
  let if_block_anchor;
  let current;
  const if_block_creators = [create_if_block6, create_else_block3];
  const if_blocks = [];
  function select_block_type(ctx2, dirty) {
    if (
      /*$filteredBySearchAndCategoryAndLabel*/
      ctx2[2].length > 0
    )
      return 0;
    return 1;
  }
  current_block_type_index = select_block_type(ctx, -1);
  if_block = if_blocks[current_block_type_index] = if_block_creators[current_block_type_index](ctx);
  return {
    c() {
      if_block.c();
      if_block_anchor = empty();
    },
    m(target, anchor) {
      if_blocks[current_block_type_index].m(target, anchor);
      insert(target, if_block_anchor, anchor);
      current = true;
    },
    p(ctx2, [dirty]) {
      let previous_block_index = current_block_type_index;
      current_block_type_index = select_block_type(ctx2, dirty);
      if (current_block_type_index === previous_block_index) {
        if_blocks[current_block_type_index].p(ctx2, dirty);
      } else {
        group_outros();
        transition_out(if_blocks[previous_block_index], 1, 1, () => {
          if_blocks[previous_block_index] = null;
        });
        check_outros();
        if_block = if_blocks[current_block_type_index];
        if (!if_block) {
          if_block = if_blocks[current_block_type_index] = if_block_creators[current_block_type_index](ctx2);
          if_block.c();
        } else {
          if_block.p(ctx2, dirty);
        }
        transition_in(if_block, 1);
        if_block.m(if_block_anchor.parentNode, if_block_anchor);
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(if_block);
      current = true;
    },
    o(local) {
      transition_out(if_block);
      current = false;
    },
    d(detaching) {
      if (detaching) {
        detach(if_block_anchor);
      }
      if_blocks[current_block_type_index].d(detaching);
    }
  };
}
function instance26($$self, $$props, $$invalidate) {
  let $filteredBySearchAndCategoryAndLabel;
  let $activeAnnotationIndex;
  component_subscribe($$self, filteredBySearchAndCategoryAndLabel, ($$value) => $$invalidate(2, $filteredBySearchAndCategoryAndLabel = $$value));
  component_subscribe($$self, activeAnnotationIndex, ($$value) => $$invalidate(3, $activeAnnotationIndex = $$value));
  let { plugin } = $$props;
  let outlineRef;
  const unsub = activeAnnotationIndex.subscribe((index) => {
    if (outlineRef) {
      const active = outlineRef.querySelectorAll("div")[index];
      if (active) {
        active.scrollIntoView({ block: "nearest" });
      }
    }
  });
  onDestroy(unsub);
  function div_binding($$value) {
    binding_callbacks[$$value ? "unshift" : "push"](() => {
      outlineRef = $$value;
      $$invalidate(1, outlineRef);
    });
  }
  $$self.$$set = ($$props2) => {
    if ("plugin" in $$props2)
      $$invalidate(0, plugin = $$props2.plugin);
  };
  return [
    plugin,
    outlineRef,
    $filteredBySearchAndCategoryAndLabel,
    $activeAnnotationIndex,
    div_binding
  ];
}
var Annotations_list = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance26, create_fragment26, safe_not_equal, { plugin: 0 }, add_css6);
  }
};
var annotations_list_default = Annotations_list;

// src/sidebar-outline/components/components/plugin-idle.svelte
function add_css7(target) {
  append_styles(target, "svelte-sicddg", ".plugin-idle.svelte-sicddg{width:100%;display:flex;flex-direction:column;align-items:center;justify-content:center}");
}
function create_fragment27(ctx) {
  let div1;
  let div0;
  let t1;
  let button;
  let mounted;
  let dispose;
  return {
    c() {
      div1 = element("div");
      div0 = element("div");
      div0.textContent = `${l.PLUGIN_NAME + l.OUTLINE_PLUGIN_IS_IDLE}`;
      t1 = space();
      button = element("button");
      button.textContent = `${l.OUTLINE_ENABLE_PLUGIN}`;
      attr(div0, "class", "pane-empty");
      attr(div1, "class", "plugin-idle svelte-sicddg");
    },
    m(target, anchor) {
      insert(target, div1, anchor);
      append(div1, div0);
      append(div1, t1);
      append(div1, button);
      if (!mounted) {
        dispose = listen(
          button,
          "click",
          /*enable*/
          ctx[0]
        );
        mounted = true;
      }
    },
    p: noop,
    i: noop,
    o: noop,
    d(detaching) {
      if (detaching) {
        detach(div1);
      }
      mounted = false;
      dispose();
    }
  };
}
function instance27($$self, $$props, $$invalidate) {
  let { plugin } = $$props;
  const enable = () => {
    plugin.idling.enable();
  };
  $$self.$$set = ($$props2) => {
    if ("plugin" in $$props2)
      $$invalidate(1, plugin = $$props2.plugin);
  };
  return [enable, plugin];
}
var Plugin_idle = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance27, create_fragment27, safe_not_equal, { plugin: 1 }, add_css7);
  }
};
var plugin_idle_default = Plugin_idle;

// src/sidebar-outline/components/components/controls-bar/components/styles/styles.svelte
var import_obsidian3 = require("obsidian");

// src/sidebar-outline/components/components/controls-bar/components/styles/multi-option-toggle-button.svelte
var import_obsidian2 = require("obsidian");
function add_css8(target) {
  append_styles(target, "svelte-l5grro", ".toggle-button.svelte-l5grro{cursor:pointer;color:var(--icon-color);opacity:30%;width:30px;height:30px;padding:2px;border-bottom:2px solid var(--tab-outline-color)}.toggle-button--enabled.svelte-l5grro{opacity:100%}");
}
function create_else_block4(ctx) {
  let switch_instance;
  let switch_instance_anchor;
  let current;
  var switch_value = (
    /*icons*/
    ctx[3][
      /*iconOption*/
      ctx[2].iconName
    ]
  );
  function switch_props(ctx2, dirty) {
    return { props: { class: "svg-icon" } };
  }
  if (switch_value) {
    switch_instance = construct_svelte_component(switch_value, switch_props(ctx));
  }
  return {
    c() {
      if (switch_instance)
        create_component(switch_instance.$$.fragment);
      switch_instance_anchor = empty();
    },
    m(target, anchor) {
      if (switch_instance)
        mount_component(switch_instance, target, anchor);
      insert(target, switch_instance_anchor, anchor);
      current = true;
    },
    p(ctx2, dirty) {
      if (dirty & /*iconOption*/
      4 && switch_value !== (switch_value = /*icons*/
      ctx2[3][
        /*iconOption*/
        ctx2[2].iconName
      ])) {
        if (switch_instance) {
          group_outros();
          const old_component = switch_instance;
          transition_out(old_component.$$.fragment, 1, 0, () => {
            destroy_component(old_component, 1);
          });
          check_outros();
        }
        if (switch_value) {
          switch_instance = construct_svelte_component(switch_value, switch_props(ctx2, dirty));
          create_component(switch_instance.$$.fragment);
          transition_in(switch_instance.$$.fragment, 1);
          mount_component(switch_instance, switch_instance_anchor.parentNode, switch_instance_anchor);
        } else {
          switch_instance = null;
        }
      } else if (switch_value) {
      }
    },
    i(local) {
      if (current)
        return;
      if (switch_instance)
        transition_in(switch_instance.$$.fragment, local);
      current = true;
    },
    o(local) {
      if (switch_instance)
        transition_out(switch_instance.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      if (detaching) {
        detach(switch_instance_anchor);
      }
      if (switch_instance)
        destroy_component(switch_instance, detaching);
    }
  };
}
function create_if_block7(ctx) {
  let html_tag;
  let raw_value = (
    /*iconOption*/
    ctx[2].iconHtml + ""
  );
  let html_anchor;
  return {
    c() {
      html_tag = new HtmlTag(false);
      html_anchor = empty();
      html_tag.a = html_anchor;
    },
    m(target, anchor) {
      html_tag.m(raw_value, target, anchor);
      insert(target, html_anchor, anchor);
    },
    p(ctx2, dirty) {
      if (dirty & /*iconOption*/
      4 && raw_value !== (raw_value = /*iconOption*/
      ctx2[2].iconHtml + ""))
        html_tag.p(raw_value);
    },
    i: noop,
    o: noop,
    d(detaching) {
      if (detaching) {
        detach(html_anchor);
        html_tag.d();
      }
    }
  };
}
function create_fragment28(ctx) {
  let button;
  let current_block_type_index;
  let if_block;
  let button_aria_label_value;
  let button_class_value;
  let current;
  let mounted;
  let dispose;
  const if_block_creators = [create_if_block7, create_else_block4];
  const if_blocks = [];
  function select_block_type(ctx2, dirty) {
    if ("iconHtml" in /*iconOption*/
    ctx2[2])
      return 0;
    return 1;
  }
  current_block_type_index = select_block_type(ctx, -1);
  if_block = if_blocks[current_block_type_index] = if_block_creators[current_block_type_index](ctx);
  return {
    c() {
      button = element("button");
      if_block.c();
      attr(button, "aria-label", button_aria_label_value = /*option*/
      ctx[1] ? `${/*props*/
      ctx[0].name}: ${/*option*/
      ctx[1].value}` : `${/*props*/
      ctx[0].name}: default`);
      attr(button, "class", button_class_value = null_to_empty("toggle-button" + /*option*/
      (ctx[1] ? " toggle-button--enabled" : "")) + " svelte-l5grro");
    },
    m(target, anchor) {
      insert(target, button, anchor);
      if_blocks[current_block_type_index].m(button, null);
      current = true;
      if (!mounted) {
        dispose = listen(
          button,
          "click",
          /*onClick*/
          ctx[4]
        );
        mounted = true;
      }
    },
    p(ctx2, [dirty]) {
      let previous_block_index = current_block_type_index;
      current_block_type_index = select_block_type(ctx2, dirty);
      if (current_block_type_index === previous_block_index) {
        if_blocks[current_block_type_index].p(ctx2, dirty);
      } else {
        group_outros();
        transition_out(if_blocks[previous_block_index], 1, 1, () => {
          if_blocks[previous_block_index] = null;
        });
        check_outros();
        if_block = if_blocks[current_block_type_index];
        if (!if_block) {
          if_block = if_blocks[current_block_type_index] = if_block_creators[current_block_type_index](ctx2);
          if_block.c();
        } else {
          if_block.p(ctx2, dirty);
        }
        transition_in(if_block, 1);
        if_block.m(button, null);
      }
      if (!current || dirty & /*option, props*/
      3 && button_aria_label_value !== (button_aria_label_value = /*option*/
      ctx2[1] ? `${/*props*/
      ctx2[0].name}: ${/*option*/
      ctx2[1].value}` : `${/*props*/
      ctx2[0].name}: default`)) {
        attr(button, "aria-label", button_aria_label_value);
      }
      if (!current || dirty & /*option*/
      2 && button_class_value !== (button_class_value = null_to_empty("toggle-button" + /*option*/
      (ctx2[1] ? " toggle-button--enabled" : "")) + " svelte-l5grro")) {
        attr(button, "class", button_class_value);
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(if_block);
      current = true;
    },
    o(local) {
      transition_out(if_block);
      current = false;
    },
    d(detaching) {
      if (detaching) {
        detach(button);
      }
      if_blocks[current_block_type_index].d();
      mounted = false;
      dispose();
    }
  };
}
function instance28($$self, $$props, $$invalidate) {
  const icons = {
    "case-lower": Case_lower$1,
    "case-upper": Case_upper$1,
    "case-sensitive": Case_sensitive$1,
    "comment": Message_square$1,
    "highlight": Highlighter$1
  };
  let { props } = $$props;
  const defaultOption = Object.assign(Object.assign({}, props.options[0]), { name: props.name });
  let option = props.options.find((o) => o.value === props.value);
  let iconOption;
  let notice;
  const onClick = () => {
    let i = option ? props.options.indexOf(option) + 1 : 0;
    if (i >= props.options.length)
      i = -1;
    const newOption = props.options[i];
    const value = newOption ? newOption.value : void 0;
    $$invalidate(1, option = newOption);
    props.onChange(value);
    if (notice)
      notice.hide();
    notice = new import_obsidian2.Notice(`'${props.name}' set to '${value || "default"}'`);
  };
  $$self.$$set = ($$props2) => {
    if ("props" in $$props2)
      $$invalidate(0, props = $$props2.props);
  };
  $$self.$$.update = () => {
    if ($$self.$$.dirty & /*option*/
    2) {
      $:
        $$invalidate(2, iconOption = option || defaultOption);
    }
  };
  return [props, option, iconOption, icons, onClick];
}
var Multi_option_toggle_button = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance28, create_fragment28, safe_not_equal, { props: 0 }, add_css8);
  }
};
var multi_option_toggle_button_default = Multi_option_toggle_button;

// src/settings/settings-tab/components/label-settings/components/text-svg.ts
var TextSVG = (text2, attributes) => {
  const { fontWeight = 400, fontFamily = "sans-serif" } = attributes || {};
  return `
	<svg
	  class="svg-icon"
	  xmlns="http://www.w3.org/2000/svg"
	  width="24"
	  height="24"
	  viewBox="0 0 24 24"
	  fill="none"
	  stroke="currentColor"
	  stroke-width="2"
	  stroke-linecap="round"
	  stroke-linejoin="round"
	>
			<text
			  font-family="${fontFamily}"
			  font-size="20px"
			  font-weight="${fontWeight}" x="${text2.length === 1 ? 5 : 0}" y="19"
			  fill="currentColor"
			  stroke="none">${text2}</text>
	</svg>
		`;
};

// src/sidebar-outline/components/components/controls-bar/components/styles/toggle-button.svelte
function add_css9(target) {
  append_styles(target, "svelte-68djlm", ".toggle-button.svelte-68djlm{cursor:pointer;color:var(--icon-color);opacity:30%;width:30px;height:30px;padding:2px}.toggle-button--enabled.svelte-68djlm{opacity:100%}");
}
function create_fragment29(ctx) {
  let button;
  let button_aria_label_value;
  let button_class_value;
  let current;
  let mounted;
  let dispose;
  const default_slot_template = (
    /*#slots*/
    ctx[4].default
  );
  const default_slot = create_slot(
    default_slot_template,
    ctx,
    /*$$scope*/
    ctx[3],
    null
  );
  return {
    c() {
      button = element("button");
      if (default_slot)
        default_slot.c();
      attr(button, "aria-label", button_aria_label_value = `${/*label*/
      ctx[0]}: ${/*enabled*/
      ctx[2] ? "enabled" : "disabled"}`);
      attr(button, "class", button_class_value = null_to_empty("toggle-button" + /*enabled*/
      (ctx[2] ? " toggle-button--enabled" : "")) + " svelte-68djlm");
    },
    m(target, anchor) {
      insert(target, button, anchor);
      if (default_slot) {
        default_slot.m(button, null);
      }
      current = true;
      if (!mounted) {
        dispose = listen(button, "click", function() {
          if (is_function(
            /*onClick*/
            ctx[1]
          ))
            ctx[1].apply(this, arguments);
        });
        mounted = true;
      }
    },
    p(new_ctx, [dirty]) {
      ctx = new_ctx;
      if (default_slot) {
        if (default_slot.p && (!current || dirty & /*$$scope*/
        8)) {
          update_slot_base(
            default_slot,
            default_slot_template,
            ctx,
            /*$$scope*/
            ctx[3],
            !current ? get_all_dirty_from_scope(
              /*$$scope*/
              ctx[3]
            ) : get_slot_changes(
              default_slot_template,
              /*$$scope*/
              ctx[3],
              dirty,
              null
            ),
            null
          );
        }
      }
      if (!current || dirty & /*label, enabled*/
      5 && button_aria_label_value !== (button_aria_label_value = `${/*label*/
      ctx[0]}: ${/*enabled*/
      ctx[2] ? "enabled" : "disabled"}`)) {
        attr(button, "aria-label", button_aria_label_value);
      }
      if (!current || dirty & /*enabled*/
      4 && button_class_value !== (button_class_value = null_to_empty("toggle-button" + /*enabled*/
      (ctx[2] ? " toggle-button--enabled" : "")) + " svelte-68djlm")) {
        attr(button, "class", button_class_value);
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(default_slot, local);
      current = true;
    },
    o(local) {
      transition_out(default_slot, local);
      current = false;
    },
    d(detaching) {
      if (detaching) {
        detach(button);
      }
      if (default_slot)
        default_slot.d(detaching);
      mounted = false;
      dispose();
    }
  };
}
function instance29($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  let { label } = $$props;
  let { onClick } = $$props;
  let { enabled = false } = $$props;
  $$self.$$set = ($$props2) => {
    if ("label" in $$props2)
      $$invalidate(0, label = $$props2.label);
    if ("onClick" in $$props2)
      $$invalidate(1, onClick = $$props2.onClick);
    if ("enabled" in $$props2)
      $$invalidate(2, enabled = $$props2.enabled);
    if ("$$scope" in $$props2)
      $$invalidate(3, $$scope = $$props2.$$scope);
  };
  return [label, onClick, enabled, $$scope, slots];
}
var Toggle_button = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance29, create_fragment29, safe_not_equal, { label: 0, onClick: 1, enabled: 2 }, add_css9);
  }
};
var toggle_button_default = Toggle_button;

// src/sidebar-outline/components/components/controls-bar/components/styles/square-button.svelte
function add_css10(target) {
  append_styles(target, "svelte-hh62cu", ".toggle-button.svelte-hh62cu{cursor:pointer;color:var(--icon-color);opacity:100%;width:30px;height:30px;padding:2px}");
}
function create_fragment30(ctx) {
  let button;
  let button_class_value;
  let current;
  let mounted;
  let dispose;
  const default_slot_template = (
    /*#slots*/
    ctx[3].default
  );
  const default_slot = create_slot(
    default_slot_template,
    ctx,
    /*$$scope*/
    ctx[2],
    null
  );
  return {
    c() {
      button = element("button");
      if (default_slot)
        default_slot.c();
      attr(
        button,
        "aria-label",
        /*label*/
        ctx[0]
      );
      attr(button, "class", button_class_value = null_to_empty("toggle-button") + " svelte-hh62cu");
    },
    m(target, anchor) {
      insert(target, button, anchor);
      if (default_slot) {
        default_slot.m(button, null);
      }
      current = true;
      if (!mounted) {
        dispose = listen(button, "click", function() {
          if (is_function(
            /*onClick*/
            ctx[1]
          ))
            ctx[1].apply(this, arguments);
        });
        mounted = true;
      }
    },
    p(new_ctx, [dirty]) {
      ctx = new_ctx;
      if (default_slot) {
        if (default_slot.p && (!current || dirty & /*$$scope*/
        4)) {
          update_slot_base(
            default_slot,
            default_slot_template,
            ctx,
            /*$$scope*/
            ctx[2],
            !current ? get_all_dirty_from_scope(
              /*$$scope*/
              ctx[2]
            ) : get_slot_changes(
              default_slot_template,
              /*$$scope*/
              ctx[2],
              dirty,
              null
            ),
            null
          );
        }
      }
      if (!current || dirty & /*label*/
      1) {
        attr(
          button,
          "aria-label",
          /*label*/
          ctx[0]
        );
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(default_slot, local);
      current = true;
    },
    o(local) {
      transition_out(default_slot, local);
      current = false;
    },
    d(detaching) {
      if (detaching) {
        detach(button);
      }
      if (default_slot)
        default_slot.d(detaching);
      mounted = false;
      dispose();
    }
  };
}
function instance30($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  let { label } = $$props;
  let { onClick } = $$props;
  $$self.$$set = ($$props2) => {
    if ("label" in $$props2)
      $$invalidate(0, label = $$props2.label);
    if ("onClick" in $$props2)
      $$invalidate(1, onClick = $$props2.onClick);
    if ("$$scope" in $$props2)
      $$invalidate(2, $$scope = $$props2.$$scope);
  };
  return [label, onClick, $$scope, slots];
}
var Square_button = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance30, create_fragment30, safe_not_equal, { label: 0, onClick: 1 }, add_css10);
  }
};
var square_button_default = Square_button;

// src/helpers/array-utils.ts
var getRandomElement = (arr) => {
  const randomIndex = Math.floor(Math.random() * arr.length);
  return arr[randomIndex];
};

// src/settings/helpers/get-default-color.ts
var palettes = {
  bright: [
    "#f65a5a",
    "#e99696",
    "#f37636",
    "#f6bc4a",
    "#f2df93",
    "#fcea26",
    "#bff341",
    "#93ef30",
    "#36e112",
    "#81fa8a",
    "#79de9e",
    "#00f384",
    "#00e4be",
    "#10cff1",
    "#0099ff",
    "#3da5f1",
    "#446df3",
    "#574ff6",
    "#9b5cf0",
    "#d777f6",
    "#fd35f6",
    "#f355b5",
    "#db2777"
  ],
  dull: [
    "#824b4b",
    "#875c3c",
    "#6b5449",
    "#774809",
    "#656535",
    "#54837b",
    "#658b8b",
    "#405d72",
    "#405d72",
    "#4f538c",
    "#69577f",
    "#61447e",
    "#7f4d80",
    "#82687d",
    "#6b3f55"
  ]
};
var getDefaultColor = (settings) => {
  const groups = Object.values(settings.decoration.styles.labels);
  const palette = settings.decoration.defaultPalette || "bright";
  const colors = new Set(groups.map((g) => g.style.color).filter(Boolean));
  return getRandomElement(palettes[palette].filter((c) => !colors.has(c)));
};

// src/sidebar-outline/components/components/controls-bar/components/styles/additional-styles.svelte
function add_css11(target) {
  append_styles(target, "svelte-17v1w3o", ".additional-settings.svelte-17v1w3o{display:flex;flex-direction:column;gap:5px}.settings-row.svelte-17v1w3o{display:flex;align-items:center;gap:5px}");
}
function create_else_block5(ctx) {
  let chevrondown;
  let current;
  chevrondown = new Chevron_down$1({ props: { size: 18 } });
  return {
    c() {
      create_component(chevrondown.$$.fragment);
    },
    m(target, anchor) {
      mount_component(chevrondown, target, anchor);
      current = true;
    },
    i(local) {
      if (current)
        return;
      transition_in(chevrondown.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(chevrondown.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(chevrondown, detaching);
    }
  };
}
function create_if_block_12(ctx) {
  let chevronup;
  let current;
  chevronup = new Chevron_up$1({ props: { size: 18 } });
  return {
    c() {
      create_component(chevronup.$$.fragment);
    },
    m(target, anchor) {
      mount_component(chevronup, target, anchor);
      current = true;
    },
    i(local) {
      if (current)
        return;
      transition_in(chevronup.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(chevronup.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(chevronup, detaching);
    }
  };
}
function create_default_slot_32(ctx) {
  let current_block_type_index;
  let if_block;
  let if_block_anchor;
  let current;
  const if_block_creators = [create_if_block_12, create_else_block5];
  const if_blocks = [];
  function select_block_type(ctx2, dirty) {
    if (
      /*showAdditionalSettings*/
      ctx2[3]
    )
      return 0;
    return 1;
  }
  current_block_type_index = select_block_type(ctx, -1);
  if_block = if_blocks[current_block_type_index] = if_block_creators[current_block_type_index](ctx);
  return {
    c() {
      if_block.c();
      if_block_anchor = empty();
    },
    m(target, anchor) {
      if_blocks[current_block_type_index].m(target, anchor);
      insert(target, if_block_anchor, anchor);
      current = true;
    },
    p(ctx2, dirty) {
      let previous_block_index = current_block_type_index;
      current_block_type_index = select_block_type(ctx2, dirty);
      if (current_block_type_index !== previous_block_index) {
        group_outros();
        transition_out(if_blocks[previous_block_index], 1, 1, () => {
          if_blocks[previous_block_index] = null;
        });
        check_outros();
        if_block = if_blocks[current_block_type_index];
        if (!if_block) {
          if_block = if_blocks[current_block_type_index] = if_block_creators[current_block_type_index](ctx2);
          if_block.c();
        } else {
        }
        transition_in(if_block, 1);
        if_block.m(if_block_anchor.parentNode, if_block_anchor);
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(if_block);
      current = true;
    },
    o(local) {
      transition_out(if_block);
      current = false;
    },
    d(detaching) {
      if (detaching) {
        detach(if_block_anchor);
      }
      if_blocks[current_block_type_index].d(detaching);
    }
  };
}
function create_if_block8(ctx) {
  let div0;
  let togglebutton0;
  let t0;
  let multioptionstogglebutton0;
  let t1;
  let togglebutton1;
  let t2;
  let squarebutton;
  let t3;
  let div1;
  let multioptionstogglebutton1;
  let t4;
  let multioptionstogglebutton2;
  let current;
  togglebutton0 = new toggle_button_default({
    props: {
      enabled: (
        /*italic*/
        ctx[5]
      ),
      label: "Italic",
      onClick: (
        /*onToggleItalic*/
        ctx[8]
      ),
      $$slots: { default: [create_default_slot_23] },
      $$scope: { ctx }
    }
  });
  multioptionstogglebutton0 = new multi_option_toggle_button_default({
    props: {
      props: {
        name: "Font weight",
        options: (
          /*fontWeights*/
          ctx[11]
        ),
        value: (
          /*label*/
          ctx[1].style.fontWeight
        ),
        onChange: (
          /*onFontWeightChange*/
          ctx[12]
        )
      }
    }
  });
  togglebutton1 = new toggle_button_default({
    props: {
      enabled: (
        /*underline*/
        ctx[4]
      ),
      label: "Underline",
      onClick: (
        /*onToggleUnderline*/
        ctx[7]
      ),
      $$slots: { default: [create_default_slot_14] },
      $$scope: { ctx }
    }
  });
  squarebutton = new square_button_default({
    props: {
      label: l.SETTINGS_LABELS_STYLES_DELETE_STYLE,
      onClick: (
        /*onDelete*/
        ctx[19]
      ),
      $$slots: { default: [create_default_slot20] },
      $$scope: { ctx }
    }
  });
  multioptionstogglebutton1 = new multi_option_toggle_button_default({
    props: {
      props: {
        name: "Font family",
        options: (
          /*fontFamilies*/
          ctx[9]
        ),
        value: (
          /*label*/
          ctx[1].style.fontFamily
        ),
        onChange: (
          /*onFontFamilyChange*/
          ctx[10]
        )
      }
    }
  });
  multioptionstogglebutton2 = new multi_option_toggle_button_default({
    props: {
      props: {
        options: (
          /*labelCases*/
          ctx[13]
        ),
        value: (
          /*label*/
          ctx[1].style.case
        ),
        name: "Case",
        onChange: (
          /*onLabelCaseChange*/
          ctx[14]
        )
      }
    }
  });
  return {
    c() {
      div0 = element("div");
      create_component(togglebutton0.$$.fragment);
      t0 = space();
      create_component(multioptionstogglebutton0.$$.fragment);
      t1 = space();
      create_component(togglebutton1.$$.fragment);
      t2 = space();
      create_component(squarebutton.$$.fragment);
      t3 = space();
      div1 = element("div");
      create_component(multioptionstogglebutton1.$$.fragment);
      t4 = space();
      create_component(multioptionstogglebutton2.$$.fragment);
      attr(div0, "class", "settings-row svelte-17v1w3o");
      attr(div1, "class", "settings-row svelte-17v1w3o");
    },
    m(target, anchor) {
      insert(target, div0, anchor);
      mount_component(togglebutton0, div0, null);
      append(div0, t0);
      mount_component(multioptionstogglebutton0, div0, null);
      append(div0, t1);
      mount_component(togglebutton1, div0, null);
      append(div0, t2);
      mount_component(squarebutton, div0, null);
      insert(target, t3, anchor);
      insert(target, div1, anchor);
      mount_component(multioptionstogglebutton1, div1, null);
      append(div1, t4);
      mount_component(multioptionstogglebutton2, div1, null);
      current = true;
    },
    p(ctx2, dirty) {
      const togglebutton0_changes = {};
      if (dirty & /*italic*/
      32)
        togglebutton0_changes.enabled = /*italic*/
        ctx2[5];
      if (dirty & /*$$scope*/
      2097152) {
        togglebutton0_changes.$$scope = { dirty, ctx: ctx2 };
      }
      togglebutton0.$set(togglebutton0_changes);
      const multioptionstogglebutton0_changes = {};
      if (dirty & /*label*/
      2)
        multioptionstogglebutton0_changes.props = {
          name: "Font weight",
          options: (
            /*fontWeights*/
            ctx2[11]
          ),
          value: (
            /*label*/
            ctx2[1].style.fontWeight
          ),
          onChange: (
            /*onFontWeightChange*/
            ctx2[12]
          )
        };
      multioptionstogglebutton0.$set(multioptionstogglebutton0_changes);
      const togglebutton1_changes = {};
      if (dirty & /*underline*/
      16)
        togglebutton1_changes.enabled = /*underline*/
        ctx2[4];
      if (dirty & /*$$scope*/
      2097152) {
        togglebutton1_changes.$$scope = { dirty, ctx: ctx2 };
      }
      togglebutton1.$set(togglebutton1_changes);
      const squarebutton_changes = {};
      if (dirty & /*$$scope*/
      2097152) {
        squarebutton_changes.$$scope = { dirty, ctx: ctx2 };
      }
      squarebutton.$set(squarebutton_changes);
      const multioptionstogglebutton1_changes = {};
      if (dirty & /*label*/
      2)
        multioptionstogglebutton1_changes.props = {
          name: "Font family",
          options: (
            /*fontFamilies*/
            ctx2[9]
          ),
          value: (
            /*label*/
            ctx2[1].style.fontFamily
          ),
          onChange: (
            /*onFontFamilyChange*/
            ctx2[10]
          )
        };
      multioptionstogglebutton1.$set(multioptionstogglebutton1_changes);
      const multioptionstogglebutton2_changes = {};
      if (dirty & /*label*/
      2)
        multioptionstogglebutton2_changes.props = {
          options: (
            /*labelCases*/
            ctx2[13]
          ),
          value: (
            /*label*/
            ctx2[1].style.case
          ),
          name: "Case",
          onChange: (
            /*onLabelCaseChange*/
            ctx2[14]
          )
        };
      multioptionstogglebutton2.$set(multioptionstogglebutton2_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(togglebutton0.$$.fragment, local);
      transition_in(multioptionstogglebutton0.$$.fragment, local);
      transition_in(togglebutton1.$$.fragment, local);
      transition_in(squarebutton.$$.fragment, local);
      transition_in(multioptionstogglebutton1.$$.fragment, local);
      transition_in(multioptionstogglebutton2.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(togglebutton0.$$.fragment, local);
      transition_out(multioptionstogglebutton0.$$.fragment, local);
      transition_out(togglebutton1.$$.fragment, local);
      transition_out(squarebutton.$$.fragment, local);
      transition_out(multioptionstogglebutton1.$$.fragment, local);
      transition_out(multioptionstogglebutton2.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      if (detaching) {
        detach(div0);
        detach(t3);
        detach(div1);
      }
      destroy_component(togglebutton0);
      destroy_component(multioptionstogglebutton0);
      destroy_component(togglebutton1);
      destroy_component(squarebutton);
      destroy_component(multioptionstogglebutton1);
      destroy_component(multioptionstogglebutton2);
    }
  };
}
function create_default_slot_23(ctx) {
  let italic_1;
  let current;
  italic_1 = new Italic$1({ props: { size: 18 } });
  return {
    c() {
      create_component(italic_1.$$.fragment);
    },
    m(target, anchor) {
      mount_component(italic_1, target, anchor);
      current = true;
    },
    p: noop,
    i(local) {
      if (current)
        return;
      transition_in(italic_1.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(italic_1.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(italic_1, detaching);
    }
  };
}
function create_default_slot_14(ctx) {
  let underline_1;
  let current;
  underline_1 = new Underline$1({ props: { size: 18 } });
  return {
    c() {
      create_component(underline_1.$$.fragment);
    },
    m(target, anchor) {
      mount_component(underline_1, target, anchor);
      current = true;
    },
    p: noop,
    i(local) {
      if (current)
        return;
      transition_in(underline_1.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(underline_1.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(underline_1, detaching);
    }
  };
}
function create_default_slot20(ctx) {
  let trash2;
  let current;
  trash2 = new Trash_2$1({ props: { size: 18, color: "red" } });
  return {
    c() {
      create_component(trash2.$$.fragment);
    },
    m(target, anchor) {
      mount_component(trash2, target, anchor);
      current = true;
    },
    p: noop,
    i(local) {
      if (current)
        return;
      transition_in(trash2.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(trash2.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(trash2, detaching);
    }
  };
}
function create_fragment31(ctx) {
  let div1;
  let div0;
  let input;
  let input_value_value;
  let t0;
  let multioptionstogglebutton0;
  let t1;
  let multioptionstogglebutton1;
  let t2;
  let squarebutton;
  let t3;
  let div1_class_value;
  let current;
  let mounted;
  let dispose;
  multioptionstogglebutton0 = new multi_option_toggle_button_default({
    props: {
      props: {
        onChange: (
          /*onFontOpacityChange*/
          ctx[18]
        ),
        options: (
          /*fontOpacities*/
          ctx[17]
        ),
        value: (
          /*label*/
          ctx[1].style.opacity
        ),
        name: "Opacity"
      }
    }
  });
  multioptionstogglebutton1 = new multi_option_toggle_button_default({
    props: {
      props: {
        onChange: (
          /*onFontSizeChange*/
          ctx[16]
        ),
        options: (
          /*fontSizes*/
          ctx[15]
        ),
        value: (
          /*label*/
          ctx[1].style.fontSize
        ),
        name: "Font size"
      }
    }
  });
  squarebutton = new square_button_default({
    props: {
      label: (
        /*showAdditionalSettings*/
        ctx[3] ? l.COLLAPSE : l.EXPAND
      ),
      onClick: (
        /*onToggleAdditionalSettings*/
        ctx[2]
      ),
      $$slots: { default: [create_default_slot_32] },
      $$scope: { ctx }
    }
  });
  let if_block = (
    /*showAdditionalSettings*/
    ctx[3] && create_if_block8(ctx)
  );
  return {
    c() {
      div1 = element("div");
      div0 = element("div");
      input = element("input");
      t0 = space();
      create_component(multioptionstogglebutton0.$$.fragment);
      t1 = space();
      create_component(multioptionstogglebutton1.$$.fragment);
      t2 = space();
      create_component(squarebutton.$$.fragment);
      t3 = space();
      if (if_block)
        if_block.c();
      attr(input, "aria-label", "Press `shift` + `left click` for a random color");
      attr(input, "type", "color");
      input.value = input_value_value = /*label*/
      ctx[1].style.color || "";
      attr(div0, "class", "settings-row svelte-17v1w3o");
      attr(div1, "class", div1_class_value = null_to_empty(`additional-settings `) + " svelte-17v1w3o");
    },
    m(target, anchor) {
      insert(target, div1, anchor);
      append(div1, div0);
      append(div0, input);
      append(div0, t0);
      mount_component(multioptionstogglebutton0, div0, null);
      append(div0, t1);
      mount_component(multioptionstogglebutton1, div0, null);
      append(div0, t2);
      mount_component(squarebutton, div0, null);
      append(div1, t3);
      if (if_block)
        if_block.m(div1, null);
      current = true;
      if (!mounted) {
        dispose = [
          listen(
            input,
            "change",
            /*onColorChange*/
            ctx[6]
          ),
          listen(
            input,
            "click",
            /*click_handler*/
            ctx[20]
          )
        ];
        mounted = true;
      }
    },
    p(ctx2, [dirty]) {
      if (!current || dirty & /*label*/
      2 && input_value_value !== (input_value_value = /*label*/
      ctx2[1].style.color || "")) {
        input.value = input_value_value;
      }
      const multioptionstogglebutton0_changes = {};
      if (dirty & /*label*/
      2)
        multioptionstogglebutton0_changes.props = {
          onChange: (
            /*onFontOpacityChange*/
            ctx2[18]
          ),
          options: (
            /*fontOpacities*/
            ctx2[17]
          ),
          value: (
            /*label*/
            ctx2[1].style.opacity
          ),
          name: "Opacity"
        };
      multioptionstogglebutton0.$set(multioptionstogglebutton0_changes);
      const multioptionstogglebutton1_changes = {};
      if (dirty & /*label*/
      2)
        multioptionstogglebutton1_changes.props = {
          onChange: (
            /*onFontSizeChange*/
            ctx2[16]
          ),
          options: (
            /*fontSizes*/
            ctx2[15]
          ),
          value: (
            /*label*/
            ctx2[1].style.fontSize
          ),
          name: "Font size"
        };
      multioptionstogglebutton1.$set(multioptionstogglebutton1_changes);
      const squarebutton_changes = {};
      if (dirty & /*showAdditionalSettings*/
      8)
        squarebutton_changes.label = /*showAdditionalSettings*/
        ctx2[3] ? l.COLLAPSE : l.EXPAND;
      if (dirty & /*onToggleAdditionalSettings*/
      4)
        squarebutton_changes.onClick = /*onToggleAdditionalSettings*/
        ctx2[2];
      if (dirty & /*$$scope, showAdditionalSettings*/
      2097160) {
        squarebutton_changes.$$scope = { dirty, ctx: ctx2 };
      }
      squarebutton.$set(squarebutton_changes);
      if (
        /*showAdditionalSettings*/
        ctx2[3]
      ) {
        if (if_block) {
          if_block.p(ctx2, dirty);
          if (dirty & /*showAdditionalSettings*/
          8) {
            transition_in(if_block, 1);
          }
        } else {
          if_block = create_if_block8(ctx2);
          if_block.c();
          transition_in(if_block, 1);
          if_block.m(div1, null);
        }
      } else if (if_block) {
        group_outros();
        transition_out(if_block, 1, 1, () => {
          if_block = null;
        });
        check_outros();
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(multioptionstogglebutton0.$$.fragment, local);
      transition_in(multioptionstogglebutton1.$$.fragment, local);
      transition_in(squarebutton.$$.fragment, local);
      transition_in(if_block);
      current = true;
    },
    o(local) {
      transition_out(multioptionstogglebutton0.$$.fragment, local);
      transition_out(multioptionstogglebutton1.$$.fragment, local);
      transition_out(squarebutton.$$.fragment, local);
      transition_out(if_block);
      current = false;
    },
    d(detaching) {
      if (detaching) {
        detach(div1);
      }
      destroy_component(multioptionstogglebutton0);
      destroy_component(multioptionstogglebutton1);
      destroy_component(squarebutton);
      if (if_block)
        if_block.d();
      mounted = false;
      run_all(dispose);
    }
  };
}
function instance31($$self, $$props, $$invalidate) {
  let { plugin } = $$props;
  let { label } = $$props;
  let { onToggleAdditionalSettings } = $$props;
  let { showAdditionalSettings = false } = $$props;
  const onColorChange = (e) => {
    const value = e.target.value;
    plugin.settings.dispatch({
      payload: { id: label.id, color: value },
      type: "SET_COLOR"
    });
  };
  let underline = Boolean(label.style.underline);
  const onToggleUnderline = () => {
    $$invalidate(4, underline = !label.style.underline);
    plugin.settings.dispatch({
      payload: { id: label.id, underline },
      type: "SET_LABEL_UNDERLINE"
    });
  };
  let italic = Boolean(label.style.italic);
  const onToggleItalic = () => {
    $$invalidate(5, italic = !label.style.italic);
    plugin.settings.dispatch({
      payload: { id: label.id, italic },
      type: "SET_LABEL_ITALIC"
    });
  };
  const fontFamilies = ["sans-serif", "serif", "monospace"].map((f) => ({
    value: f,
    iconHtml: TextSVG("F", { fontFamily: f })
  }));
  const onFontFamilyChange = (value) => {
    plugin.settings.dispatch({
      payload: { id: label.id, family: value },
      type: "SET_LABEL_FONT_FAMILY"
    });
  };
  const fontWeights = [
    {
      value: "thin",
      iconHtml: TextSVG("B", { fontWeight: 400 })
    },
    {
      value: "bold",
      iconHtml: TextSVG("B", { fontWeight: 600 })
    }
  ];
  const onFontWeightChange = (value) => {
    plugin.settings.dispatch({
      payload: { id: label.id, weight: value },
      type: "SET_LABEL_FONT_WEIGHT"
    });
  };
  const labelCases = [
    {
      iconName: "case-sensitive",
      value: "title"
    },
    { iconName: "case-upper", value: "upper" },
    { iconName: "case-lower", value: "lower" }
  ];
  const onLabelCaseChange = (value) => plugin.settings.dispatch({
    type: "SET_LABEL_CASE",
    payload: { id: label.id, case: value }
  });
  const fontSizes = [12, 16, 20, 24, 32].map((n) => ({
    name: n + "px",
    value: n,
    iconHtml: TextSVG(String(n))
  }));
  const onFontSizeChange = (value) => plugin.settings.dispatch({
    type: "SET_LABEL_FONT_SIZE",
    payload: { id: label.id, fontSize: value }
  });
  const fontOpacities = [80, 60, 40, 20].map((n) => ({
    name: n + "%",
    value: n,
    iconHtml: TextSVG(String(n))
  }));
  const onFontOpacityChange = (value) => plugin.settings.dispatch({
    type: "SET_LABEL_FONT_OPACITY",
    payload: { id: label.id, opacity: value }
  });
  const onDelete = () => {
    plugin.settings.dispatch({
      payload: { id: label.id },
      type: "DELETE_GROUP"
    });
  };
  const click_handler = (e) => {
    if (e.shiftKey) {
      e.preventDefault();
      e.currentTarget.value = getDefaultColor(plugin.settings.getValue());
      onColorChange(e);
    }
  };
  $$self.$$set = ($$props2) => {
    if ("plugin" in $$props2)
      $$invalidate(0, plugin = $$props2.plugin);
    if ("label" in $$props2)
      $$invalidate(1, label = $$props2.label);
    if ("onToggleAdditionalSettings" in $$props2)
      $$invalidate(2, onToggleAdditionalSettings = $$props2.onToggleAdditionalSettings);
    if ("showAdditionalSettings" in $$props2)
      $$invalidate(3, showAdditionalSettings = $$props2.showAdditionalSettings);
  };
  return [
    plugin,
    label,
    onToggleAdditionalSettings,
    showAdditionalSettings,
    underline,
    italic,
    onColorChange,
    onToggleUnderline,
    onToggleItalic,
    fontFamilies,
    onFontFamilyChange,
    fontWeights,
    onFontWeightChange,
    labelCases,
    onLabelCaseChange,
    fontSizes,
    onFontSizeChange,
    fontOpacities,
    onFontOpacityChange,
    onDelete,
    click_handler
  ];
}
var Additional_styles = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(
      this,
      options2,
      instance31,
      create_fragment31,
      safe_not_equal,
      {
        plugin: 0,
        label: 1,
        onToggleAdditionalSettings: 2,
        showAdditionalSettings: 3
      },
      add_css11
    );
  }
};
var additional_styles_default = Additional_styles;

// src/editor-suggest/helpers/is-valid-label.ts
var isValidLabel = (label) => {
  return !/[:\s]/.test(label) && label !== "/" && label.length > 0;
};

// src/sidebar-outline/components/components/controls-bar/components/styles/styles.svelte
function add_css12(target) {
  append_styles(target, "svelte-15wea5d", ".main-styles.svelte-15wea5d{display:flex;gap:5px;flex-wrap:wrap;justify-content:center}.invalid-label.svelte-15wea5d{outline:var(--color-red) 2px solid}");
}
function create_fragment32(ctx) {
  let div;
  let multioptionstogglebutton;
  let t0;
  let input;
  let input_class_value;
  let input_pattern_value;
  let input_placeholder_value;
  let input_value_value;
  let t1;
  let additionalstyles;
  let div_class_value;
  let current;
  let mounted;
  let dispose;
  multioptionstogglebutton = new multi_option_toggle_button_default({
    props: {
      props: {
        name: "Scope",
        options: (
          /*scopes*/
          ctx[9]
        ),
        value: (
          /*label*/
          ctx[0].style.scope
        ),
        onChange: (
          /*onScopeChange*/
          ctx[8]
        )
      }
    }
  });
  additionalstyles = new additional_styles_default({
    props: {
      label: (
        /*label*/
        ctx[0]
      ),
      onToggleAdditionalSettings: (
        /*onToggleAdditionalSettings*/
        ctx[7]
      ),
      plugin: (
        /*plugin*/
        ctx[1]
      ),
      showAdditionalSettings: (
        /*showAdditionalSettings*/
        ctx[4]
      )
    }
  });
  return {
    c() {
      div = element("div");
      create_component(multioptionstogglebutton.$$.fragment);
      t0 = space();
      input = element("input");
      t1 = space();
      create_component(additionalstyles.$$.fragment);
      attr(input, "class", input_class_value = null_to_empty(!/*valid*/
      ctx[2] && !/*isEmpty*/
      ctx[3] ? "invalid-label" : "") + " svelte-15wea5d");
      attr(input, "pattern", input_pattern_value = "[^:\\s]");
      attr(input, "placeholder", input_placeholder_value = l.SETTINGS_LABELS_STYLES_NAME_PLACE_HOLDER);
      set_style(input, "width", "75px");
      attr(input, "type", "text");
      input.value = input_value_value = /*label*/
      ctx[0].label;
      attr(div, "class", div_class_value = null_to_empty("main-styles") + " svelte-15wea5d");
    },
    m(target, anchor) {
      insert(target, div, anchor);
      mount_component(multioptionstogglebutton, div, null);
      append(div, t0);
      append(div, input);
      append(div, t1);
      mount_component(additionalstyles, div, null);
      current = true;
      if (!mounted) {
        dispose = [
          listen(
            input,
            "change",
            /*onLabelChange*/
            ctx[6]
          ),
          listen(
            input,
            "input",
            /*onLabelInput*/
            ctx[5]
          )
        ];
        mounted = true;
      }
    },
    p(ctx2, [dirty]) {
      const multioptionstogglebutton_changes = {};
      if (dirty & /*label*/
      1)
        multioptionstogglebutton_changes.props = {
          name: "Scope",
          options: (
            /*scopes*/
            ctx2[9]
          ),
          value: (
            /*label*/
            ctx2[0].style.scope
          ),
          onChange: (
            /*onScopeChange*/
            ctx2[8]
          )
        };
      multioptionstogglebutton.$set(multioptionstogglebutton_changes);
      if (!current || dirty & /*valid, isEmpty*/
      12 && input_class_value !== (input_class_value = null_to_empty(!/*valid*/
      ctx2[2] && !/*isEmpty*/
      ctx2[3] ? "invalid-label" : "") + " svelte-15wea5d")) {
        attr(input, "class", input_class_value);
      }
      if (!current || dirty & /*label*/
      1 && input_value_value !== (input_value_value = /*label*/
      ctx2[0].label) && input.value !== input_value_value) {
        input.value = input_value_value;
      }
      const additionalstyles_changes = {};
      if (dirty & /*label*/
      1)
        additionalstyles_changes.label = /*label*/
        ctx2[0];
      if (dirty & /*plugin*/
      2)
        additionalstyles_changes.plugin = /*plugin*/
        ctx2[1];
      if (dirty & /*showAdditionalSettings*/
      16)
        additionalstyles_changes.showAdditionalSettings = /*showAdditionalSettings*/
        ctx2[4];
      additionalstyles.$set(additionalstyles_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(multioptionstogglebutton.$$.fragment, local);
      transition_in(additionalstyles.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(multioptionstogglebutton.$$.fragment, local);
      transition_out(additionalstyles.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      if (detaching) {
        detach(div);
      }
      destroy_component(multioptionstogglebutton);
      destroy_component(additionalstyles);
      mounted = false;
      run_all(dispose);
    }
  };
}
function instance32($$self, $$props, $$invalidate) {
  let { label } = $$props;
  let { plugin } = $$props;
  let notice = null;
  let valid = true;
  let previousMessage = "";
  let previousMessageTs = 0;
  let isEmpty = true;
  const onLabelInput = (e) => {
    const value = e.target.value;
    $$invalidate(3, isEmpty = value.length === 0);
    $$invalidate(2, valid = isValidLabel(value));
    if (!valid) {
      let message = "";
      if (value === "/")
        message = "A label should not be equal to '/'";
      else if (value.contains(":"))
        message = "A label should not contain ':'";
      else if (value.contains(" "))
        message = "A label should not contain spaces";
      if (message && (message !== previousMessage || Date.now() - previousMessageTs > 1e4)) {
        if (notice)
          notice.hide();
        previousMessage = message;
        previousMessageTs = Date.now();
        notice = new import_obsidian3.Notice(message, 1e4);
      }
    } else {
      if (notice)
        notice.hide();
    }
  };
  const onLabelChange = (e) => {
    const value = e.target.value;
    $$invalidate(2, valid = isValidLabel(value));
    if (valid) {
      plugin.settings.dispatch({
        payload: { pattern: value, id: label.id },
        type: "SET_PATTERN"
      });
    }
  };
  let showAdditionalSettings = false;
  const onToggleAdditionalSettings = () => {
    $$invalidate(4, showAdditionalSettings = !showAdditionalSettings);
  };
  const cleanupEmptyLabels = () => {
    const labels = plugin.settings.getValue().decoration.styles.labels;
    const stylesWithEmptyLabels = Object.values(labels).flat().filter((v) => !v.label && !v.style.scope).sort((a, b) => Number(a.id) - Number(b.id));
    if (stylesWithEmptyLabels.length > 1) {
      stylesWithEmptyLabels.splice(stylesWithEmptyLabels.length - 1);
      stylesWithEmptyLabels.forEach((v) => {
        plugin.settings.dispatch({
          type: "DELETE_GROUP",
          payload: { id: v.id }
        });
      });
    }
  };
  onDestroy(cleanupEmptyLabels);
  const onScopeChange = (value) => plugin.settings.dispatch({
    type: "SET_LABEL_SCOPE",
    payload: { id: label.id, scope: value }
  });
  const scopes = [
    { iconName: "comment", value: "comments" },
    {
      iconName: "highlight",
      value: "highlights"
    }
  ];
  $$self.$$set = ($$props2) => {
    if ("label" in $$props2)
      $$invalidate(0, label = $$props2.label);
    if ("plugin" in $$props2)
      $$invalidate(1, plugin = $$props2.plugin);
  };
  return [
    label,
    plugin,
    valid,
    isEmpty,
    showAdditionalSettings,
    onLabelInput,
    onLabelChange,
    onToggleAdditionalSettings,
    onScopeChange,
    scopes
  ];
}
var Styles = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance32, create_fragment32, safe_not_equal, { label: 0, plugin: 1 }, add_css12);
  }
};
var styles_default = Styles;

// src/sidebar-outline/components/components/controls-bar/components/styles/new-style.svelte
function create_default_slot21(ctx) {
  let plus;
  let current;
  plus = new Plus$1({ props: { class: "svg-icon" } });
  return {
    c() {
      create_component(plus.$$.fragment);
    },
    m(target, anchor) {
      mount_component(plus, target, anchor);
      current = true;
    },
    p: noop,
    i(local) {
      if (current)
        return;
      transition_in(plus.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(plus.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(plus, detaching);
    }
  };
}
function create_fragment33(ctx) {
  let div;
  let squarebutton;
  let current;
  squarebutton = new square_button_default({
    props: {
      label: l.SETTINGS_LABELS_STYLES_NEW,
      onClick: (
        /*newStyle*/
        ctx[0]
      ),
      $$slots: { default: [create_default_slot21] },
      $$scope: { ctx }
    }
  });
  return {
    c() {
      div = element("div");
      create_component(squarebutton.$$.fragment);
      set_style(div, "display", "flex");
      set_style(div, "align-items", "center");
      set_style(div, "justify-content", "end");
    },
    m(target, anchor) {
      insert(target, div, anchor);
      mount_component(squarebutton, div, null);
      current = true;
    },
    p(ctx2, [dirty]) {
      const squarebutton_changes = {};
      if (dirty & /*$$scope*/
      4) {
        squarebutton_changes.$$scope = { dirty, ctx: ctx2 };
      }
      squarebutton.$set(squarebutton_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(squarebutton.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(squarebutton.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      if (detaching) {
        detach(div);
      }
      destroy_component(squarebutton);
    }
  };
}
function instance33($$self, $$props, $$invalidate) {
  let { plugin } = $$props;
  const newStyle = () => {
    plugin.settings.dispatch({
      type: "NEW_GROUP",
      payload: { pattern: "" }
    });
  };
  $$self.$$set = ($$props2) => {
    if ("plugin" in $$props2)
      $$invalidate(1, plugin = $$props2.plugin);
  };
  return [newStyle, plugin];
}
var New_style = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance33, create_fragment33, safe_not_equal, { plugin: 1 });
  }
};
var new_style_default = New_style;

// src/sidebar-outline/components/components/controls-bar/components/styles/styles-list.svelte
function add_css13(target) {
  append_styles(target, "svelte-68omp7", ".styles-list.svelte-68omp7{box-sizing:border-box;display:flex;flex-direction:column;gap:5px;width:100%;align-items:center;padding:10px 0;overflow-y:auto}");
}
function get_each_context4(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[4] = list[i];
  return child_ctx;
}
function create_each_block4(key_1, ctx) {
  let first;
  let styles;
  let current;
  styles = new styles_default({
    props: {
      plugin: (
        /*plugin*/
        ctx[0]
      ),
      label: (
        /*label*/
        ctx[4]
      )
    }
  });
  return {
    key: key_1,
    first: null,
    c() {
      first = empty();
      create_component(styles.$$.fragment);
      this.first = first;
    },
    m(target, anchor) {
      insert(target, first, anchor);
      mount_component(styles, target, anchor);
      current = true;
    },
    p(new_ctx, dirty) {
      ctx = new_ctx;
      const styles_changes = {};
      if (dirty & /*plugin*/
      1)
        styles_changes.plugin = /*plugin*/
        ctx[0];
      if (dirty & /*labels*/
      2)
        styles_changes.label = /*label*/
        ctx[4];
      styles.$set(styles_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(styles.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(styles.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      if (detaching) {
        detach(first);
      }
      destroy_component(styles, detaching);
    }
  };
}
function create_fragment34(ctx) {
  let div;
  let newstyle;
  let t;
  let each_blocks = [];
  let each_1_lookup = /* @__PURE__ */ new Map();
  let current;
  newstyle = new new_style_default({ props: { plugin: (
    /*plugin*/
    ctx[0]
  ) } });
  let each_value = ensure_array_like(
    /*labels*/
    ctx[1]
  );
  const get_key = (ctx2) => (
    /*label*/
    ctx2[4].id
  );
  for (let i = 0; i < each_value.length; i += 1) {
    let child_ctx = get_each_context4(ctx, each_value, i);
    let key = get_key(child_ctx);
    each_1_lookup.set(key, each_blocks[i] = create_each_block4(key, child_ctx));
  }
  return {
    c() {
      div = element("div");
      create_component(newstyle.$$.fragment);
      t = space();
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      attr(div, "class", "styles-list svelte-68omp7");
    },
    m(target, anchor) {
      insert(target, div, anchor);
      mount_component(newstyle, div, null);
      append(div, t);
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(div, null);
        }
      }
      current = true;
    },
    p(ctx2, [dirty]) {
      const newstyle_changes = {};
      if (dirty & /*plugin*/
      1)
        newstyle_changes.plugin = /*plugin*/
        ctx2[0];
      newstyle.$set(newstyle_changes);
      if (dirty & /*plugin, labels*/
      3) {
        each_value = ensure_array_like(
          /*labels*/
          ctx2[1]
        );
        group_outros();
        each_blocks = update_keyed_each(each_blocks, dirty, get_key, 1, ctx2, each_value, each_1_lookup, div, outro_and_destroy_block, create_each_block4, null, get_each_context4);
        check_outros();
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(newstyle.$$.fragment, local);
      for (let i = 0; i < each_value.length; i += 1) {
        transition_in(each_blocks[i]);
      }
      current = true;
    },
    o(local) {
      transition_out(newstyle.$$.fragment, local);
      for (let i = 0; i < each_blocks.length; i += 1) {
        transition_out(each_blocks[i]);
      }
      current = false;
    },
    d(detaching) {
      if (detaching) {
        detach(div);
      }
      destroy_component(newstyle);
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].d();
      }
    }
  };
}
function instance34($$self, $$props, $$invalidate) {
  let { plugin } = $$props;
  const getAndSortLabels = (settings) => Object.values(settings.decoration.styles.labels).sort((a, b) => a.label === b.label ? b.id.localeCompare(a.id) : a.label.localeCompare(b.label));
  let labels = getAndSortLabels(plugin.settings.getValue());
  const unsub = plugin.settings.subscribe((v) => {
    $$invalidate(1, labels = getAndSortLabels(v));
  });
  onDestroy(unsub);
  $$self.$$set = ($$props2) => {
    if ("plugin" in $$props2)
      $$invalidate(0, plugin = $$props2.plugin);
  };
  return [plugin, labels];
}
var Styles_list = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance34, create_fragment34, safe_not_equal, { plugin: 0 }, add_css13);
  }
};
var styles_list_default = Styles_list;

// src/sidebar-outline/components/sidebar-outline.svelte
function add_css14(target) {
  append_styles(target, "svelte-18tbhci", ".outline.svelte-18tbhci{height:100%;width:100%;box-sizing:border-box;display:flex;gap:8px;flex-direction:column;align-items:start;justify-content:start}");
}
function create_else_block6(ctx) {
  let controlsbar;
  let t;
  let show_if;
  let current_block_type_index;
  let if_block;
  let if_block_anchor;
  let current;
  controlsbar = new controls_bar_default({ props: { plugin: (
    /*plugin*/
    ctx[0]
  ) } });
  const if_block_creators = [create_if_block_13, create_if_block_22, create_else_block_1];
  const if_blocks = [];
  function select_block_type_1(ctx2, dirty) {
    if (dirty & /*$filteredBySearch, $searchTerm, $filteredHiddenLabels, $filteredHiddenCategories*/
    120)
      show_if = null;
    if (
      /*$controls*/
      ctx2[2].showStylesSettings
    )
      return 0;
    if (show_if == null)
      show_if = !!(Object.values(
        /*$filteredBySearch*/
        ctx2[3].labels
      ).flat().length || /*$searchTerm*/
      ctx2[4].length || /*$filteredHiddenLabels*/
      ctx2[5].size || /*$filteredHiddenCategories*/
      ctx2[6].size);
    if (show_if)
      return 1;
    return 2;
  }
  current_block_type_index = select_block_type_1(ctx, -1);
  if_block = if_blocks[current_block_type_index] = if_block_creators[current_block_type_index](ctx);
  return {
    c() {
      create_component(controlsbar.$$.fragment);
      t = space();
      if_block.c();
      if_block_anchor = empty();
    },
    m(target, anchor) {
      mount_component(controlsbar, target, anchor);
      insert(target, t, anchor);
      if_blocks[current_block_type_index].m(target, anchor);
      insert(target, if_block_anchor, anchor);
      current = true;
    },
    p(ctx2, dirty) {
      const controlsbar_changes = {};
      if (dirty & /*plugin*/
      1)
        controlsbar_changes.plugin = /*plugin*/
        ctx2[0];
      controlsbar.$set(controlsbar_changes);
      let previous_block_index = current_block_type_index;
      current_block_type_index = select_block_type_1(ctx2, dirty);
      if (current_block_type_index === previous_block_index) {
        if_blocks[current_block_type_index].p(ctx2, dirty);
      } else {
        group_outros();
        transition_out(if_blocks[previous_block_index], 1, 1, () => {
          if_blocks[previous_block_index] = null;
        });
        check_outros();
        if_block = if_blocks[current_block_type_index];
        if (!if_block) {
          if_block = if_blocks[current_block_type_index] = if_block_creators[current_block_type_index](ctx2);
          if_block.c();
        } else {
          if_block.p(ctx2, dirty);
        }
        transition_in(if_block, 1);
        if_block.m(if_block_anchor.parentNode, if_block_anchor);
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(controlsbar.$$.fragment, local);
      transition_in(if_block);
      current = true;
    },
    o(local) {
      transition_out(controlsbar.$$.fragment, local);
      transition_out(if_block);
      current = false;
    },
    d(detaching) {
      if (detaching) {
        detach(t);
        detach(if_block_anchor);
      }
      destroy_component(controlsbar, detaching);
      if_blocks[current_block_type_index].d(detaching);
    }
  };
}
function create_if_block9(ctx) {
  let pluginidle;
  let current;
  pluginidle = new plugin_idle_default({ props: { plugin: (
    /*plugin*/
    ctx[0]
  ) } });
  return {
    c() {
      create_component(pluginidle.$$.fragment);
    },
    m(target, anchor) {
      mount_component(pluginidle, target, anchor);
      current = true;
    },
    p(ctx2, dirty) {
      const pluginidle_changes = {};
      if (dirty & /*plugin*/
      1)
        pluginidle_changes.plugin = /*plugin*/
        ctx2[0];
      pluginidle.$set(pluginidle_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(pluginidle.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(pluginidle.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(pluginidle, detaching);
    }
  };
}
function create_else_block_1(ctx) {
  let noannotations;
  let current;
  noannotations = new no_annotations_default({});
  return {
    c() {
      create_component(noannotations.$$.fragment);
    },
    m(target, anchor) {
      mount_component(noannotations, target, anchor);
      current = true;
    },
    p: noop,
    i(local) {
      if (current)
        return;
      transition_in(noannotations.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(noannotations.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(noannotations, detaching);
    }
  };
}
function create_if_block_22(ctx) {
  let flatoutline;
  let current;
  flatoutline = new annotations_list_default({ props: { plugin: (
    /*plugin*/
    ctx[0]
  ) } });
  return {
    c() {
      create_component(flatoutline.$$.fragment);
    },
    m(target, anchor) {
      mount_component(flatoutline, target, anchor);
      current = true;
    },
    p(ctx2, dirty) {
      const flatoutline_changes = {};
      if (dirty & /*plugin*/
      1)
        flatoutline_changes.plugin = /*plugin*/
        ctx2[0];
      flatoutline.$set(flatoutline_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(flatoutline.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(flatoutline.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(flatoutline, detaching);
    }
  };
}
function create_if_block_13(ctx) {
  let styleslist;
  let current;
  styleslist = new styles_list_default({ props: { plugin: (
    /*plugin*/
    ctx[0]
  ) } });
  return {
    c() {
      create_component(styleslist.$$.fragment);
    },
    m(target, anchor) {
      mount_component(styleslist, target, anchor);
      current = true;
    },
    p(ctx2, dirty) {
      const styleslist_changes = {};
      if (dirty & /*plugin*/
      1)
        styleslist_changes.plugin = /*plugin*/
        ctx2[0];
      styleslist.$set(styleslist_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(styleslist.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(styleslist.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      destroy_component(styleslist, detaching);
    }
  };
}
function create_fragment35(ctx) {
  let div;
  let current_block_type_index;
  let if_block;
  let current;
  const if_block_creators = [create_if_block9, create_else_block6];
  const if_blocks = [];
  function select_block_type(ctx2, dirty) {
    if (
      /*$pluginIdle*/
      ctx2[1]
    )
      return 0;
    return 1;
  }
  current_block_type_index = select_block_type(ctx, -1);
  if_block = if_blocks[current_block_type_index] = if_block_creators[current_block_type_index](ctx);
  return {
    c() {
      div = element("div");
      if_block.c();
      attr(div, "class", "outline svelte-18tbhci");
    },
    m(target, anchor) {
      insert(target, div, anchor);
      if_blocks[current_block_type_index].m(div, null);
      current = true;
    },
    p(ctx2, [dirty]) {
      let previous_block_index = current_block_type_index;
      current_block_type_index = select_block_type(ctx2, dirty);
      if (current_block_type_index === previous_block_index) {
        if_blocks[current_block_type_index].p(ctx2, dirty);
      } else {
        group_outros();
        transition_out(if_blocks[previous_block_index], 1, 1, () => {
          if_blocks[previous_block_index] = null;
        });
        check_outros();
        if_block = if_blocks[current_block_type_index];
        if (!if_block) {
          if_block = if_blocks[current_block_type_index] = if_block_creators[current_block_type_index](ctx2);
          if_block.c();
        } else {
          if_block.p(ctx2, dirty);
        }
        transition_in(if_block, 1);
        if_block.m(div, null);
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(if_block);
      current = true;
    },
    o(local) {
      transition_out(if_block);
      current = false;
    },
    d(detaching) {
      if (detaching) {
        detach(div);
      }
      if_blocks[current_block_type_index].d();
    }
  };
}
function instance35($$self, $$props, $$invalidate) {
  let $pluginIdle;
  let $controls;
  let $filteredBySearch;
  let $searchTerm;
  let $filteredHiddenLabels;
  let $filteredHiddenCategories;
  component_subscribe($$self, pluginIdle, ($$value) => $$invalidate(1, $pluginIdle = $$value));
  component_subscribe($$self, controls, ($$value) => $$invalidate(2, $controls = $$value));
  component_subscribe($$self, filteredBySearch, ($$value) => $$invalidate(3, $filteredBySearch = $$value));
  component_subscribe($$self, searchTerm, ($$value) => $$invalidate(4, $searchTerm = $$value));
  component_subscribe($$self, filteredHiddenLabels, ($$value) => $$invalidate(5, $filteredHiddenLabels = $$value));
  component_subscribe($$self, filteredHiddenCategories, ($$value) => $$invalidate(6, $filteredHiddenCategories = $$value));
  let { plugin } = $$props;
  $$self.$$set = ($$props2) => {
    if ("plugin" in $$props2)
      $$invalidate(0, plugin = $$props2.plugin);
  };
  return [
    plugin,
    $pluginIdle,
    $controls,
    $filteredBySearch,
    $searchTerm,
    $filteredHiddenLabels,
    $filteredHiddenCategories
  ];
}
var Sidebar_outline = class extends SvelteComponent {
  constructor(options2) {
    super();
    init(this, options2, instance35, create_fragment35, safe_not_equal, { plugin: 0 }, add_css14);
  }
};
var sidebar_outline_default = Sidebar_outline;

// src/sidebar-outline/sidebar-outline-view.ts
var SIDEBAR_OUTLINE_VIEW_TYPE = "annotations-outline";
var SidebarOutlineView = class extends import_obsidian4.ItemView {
  constructor(leaf, plugin) {
    super(leaf);
    this.plugin = plugin;
    this.icon = "message-square";
  }
  getViewType() {
    return SIDEBAR_OUTLINE_VIEW_TYPE;
  }
  getDisplayText() {
    return l.PLUGIN_NAME;
  }
  async onOpen() {
    this.contentEl.addClass("enhanced-annotations__sidebar-outline");
    this.component = new sidebar_outline_default({
      target: this.contentEl,
      props: {
        plugin: this.plugin
      }
    });
  }
  async onClose() {
    this.component?.$destroy();
  }
};

// src/settings/settings-tab/settings-tab.ts
var import_obsidian15 = require("obsidian");

// src/settings/settings-tab/components/auto-suggest-settings.ts
var import_obsidian5 = require("obsidian");

// src/status-bar/helpers/class-names.ts
var displayNone = "enhanced-annotations__display-none";
var settingsHeader = "enhanced-annotations__settings-header";

// src/settings/settings-tab/components/auto-suggest-settings.ts
var AutoSuggestSettings = ({ plugin, containerEl }) => {
  new import_obsidian5.Setting(containerEl).setName(l.SETTINGS_AUTO_SUGGEST_TITLE).setHeading().settingEl.addClass(settingsHeader);
  const settings = plugin.settings.getValue();
  new import_obsidian5.Setting(containerEl).setName(l.SETTINGS_AUTO_SUGGEST_ENABLE).setDesc(l.SETTINGS_AUTO_SUGGEST_ENABLE_DESC).addToggle((toggle) => {
    toggle.onChange(
      (value) => plugin.settings.dispatch({
        payload: { enable: value },
        type: "ENABLE_AUTO_SUGGEST"
      })
    ).setValue(settings.editorSuggest.enableAutoSuggest);
  });
  new import_obsidian5.Setting(containerEl).setName(l.SETTINGS_AUTO_SUGGEST_TRIGGER_PHRASE).setDesc(l.SETTINGS_AUTO_SUGGEST_TRIGGER_PHRASE_DESC).addText((component) => {
    component.onChange(
      (value) => plugin.settings.dispatch({
        payload: { trigger: value },
        type: "SET_AUTO_SUGGEST_TRIGGER"
      })
    ).setValue(settings.editorSuggest.triggerPhrase).setPlaceholder("//");
  });
  new import_obsidian5.Setting(containerEl).setName(l.SETTINGS_AUTO_SUGGEST_COMMENT_TYPE).setDesc(l.SETTINGS_AUTO_SUGGEST_COMMENT_TYPE_DESC).addDropdown((component) => {
    component.addOption("html", "HTML");
    component.addOption("markdown", "Obsidian");
    component.onChange(
      (value) => plugin.settings.dispatch({
        payload: { type: value },
        type: "SET_AUTO_SUGGEST_COMMENT_TYPE"
      })
    ).setValue(settings.editorSuggest.commentFormat);
  });
};

// src/settings/settings-tab/components/tts-settings.ts
var import_obsidian8 = require("obsidian");

// src/note-creation/create-note-file.ts
var import_obsidian7 = require("obsidian");

// src/note-creation/helpers/generate-block-id.ts
var generateBlockId = () => Math.random().toString(36).substring(2, 8);

// src/note-creation/helpers/insert-block-id.ts
var findNextEmptyLine = ({
  cursor,
  editor
}) => {
  for (let i = cursor.line + 1; i < editor.lineCount() - 1; i++) {
    const line = editor.getLine(i).trim();
    if (!line || parseAnnotations(line).length) {
      return i - 1;
    }
  }
  return editor.lineCount() - 1;
};
var getExistingBlockId = (line) => {
  line = line.trim();
  return /\s+(\^[a-zA-Z0-9]{4,})$/.exec(line)?.[1];
};
var insertBlockId = ({
  cursor,
  editor,
  annotation
}) => {
  const idLineNumber = annotation.isHighlight ? cursor.line : findNextEmptyLine({ cursor, editor });
  if (annotation.isHighlight || idLineNumber > cursor.line) {
    const existingLine = editor.getLine(idLineNumber);
    const existingBlockId = getExistingBlockId(existingLine);
    if (existingBlockId) {
      return {
        blockId: existingBlockId,
        cursor: {
          line: idLineNumber,
          ch: existingLine.length
        }
      };
    } else {
      const newBlockId = `^${generateBlockId()}`;
      const newLine = `${existingLine.trim()} ${newBlockId}`;
      editor.setLine(idLineNumber, newLine);
      return {
        blockId: newBlockId,
        cursor: {
          line: idLineNumber,
          ch: newLine.length
        }
      };
    }
  }
};

// src/note-creation/helpers/sanitize-file-name.ts
function sanitizeFileName(path, replacement = "-") {
  const illegalCharacters = /[*"\\/<>:|?]/g;
  const unsafeCharactersForObsidianLinks = /[#^[\]|]/g;
  const dotAtTheStart = /^\./g;
  const controlRe = /[\x00-\x1f\x80-\x9f]/g;
  const reservedRe = /^\.+$/;
  const windowsReservedRe = /^(con|prn|aux|nul|com[0-9]|lpt[0-9])(\..*)?$/i;
  const windowsTrailingRe = /[. ]+$/;
  let sanitized = path.replace(/"/g, "'").replace(illegalCharacters, replacement).replace(unsafeCharactersForObsidianLinks, replacement).replace(dotAtTheStart, replacement).replace(controlRe, replacement).replace(reservedRe, replacement).replace(windowsReservedRe, replacement).replace(windowsTrailingRe, replacement);
  if (replacement)
    sanitized = sanitized.replace(new RegExp(`${replacement}+`, "g"), replacement).replace(
      new RegExp(`^${replacement}(.)|(.)${replacement}$`, "g"),
      "$1$2"
    );
  return sanitized;
}

// src/helpers/string-utils.ts
var truncateString = (str, maxLength) => {
  if (str.length > maxLength) {
    return str.slice(0, maxLength - 3).trim() + "...";
  }
  return str;
};

// src/note-creation/helpers/get-file-name.ts
var getFileName = (annotation, settings, currentFolder, currentFileName) => {
  const sanitizedAnnotation = sanitizeFileName(annotation.text);
  const sanitizedLabel = sanitizeFileName(annotation.label);
  const folderParts = [];
  const nameParts = [];
  if (settings.defaultFolderMode === "customFolder") {
    folderParts.push(settings.defaultFolder);
  } else if (settings.defaultFolderMode === "current folder") {
    folderParts.push(currentFolder);
  } else if (settings.defaultFolderMode === "current folder/notes") {
    folderParts.push(currentFolder, "notes");
  } else if (settings.defaultFolderMode === "current folder/notes/<file-name>") {
    folderParts.push(currentFolder, "notes", currentFileName);
  }
  if (settings.notesNamingMode === "annotation-label - annotation-text") {
    nameParts.push(`${sanitizedLabel} - ${sanitizedAnnotation}`);
  } else if (settings.notesNamingMode === "annotation-label/annotation-text") {
    folderParts.push(sanitizedLabel);
    nameParts.push(`${sanitizedAnnotation}`);
  } else {
    nameParts.push(sanitizedAnnotation);
  }
  const folderPath = folderParts.filter((p) => p && p !== "/").join("/");
  const fileBasename = truncateString(
    nameParts.join("/"),
    settings.truncateFileName ? 100 : 250
  );
  return {
    filePath: `${folderPath}/${fileBasename}.md`,
    folderPath,
    fileBasename
  };
};

// src/note-creation/helpers/write-file.ts
var import_obsidian6 = require("obsidian");
var writeFile = async ({
  fileContent,
  filePath,
  folderPath,
  openNoteAfterCreation,
  plugin
}) => {
  try {
    const maybeFolder = plugin.app.vault.getAbstractFileByPath(folderPath);
    if (!maybeFolder)
      await plugin.app.vault.createFolder(folderPath);
    const file = await plugin.app.vault.create(filePath, fileContent);
    if (openNoteAfterCreation) {
      const leaf = plugin.app.workspace.getLeaf(true);
      await leaf.openFile(file);
    }
  } catch (e) {
    new import_obsidian6.Notice(l.COMMANDS_COULD_NOT_CREATE_FILE + e.message);
    throw e;
  }
};

// src/note-creation/helpers/insert-link-to-note.ts
var updateLine = (line, fileBasename, blockId) => {
  line = line.trim();
  const blockIdIndex = line.lastIndexOf(blockId);
  const lineEndsWithBlockId = blockIdIndex + blockId.length === line.length;
  if (blockIdIndex > 0 && lineEndsWithBlockId) {
    return line.substring(0, blockIdIndex) + `[[${fileBasename}|\u2197]] ${blockId}`;
  }
  return line;
};
var insertLinkToNote = ({
  fileBasename,
  blockId,
  editor
}) => {
  let lineText = editor.getLine(blockId.cursor.line);
  lineText = updateLine(lineText, fileBasename, blockId.blockId);
  editor.setLine(blockId.cursor.line, lineText);
};

// src/note-creation/create-note-file.ts
var noteTemplate = `{{block_link}}`;
var noteVariables = [
  "block_link",
  "annotation_text",
  "annotation_label",
  "date",
  "time",
  "time_tag"
];
var createNoteFile = async ({
  annotation,
  currentFileName,
  currentFileFolder,
  cursor,
  editor,
  plugin
}) => {
  const blockId = insertBlockId({ cursor, editor, annotation });
  if (!blockId) {
    new import_obsidian7.Notice("Could not create a block ID");
    return;
  }
  const settings = plugin.settings.getValue();
  const variables = {
    block_link: `![[${currentFileName}#${blockId.blockId}]]`,
    annotation_label: annotation.label,
    annotation_text: annotation.text,
    time_tag: timeTag(),
    date: formattedDate(),
    time: formattedTime()
  };
  const fileContent = applyVariablesToTemplate({
    template: settings.notes.template,
    variables
  });
  const { filePath, folderPath, fileBasename } = getFileName(
    annotation,
    settings.notes,
    currentFileFolder,
    currentFileName
  );
  await writeFile({
    fileContent,
    filePath,
    folderPath,
    plugin,
    openNoteAfterCreation: settings.notes.openNoteAfterCreation
  });
  if (settings.notes.insertLinkToNote)
    insertLinkToNote({ blockId, editor, fileBasename });
};

// src/settings/default-settings.ts
var DEFAULT_SETTINGS = () => ({
  editorSuggest: {
    enableAutoSuggest: true,
    triggerPhrase: "//",
    commentFormat: "html"
  },
  decoration: {
    autoRegisterLabels: true,
    styles: {
      labels: {},
      tag: {
        style: { fontSize: 10, opacity: 40 },
        enableStyle: true
      }
    },
    defaultPalette: "bright"
  },
  outline: {
    fontSize: 12,
    showLabelsFilter: false,
    showSearchInput: false,
    hiddenLabels: [],
    hiddenTypes: []
  },
  tts: {
    rate: 1.1,
    pitch: 1,
    volume: 1,
    voice: window.speechSynthesis.getVoices().find((v) => v.default)?.name,
    focusAnnotationInEditor: true
  },
  notes: {
    defaultFolder: "notes",
    notesNamingMode: "annotation-label/annotation-text",
    openNoteAfterCreation: false,
    insertLinkToNote: true,
    defaultFolderMode: "current folder/notes/<file-name>",
    template: noteTemplate,
    truncateFileName: false
  },
  idling: {
    daysUnused: []
  },
  clipboard: {
    templates: copiedAnnotationsTemplates
  }
});

// src/settings/settings-tab/components/tts-settings.ts
var TTSSettings = ({ plugin, containerEl }) => {
  const render = () => {
    containerEl.empty();
    TTSSettings({
      plugin,
      containerEl
    });
  };
  new import_obsidian8.Setting(containerEl).setName(l.SETTINGS_TTS_TITLE).setHeading().settingEl.addClass(settingsHeader);
  new import_obsidian8.Setting(containerEl).addDropdown((component) => {
    const voices = window.speechSynthesis.getVoices().map((v) => [v.name, v.name]);
    component.addOptions(Object.fromEntries(voices));
    component.setValue(
      plugin.settings.getValue().tts.voice || voices[0][0]
    );
    component.onChange((v) => {
      plugin.settings.dispatch({
        type: "SET_TTS_VOICE",
        payload: {
          voice: v
        }
      });
    });
  }).setName(l.SETTINGS_TTS_VOICE);
  new import_obsidian8.Setting(containerEl).setName(l.SETTINGS_TTS_VOLUME).addSlider(
    (component) => component.setValue(plugin.settings.getValue().tts.volume * 100).setDynamicTooltip().setLimits(0, 100, 1).onChange((value) => {
      plugin.settings.dispatch({
        type: "SET_TTS_VOLUME",
        payload: { volume: value / 100 }
      });
    })
  ).addExtraButton((button) => {
    button.setIcon("reset").setTooltip(l.SETTINGS_TTS_RESTORE_DEFAULTS).onClick(() => {
      plugin.settings.dispatch({
        type: "SET_TTS_VOLUME",
        payload: { volume: DEFAULT_SETTINGS().tts.volume }
      });
      render();
    });
  });
  new import_obsidian8.Setting(containerEl).setName(l.SETTINGS_TTS_RATE).addSlider((slider) => {
    slider.setValue(plugin.settings.getValue().tts.rate).setDynamicTooltip().setLimits(0.1, 10, 0.1).onChange((value) => {
      plugin.settings.dispatch({
        type: "SET_TTS_RATE",
        payload: { rate: value }
      });
    });
  }).addExtraButton((button) => {
    button.setIcon("reset").setTooltip(l.SETTINGS_TTS_RESTORE_DEFAULTS).onClick(() => {
      plugin.settings.dispatch({
        type: "SET_TTS_RATE",
        payload: { rate: DEFAULT_SETTINGS().tts.rate }
      });
      render();
    });
  });
  new import_obsidian8.Setting(containerEl).setName(l.SETTINGS_TTS_PITCH).addSlider((slider) => {
    slider.setValue(plugin.settings.getValue().tts.pitch).setDynamicTooltip().setLimits(0, 2, 0.1).onChange((value) => {
      plugin.settings.dispatch({
        type: "SET_TTS_PITCH",
        payload: { pitch: value }
      });
    });
  }).addExtraButton((button) => {
    button.setIcon("reset").setTooltip(l.SETTINGS_TTS_RESTORE_DEFAULTS).onClick(() => {
      plugin.settings.dispatch({
        type: "SET_TTS_PITCH",
        payload: { pitch: DEFAULT_SETTINGS().tts.pitch }
      });
      render();
    });
  });
  new import_obsidian8.Setting(containerEl).addToggle((c) => {
    c.setValue(plugin.settings.getValue().tts.focusAnnotationInEditor);
    c.onChange((v) => {
      plugin.settings.dispatch({
        type: "SET_TTS_FOCUS_COMMENT_IN_EDITOR",
        payload: { enable: v }
      });
    });
  }).setName(l.SETTINGS_TTS_FOCUS_ANNOTATION_IN_EDITOR).setDesc(l.SETTINGS_TTS_FOCUS_ANNOTATION_IN_EDITOR_DESC);
};

// src/settings/settings-tab/components/note-settings/note-settings.ts
var import_obsidian10 = require("obsidian");

// src/settings/settings-tab/components/note-settings/helpers/folder-suggestions.ts
var import_obsidian9 = require("obsidian");
var FolderSuggest = class extends import_obsidian9.AbstractInputSuggest {
  constructor(app, inputEl, onSelectCallback) {
    super(app, inputEl);
    this.inputEl = inputEl;
    this.onSelectCallback = onSelectCallback;
    this.content = this.loadContent();
  }
  loadContent() {
    const abstractFiles = this.app.vault.getAllLoadedFiles();
    const folders = /* @__PURE__ */ new Set();
    for (const folder of abstractFiles) {
      if (folder instanceof import_obsidian9.TFolder) {
        folders.add(folder.path);
      }
    }
    return folders;
  }
  getSuggestions(inputStr) {
    const lowerCaseInputStr = inputStr.toLocaleLowerCase();
    return [...this.content].filter(
      (content) => content.toLocaleLowerCase().contains(lowerCaseInputStr)
    );
  }
  renderSuggestion(content, el) {
    el.setText(content);
  }
  selectSuggestion(content) {
    this.onSelectCallback(content);
    this.inputEl.value = content;
    this.inputEl.blur();
    this.close();
  }
};

// src/settings/settings-tab/components/note-settings/note-settings.ts
var options = {
  vault: "vault folder",
  "current folder": "current folder",
  "current folder/notes": "current folder / notes",
  "current folder/notes/<file-name>": "current folder / notes / <file-name> ",
  customFolder: "folder specified below"
};
var NoteSettings = ({ containerEl, plugin }) => {
  const render = () => {
    containerEl.empty();
    NoteSettings({
      containerEl,
      plugin
    });
  };
  new import_obsidian10.Setting(containerEl).setName(l.SETTINGS_NOTE_CREATION_TITLE).setHeading();
  const settings = plugin.settings;
  const noteSettings = settings.getValue().notes;
  new import_obsidian10.Setting(containerEl).addDropdown((c) => {
    c.addOptions(options);
    c.onChange((v) => {
      settings.dispatch({
        type: "SET_NOTES_FOLDER_MODE",
        payload: { mode: v }
      });
      render();
    });
    c.setValue(noteSettings.defaultFolderMode);
  }).setName(l.SETTINGS_NOTE_CREATION_FOLDER_MODE);
  if (noteSettings.defaultFolderMode === "customFolder")
    new import_obsidian10.Setting(containerEl).setName(l.SETTINGS_NOTE_CREATION_FOLDER).addSearch((cb) => {
      const onSelectCallback = (e) => {
        if (e) {
          settings.dispatch({
            type: "SET_NOTES_FOLDER",
            payload: { folder: e }
          });
        }
      };
      new FolderSuggest(plugin.app, cb.inputEl, onSelectCallback);
      cb.setValue(noteSettings.defaultFolder).setPlaceholder(
        l.SETTINGS_NOTE_CREATION_FOLDER_PLACEHOLDER
      );
    });
  new import_obsidian10.Setting(containerEl).setName(l.SETTINGS_NOTE_CREATION_NAME).addDropdown((c) => {
    c.addOptions({
      "annotation-text": "annotation text",
      "annotation-label/annotation-text": "annotation label / annotation text",
      "annotation-label - annotation-text": "annotation label - annotation text"
    });
    c.setValue(noteSettings.notesNamingMode);
    c.onChange((v) => {
      settings.dispatch({
        type: "SET_NOTES_NAMING_MODE",
        payload: { folder: v }
      });
    });
  });
  new import_obsidian10.Setting(containerEl).setName(l.SETTINGS_NOTE_TRUNCATE_FILE_NAME).setDesc(l.SETTINGS_NOTE_TRUNCATE_FILE_NAME_DESC).addToggle((c) => {
    c.setValue(noteSettings.truncateFileName);
    c.onChange((v) => {
      settings.dispatch({
        type: "TOGGLE_TRUNCATE_FILE_NAME"
      });
    });
  });
  new import_obsidian10.Setting(containerEl).setName(l.SETTINGS_NOTE_CREATION_INSERT).setDesc(l.SETTINGS_NOTE_CREATION_INSERT_DESC).addToggle((c) => {
    c.setValue(noteSettings.insertLinkToNote);
    c.onChange((v) => {
      settings.dispatch({
        type: "SET_NOTES_INSERT_LINK_TO_NOTE",
        payload: { insert: v }
      });
    });
  });
  new import_obsidian10.Setting(containerEl).setName(l.SETTINGS_NOTE_CREATION_OPEN).setDesc(l.SETTINGS_NOTE_CREATION_OPEN_DESC).addToggle((c) => {
    c.setValue(noteSettings.openNoteAfterCreation);
    c.onChange((v) => {
      settings.dispatch({
        type: "SET_NOTES_OPEN_AFTER_CREATION",
        payload: { open: v }
      });
    });
  });
  new import_obsidian10.Setting(containerEl).setName(l.SETTINGS_NOTE_CREATION_TEMPLATE).setDesc(
    l.SETTINGS_TEMPLATE_desc + noteVariables.map((v) => `{{${v}}}`).join(", ")
  ).addTextArea((c) => {
    c.setPlaceholder("{{content}}");
    c.setValue(noteSettings.template);
    c.onChange((v) => {
      settings.dispatch({
        type: "SET_NOTES_TEMPLATE",
        payload: { template: v }
      });
    });
  }).addExtraButton((c) => {
    c.setIcon("reset");
    c.setTooltip("Reset");
    c.onClick(() => {
      settings.dispatch({
        type: "SET_NOTES_TEMPLATE",
        payload: { template: noteTemplate }
      });
      render();
    });
  });
};

// src/settings/settings-tab/components/label-settings/labels-settings.ts
var import_obsidian13 = require("obsidian");

// src/settings/settings-tab/components/label-settings/components/tag-settings.ts
var import_obsidian12 = require("obsidian");

// src/settings/settings-tab/components/label-settings/components/multi-option-extra-button.ts
var import_obsidian11 = require("obsidian");
var MultiOptionExtraButton = ({
  button,
  options: options2,
  value,
  onChange,
  name
}) => {
  const state = {
    selected: options2.find((o) => o.value === value)
  };
  const defaultOption = {
    ...options2[0],
    name
  };
  const renderButton = () => {
    const option = state.selected || defaultOption;
    if (option.iconType === "svg-html") {
      button.extraSettingsEl.empty();
      const parser = new DOMParser();
      const doc = parser.parseFromString(
        option.iconContent,
        "image/svg+xml"
      );
      button.extraSettingsEl.append(doc.documentElement);
    } else {
      button.setIcon(option.iconContent);
    }
    button.setTooltip(
      state.selected ? `${name}: ${option.name || option.value}` : name
    );
    button.extraSettingsEl.setCssStyles({
      borderBottom: "1px solid var(--tab-outline-color)"
    });
    const svg = button.extraSettingsEl.firstElementChild;
    if (svg) {
      if (state.selected) {
        svg.setCssStyles({
          color: "var(--color-accent)",
          opacity: "100%"
        });
      } else {
        svg.setCssStyles({
          color: "var(--icon-color)",
          opacity: "30%"
        });
      }
    }
  };
  renderButton();
  button.onClick(() => {
    let i = state.selected ? options2.indexOf(state.selected) + 1 : 0;
    if (i >= options2.length)
      i = -1;
    state.selected = options2[i];
    renderButton();
    const value2 = state.selected ? state.selected.value : void 0;
    onChange(value2);
    new import_obsidian11.Notice(`'${name}' set to '${value2 || "default"}'`);
  });
};

// src/settings/settings-tab/components/label-settings/components/tag-settings.ts
var TagSettings = ({ containerEl, tag, plugin, render }) => {
  const el = new import_obsidian12.Setting(containerEl);
  const style = tag.style;
  el.controlEl.empty();
  el.setName(l.SETTINGS_STYLES_WRAPPERS_STYLE).setDesc(
    l.SETTINGS_STYLES_WRAPPERS_STYLE_DESC
  );
  el.addExtraButton((button) => {
    MultiOptionExtraButton({
      name: "Font weight",
      options: [
        {
          value: "thin",
          iconContent: TextSVG("B", { fontWeight: 400 }),
          iconType: "svg-html"
        },
        {
          value: "bold",
          iconContent: TextSVG("B", { fontWeight: 600 }),
          iconType: "svg-html"
        }
      ],
      button,
      value: style.fontWeight,
      onChange: (value) => {
        plugin.settings.dispatch({
          payload: {
            weight: value
          },
          type: "SET_TAG_FONT_WEIGHT"
        });
      }
    });
  }).addExtraButton((button) => {
    MultiOptionExtraButton({
      name: "Font family",
      options: ["sans-serif", "serif", "monospace"].map((f) => ({
        value: f,
        iconContent: TextSVG("F", {
          fontFamily: f
        }),
        iconType: "svg-html"
      })),
      value: style.fontFamily,
      button,
      onChange: (value) => {
        plugin.settings.dispatch({
          payload: {
            family: value
          },
          type: "SET_TAG_FONT_FAMILY"
        });
      }
    });
  }).addExtraButton((button) => {
    MultiOptionExtraButton({
      button,
      onChange: (value) => plugin.settings.dispatch({
        type: "SET_TAG_FONT_SIZE",
        payload: { fontSize: value }
      }),
      options: [10, 12, 16, 20, 24, 32].map((n) => ({
        name: n + "px",
        value: n,
        iconContent: TextSVG(String(n)),
        iconType: "svg-html"
      })),
      value: style.fontSize,
      name: "Font size"
    });
  }).addExtraButton((button) => {
    MultiOptionExtraButton({
      button,
      onChange: (value) => plugin.settings.dispatch({
        type: "SET_TAG_OPACITY",
        payload: { opacity: value }
      }),
      options: [80, 60, 40, 20].map((n) => ({
        name: n + "%",
        value: n,
        iconContent: TextSVG(String(n)),
        iconType: "svg-html"
      })),
      value: style.opacity,
      name: "Text opacity"
    });
  });
  el.addToggle((toggle) => {
    toggle.setValue(tag.enableStyle).setTooltip(l.SETTINGS_LABELS_STYLES_ENABLE_STYLE).onChange((value) => {
      plugin.settings.dispatch({
        payload: { enable: value },
        type: "ENABLE_TAG_STYLES"
      });
    });
  });
};

// src/settings/settings-tab/components/label-settings/labels-settings.ts
var LabelsSettings = ({ plugin, containerEl }) => {
  new import_obsidian13.Setting(containerEl).setName(l.SETTINGS_LABELS_STYLES_TITLE).setHeading().settingEl.addClass(settingsHeader);
  const settings = plugin.settings.getValue();
  const render = () => {
    containerEl.empty();
    LabelsSettings({
      plugin,
      containerEl
    });
  };
  TagSettings({
    tag: plugin.settings.getValue().decoration.styles.tag,
    plugin,
    containerEl,
    render
  });
  new import_obsidian13.Setting(containerEl).addDropdown((component) => {
    const options2 = {
      bright: "Bright",
      dull: "Dull"
    };
    component.addOptions(options2);
    component.setValue(
      plugin.settings.getValue().decoration.defaultPalette
    );
    component.onChange((v) => {
      plugin.settings.dispatch({
        type: "SET_DEFAULT_PALETTE",
        payload: {
          palette: v
        }
      });
    });
  }).setName(l.SETTINGS_DEFAULT_PALETTE).setDesc(l.SETTINGS_DEFAULT_PALETTE_DESC);
  new import_obsidian13.Setting(containerEl).setName(l.SETTINGS_AUTO_SUGGEST_AUTO_REGISTER).setDesc(l.SETTINGS_AUTO_SUGGEST_AUTO_REGISTER_DESC).addToggle((component) => {
    component.onChange(
      (value) => plugin.settings.dispatch({
        payload: { enable: value },
        type: "ENABLE_AUTO_REGISTER_LABELS"
      })
    ).setValue(settings.decoration.autoRegisterLabels);
  });
};

// src/settings/settings-tab/components/clipboard-settings.ts
var import_obsidian14 = require("obsidian");
var ClipboardSettings = ({ plugin, containerEl }) => {
  const settings = plugin.settings;
  const render = () => {
    containerEl.empty();
    new import_obsidian14.Setting(containerEl).setName(l.SETTINGS_CLIPBOARD_TITLE).setHeading().settingEl.addClass(settingsHeader);
    for (const Key of ["Front", "Header", "Comment", "Highlight"]) {
      const key = Key.toLowerCase();
      new import_obsidian14.Setting(containerEl).setName(Key + " " + l.SETTINGS_CLIPBOARD_TEMPLATE).setDesc(
        l.SETTINGS_TEMPLATE_desc + copiedAnnotationsVariables[key].map((v) => `{{${v}}}`).join(", ")
      ).addTextArea((c) => {
        c.setPlaceholder(copiedAnnotationsTemplates[key]);
        c.setValue(settings.getValue().clipboard.templates[key]);
        c.onChange((v) => {
          settings.dispatch({
            type: `SET_CLIPBOARD_TEMPLATE`,
            payload: { template: v, name: key }
          });
        });
      }).addExtraButton((c) => {
        c.setIcon("reset");
        c.setTooltip("Reset");
        c.onClick(() => {
          settings.dispatch({
            type: "SET_CLIPBOARD_TEMPLATE",
            payload: {
              template: copiedAnnotationsTemplates[key],
              name: key
            }
          });
          render();
        });
      });
    }
  };
  render();
};

// src/settings/settings-tab/settings-tab.ts
var SettingsTab = class extends import_obsidian15.PluginSettingTab {
  constructor(app, plugin) {
    super(app, plugin);
    this.display = () => {
      const { containerEl } = this;
      containerEl.empty();
      NoteSettings({
        containerEl: containerEl.createEl("div"),
        plugin: this.plugin
      });
      ClipboardSettings({
        plugin: this.plugin,
        containerEl: containerEl.createEl("div")
      });
      AutoSuggestSettings({
        plugin: this.plugin,
        containerEl: containerEl.createEl("div")
      });
      LabelsSettings({
        plugin: this.plugin,
        containerEl: containerEl.createEl("div")
      });
      TTSSettings({
        plugin: this.plugin,
        containerEl: containerEl.createEl("div")
      });
    };
    this.plugin = plugin;
  }
};

// src/settings/settings-selectors.ts
var DAYS_UNUSED = 3;
var pluginIsIdle = (plugin) => plugin.settings.getValue().idling.daysUnused.length > DAYS_UNUSED;

// src/settings/settings-reducer.ts
var updateState2 = (store, action) => {
  const labels = store.decoration.styles.labels;
  const tag = store.decoration.styles.tag;
  if (action.type === "SET_PATTERN") {
    if (!action.payload.pattern || isValidLabel(action.payload.pattern))
      labels[action.payload.id].label = action.payload.pattern;
  } else if (action.type === "SET_COLOR") {
    labels[action.payload.id].style.color = action.payload.color;
  } else if (action.type === "DELETE_GROUP") {
    delete labels[action.payload.id];
  } else if (action.type === "NEW_GROUP") {
    if (!action.payload.pattern || isValidLabel(action.payload.pattern)) {
      const id = String(Date.now());
      labels[id] = {
        label: action.payload.pattern,
        id,
        enableStyle: true,
        style: {
          color: getDefaultColor(store),
          italic: true,
          fontWeight: "thin"
        }
      };
    }
  } else if (action.type === "ENABLE_AUTO_SUGGEST") {
    store.editorSuggest.enableAutoSuggest = action.payload.enable;
  } else if (action.type === "SET_AUTO_SUGGEST_TRIGGER") {
    if (action.payload.trigger)
      store.editorSuggest.triggerPhrase = action.payload.trigger;
  } else if (action.type === "ENABLE_AUTO_REGISTER_LABELS")
    store.decoration.autoRegisterLabels = action.payload.enable;
  else if (action.type === "ENABLE_LABEL_STYLES")
    labels[action.payload.id].enableStyle = action.payload.enable;
  else if (action.type === "SET_LABEL_UNDERLINE")
    labels[action.payload.id].style.underline = action.payload.underline;
  else if (action.type === "SET_LABEL_FONT_WEIGHT")
    labels[action.payload.id].style.fontWeight = action.payload.weight;
  else if (action.type === "SET_LABEL_SCOPE")
    labels[action.payload.id].style.scope = action.payload.scope;
  else if (action.type === "SET_LABEL_FONT_OPACITY")
    labels[action.payload.id].style.opacity = action.payload.opacity;
  else if (action.type === "SET_LABEL_FONT_FAMILY")
    labels[action.payload.id].style.fontFamily = action.payload.family;
  else if (action.type === "SET_LABEL_FONT_SIZE")
    labels[action.payload.id].style.fontSize = action.payload.fontSize;
  else if (action.type === "SET_LABEL_ITALIC")
    labels[action.payload.id].style.italic = action.payload.italic;
  else if (action.type === "SET_LABEL_CASE")
    labels[action.payload.id].style.case = action.payload.case;
  else if (action.type === "SET_TTS_PITCH")
    store.tts.pitch = action.payload.pitch;
  else if (action.type === "SET_TTS_RATE")
    store.tts.rate = action.payload.rate;
  else if (action.type === "SET_TTS_VOLUME")
    store.tts.volume = action.payload.volume;
  else if (action.type === "SET_TTS_VOICE")
    store.tts.voice = action.payload.voice;
  else if (action.type === "SET_NOTES_FOLDER")
    store.notes.defaultFolder = action.payload.folder;
  else if (action.type === "SET_NOTES_FOLDER_MODE")
    store.notes.defaultFolderMode = action.payload.mode;
  else if (action.type === "SET_NOTES_NAMING_MODE")
    store.notes.notesNamingMode = action.payload.folder;
  else if (action.type === "SET_NOTES_OPEN_AFTER_CREATION")
    store.notes.openNoteAfterCreation = action.payload.open;
  else if (action.type === "SET_AUTO_SUGGEST_COMMENT_TYPE")
    store.editorSuggest.commentFormat = action.payload.type;
  else if (action.type === "SET_NOTES_INSERT_LINK_TO_NOTE")
    store.notes.insertLinkToNote = action.payload.insert;
  else if (action.type === "SET_NOTES_TEMPLATE")
    store.notes.template = action.payload.template;
  else if (action.type === "ENABLE_TAG_STYLES")
    tag.enableStyle = action.payload.enable;
  else if (action.type === "SET_TAG_FONT_FAMILY")
    tag.style.fontFamily = action.payload.family;
  else if (action.type === "SET_TAG_FONT_SIZE")
    tag.style.fontSize = action.payload.fontSize;
  else if (action.type === "SET_TAG_FONT_WEIGHT")
    tag.style.fontWeight = action.payload.weight;
  else if (action.type === "SET_TAG_OPACITY")
    tag.style.opacity = action.payload.opacity;
  else if (action.type === "SET_TTS_FOCUS_COMMENT_IN_EDITOR")
    store.tts.focusAnnotationInEditor = action.payload.enable;
  else if (action.type === "LOG_PLUGIN_USED") {
    store.idling.daysUnused = [];
  } else if (action.type === "LOG_PLUGIN_STARTED") {
    const isIdle = store.idling.daysUnused.length > DAYS_UNUSED;
    if (!isIdle) {
      const date = formattedDate();
      const daysUnused = store.idling.daysUnused.sort();
      if (!daysUnused.includes(date)) {
        daysUnused.push(date);
        store.idling.daysUnused = daysUnused;
      }
    }
  } else if (action.type === "SET_CLIPBOARD_TEMPLATE") {
    const { template, name } = action.payload;
    store.clipboard.templates[name] = template;
  } else if (action.type === "TOGGLE_TRUNCATE_FILE_NAME") {
    store.notes.truncateFileName = !store.notes.truncateFileName;
  } else if (action.type === "SET_DEFAULT_PALETTE") {
    store.decoration.defaultPalette = action.payload.palette;
  }
};
var settingsReducer = (store, action) => {
  updateState2(store, action);
  return store;
};

// src/editor-suggest/annotation-suggest.ts
var import_obsidian16 = require("obsidian");

// src/editor-suggest/helpers/is-inside-annotation.ts
var isInsideAnnotation = (line, text2, ch) => {
  line = line.substring(0, ch) + line.substring(ch + text2.length);
  const annotation = parseAnnotations(line)[0];
  return annotation && ch > annotation.position.from && ch < annotation.position.to;
};

// src/editor-suggest/annotation-suggest.ts
var AnnotationSuggest = class extends import_obsidian16.EditorSuggest {
  constructor(app, plugin) {
    super(app);
    this.usedSuggestions = {};
    this.recordUsedSuggestion = (suggestion, updateMostRecent = false) => {
      if (!this.usedSuggestions[suggestion])
        this.usedSuggestions[suggestion] = {
          count: 1,
          timestamp: Date.now()
        };
      else {
        this.usedSuggestions[suggestion].timestamp = Date.now();
        this.usedSuggestions[suggestion].count++;
      }
      if (updateMostRecent) {
        if (this.mostRecentSuggestion !== suggestion)
          this.secondMostRecentSuggestion = this.mostRecentSuggestion;
        this.mostRecentSuggestion = suggestion;
      }
    };
    this.app = app;
    this.plugin = plugin;
    this.scope.register(["Shift"], "Enter", (evt) => {
      this.suggestions.useSelectedItem(evt);
      return false;
    });
  }
  getSuggestions(context) {
    const groups = this.plugin.settings.getValue().decoration.styles.labels;
    const labels = Array.from(
      new Set(
        Object.values(groups).map((g) => g.label).filter((v) => v)
      )
    );
    const suggestions = labels.map((val) => ({ label: val })).filter(
      (item) => item.label.toLowerCase().startsWith(context.query)
    ).sort((a, b) => {
      const nA = this.usedSuggestions[a.label];
      const nB = this.usedSuggestions[b.label];
      const countB = nB?.count || 0;
      const countA = nA?.count || 0;
      return countB === countA ? nB?.timestamp - nB?.timestamp : countB - countA;
    });
    let result = [];
    if (suggestions.length) {
      result = suggestions;
    } else if (isValidLabel(context.query)) {
      result = [{ label: context.query }];
    }
    result.push({ label: "", type: "empty-comment" });
    return result;
  }
  renderSuggestion(suggestion, el) {
    el.addClass("enhanced-annotations__suggestion");
    const textEL = el.createEl("span");
    const label = suggestion.label;
    if (suggestion.type === "empty-comment") {
      textEL.addClass("enhanced-annotations__empty-comment");
      textEL.setText("(no label)");
    } else {
      textEL.setText(label);
    }
    el.appendChild(textEL);
    const count = this.usedSuggestions[label]?.count;
    if (count > 0) {
      const countEl = el.createEl("sup");
      countEl.addClass("enhanced-annotations__suggestion-count");
      countEl.setText(String(count));
      el.appendChild(countEl);
    }
  }
  selectSuggestion(suggestion) {
    if (!this.context)
      return;
    const editor = this.context.editor;
    const settings = this.plugin.settings.getValue();
    const label = suggestion.label.trim();
    const content = label ? `${label}: ` : label;
    const text2 = settings.editorSuggest.commentFormat === "html" ? `<!--${content}-->` : `%%${content}%%`;
    editor.replaceRange(text2, this.context.start, this.context.end);
    const cursor = editor.getCursor();
    editor.setCursor({
      line: cursor.line,
      ch: cursor.ch - (settings.editorSuggest.commentFormat === "html" ? 3 : 2)
    });
    if (label)
      this.recordUsedSuggestion(label, true);
  }
  onTrigger(cursor, editor) {
    const settings = this.plugin.settings.getValue();
    if (!settings.editorSuggest.enableAutoSuggest) {
      return null;
    }
    const triggerPhrase = settings.editorSuggest.triggerPhrase;
    const startPos = this.context?.start || {
      line: cursor.line,
      ch: cursor.ch - triggerPhrase.length
    };
    if (!editor.getRange(startPos, cursor).startsWith(triggerPhrase)) {
      return null;
    }
    const precedingChar = editor.getRange(
      {
        line: startPos.line,
        ch: startPos.ch - 1
      },
      startPos
    );
    if (precedingChar && /[`a-zA-Z0-9]/.test(precedingChar)) {
      return null;
    }
    const line = editor.getLine(cursor.line);
    if (isInsideAnnotation(line, "//", startPos.ch))
      return null;
    return {
      start: startPos,
      end: cursor,
      query: editor.getRange(startPos, cursor).substring(triggerPhrase.length)
    };
  }
  useSecondMostRecentSuggestion() {
    this.recordUsedSuggestion(this.secondMostRecentSuggestion);
    return this.secondMostRecentSuggestion;
  }
  useMostRecentSuggestion() {
    this.recordUsedSuggestion(this.mostRecentSuggestion);
    return this.mostRecentSuggestion;
  }
};

// src/settings/helpers/merge-objects.ts
var isObject = (item) => item && typeof item === "object" && !Array.isArray(item);
var mergeDeep = (target, ...sources) => {
  if (!sources.length)
    return target;
  const source = sources.shift();
  if (isObject(target) && isObject(source)) {
    for (const key in source) {
      if (isObject(source[key])) {
        if (!target[key])
          Object.assign(target, { [key]: {} });
        mergeDeep(target[key], source[key]);
      } else {
        if (typeof target[key] === "undefined")
          Object.assign(target, { [key]: source[key] });
      }
    }
  }
  return mergeDeep(target, ...sources);
};

// src/note-creation/register-editor-menu-event.ts
var registerEditorMenuEvent = (plugin) => {
  plugin.registerEvent(
    plugin.app.workspace.on("editor-menu", (menu, editor, view) => {
      const cursor = editor.getCursor();
      const line = editor.getRange(
        { line: cursor.line, ch: 0 },
        { line: cursor.line + 3, ch: Infinity }
      );
      const annotations = parseAnnotations(line, cursor.line);
      const annotation = annotations.length === 1 ? annotations[0] : annotations.find(
        (a) => a.range.from.ch <= cursor.ch && Number(a.range.to?.ch) >= cursor.ch
      );
      if (annotation && annotation.range.from.line === cursor.line) {
        const onClick = async () => {
          const currentFileName = view.file?.basename;
          const currentFileFolder = view.file?.parent?.path;
          await createNoteFile({
            plugin,
            cursor,
            editor,
            currentFileName,
            currentFileFolder,
            annotation
          });
        };
        menu.addItem((item) => {
          item.setTitle(
            annotation.isHighlight ? l.OUTLINE_EDITOR_CREATE_NOTE_FROM_HIGHLIGHT : l.OUTLINE_EDITOR_CREATE_NOTE_FROM_COMMENT
          ).setIcon("links-coming-in").onClick(() => onClick());
        });
      }
    })
  );
};

// src/sidebar-outline/helpers/outline-updater/outline-updater.ts
var import_obsidian17 = require("obsidian");

// src/sidebar-outline/helpers/outline-updater/helpers/register-new-labels.ts
var registerNewLabels = (annotations, plugin) => {
  const settings = plugin.settings.getValue();
  if (!settings.decoration.autoRegisterLabels)
    return;
  const groups = new Set(annotations.map((c) => c.label).filter((l2) => l2));
  const existingGroups = new Set(
    Object.values(settings.decoration.styles.labels).map((g) => g.label)
  );
  const newGroups = [...groups].filter((group) => !existingGroups.has(group));
  for (const group of newGroups) {
    plugin.settings.dispatch({
      type: "NEW_GROUP",
      payload: {
        pattern: group
      }
    });
  }
};

// src/sidebar-outline/helpers/outline-updater/helpers/update-outline.ts
var updateOutline = (annotations, plugin) => {
  const labels = annotations.sort((a, b) => a.label.localeCompare(b.label)).reduce(
    (acc, val) => {
      if (val.text) {
        const label = val.label || "/";
        if (!acc[label]) {
          acc[label] = [];
        }
        acc[label].push(val);
      }
      return acc;
    },
    {}
  );
  fileAnnotations.set({ labels });
  if (!pluginIsIdle(plugin))
    registerNewLabels(annotations, plugin);
};

// src/sidebar-outline/helpers/outline-updater/helpers/reset-outline.ts
var resetOutline = () => {
  fileAnnotations.set({ labels: {} });
};

// inline-worker:__inline-worker
function inlineWorker(scriptText) {
  let blob = new Blob([scriptText], { type: "text/javascript" });
  let url = URL.createObjectURL(blob);
  let worker = new Worker(url);
  URL.revokeObjectURL(url);
  return worker;
}

// src/editor-plugin/helpers/decorate-annotations/helpers/parse-annotations/parse-annotations.worker.ts
function Worker2() {
  return inlineWorker('var C=Object.defineProperty,E=Object.defineProperties;var H=Object.getOwnPropertyDescriptors;var S=Object.getOwnPropertySymbols;var O=Object.prototype.hasOwnProperty,P=Object.prototype.propertyIsEnumerable;var T=(e,n,i)=>n in e?C(e,n,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[n]=i,k=(e,n)=>{for(var i in n||(n={}))O.call(n,i)&&T(e,i,n[i]);if(S)for(var i of S(n))P.call(n,i)&&T(e,i,n[i]);return e},B=(e,n)=>E(e,H(n));var y={"<!--":"-->","==":"==","%%":"%%"},g=/(<!--|%%|==)/g,A=/(-->|%%|==)/g,j=/```/g,F=/`([^`]+)`/g,$=(e,n=0,i=0,p=!1)=>{let h=e.split(`\n`),b=[],t={multiLineAnnotation:null,line:n,from:i,multiLineStart:"",multiAnnotationLine:!1,isInsideBlock:!1};for(let c=0;c<h.length;c++){let o=h[c];if(j.test(o)&&(t.isInsideBlock=!t.isInsideBlock),F.test(o)&&(o=o.replace(/`([^`]*?)==([^`]*?)`/g,"`$1++$2`"),F.lastIndex=0),!t.isInsideBlock){t.multiAnnotationLine?t.multiAnnotationLine=!1:(g.lastIndex=0,A.lastIndex=0);let l=g.exec(o),f;if((l||t.multiLineAnnotation)&&(t.multiLineAnnotation||(A.lastIndex=g.lastIndex),f=A.exec(o)),l&&f){let r=l[1],u=f[1];if(y[r]===u){let s=l.index,a=f.index,m=s+r.length,x=a+u.length;if(s<a){let d=o.substring(m,a),L=/^([^:\\s]+): ?(.+)$/g.exec(d),I=(L?L[2]:d).trim(),R=(L?L[1]:"").trim();if((I||R||p)&&b.push({text:I,label:R,isHighlight:r==="==",position:{from:t.from+s,to:t.from+x,afterFrom:t.from+m,beforeTo:t.from+a},range:{from:{line:t.line,ch:s},to:{line:t.line,ch:x}}}),g.exec(o)!==null){t.multiAnnotationLine=!0,g.lastIndex=A.lastIndex,c=c-1;continue}t.multiLineAnnotation=null}else s===a&&(l=null)}}if(l&&!f){let r=l[1],u=t.from+l.index,s=l.index+r.length,a=o.substring(s),m=/^([^:\\s]+): ?(.+)$/g.exec(a),x=m?m[2]:a,d=(m?m[1]:"").trim();(x||d)&&(t.multiLineAnnotation={label:d,text:[x],isHighlight:r==="==",position:{from:u,afterFrom:t.from+s,beforeTo:-1,to:-1},range:{from:{line:t.line,ch:l.index}}},t.multiLineStart=r)}else if(f&&t.multiLineAnnotation){let r=f[1];if(y[t.multiLineStart]===r){let u=f.index,s=u+r.length;t.multiLineAnnotation.text.push(o.substring(0,u)),t.multiLineAnnotation.position.to=t.from+s,t.multiLineAnnotation.position.beforeTo=t.from+u,t.multiLineAnnotation.range.to={line:t.line,ch:s};let a=t.multiLineAnnotation.text.map(m=>m.trim()).filter(Boolean).join(" ");if((a||p)&&b.push(B(k({},t.multiLineAnnotation),{text:a})),t.multiLineAnnotation=null,t.multiLineStart=null,l){g.lastIndex=A.lastIndex,c=c-1;continue}}}else t.multiLineAnnotation&&t.multiLineAnnotation.text.push(o)}t.line++,t.from+=o.length+1}return b};self.onmessage=function(e){try{self.postMessage({id:e.data.id,payload:$(e.data.payload)})}catch(n){console.error("parseAnnotations",n)}};\n');
}

// src/helpers/worker-promise.ts
var WorkerPromise = class {
  constructor(worker) {
    this.id = 0;
    this.resolvers = {};
    this.onMessage = (message) => {
      const id = message.data.id;
      const resolver = this.resolvers[id];
      if (resolver) {
        resolver(message.data.payload);
        delete this.resolvers[id];
      }
    };
    this.run = (payload) => {
      return new Promise((resolve) => {
        if (this.id === 1e3)
          this.id = 0;
        const id = this.id++;
        this.resolvers[id] = resolve;
        this.worker.postMessage({ id, payload });
      });
    };
    this.worker = worker;
    this.worker.addEventListener("message", this.onMessage);
  }
};

// src/sidebar-outline/helpers/outline-updater/outline-updater.ts
var OutlineUpdater = class extends Store {
  constructor(plugin) {
    super({ view: null });
    this.plugin = plugin;
    this.onLoad();
  }
  updateOutline(view, immediate = false) {
    clearTimeout(this.timeout);
    if (view instanceof import_obsidian17.MarkdownView) {
      this.set({ view });
      if (immediate) {
        this.worker.run(view.editor.getValue()).then((d) => updateOutline(d, this.plugin));
      } else {
        this.timeout = setTimeout(() => {
          this.worker.run(view.editor.getValue()).then((d) => updateOutline(d, this.plugin));
        }, 2e3);
      }
    } else {
      this.set({ view: null });
      resetOutline();
    }
  }
  onLoad() {
    const app = this.plugin.app;
    const worker = new Worker2();
    this.worker = new WorkerPromise(worker);
    this.plugin.registerEvent(
      app.workspace.on("editor-change", (editor, view) => {
        this.updateOutline(view);
      })
    );
    this.plugin.registerEvent(
      app.workspace.on("active-leaf-change", (leaf) => {
        const side = leaf?.getRoot()?.side;
        if (!side)
          this.updateOutline(leaf?.view, true);
        if (leaf?.view instanceof import_obsidian17.MarkdownView) {
          leaf.view.contentEl.toggleClass(
            "enhanced-annotations__enable-decoration",
            !pluginIsIdle(this.plugin)
          );
        }
      })
    );
    const onStart = () => {
      this.updateOutline(
        this.plugin.app.workspace.getActiveViewOfType(import_obsidian17.MarkdownView)
      );
    };
    this.plugin.app.workspace.onLayoutReady(onStart);
    setTimeout(onStart, 1e3);
  }
};

// src/settings/helpers/load-outline-state-from-settings.ts
var loadOutlineStateFromSettings = (plugin) => {
  const settings = plugin.settings.getValue();
  const outlineSettings = settings.outline;
  fontSize.set(outlineSettings.fontSize);
  controls.update((v) => ({
    ...v,
    showLabelsFilter: settings.outline.showLabelsFilter,
    showSearchInput: settings.outline.showSearchInput
  }));
  hiddenLabels.set(new Set(outlineSettings.hiddenLabels));
  hiddenTypes.set(new Set(outlineSettings.hiddenTypes));
  pluginIdle.set(pluginIsIdle(plugin));
};

// src/settings/helpers/subscribe-settings-to-outline-state.ts
var subscribeSettingsToOutlineState = (plugin) => {
  const settings = plugin.settings;
  const unsubs = /* @__PURE__ */ new Set();
  unsubs.add(
    fontSize.subscribe((fontSize2) => {
      const value = settings.getValue();
      value.outline.fontSize = fontSize2;
      settings.set(value);
    })
  );
  unsubs.add(
    controls.subscribe((controls2) => {
      const value = settings.getValue();
      value.outline.showSearchInput = controls2.showSearchInput;
      value.outline.showLabelsFilter = controls2.showLabelsFilter;
      settings.set(value);
    })
  );
  unsubs.add(
    hiddenLabels.subscribe((hiddenLabels2) => {
      const value = settings.getValue();
      value.outline.hiddenLabels = [...hiddenLabels2];
      settings.set(value);
    })
  );
  unsubs.add(
    hiddenTypes.subscribe((hiddenCategories) => {
      const value = settings.getValue();
      value.outline.hiddenTypes = [...hiddenCategories];
      settings.set(value);
    })
  );
  return unsubs;
};

// src/helpers/pluralize.ts
var pluralRules = new Intl.PluralRules("en-US");
var pluralize = (count, singular, plural) => {
  const grammaticalNumber = pluralRules.select(count);
  switch (grammaticalNumber) {
    case "one":
      return count + " " + singular;
    case "other":
      return count + " " + plural;
    default:
      throw new Error("Unknown: " + grammaticalNumber);
  }
};

// src/status-bar/helpers/word-count.ts
var pattern = " /(?:[0-9]+(?:(?:,|.)[0-9]+)*|[-'\u2019A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0-\u08B4\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16F1-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC\u0B80-\u0BFF\uAC00-\uD7A3\uA960-\uA97C\uD7B0-\uD7C6])+|[\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u4E00-\u9FD5\uAC00-\uD7A3\uA960-\uA97C\uD7B0-\uD7C6]/g;";
var script = `
let pattern = ${pattern}
   
function countWords(str) {

    pattern.lastIndex = 0;
    let m = str.match(pattern);
    return m ? m.length : 0;
}

self.onmessage = function (e) {
    try {
        self.postMessage({id: e.data.id, payload: countWords(e.data.payload)});
    } catch (e) {
        console.error('countWords', e);
    }
};`;
var W = new Worker(
  URL.createObjectURL(
    new Blob([script], {
      type: "text/javascript"
    })
  )
);
var workerPromise = new WorkerPromise(W);
var wordCount = workerPromise.run;

// src/status-bar/helpers/characters-count.ts
var script2 = `
let pattern = ${pattern}
function charactersCount(str) {
  
   return str.length - str.replace(/--/g,"##").replace(pattern, '').length
}

self.onmessage = function (e) {
    try {
        self.postMessage({id: e.data.id, payload: charactersCount(e.data.payload)});
    } catch (e) {
        console.error('charactersCount', e);
    }
};`;
var W2 = new Worker(
  URL.createObjectURL(
    new Blob([script2], {
      type: "text/javascript"
    })
  )
);
var workerPromise2 = new WorkerPromise(W2);
var charactersCount = workerPromise2.run;

// src/status-bar/helpers/create-tooltip.ts
var createTooltip = async (annotations, text2) => {
  const annotationsText = annotations.map((c) => (c.label || "") + " " + c.text).join();
  const fileTextChars = await charactersCount(text2);
  const annotationsTextChars = await charactersCount(annotationsText);
  const words = await wordCount(annotationsText);
  let tooltip = `${pluralize(words, "word", "words")} ${pluralize(
    annotationsTextChars,
    "character",
    "characters"
  )}`;
  if (fileTextChars > 0) {
    tooltip += ` (${Math.floor(
      annotationsTextChars / fileTextChars * 100
    )}%)`;
  }
  return tooltip;
};

// src/status-bar/status-bar.ts
var StatusBar = class {
  constructor(plugin) {
    this.plugin = plugin;
    this.onLoad = () => {
      const container = this.plugin.addStatusBarItem();
      this.elements = {
        container,
        comments: container.createEl("span"),
        highlights: container.createEl("span")
      };
      this.elements.container.addClass(
        "mod-clickable",
        "enhanced-annotations__status"
      );
      this.elements.container.onClickEvent(this.onClick);
      fileAnnotations.subscribe(async (v) => {
        const [comments, highlights] = Object.values(v.labels).flat().reduce(
          (acc, v2) => {
            if (v2.isHighlight)
              acc[1].push(v2);
            else
              acc[0].push(v2);
            return acc;
          },
          [[], []]
        );
        this.resetTooltip();
        await this.updateText(comments, highlights);
      });
      this.elements.container.onmouseenter = this.onHover;
    };
    this.updateTooltip = async (comments, highlights) => {
      let file;
      let text2 = "";
      if (comments.length | highlights.length) {
        file = this.plugin.outline.getValue().view?.file;
        if (file)
          text2 = await this.plugin.app.vault.read(file);
      }
      if (comments.length) {
        this.elements.comments.ariaLabel = await createTooltip(
          comments,
          text2
        );
      }
      if (highlights.length) {
        this.elements.highlights.ariaLabel = await createTooltip(
          highlights,
          text2
        );
      }
    };
    this.updateText = async (comments, highlights) => {
      const numberOfComments = comments.length;
      const numberOfHighlights = highlights.length;
      const noComments = numberOfComments === 0;
      const noHighlights = numberOfHighlights === 0;
      this.elements.comments.toggleClass(displayNone, noComments);
      this.elements.highlights.toggleClass(displayNone, noHighlights);
      this.elements.container.toggleClass(
        displayNone,
        noHighlights && noComments
      );
      if (numberOfComments) {
        this.elements.comments.setText(
          `${pluralize(numberOfComments, "comment", "comments")}`
        );
      }
      if (numberOfHighlights) {
        this.elements.highlights.setText(
          `${pluralize(numberOfHighlights, "highlight", "highlights")}`
        );
      }
    };
    this.onClick = async () => {
      await this.plugin.revealLeaf();
    };
    this.onHover = async () => {
      const v = get_store_value(fileAnnotations);
      const [comments, highlights] = Object.values(v.labels).flat().reduce(
        (acc, v2) => {
          if (v2.isHighlight)
            acc[1].push(v2);
          else
            acc[0].push(v2);
          return acc;
        },
        [[], []]
      );
      await this.updateTooltip(comments, highlights);
    };
    this.onLoad();
  }
  resetTooltip() {
    this.elements.comments.ariaLabel = "";
    this.elements.highlights.ariaLabel = "";
  }
};

// src/clipboard/helpers/copy-annotations-to-clipboard.ts
var import_obsidian19 = require("obsidian");

// src/clipboard/helpers/parse-annotations-from-files.ts
var import_obsidian18 = require("obsidian");
var parseAnnotationsFromFiles = async (abstractFiles, plugin, content = []) => {
  for (const abstractFile of abstractFiles) {
    if (abstractFile instanceof import_obsidian18.TFile) {
      if (abstractFile.extension === "md") {
        const annotations = parseAnnotations(
          await plugin.app.vault.read(abstractFile)
        );
        if (annotations.length)
          content.push({
            annotations,
            path: abstractFile.path,
            basename: abstractFile.basename,
            folder: abstractFile.parent?.path
          });
      }
    } else if (abstractFile instanceof import_obsidian18.TFolder) {
      await parseAnnotationsFromFiles(
        abstractFile.children,
        plugin,
        content
      );
    }
  }
  return content;
};

// src/clipboard/helpers/copy-annotations-to-clipboard.ts
var copyAnnotationsToClipboard = async (abstractFiles, plugin) => {
  const abstractFilesArray = Array.isArray(abstractFiles) ? abstractFiles : [abstractFiles];
  const root = abstractFilesArray[0].parent?.path;
  const unsortedAnnotations = await parseAnnotationsFromFiles(
    abstractFilesArray,
    plugin
  );
  const sortedAnnotations = unsortedAnnotations.sort(
    (a, b) => a.folder.localeCompare(b.folder)
  );
  const nOfFiles = Object.keys(sortedAnnotations).length;
  const nOfAnnotations = sortedAnnotations.map((c) => c.annotations).flat().length;
  if (nOfAnnotations) {
    new import_obsidian19.Notice(
      `Copied ${pluralize(
        nOfAnnotations,
        "annotation",
        "annotations"
      )} from ${pluralize(nOfFiles, "file", "files")} to clipboard`
    );
    const templates = plugin.settings.getValue().clipboard.templates;
    await navigator.clipboard.writeText(
      annotationsToText(sortedAnnotations, templates, root)
    );
  } else {
    new import_obsidian19.Notice(`No annotations found`);
  }
};

// src/clipboard/file-menu-items.ts
var fileMenuItems = (plugin) => async (menu, abstractFiles) => {
  menu.addItem((m) => {
    m.setTitle(l.OUTLINE_COPY_ANNOTATIONS_TO_CLIPBOARD);
    m.setIcon("clipboard-copy");
    m.onClick(() => {
      copyAnnotationsToClipboard(abstractFiles, plugin);
    });
  });
};

// src/settings/helpers/subscribe-decoration-state-to-settings.ts
var subscribeDecorationStateToSettings = (plugin) => {
  const settings = plugin.settings;
  const previousValue = {
    current: ""
  };
  return settings.subscribe((value) => {
    const styles = value.decoration.styles;
    const stylesStr = JSON.stringify(styles);
    if (stylesStr !== previousValue.current) {
      previousValue.current = stylesStr;
      labelSettings.set(
        Object.fromEntries(
          Object.values(styles.labels).map((label) => [
            label.label,
            label
          ])
        )
      );
      plugin.decorationSettings.setSettings(value.decoration.styles);
    }
  });
};

// src/editor-plugin/helpers/decorate-annotations/decoration-settings.ts
var import_view = require("@codemirror/view");

// src/editor-plugin/helpers/decorate-annotations/helpers/generate-label-style-string.ts
var hexToRgb = (hex, opacity) => {
  const bigint = parseInt(hex.replace("#", ""), 16);
  const r = bigint >> 16 & 255;
  const g = bigint >> 8 & 255;
  const b = bigint & 255;
  return `rgba(${r},${g},${b},${opacity})`;
};
var generateLabelStyleString = (style, isHighlight) => {
  const styleStringParts = [];
  if (style.color) {
    if (isHighlight) {
      const rgb = hexToRgb(style.color, (style.opacity || 80) / 100);
      styleStringParts.push(`background-color: ${rgb}`);
      styleStringParts.push(`color: var(--text-normal)`);
    } else
      styleStringParts.push(`color: ${style.color}`);
  }
  if (style.italic) {
    styleStringParts.push("font-style: italic");
  }
  if (style.fontFamily) {
    styleStringParts.push("font-family: " + style.fontFamily);
  }
  if (style.fontWeight) {
    styleStringParts.push(
      "font-weight: " + (style.fontWeight === "thin" ? "lighter" : "bolder")
    );
  }
  if (style.opacity) {
    if (!isHighlight)
      styleStringParts.push("opacity: " + style.opacity / 100);
  }
  if (style.underline) {
    styleStringParts.push("text-decoration: underline");
  }
  if (style.fontSize && !isNaN(style.fontSize)) {
    styleStringParts.push(`font-size: ${style.fontSize}px`);
  }
  if (style.case) {
    if (style.case === "upper") {
      styleStringParts.push("text-transform: uppercase");
    } else if (style.case === "lower") {
      styleStringParts.push("text-transform: lowercase");
    } else if (style.case === "title") {
      styleStringParts.push("text-transform: capitalize");
    }
  }
  return styleStringParts.join("; ");
};

// src/sidebar-outline/helpers/outline-updater/helpers/trigger-editor-update.ts
var import_state = require("@codemirror/state");
var outlineAnnotation = import_state.Annotation.define().of("outline update");
var triggerEditorUpdate = (editor) => {
  const view = editor.cm.docView.view;
  const update2 = view.state.update({
    scrollIntoView: false,
    changes: [],
    annotations: outlineAnnotation
  });
  view.dispatch(update2);
};

// src/editor-plugin/helpers/decorate-annotations/decoration-settings.ts
var DecorationSettings = class {
  constructor(plugin) {
    this.plugin = plugin;
    this._enabled = true;
  }
  get enabled() {
    return this._enabled;
  }
  set enabled(value) {
    this._enabled = value;
    this.decorate();
  }
  get decorations() {
    return this._decorations;
  }
  get decorateTags() {
    return this._decorateTags;
  }
  setSettings(styles) {
    this._decorations = Object.values(styles.labels).reduce(
      (acc, val) => {
        if (val.enableStyle) {
          const decorations = acc[val.label] || {
            comment: null,
            highlight: null,
            tag: null
          };
          if (!val.style.scope || val.style.scope === "highlights") {
            decorations.highlight = import_view.Decoration.mark({
              attributes: {
                style: generateLabelStyleString(
                  val.style,
                  true
                )
              }
            });
          }
          if (!val.style.scope || val.style.scope === "comments") {
            decorations.comment = import_view.Decoration.mark({
              attributes: {
                style: generateLabelStyleString(
                  val.style,
                  false
                )
              }
            });
            decorations.tag = import_view.Decoration.mark({
              attributes: {
                style: generateLabelStyleString(
                  {
                    ...val.style,
                    ...styles.tag.style
                  },
                  false
                )
              }
            });
          }
          acc[val.label] = decorations;
        }
        return acc;
      },
      {}
    );
    this._decorateTags = styles.tag.enableStyle;
    this.decorate();
  }
  decorate() {
    const editor = this.plugin.outline.getValue().view?.editor;
    if (editor) {
      triggerEditorUpdate(editor);
    }
  }
};

// src/editor-plugin/editor-plugin.ts
var import_view3 = require("@codemirror/view");

// src/editor-plugin/helpers/decorate-annotations/decorate-annotations.ts
var import_state2 = require("@codemirror/state");
var import_view2 = require("@codemirror/view");
var defaultHighlightDecoration = import_view2.Decoration.mark({
  class: "cm-highlight-default"
});
var decorateAnnotations = (view, plugin) => {
  const builder = new import_state2.RangeSetBuilder();
  if (plugin.decorationSettings.enabled) {
    for (const { from, to } of view.visibleRanges) {
      const line = view.state.doc.lineAt(from);
      const annotations = parseAnnotations(
        view.state.sliceDoc(from, to),
        line.number,
        from
      );
      for (const annotation of annotations) {
        const decoration = plugin.decorationSettings.decorations[annotation.label];
        if (decoration) {
          if (annotation.isHighlight) {
            if (decoration.highlight) {
              builder.add(
                annotation.position.from,
                annotation.position.to,
                decoration.highlight
              );
            } else {
              builder.add(
                annotation.position.from,
                annotation.position.to,
                defaultHighlightDecoration
              );
            }
          } else if (plugin.decorationSettings.decorateTags) {
            if (decoration.tag && decoration.comment) {
              builder.add(
                annotation.position.from,
                annotation.position.afterFrom,
                decoration.tag
              );
              builder.add(
                annotation.position.afterFrom,
                annotation.position.beforeTo,
                decoration.comment
              );
              builder.add(
                annotation.position.beforeTo,
                annotation.position.to,
                decoration.tag
              );
            }
          } else if (decoration.comment) {
            builder.add(
              annotation.position.from,
              annotation.position.to,
              decoration.comment
            );
          }
        } else if (annotation.isHighlight) {
          builder.add(
            annotation.position.from,
            annotation.position.to,
            defaultHighlightDecoration
          );
        }
      }
    }
  }
  return builder.finish();
};

// src/editor-plugin/editor-plugin.ts
var EditorPlugin = class {
  constructor(view) {
    this.decorations = decorateAnnotations(view, EditorPlugin.plugin);
  }
  update(update2) {
    if (update2.docChanged || update2.viewportChanged || update2.transactions?.[0]?.annotations?.[0] === outlineAnnotation) {
      this.decorations = decorateAnnotations(
        update2.view,
        EditorPlugin.plugin
      );
    }
  }
  destroy() {
  }
};
var pluginSpec = {
  decorations: (value) => value.decorations
};
var editorPlugin = import_view3.ViewPlugin.fromClass(EditorPlugin, pluginSpec);

// src/idling/idling.ts
var Idling = class {
  constructor(plugin) {
    this.plugin = plugin;
    this.enabled = false;
    this.subscriptions = /* @__PURE__ */ new Set();
    this.plugin.settings.dispatch({ type: "LOG_PLUGIN_STARTED" });
    if (!pluginIsIdle(plugin))
      this.plugin.loadPlugin();
    this.subscribe();
  }
  enable() {
    this.logActivity();
  }
  logActivity() {
    if (!this.enabled) {
      this.enabled = true;
      const wasIdle = pluginIsIdle(this.plugin);
      this.plugin.settings.dispatch({ type: "LOG_PLUGIN_USED" });
      if (wasIdle) {
        pluginIdle.set(false);
        this.plugin.loadPlugin();
      }
      for (const unsub of this.subscriptions) {
        unsub();
        this.subscriptions.delete(unsub);
      }
    }
  }
  subscribe() {
    if (pluginIsIdle(this.plugin))
      return;
    this.subscriptions.add(
      controls.subscribe((v, action) => {
        if (action) {
          this.logActivity();
        }
      })
    );
    this.subscriptions.add(
      fileAnnotations.subscribe((v) => {
        if (Object.values(v.labels).flat().some((v2) => v2.label)) {
          this.logActivity();
        }
      })
    );
  }
};

// src/main.ts
var LabeledAnnotations = class extends import_obsidian20.Plugin {
  constructor() {
    super(...arguments);
    this.unsubscribeCallbacks = /* @__PURE__ */ new Set();
  }
  async onload() {
    await this.loadSettings();
    this.editorSuggest = new AnnotationSuggest(this.app, this);
    this.registerEditorSuggest(this.editorSuggest);
    this.registerEvent(
      this.app.workspace.on(
        "file-menu",
        (menu, abstractFiles) => {
          if (abstractFiles instanceof import_obsidian20.TFolder || abstractFiles instanceof import_obsidian20.TFile && abstractFiles.extension === "md")
            fileMenuItems(this)(menu, abstractFiles);
        }
      )
    );
    this.registerEvent(
      this.app.workspace.on("files-menu", fileMenuItems(this))
    );
    addInsertCommentCommands(this);
    this.registerView(
      SIDEBAR_OUTLINE_VIEW_TYPE,
      (leaf) => new SidebarOutlineView(leaf, this)
    );
    this.app.workspace.onLayoutReady(async () => {
      await this.attachLeaf();
      loadOutlineStateFromSettings(this);
      this.registerSubscription(...subscribeSettingsToOutlineState(this));
      this.addSettingTab(new SettingsTab(this.app, this));
      registerEditorMenuEvent(this);
      this.outline = new OutlineUpdater(this);
      this.statusBar = new StatusBar(this);
      tts.setPlugin(this);
      this.idling = new Idling(this);
    });
  }
  onunload() {
    tts.stop();
    for (const callback of this.unsubscribeCallbacks) {
      callback();
    }
  }
  loadPlugin() {
    this.decorationSettings = new DecorationSettings(this);
    EditorPlugin.plugin = this;
    this.unsubscribeCallbacks.add(subscribeDecorationStateToSettings(this));
    this.registerEditorExtension([editorPlugin]);
  }
  async loadSettings() {
    const settings = await this.loadData() || {};
    this.settings = new Store(
      mergeDeep(settings, DEFAULT_SETTINGS()),
      settingsReducer
    );
    this.registerSubscription(
      this.settings.subscribe(() => {
        this.saveSettings();
      })
    );
  }
  async saveSettings() {
    await this.saveData(this.settings.getValue());
  }
  async attachLeaf() {
    const leaves = this.app.workspace.getLeavesOfType(
      SIDEBAR_OUTLINE_VIEW_TYPE
    );
    if (leaves.length === 0) {
      await this.app.workspace.getRightLeaf(false).setViewState({
        type: SIDEBAR_OUTLINE_VIEW_TYPE,
        active: true
      });
    }
  }
  async revealLeaf() {
    const leaf = this.app.workspace.getLeavesOfType(
      SIDEBAR_OUTLINE_VIEW_TYPE
    )[0];
    if (leaf)
      this.app.workspace.revealLeaf(leaf);
    else {
      await this.attachLeaf();
      await this.revealLeaf();
    }
  }
  registerSubscription(...callback) {
    callback.forEach((callback2) => {
      this.unsubscribeCallbacks.add(callback2);
    });
  }
};

/* nosourcemap */
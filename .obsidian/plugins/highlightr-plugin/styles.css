/*highlighter style*/
/*lowlight*/
.highlightr-lowlight mark,
.highlightr-lowlight span.cm-highlight,
.highlightr-lowlight .markdown-preview-view mark {
  --lowlight-background: var(--background-primary);
  border-radius: 0;
  background-image: linear-gradient(
    360deg,
    rgba(255, 255, 255, 0) 40%,
    var(--lowlight-background) 40%
  ) !important;
}

.highlightr-lowlight .workspace-split.mod-left-split mark,
.highlightr-lowlight .workspace-split.mod-left-split span.cm-highlight,
.highlightr-lowlight
  .workspace-split.mod-left-split
  .markdown-preview-view
  mark,
.highlightr-lowlight .workspace-split.mod-right-split mark,
.highlightr-lowlight .workspace-split.mod-right-split span.cm-highlight,
.highlightr-lowlight
  .workspace-split.mod-right-split
  .markdown-preview-view
  mark {
  --lowlight-background: var(--background-secondary);
}

.highlightr-lowlight .admonition-content mark,
.highlightr-lowlight .admonition-content span.cm-highlight,
.highlightr-lowlight .admonition-content > .markdown-preview-view mark {
  --lowlight-background: var(--background-primary-alt);
}

/*floating*/
.highlightr-floating mark,
.highlightr-floating span.cm-highlight,
.highlightr-floating .markdown-preview-view mark {
  --floating-background: var(--background-primary);
  border-radius: 0;
  padding-bottom: 5px;
  background-image: linear-gradient(
    360deg,
    rgba(255, 255, 255, 0) 28%,
    var(--floating-background) 28%
  ) !important;
}

.highlightr-floating .workspace-split.mod-left-split mark,
.highlightr-floating .workspace-split.mod-left-split span.cm-highlight,
.highlightr-floating
  .workspace-split.mod-left-split
  .markdown-preview-view
  mark,
.highlightr-floating .workspace-split.mod-right-split mark,
.highlightr-floating .workspace-split.mod-right-split span.cm-highlight,
.highlightr-floating
  .workspace-split.mod-right-split
  .markdown-preview-view
  mark {
  --floating-background: var(--background-secondary);
}

.highlightr-floating .admonition-content mark,
.highlightr-floating .admonition-content span.cm-highlight,
.highlightr-floating .admonition-content > .markdown-preview-view mark {
  --floating-background: var(--background-primary-alt);
}

/*rounded*/
.highlightr-rounded mark,
.highlightr-rounded .markdown-preview-view mark {
  margin: 0 -0.05em;
  padding: 0.125em 0.15em;
  border-radius: 0.2em;
  -webkit-box-decoration-break: clone;
  box-decoration-break: clone;
}

.highlightr-rounded span.cm-highlight {
  border-radius: 0.2em;
  -webkit-box-decoration-break: clone;
  box-decoration-break: clone;
}

.highlightr-rounded .cm-s-obsidian span.cm-highlight {
  font-weight: inherit;
}

.highlightr-rounded .cm-highlight + span.cm-formatting-highlight {
  padding-left: 0em;
  padding-right: 0.15em;
  -webkit-box-decoration-break: clone;
  box-decoration-break: clone;
}

/*realistic*/
.highlightr-realistic mark,
.highlightr-realistic .markdown-preview-view mark {
  margin: 0 -0.05em;
  padding: 0.1em 0.4em;
  border-radius: 0.8em 0.3em;
  -webkit-box-decoration-break: clone;
  box-decoration-break: clone;
  text-shadow: 0 0 0.75em var(--background-primary-alt);
}

.highlightr-realistic.hide-tokens .cm-s-obsidian span.cm-highlight,
.hide-tokens.highlightr-realistic .cm-s-obsidian span.cm-highlight {
  border-radius: 0.8em 0.3em;
}

.highlightr-realistic .cm-s-obsidian span.cm-highlight {
  padding: 0.15em 0.25em;
  -webkit-box-decoration-break: clone;
  box-decoration-break: clone;
  text-shadow: 0 0 0.75em var(--background-primary-alt);
}

.highlightr-realistic .cm-s-obsidian span.cm-formatting-highlight {
  margin: 0 0 0 -0.05em;
  padding: 0.15em 0.25em;
  border-radius: 0.8em 0 0 0.3em;
  -webkit-box-decoration-break: clone;
  box-decoration-break: clone;
}

.highlightr-realistic
  .cm-s-obsidian
  .cm-highlight
  + span.cm-formatting-highlight {
  margin: 0 -0.05em 0 0;
  padding: 0.15em 0.25em;
  border-radius: 0 0.3em 0.8em 0;
  -webkit-box-decoration-break: clone;
  box-decoration-break: clone;
}

/**/

button.copy-highlights {
  padding: 4px 14px;
  border-radius: 5px;
  background-color: var(--interactive-accent);
}

.copy-highlights svg {
  width: 1.3em;
  height: 1.3em;
  display: flex;
}

details summary.highlight-summary:before {
  width: 2em;
  height: 2em;
  content: "☐";
  font-size: 9px;
  cursor: pointer;
  margin-right: 5px;
  display: inline-block;
  vertical-align: 0.05em;
  background-color: currentColor;
  -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="black" d="M10.707 17.707L16.414 12l-5.707-5.707l-1.414 1.414L13.586 12l-4.293 4.293z"></path></svg>');
}

details[open] summary.highlight-summary::before {
  transform: rotate(90deg);
}

/*highlightr component*/

@keyframes fade {
  0% {
    opacity: 0;
  }
  20% {
    opacity: 0.9;
  }
  40% {
    opacity: 0.95;
  }
  100% {
    opacity: 1;
  }
}

.menu.highlighterContainer {
  min-width: 140px;
  max-width: 225px;
  max-height: 180px;
  padding-left: 0em;
  padding: 0;
  margin: 0 auto;
  border-radius: 6px;
  font-size: 14px;
  overflow-y: auto;
  overflow-x: hidden;
  position: fixed;
  animation: fade 300ms ease-in-out;
  background-color: var(--background-primary);
  box-shadow: 0px 3px 25px rgba(31, 38, 135, 0.1);
  border: 1px solid var(--background-modifier-border);
}

.menu.highlighterContainer .menu-item .menu-item-icon {
  display: inline-block;
  width: 25px;
  position: relative;
  top: 2px;
}

.menu.highlighterContainer .menu-item {
  display: flex;
  padding: 2px 14px 3px 14px;
  align-items: unset;
  margin: auto;
  cursor: pointer;
  font-size: 14px;
  height: 32px;
  line-height: 31px;
  white-space: nowrap;
  border-radius: 0;
  border-bottom: 1px solid var(--background-modifier-border);
}

.menu.highlighterContainer .menu-item:last-of-type {
  border-bottom: none;
}

/*----------------------------------------------------------------
SETTINGS TAB
----------------------------------------------------------------*/

button.HighlightrSettingsButton {
  padding: 4px 14px;
  border-radius: 6px;
  height: fit-content;
}

button.HighlightrSettingsButton svg {
  width: 1.3em;
  height: 1.3em;
  display: flex;
}

.highlighter-settings-color,
.highlighter-settings-value {
  width: 42%;
  color: var(--text-normal);
}

.highlighterplugin-setting-item {
  display: block;
}

.highlighterplugin-setting-item .setting-item-control {
  justify-content: space-between;
  margin-top: 12px;
  align-content: center;
  align-items: flex-end;
  grid-gap: 6px;
}

.modal.mod-settings
  button:not(.mod-cta):not(.mod-warning).HighlightrSettingsButton.HighlightrSettingsButtonAdd,
button:not(.mod-cta):not(.mod-warning).HighlightrSettingsButton.HighlightrSettingsButtonAdd {
  background-color: var(--interactive-accent);
}

.modal.mod-settings
  button:not(.mod-cta):not(.mod-warning).HighlightrSettingsButton.HighlightrSettingsButtonDelete,
button:not(.mod-cta):not(.mod-warning).HighlightrSettingsButton.HighlightrSettingsButtonDelete {
  background-color: #989cab;
}

/**/
.highlighter-setting-icon {
  display: flex;
  height: 24px;
  width: 24px;
}

.highlighter-item-draggable {
  cursor: grab;
  display: grid;
  grid-gap: 8px;
  grid-template-columns: 0.5fr 7fr;
  align-items: center;
  border-top: 1px solid var(--background-modifier-border);
}

.HighlightrSettingsTabsContainer {
  border-bottom: 1px solid var(--background-modifier-border);
}

.setting-item.highlighter-setting-item:first-child {
  padding-top: 18px;
}

.highlighter-setting-item {
  padding: 18px 0 18px 0;
  border-top: none;
}

.highlighter-sortable-fallback {
  cursor: grabbing;
  box-shadow: 0px 3px 32px rgb(31 38 135 / 15%);
}

.highlighter-sortable-grab {
  cursor: grabbing !important;
}

.highlighter-sortable-ghost {
  opacity: 0.4;
  cursor: grabbing;
}

.highlighter-sortable-chosen {
  cursor: grabbing;
  padding: 0 0 0 18px;
  background-color: var(--background-primary);
}

.highlighter-sortable-drag {
  cursor: grabbing;
  box-shadow: 0px 3px 32px rgb(31 38 135 / 15%);
}

/*----------------------------------------------------------------
HIGHLIGHTR SUPPORT
----------------------------------------------------------------*/

.hltrDonationSection {
  width: 65%;
  height: 50vh;
  margin: 0 auto;
  text-align: center;
  color: var(--text-normal);
}

.pcr-app .pcr-swatches > button {
  padding: 0;
}

.pickr .pcr-button {
  margin-right: 0;
}

.themed-color-wrapper > div {
  background: var(--background-primary);
  padding: 10px;
  display: flex;
  align-items: center;
  border-radius: 4px;
}

.themed-color-wrapper > div + div {
  margin-top: 6px;
}

.themed-color-wrapper button {
  display: block;
}

.themed-color-wrapper .pickr-reset > button {
  margin: 0 0 0 10px;
  padding: 9px;
  line-height: 1;
}

.themed-color-wrapper .pickr-reset > button > svg {
  display: block;
}

/*----------------------------------------------------------------
PICKR 1.8.2 MIT | https://github.com/Simonwep/pickr
----------------------------------------------------------------*/
.pickr {
  position: relative;
  overflow: visible;
  transform: translateY(0);
}
.pickr * {
  box-sizing: border-box;
  outline: none;
  border: none;
  -webkit-appearance: none;
}
.pickr .pcr-button {
  position: relative;
  height: 2em;
  width: 2em;
  padding: 0.5em;
  cursor: pointer;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    "Helvetica Neue", Arial, sans-serif;
  border-radius: 0.15em;
  background: url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 50" stroke="%2342445A" stroke-width="5px" stroke-linecap="round"><path d="M45,45L5,5"></path><path d="M45,5L5,45"></path></svg>')
    no-repeat center;
  background-size: 0;
  transition: all 0.3s;
}
.pickr .pcr-button::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2"><path fill="white" d="M1,0H2V1H1V0ZM0,1H1V2H0V1Z"/><path fill="gray" d="M0,0H1V1H0V0ZM1,1H2V2H1V1Z"/></svg>');
  background-size: 0.5em;
  border-radius: 0.15em;
  z-index: -1;
}
.pickr .pcr-button::before {
  z-index: initial;
}
.pickr .pcr-button::after {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  transition: background 0.3s;
  background: var(--pcr-color);
  border-radius: 0.15em;
}
.pickr .pcr-button.clear {
  background-size: 70%;
}
.pickr .pcr-button.clear::before {
  opacity: 0;
}
.pickr .pcr-button.clear:focus {
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.85), 0 0 0 3px var(--pcr-color);
}
.pickr .pcr-button.disabled {
  cursor: not-allowed;
}
.pickr *,
.pcr-app * {
  box-sizing: border-box;
  outline: none;
  border: none;
  -webkit-appearance: none;
}
.pickr input:focus,
.pickr input.pcr-active,
.pickr button:focus,
.pickr button.pcr-active,
.pcr-app input:focus,
.pcr-app input.pcr-active,
.pcr-app button:focus,
.pcr-app button.pcr-active {
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.85), 0 0 0 3px var(--pcr-color);
}
.pickr .pcr-palette,
.pickr .pcr-slider,
.pcr-app .pcr-palette,
.pcr-app .pcr-slider {
  transition: box-shadow 0.3s;
}
.pickr .pcr-palette:focus,
.pickr .pcr-slider:focus,
.pcr-app .pcr-palette:focus,
.pcr-app .pcr-slider:focus {
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.85), 0 0 0 3px rgba(0, 0, 0, 0.25);
}
.pcr-app {
  position: fixed;
  display: flex;
  flex-direction: column;
  z-index: 10000;
  border-radius: 0.1em;
  background: #fff;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0s 0.3s;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    "Helvetica Neue", Arial, sans-serif;
  box-shadow: 0 0.15em 1.5em 0 rgba(0, 0, 0, 0.1), 0 0 1em 0 rgba(0, 0, 0, 0.03);
  left: 0;
  top: 0;
}
.pcr-app.visible {
  transition: opacity 0.3s;
  visibility: visible;
  opacity: 1;
}
.pcr-app .pcr-swatches {
  display: flex;
  flex-wrap: wrap;
  margin-top: 0.75em;
}
.pcr-app .pcr-swatches.pcr-last {
  margin: 0;
}
@supports (display: grid) {
  .pcr-app .pcr-swatches {
    display: grid;
    align-items: center;
    grid-template-columns: repeat(auto-fit, 1.75em);
  }
}
.pcr-app .pcr-swatches > button {
  font-size: 1em;
  position: relative;
  width: calc(1.75em - 5px);
  height: calc(1.75em - 5px);
  border-radius: 0.15em;
  cursor: pointer;
  margin: 2.5px;
  flex-shrink: 0;
  justify-self: center;
  transition: all 0.15s;
  overflow: hidden;
  background: transparent;
  z-index: 1;
}
.pcr-app .pcr-swatches > button::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2"><path fill="white" d="M1,0H2V1H1V0ZM0,1H1V2H0V1Z"/><path fill="gray" d="M0,0H1V1H0V0ZM1,1H2V2H1V1Z"/></svg>');
  background-size: 6px;
  border-radius: 0.15em;
  z-index: -1;
}
.pcr-app .pcr-swatches > button::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--pcr-color);
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 0.15em;
  box-sizing: border-box;
}
.pcr-app .pcr-swatches > button:hover {
  filter: brightness(1.05);
}
.pcr-app .pcr-swatches > button:not(.pcr-active) {
  box-shadow: none;
}
.pcr-app .pcr-interaction {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin: 0 -0.2em 0 -0.2em;
}
.pcr-app .pcr-interaction > * {
  margin: 0 0.2em;
}
.pcr-app .pcr-interaction input {
  letter-spacing: 0.07em;
  font-size: 0.75em;
  text-align: center;
  cursor: pointer;
  color: #75797e;
  background: #f1f3f4;
  border-radius: 0.15em;
  transition: all 0.15s;
  padding: 0.45em 0.5em;
  margin-top: 0.75em;
}
.pcr-app .pcr-interaction input:hover {
  filter: brightness(0.975);
}
.pcr-app .pcr-interaction input:focus {
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.85),
    0 0 0 3px rgba(66, 133, 244, 0.75);
}
.pcr-app .pcr-interaction .pcr-result {
  color: #75797e;
  text-align: left;
  flex: 1 1 8em;
  min-width: 8em;
  transition: all 0.2s;
  border-radius: 0.15em;
  background: #f1f3f4;
  cursor: text;
}
.pcr-app .pcr-interaction .pcr-result::-moz-selection {
  background: #4285f4;
  color: #fff;
}
.pcr-app .pcr-interaction .pcr-result::selection {
  background: #4285f4;
  color: #fff;
}
.pcr-app .pcr-interaction .pcr-type.active {
  color: #fff;
  background: #4285f4;
}
.pcr-app .pcr-interaction .pcr-save,
.pcr-app .pcr-interaction .pcr-cancel,
.pcr-app .pcr-interaction .pcr-clear {
  color: #fff;
  width: auto;
}
.pcr-app .pcr-interaction .pcr-save,
.pcr-app .pcr-interaction .pcr-cancel,
.pcr-app .pcr-interaction .pcr-clear {
  color: #fff;
}
.pcr-app .pcr-interaction .pcr-save:hover,
.pcr-app .pcr-interaction .pcr-cancel:hover,
.pcr-app .pcr-interaction .pcr-clear:hover {
  filter: brightness(0.925);
}
.pcr-app .pcr-interaction .pcr-save {
  background: #4285f4;
}
.pcr-app .pcr-interaction .pcr-clear,
.pcr-app .pcr-interaction .pcr-cancel {
  background: #f44250;
}
.pcr-app .pcr-interaction .pcr-clear:focus,
.pcr-app .pcr-interaction .pcr-cancel:focus {
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.85),
    0 0 0 3px rgba(244, 66, 80, 0.75);
}
.pcr-app .pcr-selection .pcr-picker {
  position: absolute;
  height: 18px;
  width: 18px;
  border: 2px solid #fff;
  border-radius: 100%;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.pcr-app .pcr-selection .pcr-color-palette,
.pcr-app .pcr-selection .pcr-color-chooser,
.pcr-app .pcr-selection .pcr-color-opacity {
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  display: flex;
  flex-direction: column;
  cursor: grab;
  cursor: -webkit-grab;
}
.pcr-app .pcr-selection .pcr-color-palette:active,
.pcr-app .pcr-selection .pcr-color-chooser:active,
.pcr-app .pcr-selection .pcr-color-opacity:active {
  cursor: grabbing;
  cursor: -webkit-grabbing;
}
.pcr-app[data-theme="nano"] {
  width: 14.25em;
  max-width: 95vw;
}
.pcr-app[data-theme="nano"] .pcr-swatches {
  margin-top: 0.6em;
  padding: 0 0.6em;
}
.pcr-app[data-theme="nano"] .pcr-interaction {
  padding: 0 0.6em 0.6em 0.6em;
}
.pcr-app[data-theme="nano"] .pcr-selection {
  display: grid;
  grid-gap: 0.6em;
  grid-template-columns: 1fr 4fr;
  grid-template-rows: 5fr auto auto;
  align-items: center;
  height: 10.5em;
  width: 100%;
  align-self: flex-start;
}
.pcr-app[data-theme="nano"] .pcr-selection .pcr-color-preview {
  grid-area: 2 / 1 / 4 / 1;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin-left: 0.6em;
}
.pcr-app[data-theme="nano"] .pcr-selection .pcr-color-preview .pcr-last-color {
  display: none;
}
.pcr-app[data-theme="nano"]
  .pcr-selection
  .pcr-color-preview
  .pcr-current-color {
  position: relative;
  background: var(--pcr-color);
  width: 2em;
  height: 2em;
  border-radius: 50em;
  overflow: hidden;
}
.pcr-app[data-theme="nano"]
  .pcr-selection
  .pcr-color-preview
  .pcr-current-color::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2"><path fill="white" d="M1,0H2V1H1V0ZM0,1H1V2H0V1Z"/><path fill="gray" d="M0,0H1V1H0V0ZM1,1H2V2H1V1Z"/></svg>');
  background-size: 0.5em;
  border-radius: 0.15em;
  z-index: -1;
}
.pcr-app[data-theme="nano"] .pcr-selection .pcr-color-palette {
  grid-area: 1 / 1 / 2 / 3;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.pcr-app[data-theme="nano"] .pcr-selection .pcr-color-palette .pcr-palette {
  border-radius: 0.15em;
  width: 100%;
  height: 100%;
}
.pcr-app[data-theme="nano"]
  .pcr-selection
  .pcr-color-palette
  .pcr-palette::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2"><path fill="white" d="M1,0H2V1H1V0ZM0,1H1V2H0V1Z"/><path fill="gray" d="M0,0H1V1H0V0ZM1,1H2V2H1V1Z"/></svg>');
  background-size: 0.5em;
  border-radius: 0.15em;
  z-index: -1;
}
.pcr-app[data-theme="nano"] .pcr-selection .pcr-color-chooser {
  grid-area: 2 / 2 / 2 / 2;
}
.pcr-app[data-theme="nano"] .pcr-selection .pcr-color-opacity {
  grid-area: 3 / 2 / 3 / 2;
}
.pcr-app[data-theme="nano"] .pcr-selection .pcr-color-chooser,
.pcr-app[data-theme="nano"] .pcr-selection .pcr-color-opacity {
  height: 0.5em;
  margin: 0 0.6em;
}
.pcr-app[data-theme="nano"] .pcr-selection .pcr-color-chooser .pcr-picker,
.pcr-app[data-theme="nano"] .pcr-selection .pcr-color-opacity .pcr-picker {
  top: 50%;
  transform: translateY(-50%);
}
.pcr-app[data-theme="nano"] .pcr-selection .pcr-color-chooser .pcr-slider,
.pcr-app[data-theme="nano"] .pcr-selection .pcr-color-opacity .pcr-slider {
  flex-grow: 1;
  border-radius: 50em;
}
.pcr-app[data-theme="nano"] .pcr-selection .pcr-color-chooser .pcr-slider {
  background: linear-gradient(to right, red, #ff0, lime, cyan, blue, #f0f, red);
}
.pcr-app[data-theme="nano"] .pcr-selection .pcr-color-opacity .pcr-slider {
  background: linear-gradient(to right, transparent, black),
    url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2"><path fill="white" d="M1,0H2V1H1V0ZM0,1H1V2H0V1Z"/><path fill="gray" d="M0,0H1V1H0V0ZM1,1H2V2H1V1Z"/></svg>');
  background-size: 100%, 0.25em;
}

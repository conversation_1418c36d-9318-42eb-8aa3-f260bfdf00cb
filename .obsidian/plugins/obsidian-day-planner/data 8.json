{"snapStepMinutes": 10, "progressIndicator": "mini-timeline", "showTaskNotification": true, "zoomLevel": 2, "timelineIcon": "calendar-with-checkmark", "endLabel": "All done", "startHour": 6, "timelineDateFormat": "YYYY-MM-DD", "centerNeedle": true, "plannerHeading": "Day planner", "plannerHeadingLevel": 1, "timelineColored": false, "timelineStartColor": "#006466", "timelineEndColor": "#4d194d", "timestampFormat": "HH:mm", "hourFormat": "H", "dataviewSource": "", "extendDurationUntilNext": false, "defaultDurationMinutes": 30, "minimalDurationMinutes": 10, "showTimestampInTaskBlock": false, "showUncheduledTasks": true, "showUnscheduledNestedTasks": true, "showNow": true, "showNext": true, "pluginVersion": "0.28.0", "showCompletedTasks": true, "showSubtasksInTaskBlocks": true, "icals": [], "colorOverrides": [], "releaseNotes": true, "taskStatusOnCreation": " ", "eventFormatOnCreation": "task", "sortTasksInPlanAfterEdit": true, "firstDayOfWeek": "monday", "multiDayRange": "3-days", "showTimeTracker": false, "showActiveClocks": false, "rawIcals": []}
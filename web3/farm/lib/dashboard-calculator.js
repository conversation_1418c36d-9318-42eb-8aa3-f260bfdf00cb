/**
 * Dashboard 计算模块
 * 
 * 包含所有核心计算逻辑：
 * - 基础指标计算（APR、投资天数、USDT转换等）
 * - 衍生指标计算（仓位状态、APR Gap、组合APR等）
 * - 时间范围处理
 * - 项目数据处理
 * 
 * @module DashboardCalculator
 */

// 确保核心对象存在
window.DashboardCore = window.DashboardCore || {};

/**
 * 基础计算器
 */
window.DashboardCore.BaseCalculator = {
    /**
     * 计算项目的历史记录数据
     * @param {Object} project - 项目对象
     * @param {Map} priceMap - 价格映射
     * @returns {Object|null} 历史数据对象
     */
    calculateProjectHistory(project, priceMap) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (!window.DashboardCore.Utils.Validation.isValidProject(project)) {
                return null;
            }

            const balanceEntries = project.file.lists.filter(item =>
                window.DashboardCore.Utils.Validation.isValidBalanceEntry(item)
            );

            if (balanceEntries.length === 0) return null;

            // 按日期排序
            balanceEntries.sort((a, b) => {
                const dateA = window.DashboardCore.Utils.Date.parseDate(a.date);
                const dateB = window.DashboardCore.Utils.Date.parseDate(b.date);
                if (!dateA || !dateB) return 0;
                return dateA.toMillis() - dateB.toMillis();
            });

            const firstEntry = balanceEntries[0];
            const lastEntry = balanceEntries.length === 1 ? balanceEntries[0] : balanceEntries[balanceEntries.length - 1];

            // 计算历史上所有add属性的总和
            let totalAddAmount = 0;
            for (const entry of balanceEntries) {
                if (entry && entry.add !== undefined && entry.add !== null) {
                    const addValue = window.DashboardCore.Utils.Number.parseNumber(entry.add);
                    totalAddAmount += addValue;
                }
            }

            const firstBalance = window.DashboardCore.Utils.Number.parseNumber(firstEntry.balance);
            const lastBalance = window.DashboardCore.Utils.Number.parseNumber(lastEntry.balance);
            const adjustedFirstBalance = firstBalance + totalAddAmount;

            return {
                firstEntry,
                lastEntry,
                firstBalance: adjustedFirstBalance,
                lastBalance,
                totalAddAmount,
                balanceEntries
            };
        }, null, '计算项目历史数据');
    },

    /**
     * 计算项目在指定时间范围内的历史记录数据
     * @param {Object} project - 项目对象
     * @param {Map} priceMap - 价格映射
     * @param {string} startDate - 开始日期
     * @param {string} endDate - 结束日期
     * @returns {Object|null} 时间范围内的历史数据对象
     */
    calculateProjectHistoryInTimeRange(project, priceMap, startDate, endDate) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (!window.DashboardCore.Utils.Validation.isValidProject(project)) {
                return null;
            }

            const balanceEntries = project.file.lists.filter(item =>
                window.DashboardCore.Utils.Validation.isValidBalanceEntry(item)
            );

            if (balanceEntries.length === 0) return null;

            // 按日期排序
            balanceEntries.sort((a, b) => {
                const dateA = window.DashboardCore.Utils.Date.parseDate(a.date);
                const dateB = window.DashboardCore.Utils.Date.parseDate(b.date);
                if (!dateA || !dateB) return 0;
                return dateA.toMillis() - dateB.toMillis();
            });

            // 只处理时间范围内有数据的情况
            const entriesInRange = balanceEntries.filter(entry => {
                const entryDate = window.DashboardCore.Utils.Date.parseDate(entry.date);
                if (!entryDate) return false;

                const entryDateStr = window.DashboardCore.Utils.Date.formatDate(entryDate, 'yyyy-MM-dd');
                return entryDateStr >= startDate && entryDateStr <= endDate;
            });

            // 如果时间范围内没有记录，直接返回null
            if (entriesInRange.length === 0) return null;

            const firstEntry = entriesInRange[0];
            const lastEntry = entriesInRange.length === 1 ? entriesInRange[0] : entriesInRange[entriesInRange.length - 1];

            // 计算时间范围内所有add属性的总和
            let totalAddAmount = 0;
            for (const entry of entriesInRange) {
                if (entry && entry.add !== undefined && entry.add !== null) {
                    const addValue = window.DashboardCore.Utils.Number.parseNumber(entry.add);
                    totalAddAmount += addValue;
                }
            }

            const firstBalance = window.DashboardCore.Utils.Number.parseNumber(firstEntry.balance);
            const lastBalance = window.DashboardCore.Utils.Number.parseNumber(lastEntry.balance);
            const adjustedFirstBalance = firstBalance + totalAddAmount;

            return {
                firstEntry,
                lastEntry,
                firstBalance: adjustedFirstBalance,
                lastBalance,
                totalAddAmount,
                balanceEntries: entriesInRange
            };
        }, null, '计算时间范围内项目历史数据');
    },

    /**
     * 计算APR
     * @param {Object} historyData - 历史数据对象
     * @returns {number} APR值
     */
    calculateAPR(historyData) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (!historyData) return 0;

            const firstDate = window.DashboardCore.Utils.Date.parseDate(historyData.firstEntry.date);
            const lastDate = window.DashboardCore.Utils.Date.parseDate(historyData.lastEntry.date);

            if (!firstDate || !lastDate) return 0;

            const daysDiff = window.DashboardCore.Utils.Date.daysBetween(firstDate, lastDate);
            const totalEarned = historyData.lastBalance - historyData.firstBalance;
            const baseAmount = historyData.firstBalance;

            if (baseAmount > 0) {
                const returnRate = totalEarned / baseAmount;
                return Math.round(((returnRate / daysDiff) * 365) * 100 * 100) / 100;
            }

            return 0;
        }, 0, '计算APR');
    },

    /**
     * 计算投资天数
     * @param {Object} historyData - 历史数据对象
     * @returns {number} 投资天数
     */
    calculateInvestDays(historyData) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (!historyData) return 0;

            const firstDate = window.DashboardCore.Utils.Date.parseDate(historyData.firstEntry.date);
            const lastDate = window.DashboardCore.Utils.Date.parseDate(historyData.lastEntry.date);

            if (!firstDate || !lastDate) return 0;

            const daysDiff = window.DashboardCore.Utils.Date.daysBetween(firstDate, lastDate);
            return Math.round(daysDiff * 100) / 100;
        }, 0, '计算投资天数');
    },

    /**
     * 计算日期维度的投资天数（使用项目完整历史的第一个日期）
     * @param {Object} project - 项目对象
     * @param {string} currentDate - 当前日期
     * @returns {number} 投资天数
     */
    calculateInvestDaysForDateView(project, currentDate) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (!window.DashboardCore.Utils.Validation.isValidProject(project)) {
                return 0;
            }

            // 获取项目的所有balance记录
            const balanceEntries = project.file.lists.filter(item =>
                window.DashboardCore.Utils.Validation.isValidBalanceEntry(item)
            );

            if (balanceEntries.length === 0) return 0;

            // 按日期排序，获取最早的记录
            balanceEntries.sort((a, b) => {
                const dateA = window.DashboardCore.Utils.Date.parseDate(a.date);
                const dateB = window.DashboardCore.Utils.Date.parseDate(b.date);
                if (!dateA || !dateB) return 0;
                return dateA.toMillis() - dateB.toMillis();
            });

            const firstDate = window.DashboardCore.Utils.Date.parseDate(balanceEntries[0].date);
            const endDate = window.DashboardCore.Utils.Date.parseDate(currentDate);

            if (!firstDate || !endDate) return 0;

            const daysDiff = window.DashboardCore.Utils.Date.daysBetween(firstDate, endDate);
            return Math.round(daysDiff * 100) / 100;
        }, 0, '计算日期维度投资天数');
    }
};

/**
 * USDT 转换和价格计算器
 */
window.DashboardCore.USDTCalculator = {
    /**
     * 计算USDT投入金额（优化版：正确处理追加投资）
     * @param {Object} historyData - 历史数据对象
     * @param {string} unit - 代币单位
     * @param {Map} priceMap - 价格映射
     * @returns {Object} USDT金额对象
     */
    calculateUSDTAmount(historyData, unit, priceMap) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (!historyData) {
                return { investAmount: 0, withdrawAmount: 0, priceFound: false };
            }

            // 对于稳定币，直接返回余额值
            if (window.DashboardCore.isStableCoin(unit)) {
                const firstBalance = window.DashboardCore.Utils.Number.parseNumber(historyData.firstEntry.balance);
                const lastBalance = historyData.lastBalance;
                const totalAddAmount = historyData.totalAddAmount;

                return {
                    investAmount: firstBalance + totalAddAmount,
                    withdrawAmount: lastBalance,
                    priceFound: true,
                    investDate: window.DashboardCore.Utils.Date.formatDate(
                        window.DashboardCore.Utils.Date.parseDate(historyData.firstEntry.date), 'yyyy-MM-dd'
                    ),
                    withdrawDate: window.DashboardCore.Utils.Date.formatDate(
                        window.DashboardCore.Utils.Date.parseDate(historyData.lastEntry.date), 'yyyy-MM-dd'
                    )
                };
            }

            // 获取价格数据
            const prices = priceMap.get(unit);
            if (!prices) {
                return { investAmount: -1, withdrawAmount: -1, priceFound: false };
            }

            // 计算总投资成本（分别计算每笔投资的USDT价值）
            let totalInvestAmount = 0;
            let allPricesFound = true;

            // 按日期排序的历史记录
            const sortedEntries = [...historyData.balanceEntries].sort((a, b) => {
                const dateA = window.DashboardCore.Utils.Date.parseDate(a.date);
                const dateB = window.DashboardCore.Utils.Date.parseDate(b.date);
                return dateA.toMillis() - dateB.toMillis();
            });

            // 处理初始投资
            const firstEntry = sortedEntries[0];
            const firstDate = window.DashboardCore.Utils.Date.formatDate(
                window.DashboardCore.Utils.Date.parseDate(firstEntry.date), 'yyyy-MM-dd'
            );
            const firstBalance = window.DashboardCore.Utils.Number.parseNumber(firstEntry.balance);

            if (prices.has(firstDate)) {
                const firstPrice = prices.get(firstDate);
                totalInvestAmount += firstBalance * firstPrice;
            } else {
                allPricesFound = false;
            }

            // 处理追加投资
            for (const entry of sortedEntries) {
                if (entry.add !== undefined && entry.add !== null) {
                    const addAmount = window.DashboardCore.Utils.Number.parseNumber(entry.add);
                    if (addAmount > 0) {
                        const addDate = window.DashboardCore.Utils.Date.formatDate(
                            window.DashboardCore.Utils.Date.parseDate(entry.date), 'yyyy-MM-dd'
                        );
                        if (prices.has(addDate)) {
                            const addPrice = prices.get(addDate);
                            totalInvestAmount += addAmount * addPrice;
                        } else {
                            allPricesFound = false;
                        }
                    }
                }
            }

            // 计算当前价值（使用最新日期的价格）
            const lastDate = window.DashboardCore.Utils.Date.formatDate(
                window.DashboardCore.Utils.Date.parseDate(historyData.lastEntry.date), 'yyyy-MM-dd'
            );
            let withdrawAmount = -1;

            if (prices.has(lastDate)) {
                const lastPrice = prices.get(lastDate);
                withdrawAmount = historyData.lastBalance * lastPrice;
            } else {
                allPricesFound = false;
            }

            return {
                investAmount: allPricesFound ? totalInvestAmount : -1,
                withdrawAmount: allPricesFound ? withdrawAmount : -1,
                priceFound: allPricesFound,
                investDate: firstDate,
                withdrawDate: lastDate
            };
        }, { investAmount: -1, withdrawAmount: -1, priceFound: false }, '计算USDT金额');
    },

    /**
     * 计算指定时间范围内的收益变化（用于日期维度的总收益列）
     * @param {Object} project - 项目对象
     * @param {Map} priceMap - 价格映射
     * @param {string} startDate - 开始日期
     * @param {string} endDate - 结束日期
     * @param {string} unit - 代币单位
     * @returns {Object} 时间范围收益对象
     */
    calculateTimeRangeEarnings(project, priceMap, startDate, endDate, unit) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (!window.DashboardCore.Utils.Validation.isValidProject(project)) {
                return { timeRangeEarnings: 0, priceFound: true };
            }

            // 获取所有balance记录并按日期排序
            const balanceEntries = project.file.lists.filter(item =>
                window.DashboardCore.Utils.Validation.isValidBalanceEntry(item)
            );

            if (balanceEntries.length === 0) {
                return { timeRangeEarnings: 0, priceFound: true };
            }

            balanceEntries.sort((a, b) => {
                const dateA = window.DashboardCore.Utils.Date.parseDate(a.date);
                const dateB = window.DashboardCore.Utils.Date.parseDate(b.date);
                if (!dateA || !dateB) return 0;
                return dateA.toMillis() - dateB.toMillis();
            });

            // 找到时间范围开始前的最近记录作为起始余额
            let startBalance = 0;
            let startBalanceFound = false;

            // 找到时间范围开始前的最后一条记录
            for (let i = balanceEntries.length - 1; i >= 0; i--) {
                const entry = balanceEntries[i];
                const entryDate = window.DashboardCore.Utils.Date.parseDate(entry.date);
                if (entryDate && window.DashboardCore.Utils.Date.formatDate(entryDate, 'yyyy-MM-dd') < startDate) {
                    startBalance = window.DashboardCore.Utils.Number.parseNumber(entry.balance);
                    startBalanceFound = true;
                    break;
                }
            }

            // 找到时间范围内的记录
            const entriesInRange = balanceEntries.filter(entry => {
                const entryDate = window.DashboardCore.Utils.Date.parseDate(entry.date);
                if (!entryDate) return false;
                const entryDateStr = window.DashboardCore.Utils.Date.formatDate(entryDate, 'yyyy-MM-dd');
                return entryDateStr >= startDate && entryDateStr <= endDate;
            });

            // 如果时间范围内没有记录，收益为0
            if (entriesInRange.length === 0) {
                return { timeRangeEarnings: 0, priceFound: true };
            }

            // 如果时间范围开始前没有记录，说明项目在这个时间范围内开始
            if (!startBalanceFound) {
                // 使用时间范围内第一条记录的余额作为起始（假设这是初始投资）
                startBalance = window.DashboardCore.Utils.Number.parseNumber(entriesInRange[0].balance);
                // 如果第一条记录有add字段，需要减去，因为这是追加投资
                if (entriesInRange[0].add !== undefined) {
                    startBalance -= window.DashboardCore.Utils.Number.parseNumber(entriesInRange[0].add);
                }
            }

            // 获取时间范围内的最后余额
            const endBalance = window.DashboardCore.Utils.Number.parseNumber(
                entriesInRange[entriesInRange.length - 1].balance
            );

            // 计算时间范围内的追加投资
            let addAmountInRange = 0;
            for (const entry of entriesInRange) {
                if (entry.add !== undefined && entry.add !== null) {
                    addAmountInRange += window.DashboardCore.Utils.Number.parseNumber(entry.add);
                }
            }

            // 计算余额变化（不包括追加投资）
            const balanceChange = endBalance - startBalance - addAmountInRange;

            // 转换为USDT价值
            if (window.DashboardCore.isStableCoin(unit)) {
                return { timeRangeEarnings: balanceChange, priceFound: true };
            }

            // 对于其他代币，需要价格转换
            const prices = priceMap.get(unit);
            if (!prices) {
                return { timeRangeEarnings: -1, priceFound: false };
            }

            // 使用时间范围结束日期的价格
            const endDate_parsed = window.DashboardCore.Utils.Date.parseDate(
                entriesInRange[entriesInRange.length - 1].date
            );
            const endDateStr = window.DashboardCore.Utils.Date.formatDate(endDate_parsed, 'yyyy-MM-dd');

            if (prices.has(endDateStr)) {
                const price = prices.get(endDateStr);
                return { timeRangeEarnings: balanceChange * price, priceFound: true };
            } else {
                return { timeRangeEarnings: -1, priceFound: false };
            }
        }, { timeRangeEarnings: 0, priceFound: false }, '计算时间范围收益');
    }
};

/**
 * 衍生指标计算器
 */
window.DashboardCore.DerivedCalculator = {
    /**
     * 基于USDT价值计算APR（优化版）
     * @param {Object} usdtAmounts - USDT金额对象
     * @param {number} investDays - 投资天数
     * @returns {number} APR值
     */
    calculateUSDTBasedAPR(usdtAmounts, investDays) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (!usdtAmounts || usdtAmounts.investAmount === -1 || usdtAmounts.withdrawAmount === -1 || investDays <= 0) {
                return 0;
            }

            const totalEarned = usdtAmounts.withdrawAmount - usdtAmounts.investAmount;
            const baseAmount = usdtAmounts.investAmount;

            if (baseAmount > 0) {
                const returnRate = totalEarned / baseAmount;
                return Math.round(((returnRate / investDays) * 365) * 100 * 100) / 100;
            }

            return 0;
        }, 0, '计算USDT基础APR');
    },

    /**
     * 计算仓位状态和建议操作（合并仓位等级显示）
     * @param {number} investAmount - 投资金额
     * @param {number} level - 投资等级
     * @param {Object} levelInfo - 等级信息
     * @returns {Object} 仓位状态对象
     */
    calculatePositionStatus(investAmount, level, levelInfo) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (!levelInfo || !levelInfo[level]) {
                return {
                    positionStatus: "配置未找到",
                    suggestedAction: "检查配置"
                };
            }

            const limitAmount = levelInfo[level].limit || 0;
            const levelText = levelInfo[level].cn || `等级${level}`;

            if (investAmount === -1) {
                return {
                    positionStatus: `${levelText} <span style="color: #6c757d;">⚪ 价格未找到</span>`,
                    suggestedAction: "价格数据未找到"
                };
            }

            const diff = investAmount - limitAmount;
            const threshold = limitAmount * window.DashboardCore.getThreshold('POSITION', 'NORMAL_RATIO');

            if (investAmount === 0) {
                return {
                    positionStatus: `${levelText} <span style="color: #6c757d;">⚪ 未配置</span>`,
                    suggestedAction: `加仓 ${window.DashboardCore.Utils.Number.formatNumber(limitAmount)}`
                };
            } else if (Math.abs(diff) <= threshold) {
                return {
                    positionStatus: `${levelText} <span style="color: #28a745;">🟢 正常</span>`,
                    suggestedAction: "保持"
                };
            } else if (diff > threshold) {
                return {
                    positionStatus: `${levelText} <span style="color: #dc3545;">🔴 超配</span>`,
                    suggestedAction: `减仓 ${window.DashboardCore.Utils.Number.formatNumber(Math.abs(diff))}`
                };
            } else {
                return {
                    positionStatus: `${levelText} <span style="color: #ffc107;">🟡 低配</span>`,
                    suggestedAction: `加仓 ${window.DashboardCore.Utils.Number.formatNumber(Math.abs(diff))}`
                };
            }
        }, { positionStatus: "计算错误", suggestedAction: "检查数据" }, '计算仓位状态');
    },

    /**
     * 计算APR Gap
     * @param {number} currentApr - 当前APR
     * @param {number} expectApr - 期望APR
     * @param {boolean} hasInvestment - 是否有投资
     * @returns {number} APR Gap值
     */
    calculateAPRGap(currentApr, expectApr, hasInvestment = true) {
        if (!hasInvestment || expectApr === 0) return 0;
        const expectAprPercent = expectApr * 100;
        return currentApr - expectAprPercent;
    },

    /**
     * 计算组合APR
     * @param {number} currentPercent - 当前占比
     * @param {number} apr - APR值
     * @returns {number} 组合APR值
     */
    calculatePortfolioAPR(currentPercent, apr) {
        return currentPercent * apr;
    },

    /**
     * 计算总收益和日收益
     * @param {Object} usdtAmounts - USDT金额对象
     * @param {number} investDays - 投资天数
     * @returns {Object} 收益对象
     */
    calculateEarnings(usdtAmounts, investDays) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            if (usdtAmounts.investAmount === -1 || usdtAmounts.withdrawAmount === -1) {
                return {
                    totalEarned: -1,
                    dailyEarned: -1
                };
            }

            const totalEarned = usdtAmounts.withdrawAmount - usdtAmounts.investAmount;
            const dailyEarned = investDays > 0 ? totalEarned / investDays : 0;

            return {
                totalEarned,
                dailyEarned
            };
        }, { totalEarned: -1, dailyEarned: -1 }, '计算收益');
    }
};

/**
 * 业务逻辑处理器
 */
window.DashboardCore.BusinessLogic = {
    /**
     * 处理单个项目数据
     * @param {Object} project - 项目对象
     * @param {Object} riskInfo - 风险信息
     * @param {Object} levelInfo - 等级信息
     * @param {Map} priceMap - 价格映射
     * @returns {Object} 处理后的项目数据
     */
    processProject(project, riskInfo, levelInfo, priceMap) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const riskLevel = project.Risk;
            const riskText = (riskInfo[riskLevel] && riskInfo[riskLevel].text) ? riskInfo[riskLevel].text : `风险 ${riskLevel}`;
            const expectApr = (riskInfo[riskLevel] && riskInfo[riskLevel].expectApr) ? riskInfo[riskLevel].expectApr : 0;
            const unit = (project.Unit || 'USDT').toUpperCase();
            const level = project.Level || 0;

            // 获取仓位等级信息
            const levelText = (levelInfo && levelInfo[level]) ? levelInfo[level].text : `仓位 ${level}`;

            // 计算基础指标
            const historyData = window.DashboardCore.BaseCalculator.calculateProjectHistory(project, priceMap);
            const investDays = window.DashboardCore.BaseCalculator.calculateInvestDays(historyData);
            const usdtAmounts = window.DashboardCore.USDTCalculator.calculateUSDTAmount(historyData, unit, priceMap);

            // 使用优化的APR计算（基于USDT价值）
            const projectApr = window.DashboardCore.DerivedCalculator.calculateUSDTBasedAPR(usdtAmounts, investDays);

            // 计算衍生指标
            const positionInfo = window.DashboardCore.DerivedCalculator.calculatePositionStatus(usdtAmounts.investAmount, level, levelInfo);
            const earnings = window.DashboardCore.DerivedCalculator.calculateEarnings(usdtAmounts, investDays);

            // 设置投资和提取日期
            const investDate = usdtAmounts.investDate || "";
            const withdrawDate = project.Status === "Done" ? (usdtAmounts.withdrawDate || "") : "-";

            return {
                riskLevel,
                riskText,
                expectApr,
                unit,
                level,
                levelText,
                projectApr,
                investDays,
                amountInUsdt: usdtAmounts.priceFound ? usdtAmounts.investAmount : 0,

                // 基础数据
                usdtInvestAmount: usdtAmounts.investAmount,
                usdtWithdrawAmount: usdtAmounts.withdrawAmount,
                usdtTotalEarned: earnings.totalEarned,
                usdtDailyEarned: earnings.dailyEarned,

                // 状态信息
                positionStatus: positionInfo.positionStatus,
                suggestedAction: positionInfo.suggestedAction,

                // 其他信息
                type: project.Type || "-",
                investDate,
                withdrawDate,
                status: project.Status || "Doing",
                projectLink: project.file.link
            };
        }, null, '处理项目数据');
    },

    /**
     * 创建项目详情列表
     * @param {Array} projects - 项目数组
     * @param {Object} riskInfo - 风险信息
     * @param {Object} levelInfo - 等级信息
     * @param {Map} priceMap - 价格映射
     * @returns {Array} 项目详情数组
     */
    createProtocolDetails(projects, riskInfo, levelInfo, priceMap) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const protocolDetails = [];

            for (const project of projects) {
                const projectData = this.processProject(project, riskInfo, levelInfo, priceMap);

                if (projectData) {
                    // 直接添加项目数据，不再按协议拆分
                    protocolDetails.push({
                        protocol: project.Protocol || "-", // 保留协议字段以兼容现有代码
                        ...projectData
                    });
                }
            }

            return protocolDetails;
        }, [], '创建项目详情列表');
    }
};

console.log('Dashboard Calculator (Complete) module loaded');

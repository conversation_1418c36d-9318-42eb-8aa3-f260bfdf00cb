/**
 * Dashboard 数据加载模块
 * 
 * 负责从各种数据源加载和解析数据，包括：
 * - 风险配置文件
 * - 投资等级配置文件
 * - 价格数据文件
 * - 项目历史数据
 * 
 * @module DashboardDataLoader
 */

// 确保核心对象存在
window.DashboardCore = window.DashboardCore || {};

/**
 * 数据加载器
 */
window.DashboardCore.DataLoader = {
    /**
     * 解析风险配置文件
     * @param {string} path - 文件路径
     * @returns {Object|null} 风险配置对象
     */
    parseRiskFile(path) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const dv = window.DashboardCore.dv;
            const page = dv.page(path);
            
            if (!page || !page.file || !page.file.lists) {
                console.warn('风险配置文件未找到或格式错误:', path);
                return null;
            }
            
            const limits = {};
            for (const item of page.file.lists) {
                if (item.risk !== undefined) {
                    const riskLabel = item.cn ? item.cn.trim() : `风险 ${item.risk}`;
                    const percentLimit = item.percent ? parseFloat(item.percent) : 0;
                    const expectApr = item.expect_apr ? parseFloat(item.expect_apr) : 0;
                    
                    limits[item.risk] = { 
                        text: riskLabel, 
                        percent: percentLimit, 
                        expectApr: expectApr 
                    };
                }
            }
            
            return limits;
        }, null, '解析风险配置文件');
    },

    /**
     * 解析投资等级配置文件
     * @param {string} path - 文件路径
     * @returns {Object|null} 投资等级配置对象
     */
    parseLevelFile(path) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const dv = window.DashboardCore.dv;
            const page = dv.page(path);
            
            if (!page || !page.file || !page.file.lists) {
                console.warn('投资等级配置文件未找到或格式错误:', path);
                return null;
            }
            
            const limits = {};
            for (const item of page.file.lists) {
                if (item.level !== undefined) {
                    const levelLabel = item.cn ? item.cn.trim() : `仓位 ${item.level}`;
                    const limitAmount = item.limit ? parseFloat(item.limit) : 0;
                    
                    limits[item.level] = { 
                        text: levelLabel, 
                        limit: limitAmount, 
                        cn: levelLabel 
                    };
                }
            }
            
            return limits;
        }, null, '解析投资等级配置文件');
    },

    /**
     * 解析价格数据文件
     * @param {Object} pricesPage - 价格页面对象
     * @returns {Map} 价格映射 Map<token, Map<date, price>>
     */
    parsePriceData(pricesPage) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const priceMap = new Map();
            
            if (!pricesPage || !pricesPage.file || !pricesPage.file.lists) {
                console.warn('价格数据文件未找到或格式错误');
                return priceMap;
            }

            for (const item of pricesPage.file.lists) {
                let token = item.token ? item.token.trim().toUpperCase() : undefined;
                let date = item.date ? window.DashboardCore.dv.date(item.date).toFormat("yyyy-MM-dd") : undefined;
                let price = item.price ? parseFloat(item.price) : undefined;

                // Fallback to regex parsing if direct properties are not available
                if (token === undefined || date === undefined || price === undefined) {
                    const tokenMatch = item.text.match(/token:\s*(.*)/);
                    const dateMatch = item.text.match(/date:\s*(.*)/);
                    const priceMatch = item.text.match(/price:\s*(.*)/);

                    token = tokenMatch ? tokenMatch[1].trim().toUpperCase() : token;
                    date = dateMatch ? window.DashboardCore.dv.date(dateMatch[1].trim()).toFormat("yyyy-MM-dd") : date;
                    price = priceMatch ? parseFloat(priceMatch[1].trim()) : price;
                }

                if (token && date && price != null) {
                    if (!priceMap.has(token)) {
                        priceMap.set(token, new Map());
                    }
                    priceMap.get(token).set(date, price);
                }
            }

            // 确保稳定币价格映射存在
            const stableCoins = window.DashboardCore.getConfig('STABLE_COINS', ['USDT', 'USDC', 'DAI']);
            stableCoins.forEach(coin => {
                if (!priceMap.has(coin)) {
                    priceMap.set(coin, new Map());
                }
            });

            return priceMap;
        }, new Map(), '解析价格数据');
    },

    /**
     * 加载项目数据
     * @param {string} historyPath - 项目历史路径
     * @param {string} status - 项目状态过滤 ('Doing', 'Done', 'all')
     * @returns {Array} 项目数组
     */
    loadProjects(historyPath, status = 'all') {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const dv = window.DashboardCore.dv;
            let projects;
            
            if (status === 'all') {
                projects = dv.pages(historyPath);
            } else {
                projects = dv.pages(historyPath).where(p => p.Status === status);
            }
            
            return projects || [];
        }, [], '加载项目数据');
    },

    /**
     * 验证项目数据完整性
     * @param {Array} projects - 项目数组
     * @returns {Array} 有效的项目数组
     */
    validateProjects(projects) {
        return projects.filter(project => {
            if (!window.DashboardCore.Utils.Validation.isValidProject(project)) {
                console.warn('项目数据无效，已跳过:', project?.file?.name || 'unknown');
                return false;
            }
            
            // 检查是否有有效的余额记录
            const validEntries = project.file.lists.filter(entry => 
                window.DashboardCore.Utils.Validation.isValidBalanceEntry(entry)
            );
            
            if (validEntries.length === 0) {
                console.warn('项目无有效余额记录，已跳过:', project.file.name);
                return false;
            }
            
            return true;
        });
    },

    /**
     * 加载所有基础数据
     * @returns {Promise<Object>} 包含所有基础数据的对象
     */
    async loadAllData() {
        return window.DashboardCore.Utils.Error.logPerformance('加载所有数据', () => {
            const config = window.DashboardCore.Config || window.DashboardCore.DefaultConfig;
            
            // 加载配置数据
            const riskInfo = this.parseRiskFile(config.RISK_LIMIT_PATH);
            const levelInfo = this.parseLevelFile(config.LEVEL_LIMIT_PATH);
            
            // 加载价格数据
            const dv = window.DashboardCore.dv;
            const pricesPage = dv.page(config.PRICES_FILE_PATH);
            const priceMap = this.parsePriceData(pricesPage);
            
            // 加载项目数据
            const doingProjects = this.validateProjects(
                this.loadProjects(config.HISTORY_PATH, 'Doing')
            );
            const allProjects = this.validateProjects(
                this.loadProjects(config.HISTORY_PATH, 'all')
            );
            
            // 数据统计
            if (window.DashboardCore.getConfig('DEBUG.ENABLE_CONSOLE_LOG', false)) {
                console.log('数据加载完成:', {
                    riskLevels: Object.keys(riskInfo || {}).length,
                    investLevels: Object.keys(levelInfo || {}).length,
                    priceTokens: priceMap.size,
                    doingProjects: doingProjects.length,
                    allProjects: allProjects.length
                });
            }
            
            return {
                riskInfo: riskInfo || {},
                levelInfo: levelInfo || {},
                priceMap,
                doingProjects,
                allProjects
            };
        });
    },

    /**
     * 缓存管理
     */
    Cache: {
        _cache: new Map(),
        _cacheTimestamps: new Map(),

        /**
         * 获取缓存数据
         * @param {string} key - 缓存键
         * @returns {*} 缓存的数据或null
         */
        get(key) {
            if (!window.DashboardCore.getConfig('PERFORMANCE.ENABLE_CACHE', true)) {
                return null;
            }

            const timestamp = this._cacheTimestamps.get(key);
            const duration = window.DashboardCore.getConfig('PERFORMANCE.CACHE_DURATION', 5 * 60 * 1000);
            
            if (timestamp && (Date.now() - timestamp) < duration) {
                return this._cache.get(key);
            }
            
            // 清理过期缓存
            this.delete(key);
            return null;
        },

        /**
         * 设置缓存数据
         * @param {string} key - 缓存键
         * @param {*} data - 要缓存的数据
         */
        set(key, data) {
            if (!window.DashboardCore.getConfig('PERFORMANCE.ENABLE_CACHE', true)) {
                return;
            }

            this._cache.set(key, data);
            this._cacheTimestamps.set(key, Date.now());
        },

        /**
         * 删除缓存数据
         * @param {string} key - 缓存键
         */
        delete(key) {
            this._cache.delete(key);
            this._cacheTimestamps.delete(key);
        },

        /**
         * 清空所有缓存
         */
        clear() {
            this._cache.clear();
            this._cacheTimestamps.clear();
        }
    }
};

// 向后兼容的全局函数
window.DashboardDataLoader = window.DashboardCore.DataLoader;

console.log('Dashboard DataLoader module loaded');

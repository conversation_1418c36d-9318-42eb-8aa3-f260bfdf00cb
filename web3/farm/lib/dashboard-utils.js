/**
 * Dashboard 工具模块
 * 
 * 包含各种通用工具函数，如日期处理、数字格式化、颜色处理等
 * 
 * @module DashboardUtils
 */

// 确保核心对象存在
window.DashboardCore = window.DashboardCore || {};

/**
 * 工具函数集合
 */
window.DashboardCore.Utils = {
    /**
     * 日期处理工具
     */
    Date: {
        /**
         * 解析日期字符串
         * @param {string|Date} dateValue - 日期值
         * @returns {Date|null} 解析后的日期对象
         */
        parseDate(dateValue) {
            if (dateValue instanceof Date) return dateValue;
            if (!dateValue) return null;
            
            const dv = window.DashboardCore.dv;
            if (!dv) {
                console.error('Dataview 对象未初始化');
                return null;
            }
            
            const dateStr = dateValue.toString().trim();
            
            // 处理纯数字日期格式 (如: 20250603)
            if (/^\d{8}/.test(dateStr)) {
                const match = dateStr.match(/^(\d{4})(\d{2})(\d{2})/);
                if (match) {
                    const [, year, month, day] = match;
                    return dv.date(`${year}-${month}-${day}`);
                }
            }
            
            // 其他格式直接使用 dv.date()
            try {
                return dv.date(dateStr);
            } catch (e) {
                console.warn('日期解析失败:', dateStr, e);
                return null;
            }
        },

        /**
         * 格式化日期为指定格式
         * @param {Date} date - 日期对象
         * @param {string} format - 格式字符串
         * @returns {string} 格式化后的日期字符串
         */
        formatDate(date, format = 'yyyy-MM-dd') {
            if (!date || !date.toFormat) return '';
            try {
                return date.toFormat(format);
            } catch (e) {
                console.warn('日期格式化失败:', date, format, e);
                return '';
            }
        },

        /**
         * 计算两个日期之间的天数差
         * @param {Date} startDate - 开始日期
         * @param {Date} endDate - 结束日期
         * @returns {number} 天数差
         */
        daysBetween(startDate, endDate) {
            if (!startDate || !endDate) return 0;
            try {
                const timeDiff = endDate.toMillis() - startDate.toMillis();
                return Math.max(1, timeDiff / (1000 * 3600 * 24));
            } catch (e) {
                console.warn('计算日期差失败:', startDate, endDate, e);
                return 0;
            }
        }
    },

    /**
     * 数字格式化工具
     */
    Number: {
        /**
         * 格式化数字
         * @param {number} num - 数字
         * @param {number} decimals - 小数位数
         * @returns {string} 格式化后的数字字符串
         */
        formatNumber(num, decimals = 2) {
            if (typeof num !== 'number' || isNaN(num)) return '0';
            return num.toLocaleString(undefined, {
                minimumFractionDigits: decimals,
                maximumFractionDigits: decimals
            });
        },

        /**
         * 格式化百分比
         * @param {number} value - 数值
         * @param {number} decimals - 小数位数
         * @returns {string} 格式化后的百分比字符串
         */
        formatPercentage(value, decimals = 2) {
            if (typeof value !== 'number' || isNaN(value)) return '0%';
            return value !== 0 ? `${value.toFixed(decimals)}%` : "0%";
        },

        /**
         * 安全的数字解析
         * @param {*} value - 要解析的值
         * @param {number} defaultValue - 默认值
         * @returns {number} 解析后的数字
         */
        parseNumber(value, defaultValue = 0) {
            if (typeof value === 'number' && !isNaN(value)) return value;
            if (typeof value === 'string') {
                const parsed = parseFloat(value);
                return isNaN(parsed) ? defaultValue : parsed;
            }
            return defaultValue;
        }
    },

    /**
     * 颜色处理工具
     */
    Color: {
        /**
         * 获取风险等级颜色文本
         * @param {number} riskLevel - 风险等级
         * @param {string} riskText - 风险文本
         * @returns {string} 带颜色的HTML字符串
         */
        getRiskColoredText(riskLevel, riskText) {
            const risk = parseInt(riskLevel);
            let color = window.DashboardCore.getColor('VALUE', 'NEUTRAL');

            if (risk <= 1) {
                color = window.DashboardCore.getColor('RISK', 'LOW');
            } else if (risk === 2) {
                color = window.DashboardCore.getColor('RISK', 'MEDIUM');
            } else if (risk >= 3) {
                color = window.DashboardCore.getColor('RISK', 'HIGH');
            }

            return `<span style="color: ${color}; font-weight: bold;">${riskText}</span>`;
        },

        /**
         * 获取APR Gap颜色文本
         * @param {number} gapValue - Gap值
         * @returns {string} 带颜色的HTML字符串
         */
        getAPRGapColoredText(gapValue) {
            const gap = window.DashboardCore.Utils.Number.parseNumber(gapValue);
            const formattedValue = window.DashboardCore.Utils.Number.formatPercentage(gap);

            const excellentThreshold = window.DashboardCore.getThreshold('APR_GAP', 'EXCELLENT');
            const warningThreshold = window.DashboardCore.getThreshold('APR_GAP', 'WARNING');
            const dangerThreshold = window.DashboardCore.getThreshold('APR_GAP', 'DANGER');

            if (gap >= excellentThreshold) {
                const color = window.DashboardCore.getColor('APR_GAP', 'EXCELLENT');
                return `<span style="color: ${color};">${formattedValue}</span>`;
            } else if (gap >= warningThreshold) {
                return formattedValue; // 正常范围：使用默认颜色
            } else if (gap >= dangerThreshold) {
                const color = window.DashboardCore.getColor('APR_GAP', 'WARNING');
                return `<span style="color: ${color};">${formattedValue}</span>`;
            } else {
                const color = window.DashboardCore.getColor('APR_GAP', 'DANGER');
                return `<span style="color: ${color};">${formattedValue}</span>`;
            }
        },

        /**
         * 格式化带颜色的数字
         * @param {number} num - 数字
         * @param {number} decimals - 小数位数
         * @returns {string} 带颜色的HTML字符串
         */
        formatColoredNumber(num, decimals = 2) {
            const value = window.DashboardCore.Utils.Number.parseNumber(num);
            const formattedValue = window.DashboardCore.Utils.Number.formatNumber(value, decimals);

            if (value > 0) {
                const color = window.DashboardCore.getColor('VALUE', 'POSITIVE');
                return `<span style="color: ${color};">${formattedValue}</span>`;
            } else if (value < 0) {
                const color = window.DashboardCore.getColor('VALUE', 'NEGATIVE');
                return `<span style="color: ${color};">${formattedValue}</span>`;
            } else {
                return formattedValue;
            }
        },

        /**
         * 格式化带颜色的百分比
         * @param {number} value - 数值
         * @param {number} decimals - 小数位数
         * @returns {string} 带颜色的HTML字符串
         */
        formatColoredPercentage(value, decimals = 2) {
            const numValue = window.DashboardCore.Utils.Number.parseNumber(value);
            const formattedValue = window.DashboardCore.Utils.Number.formatPercentage(numValue, decimals);

            if (numValue > 0) {
                const color = window.DashboardCore.getColor('VALUE', 'POSITIVE');
                return `<span style="color: ${color};">${formattedValue}</span>`;
            } else if (numValue < 0) {
                const color = window.DashboardCore.getColor('VALUE', 'NEGATIVE');
                return `<span style="color: ${color};">${formattedValue}</span>`;
            } else {
                return formattedValue;
            }
        },

        /**
         * 格式化项目状态
         * @param {string} status - 项目状态
         * @returns {string} 带颜色的HTML字符串
         */
        formatProjectStatus(status) {
            const statusText = status || "Doing";

            if (statusText === "Doing") {
                const color = window.DashboardCore.getColor('STATUS', 'DOING');
                return `<span style="color: ${color};">🟢 ${statusText}</span>`;
            } else if (statusText === "Done") {
                const color = window.DashboardCore.getColor('STATUS', 'DONE');
                return `<span style="color: ${color};">⚪ ${statusText}</span>`;
            } else {
                return statusText;
            }
        }
    },

    /**
     * 数据验证工具
     */
    Validation: {
        /**
         * 验证项目数据完整性
         * @param {Object} project - 项目对象
         * @returns {boolean} 是否有效
         */
        isValidProject(project) {
            return project && 
                   project.file && 
                   project.file.lists && 
                   Array.isArray(project.file.lists) &&
                   project.file.lists.length > 0;
        },

        /**
         * 验证余额记录
         * @param {Object} entry - 余额记录
         * @returns {boolean} 是否有效
         */
        isValidBalanceEntry(entry) {
            return entry &&
                   entry.balance !== undefined &&
                   entry.date !== undefined &&
                   entry.date !== null &&
                   entry.date.toString().trim() !== "";
        },

        /**
         * 验证价格数据
         * @param {Map} priceMap - 价格映射
         * @param {string} token - 代币符号
         * @param {string} date - 日期
         * @returns {boolean} 是否有价格数据
         */
        hasPriceData(priceMap, token, date) {
            if (!priceMap || !token || !date) return false;
            const prices = priceMap.get(token.toUpperCase());
            return prices && prices.has(date);
        }
    },

    /**
     * 错误处理工具
     */
    Error: {
        /**
         * 安全执行函数
         * @param {Function} fn - 要执行的函数
         * @param {*} defaultValue - 默认返回值
         * @param {string} context - 上下文信息
         * @returns {*} 执行结果或默认值
         */
        safeExecute(fn, defaultValue = null, context = '') {
            try {
                return fn();
            } catch (error) {
                if (window.DashboardCore.getConfig('DEBUG.ENABLE_ERROR_DETAILS', true)) {
                    console.error(`执行失败 [${context}]:`, error);
                }
                return defaultValue;
            }
        },

        /**
         * 记录性能日志
         * @param {string} operation - 操作名称
         * @param {Function} fn - 要执行的函数
         * @returns {*} 执行结果
         */
        logPerformance(operation, fn) {
            if (!window.DashboardCore.getConfig('DEBUG.ENABLE_PERFORMANCE_LOG', false)) {
                return fn();
            }

            const startTime = performance.now();
            const result = fn();
            const endTime = performance.now();
            
            console.log(`性能日志 [${operation}]: ${(endTime - startTime).toFixed(2)}ms`);
            return result;
        }
    }
};

// 向后兼容的全局函数
window.DashboardUtils = window.DashboardCore.Utils;

console.log('Dashboard Utils module loaded');

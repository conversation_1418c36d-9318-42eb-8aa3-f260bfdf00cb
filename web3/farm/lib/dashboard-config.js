/**
 * Dashboard 配置模块
 * 
 * 包含所有配置常量、默认值和设置项
 * 
 * @module DashboardConfig
 */

// 确保核心对象存在
window.DashboardCore = window.DashboardCore || {};

/**
 * 默认配置
 */
window.DashboardCore.DefaultConfig = {
    // 数据路径配置
    HISTORY_PATH: '"web3/farm/history"',
    RISK_LIMIT_PATH: 'web3/farm/data/risk_percentage',
    PRICES_FILE_PATH: 'web3/farm/data/token_prices',
    LEVEL_LIMIT_PATH: 'web3/farm/data/invest_level_limit',
    
    // 显示配置
    ITEMS_PER_PAGE: 10,
    MAX_PAGES: 5,
    
    // 表格样式配置
    TABLE_MAX_HEIGHT: '80vh',
    TABLE_FONT_SIZE: '14px',
    TABLE_BORDER_RADIUS: '6px',
    
    // 计算精度配置
    DECIMAL_PLACES: 2,
    PERCENTAGE_DECIMAL_PLACES: 2,
    
    // 颜色主题配置
    COLORS: {
        // 风险等级颜色
        RISK: {
            LOW: '#28a745',      // 绿色：无风险、低风险 (0-1)
            MEDIUM: '#ffc107',   // 黄色：中风险 (2)
            HIGH: '#dc3545'      // 红色：高风险、极高风险 (3+)
        },
        
        // APR Gap 颜色
        APR_GAP: {
            EXCELLENT: '#28a745',  // 绿色：gap >= 10%
            GOOD: '#000000',       // 黑色：-10% <= gap < 10%
            WARNING: '#ffc107',    // 黄色：-20% <= gap < -10%
            DANGER: '#dc3545'      // 红色：gap < -20%
        },
        
        // 正负数颜色
        VALUE: {
            POSITIVE: '#28a745',   // 绿色：正数
            NEGATIVE: '#dc3545',   // 红色：负数
            NEUTRAL: '#000000'     // 黑色：零值
        },
        
        // 项目状态颜色
        STATUS: {
            DOING: '#28a745',      // 绿色：进行中
            DONE: '#6c757d'        // 灰色：已完成
        },
        
        // 仓位状态颜色
        POSITION: {
            NORMAL: '#28a745',     // 绿色：正常
            OVER: '#dc3545',       // 红色：超配
            UNDER: '#ffc107',      // 黄色：低配
            EMPTY: '#6c757d'       // 灰色：未配置
        }
    },
    
    // APR Gap 阈值配置
    APR_GAP_THRESHOLDS: {
        EXCELLENT: 10,    // >= 10% 显示绿色
        WARNING: -10,     // >= -10% 显示默认颜色
        DANGER: -20       // >= -20% 显示黄色，< -20% 显示红色
    },
    
    // 仓位状态阈值配置
    POSITION_THRESHOLDS: {
        NORMAL_RATIO: 0.2  // 20% 的阈值范围
    },
    
    // 稳定币列表
    STABLE_COINS: ['USDT', 'USDC', 'DAI'],
    
    // 日期格式配置
    DATE_FORMATS: {
        INPUT: 'yyyyMMdd',           // 输入格式
        INTERNAL: 'yyyy-MM-dd',      // 内部处理格式
        DISPLAY_MONTH: 'yyyy年M月',   // 月份显示格式
        DISPLAY_WEEK: 'yyyy年第W周',  // 周显示格式
        DISPLAY_YEAR: 'yyyy年'       // 年份显示格式
    },
    
    // 表格列配置
    COLUMN_CONFIG: {
        RISK_VIEW: {
            STICKY_COLUMNS: 1,  // 固定列数
            MIN_COLUMN_WIDTH: '80px'
        },
        DATE_VIEW: {
            STICKY_COLUMNS: 1,  // 固定列数
            MIN_COLUMN_WIDTH: '80px'
        }
    },
    
    // 性能配置
    PERFORMANCE: {
        ENABLE_CACHE: true,
        CACHE_DURATION: 5 * 60 * 1000,  // 5分钟缓存
        MAX_PROJECTS: 1000,              // 最大项目数
        BATCH_SIZE: 50                   // 批处理大小
    },
    
    // 调试配置
    DEBUG: {
        ENABLE_CONSOLE_LOG: false,
        ENABLE_PERFORMANCE_LOG: false,
        ENABLE_ERROR_DETAILS: true
    }
};

/**
 * 获取配置值
 * @param {string} path - 配置路径，支持点号分隔
 * @param {*} defaultValue - 默认值
 * @returns {*} 配置值
 */
window.DashboardCore.getConfig = function(path, defaultValue = null) {
    const config = window.DashboardCore.Config || window.DashboardCore.DefaultConfig;
    const keys = path.split('.');
    let value = config;
    
    for (const key of keys) {
        if (value && typeof value === 'object' && key in value) {
            value = value[key];
        } else {
            return defaultValue;
        }
    }
    
    return value;
};

/**
 * 设置配置值
 * @param {string} path - 配置路径，支持点号分隔
 * @param {*} value - 配置值
 */
window.DashboardCore.setConfig = function(path, value) {
    window.DashboardCore.Config = window.DashboardCore.Config || { ...window.DashboardCore.DefaultConfig };
    const keys = path.split('.');
    let config = window.DashboardCore.Config;
    
    for (let i = 0; i < keys.length - 1; i++) {
        const key = keys[i];
        if (!config[key] || typeof config[key] !== 'object') {
            config[key] = {};
        }
        config = config[key];
    }
    
    config[keys[keys.length - 1]] = value;
};

/**
 * 获取颜色配置
 * @param {string} type - 颜色类型 (RISK, APR_GAP, VALUE, STATUS, POSITION)
 * @param {string} level - 颜色级别
 * @returns {string} 颜色值
 */
window.DashboardCore.getColor = function(type, level) {
    return window.DashboardCore.getConfig(`COLORS.${type}.${level}`, '#000000');
};

/**
 * 获取阈值配置
 * @param {string} type - 阈值类型
 * @param {string} level - 阈值级别
 * @returns {number} 阈值
 */
window.DashboardCore.getThreshold = function(type, level) {
    return window.DashboardCore.getConfig(`${type}_THRESHOLDS.${level}`, 0);
};

/**
 * 检查是否为稳定币
 * @param {string} token - 代币符号
 * @returns {boolean} 是否为稳定币
 */
window.DashboardCore.isStableCoin = function(token) {
    const stableCoins = window.DashboardCore.getConfig('STABLE_COINS', []);
    return stableCoins.includes(token.toUpperCase());
};

/**
 * 获取日期格式
 * @param {string} type - 格式类型
 * @returns {string} 日期格式字符串
 */
window.DashboardCore.getDateFormat = function(type) {
    return window.DashboardCore.getConfig(`DATE_FORMATS.${type}`, 'yyyy-MM-dd');
};

console.log('Dashboard Config module loaded');

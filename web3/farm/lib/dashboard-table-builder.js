/**
 * Dashboard 表格构建模块
 * 
 * 负责构建各种维度的表格数据：
 * - 风险维度表格
 * - 日期维度表格
 * - 表格数据汇总和计算
 * 
 * @module DashboardTableBuilder
 */

// 确保核心对象存在
window.DashboardCore = window.DashboardCore || {};

/**
 * 表格构建器
 */
window.DashboardCore.TableBuilder = {
    /**
     * 构建风险维度表格
     * @param {Object} data - 基础数据对象
     * @returns {Object} 表格结果对象 {headers, data}
     */
    buildRiskTable(data) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const { riskInfo, levelInfo, priceMap, doingProjects } = data;
            
            if (!doingProjects || doingProjects.length === 0) {
                return {
                    headers: ['风险等级', '投入金额', '当前占比', '健康程度', '项目'],
                    data: [['暂无数据', '', '', '', '']]
                };
            }

            // 处理项目数据
            const protocolDetails = window.DashboardCore.BusinessLogic.createProtocolDetails(
                doingProjects, riskInfo, levelInfo, priceMap
            );

            if (protocolDetails.length === 0) {
                return {
                    headers: ['风险等级', '投入金额', '当前占比', '健康程度', '项目'],
                    data: [['暂无有效数据', '', '', '', '']]
                };
            }

            // 计算汇总数据
            const summary = this.calculateRiskSummary(protocolDetails, riskInfo);
            const totalAmountInUsdt = Object.values(summary.amountByRisk).reduce((sum, amount) => sum + amount, 0);

            // 构建表头
            const headers = [
                '风险等级', '投入金额', '当前占比', '健康程度', '项目',
                '仓位状态', '建议操作', '总收益', '投资天数', '日收益',
                '当前APR', '期望APR', 'APR Gap', '组合当前APR', '组合期望APR', '组合APR Gap'
            ];

            // 构建表格数据
            const tableData = [];
            
            // 按风险等级排序
            const sortedRiskLevels = Object.keys(riskInfo).sort((a, b) => parseInt(a) - parseInt(b));

            for (const riskLevel of sortedRiskLevels) {
                // 添加风险等级汇总行
                const summaryRow = this.buildRiskSummaryRow(riskLevel, riskInfo, summary, totalAmountInUsdt);
                tableData.push(summaryRow);

                // 添加该风险等级下的项目详情行
                const projectsInThisRisk = protocolDetails.filter(p => p.riskLevel == riskLevel);
                
                // 按项目名称排序
                projectsInThisRisk.sort((a, b) => {
                    const nameA = a.projectLink && a.projectLink.path ? a.projectLink.path.split('/').pop() : '';
                    const nameB = b.projectLink && b.projectLink.path ? b.projectLink.path.split('/').pop() : '';
                    return nameA.localeCompare(nameB);
                });

                for (const project of projectsInThisRisk) {
                    const projectRow = this.buildProjectRow(project, totalAmountInUsdt);
                    tableData.push(projectRow);
                }
            }

            // 添加总计行
            const totalRow = this.buildTotalRow(summary, riskInfo, totalAmountInUsdt);
            tableData.push(totalRow);

            return {
                headers,
                data: tableData
            };
        }, { headers: ['错误'], data: [['构建表格时出错']] }, '构建风险维度表格');
    },

    /**
     * 计算风险等级汇总数据
     * @param {Array} protocolDetails - 项目详情数组
     * @param {Object} riskInfo - 风险信息
     * @returns {Object} 汇总数据对象
     */
    calculateRiskSummary(protocolDetails, riskInfo) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const amountByRisk = {};
            const aprByRisk = {};
            const expectAprByRisk = {};
            const totalEarnedByRisk = {};
            const maxInvestDaysByRisk = {};
            const dailyEarnedByRisk = {};

            // 初始化所有风险等级
            for (const riskLevel in riskInfo) {
                amountByRisk[riskLevel] = 0;
                aprByRisk[riskLevel] = 0;
                expectAprByRisk[riskLevel] = riskInfo[riskLevel].expectApr || 0;
                totalEarnedByRisk[riskLevel] = 0;
                maxInvestDaysByRisk[riskLevel] = 0;
                dailyEarnedByRisk[riskLevel] = 0;
            }

            // 计算各风险等级的投入总额和收益数据
            for (const detail of protocolDetails) {
                if (amountByRisk[detail.riskLevel] !== undefined) {
                    amountByRisk[detail.riskLevel] += detail.amountInUsdt;

                    // 累计总收益（只有价格数据可用的项目）
                    if (detail.usdtTotalEarned !== -1) {
                        totalEarnedByRisk[detail.riskLevel] += detail.usdtTotalEarned;
                    }

                    // 记录最大投资天数
                    if (detail.investDays > maxInvestDaysByRisk[detail.riskLevel]) {
                        maxInvestDaysByRisk[detail.riskLevel] = detail.investDays;
                    }

                    // 累计日收益（只有价格数据可用的项目）
                    if (detail.usdtDailyEarned !== -1) {
                        dailyEarnedByRisk[detail.riskLevel] += detail.usdtDailyEarned;
                    }
                }
            }

            // 计算各风险等级的简单平均APR
            for (const riskLevel in amountByRisk) {
                const projectsInRisk = protocolDetails.filter(p => p.riskLevel == riskLevel);
                if (projectsInRisk.length > 0) {
                    const averageApr = projectsInRisk.reduce((sum, p) => sum + p.projectApr, 0) / projectsInRisk.length;
                    aprByRisk[riskLevel] = Math.round(averageApr * 100) / 100;
                }
            }

            return {
                amountByRisk,
                aprByRisk,
                expectAprByRisk,
                totalEarnedByRisk,
                maxInvestDaysByRisk,
                dailyEarnedByRisk
            };
        }, {}, '计算风险等级汇总');
    },

    /**
     * 构建风险等级汇总行
     * @param {string} riskLevel - 风险等级
     * @param {Object} riskInfo - 风险信息
     * @param {Object} summary - 汇总数据
     * @param {number} totalAmountInUsdt - 总投入金额
     * @returns {Array} 表格行数据
     */
    buildRiskSummaryRow(riskLevel, riskInfo, summary, totalAmountInUsdt) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const invested = summary.amountByRisk[riskLevel] || 0;
            const currentPercent = totalAmountInUsdt > 0 ? (invested / totalAmountInUsdt) : 0;
            const limitPercent = riskInfo[riskLevel].percent;
            const currentApr = summary.aprByRisk[riskLevel] || 0;
            const expectApr = summary.expectAprByRisk[riskLevel] || 0;

            // 获取聚合的收益数据
            const totalEarned = summary.totalEarnedByRisk[riskLevel] || 0;
            const maxInvestDays = summary.maxInvestDaysByRisk[riskLevel] || 0;
            const dailyEarned = summary.dailyEarnedByRisk[riskLevel] || 0;

            // 计算各种APR指标
            const expectAprPercent = expectApr * 100;
            const aprGap = (invested > 0 && expectAprPercent > 0) ?
                window.DashboardCore.DerivedCalculator.calculateAPRGap(currentApr, expectApr, true) : 0;
            const portfolioCurrentApr = window.DashboardCore.DerivedCalculator.calculatePortfolioAPR(currentPercent, currentApr);
            const portfolioExpectApr = window.DashboardCore.DerivedCalculator.calculatePortfolioAPR(currentPercent, expectAprPercent);
            const portfolioAprGap = portfolioCurrentApr - portfolioExpectApr;

            // 计算状态文本
            let statusText;
            if (invested === 0) {
                statusText = `⚪ 未配置 (${(limitPercent * 100).toFixed(1)}%)`;
            } else {
                const diff = currentPercent - limitPercent;
                if (diff > 0.05) {
                    statusText = `🔴 超配 (${(limitPercent * 100).toFixed(1)}%)`;
                } else if (diff < -0.05) {
                    statusText = `🟡 低配 (${(limitPercent * 100).toFixed(1)}%)`;
                } else {
                    statusText = `🟢 正常 (${(limitPercent * 100).toFixed(1)}%)`;
                }
            }

            return [
                window.DashboardCore.Utils.Color.getRiskColoredText(riskLevel, riskInfo[riskLevel].text),
                `**${window.DashboardCore.Utils.Number.formatNumber(invested)}**`, // 投入金额（加粗）
                `**${window.DashboardCore.Utils.Number.formatPercentage(currentPercent * 100)}**`, // 当前占比（加粗）
                `**${statusText}**`, // 健康程度（加粗）
                "", // 项目列为空
                "", // 仓位状态（聚合行为空）
                "", // 建议操作（聚合行为空）
                // 总收益（带颜色和粗体）
                totalEarned !== 0 ?
                    (totalEarned > 0 ?
                        `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalEarned)}</span>` :
                        `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalEarned)}</span>`) :
                    "**0**",
                maxInvestDays > 0 ? `**${maxInvestDays.toFixed(2)}**` : "**0**", // 投资天数（加粗）
                // 日收益（带颜色和粗体）
                dailyEarned !== 0 ?
                    (dailyEarned > 0 ?
                        `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(dailyEarned)}</span>` :
                        `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(dailyEarned)}</span>`) :
                    "**0**",
                // APR相关列
                // 当前APR（带颜色和粗体）
                currentApr > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(currentApr)}</span>` :
                    currentApr < 0 ?
                        `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(currentApr)}</span>` :
                        `**${window.DashboardCore.Utils.Number.formatPercentage(currentApr)}**`,
                invested > 0 && expectApr > 0 ? `**${window.DashboardCore.Utils.Number.formatPercentage(expectAprPercent, 0)}**` : "**0%**", // 期望APR（加粗）
                invested > 0 && expectApr > 0 ?
                    window.DashboardCore.Utils.Color.getAPRGapColoredText(aprGap).replace('<span style="color:', '<span style="color:').replace(';">', '; font-weight: bold;">') :
                    "**0%**", // APR Gap（加粗）
                // 当前APR总比（带颜色和粗体）
                portfolioCurrentApr > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(portfolioCurrentApr)}</span>` :
                    portfolioCurrentApr < 0 ?
                        `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(portfolioCurrentApr)}</span>` :
                        `**${window.DashboardCore.Utils.Number.formatPercentage(portfolioCurrentApr)}**`,
                `**${window.DashboardCore.Utils.Number.formatPercentage(portfolioExpectApr)}**`, // 期望APR总比（加粗）
                invested > 0 && expectApr > 0 ?
                    window.DashboardCore.Utils.Color.getAPRGapColoredText(portfolioAprGap).replace('<span style="color:', '<span style="color:').replace(';">', '; font-weight: bold;">') :
                    "**0%**" // APR Gap总比（加粗）
            ];
        }, [], '构建风险等级汇总行');
    },

    /**
     * 构建项目详情行
     * @param {Object} project - 项目数据
     * @param {number} totalAmountInUsdt - 总投入金额
     * @returns {Array} 表格行数据
     */
    buildProjectRow(project, totalAmountInUsdt) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const projectPercent = totalAmountInUsdt > 0 ? (project.amountInUsdt / totalAmountInUsdt) : 0;
            const projectExpectApr = project.expectApr * 100;
            const projectAprGap = window.DashboardCore.DerivedCalculator.calculateAPRGap(project.projectApr, project.expectApr, project.amountInUsdt > 0);
            const projectPortfolioCurrentApr = window.DashboardCore.DerivedCalculator.calculatePortfolioAPR(projectPercent, project.projectApr);
            const projectPortfolioExpectApr = window.DashboardCore.DerivedCalculator.calculatePortfolioAPR(projectPercent, projectExpectApr);
            const projectPortfolioAprGap = projectPortfolioCurrentApr - projectPortfolioExpectApr;

            return [
                "", // 风险等级列为空
                window.DashboardCore.Utils.Number.formatNumber(project.amountInUsdt),
                window.DashboardCore.Utils.Number.formatPercentage(projectPercent * 100),
                "-", // 健康程度（项目行显示横杠）
                project.projectLink,
                // 调整顺序：仓位状态、建议操作、总收益、投资天数、日收益
                project.positionStatus || "-",
                project.suggestedAction || "-",
                project.usdtTotalEarned === -1 ? "价格数据未找到" :
                    (project.usdtTotalEarned !== 0 ? window.DashboardCore.Utils.Color.formatColoredNumber(project.usdtTotalEarned) : "0"), // 总收益（带颜色）
                project.investDays > 0 ? `${project.investDays.toFixed(2)}` : "0",
                project.usdtDailyEarned === -1 ? "价格数据未找到" :
                    (project.usdtDailyEarned !== 0 ? window.DashboardCore.Utils.Color.formatColoredNumber(project.usdtDailyEarned) : "0"), // 日收益（带颜色）
                // APR相关列
                window.DashboardCore.Utils.Color.formatColoredPercentage(project.projectApr), // 当前APR（带颜色）
                project.amountInUsdt > 0 && projectExpectApr > 0 ? window.DashboardCore.Utils.Number.formatPercentage(projectExpectApr, 0) : "0%",
                project.amountInUsdt > 0 && projectExpectApr > 0 ? window.DashboardCore.Utils.Color.getAPRGapColoredText(projectAprGap) : "0%",
                window.DashboardCore.Utils.Color.formatColoredPercentage(projectPortfolioCurrentApr), // 当前APR总比（带颜色）
                window.DashboardCore.Utils.Number.formatPercentage(projectPortfolioExpectApr),
                project.amountInUsdt > 0 && projectExpectApr > 0 ? window.DashboardCore.Utils.Color.getAPRGapColoredText(projectPortfolioAprGap) : "0%"
            ];
        }, [], '构建项目详情行');
    },

    /**
     * 构建总计行
     * @param {Object} summary - 汇总数据
     * @param {Object} riskInfo - 风险信息
     * @param {number} totalAmountInUsdt - 总投入金额
     * @returns {Array} 表格行数据
     */
    buildTotalRow(summary, riskInfo, totalAmountInUsdt) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            // 计算总计数据
            const totalTotalEarned = Object.values(summary.totalEarnedByRisk).reduce((sum, earned) => sum + earned, 0);
            const totalDailyEarned = Object.values(summary.dailyEarnedByRisk).reduce((sum, daily) => sum + daily, 0);
            const maxTotalInvestDays = Math.max(...Object.values(summary.maxInvestDaysByRisk));

            // 计算有项目的风险等级的平均APR
            const riskLevelsWithProjects = Object.keys(summary.amountByRisk).filter(level => summary.amountByRisk[level] > 0);
            const totalCurrentApr = riskLevelsWithProjects.length > 0 ?
                riskLevelsWithProjects.reduce((sum, level) => sum + summary.aprByRisk[level], 0) / riskLevelsWithProjects.length : 0;
            const totalExpectApr = riskLevelsWithProjects.length > 0 ?
                riskLevelsWithProjects.reduce((sum, level) => sum + (summary.expectAprByRisk[level] * 100), 0) / riskLevelsWithProjects.length : 0;

            const totalAprGap = totalCurrentApr - totalExpectApr;

            // 组合APR总比就是当前APR和期望APR（因为总占比是100%）
            const totalPortfolioCurrentApr = totalCurrentApr;
            const totalPortfolioExpectApr = totalExpectApr;
            const totalPortfolioAprGap = totalAprGap;

            return [
                `**总计**`, // 总计标识（加粗）
                `**${window.DashboardCore.Utils.Number.formatNumber(totalAmountInUsdt)}**`, // 总投入金额（加粗）
                `**100%**`, // 总占比（加粗）
                `**-**`, // 健康程度（加粗）
                "", // 项目列为空
                "", // 仓位状态（总计行为空）
                "", // 建议操作（总计行为空）
                // 总收益（带颜色和粗体）
                totalTotalEarned !== 0 ?
                    (totalTotalEarned > 0 ?
                        `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalTotalEarned)}</span>` :
                        `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalTotalEarned)}</span>`) :
                    "**0**",
                maxTotalInvestDays > 0 ? `**${maxTotalInvestDays.toFixed(2)}**` : "**0**", // 投资天数（加粗）
                // 日收益（带颜色和粗体）
                totalDailyEarned !== 0 ?
                    (totalDailyEarned > 0 ?
                        `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalDailyEarned)}</span>` :
                        `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalDailyEarned)}</span>`) :
                    "**0**",
                // APR相关列
                // 当前APR（带颜色和粗体）
                totalCurrentApr > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(totalCurrentApr)}</span>` :
                    totalCurrentApr < 0 ?
                        `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(totalCurrentApr)}</span>` :
                        `**${window.DashboardCore.Utils.Number.formatPercentage(totalCurrentApr)}**`,
                `**${window.DashboardCore.Utils.Number.formatPercentage(totalExpectApr, 0)}**`, // 期望APR（加粗）
                window.DashboardCore.Utils.Color.getAPRGapColoredText(totalAprGap).replace('<span style="color:', '<span style="color:').replace(';">', '; font-weight: bold;">'), // APR Gap（加粗）
                // 当前APR总比（带颜色和粗体）
                totalPortfolioCurrentApr > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(totalPortfolioCurrentApr)}</span>` :
                    totalPortfolioCurrentApr < 0 ?
                        `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(totalPortfolioCurrentApr)}</span>` :
                        `**${window.DashboardCore.Utils.Number.formatPercentage(totalPortfolioCurrentApr)}**`,
                `**${window.DashboardCore.Utils.Number.formatPercentage(totalPortfolioExpectApr)}**`, // 期望APR总比（加粗）
                window.DashboardCore.Utils.Color.getAPRGapColoredText(totalPortfolioAprGap).replace('<span style="color:', '<span style="color:').replace(';">', '; font-weight: bold;">') // APR Gap总比（加粗）
            ];
        }, [], '构建总计行');
    }
};

/**
 * 日期数据聚合器
 * 负责按不同时间粒度聚合项目数据
 */
window.DashboardCore.DateAggregator = {
    /**
     * 按项目提取日期数据
     */
    extractProjectDateData(projects) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const projectDateData = [];

            for (const project of projects) {
                if (!project.file || !project.file.lists || !Array.isArray(project.file.lists)) {
                    continue;
                }

                const balanceEntries = project.file.lists.filter(item =>
                    item &&
                    item.balance !== undefined &&
                    item.date !== undefined &&
                    item.date !== null &&
                    item.date.toString().trim() !== ""
                );

                if (balanceEntries.length === 0) continue;

                // 按日期排序
                balanceEntries.sort((a, b) => {
                    try {
                        const dateA = window.DashboardCore.Utils.Date.parseDate(a.date);
                        const dateB = window.DashboardCore.Utils.Date.parseDate(b.date);
                        if (!dateA || !dateB) return 0;
                        return dateA.toMillis() - dateB.toMillis();
                    } catch (e) {
                        return 0;
                    }
                });

                // 为每个日期条目添加项目信息
                for (const entry of balanceEntries) {
                    try {
                        const dateObj = window.DashboardCore.Utils.Date.parseDate(entry.date);
                        if (!dateObj) continue;

                        const dateStr = window.DashboardCore.Utils.Date.formatDate(dateObj, 'yyyy-MM-dd');

                        projectDateData.push({
                            project: project,
                            date: dateStr,
                            dateObj: dateObj,
                            balance: window.DashboardCore.Utils.Number.parseNumber(entry.balance),
                            add: entry.add ? window.DashboardCore.Utils.Number.parseNumber(entry.add) : 0,
                            remark: entry.remark || ""
                        });
                    } catch (e) {
                        console.warn('处理日期条目时出错:', e);
                    }
                }
            }

            return projectDateData;
        }, [], '提取项目日期数据');
    },

    /**
     * 按月聚合数据
     */
    aggregateByMonth(projectDateData) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const monthlyGroups = new Map();

            for (const item of projectDateData) {
                const monthKey = item.dateObj.toFormat("yyyy-MM");
                if (!monthlyGroups.has(monthKey)) {
                    monthlyGroups.set(monthKey, {
                        timeKey: monthKey,
                        displayText: item.dateObj.toFormat("yyyy年M月"),
                        projectData: []
                    });
                }
                monthlyGroups.get(monthKey).projectData.push(item);
            }

            return Array.from(monthlyGroups.values()).sort((a, b) => b.timeKey.localeCompare(a.timeKey));
        }, [], '按月聚合数据');
    },

    /**
     * 按周聚合数据
     */
    aggregateByWeek(projectDateData) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const weeklyGroups = new Map();

            for (const item of projectDateData) {
                const weekStart = item.dateObj.startOf('week');
                const weekEnd = item.dateObj.endOf('week');
                const weekKey = weekStart.toFormat("yyyy-'W'WW");

                if (!weeklyGroups.has(weekKey)) {
                    weeklyGroups.set(weekKey, {
                        timeKey: weekKey,
                        displayText: `${weekStart.toFormat("yyyy年M月d日")} - ${weekEnd.toFormat("M月d日")}`,
                        weekStart: weekStart.toFormat("yyyy-MM-dd"),
                        weekEnd: weekEnd.toFormat("yyyy-MM-dd"),
                        projectData: []
                    });
                }
                weeklyGroups.get(weekKey).projectData.push(item);
            }

            return Array.from(weeklyGroups.values()).sort((a, b) => b.timeKey.localeCompare(a.timeKey));
        }, [], '按周聚合数据');
    },

    /**
     * 按年聚合数据
     */
    aggregateByYear(projectDateData) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const yearlyGroups = new Map();

            for (const item of projectDateData) {
                const yearKey = item.dateObj.toFormat("yyyy");
                if (!yearlyGroups.has(yearKey)) {
                    yearlyGroups.set(yearKey, {
                        timeKey: yearKey,
                        displayText: `${yearKey}年`,
                        projectData: []
                    });
                }
                yearlyGroups.get(yearKey).projectData.push(item);
            }

            return Array.from(yearlyGroups.values()).sort((a, b) => b.timeKey.localeCompare(a.timeKey));
        }, [], '按年聚合数据');
    },

    /**
     * 按日聚合数据
     */
    aggregateByDay(projectDateData) {
        return window.DashboardCore.Utils.Error.safeExecute(() => {
            const dailyGroups = new Map();

            for (const item of projectDateData) {
                const dateKey = item.date;
                if (!dailyGroups.has(dateKey)) {
                    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
                    const weekday = weekdays[item.dateObj.weekday % 7];
                    dailyGroups.set(dateKey, {
                        timeKey: dateKey,
                        displayText: `${item.dateObj.toFormat("yyyy年M月d日")} (${weekday})`,
                        projectData: []
                    });
                }
                dailyGroups.get(dateKey).projectData.push(item);
            }

            return Array.from(dailyGroups.values()).sort((a, b) => b.timeKey.localeCompare(a.timeKey));
        }, [], '按日聚合数据');
    }
};

/**
 * 日期维度表格构建器
 * 负责构建日期维度的表格数据
 */
window.DashboardCore.TableBuilder.buildDateTable = function(data, granularity = 'monthly') {
    return window.DashboardCore.Utils.Error.safeExecute(() => {
        const { allProjects, riskInfo, levelInfo, priceMap } = data;

        // 数据验证
        if (!allProjects || allProjects.length === 0) {
            return {
                headers: ['时间', '项目数', '总投入', '总收益', '平均APR'],
                data: []
            };
        }

        // 1. 提取项目日期数据
        const projectDateData = window.DashboardCore.DateAggregator.extractProjectDateData(allProjects);
        if (projectDateData.length === 0) {
            console.info('日期维度表格构建：未找到有效的日期数据');
            return {
                headers: ['时间', '项目数', '总投入', '总收益', '平均APR'],
                data: []
            };
        }

        // 2. 根据粒度聚合项目日期数据
        let timeGroups = [];
        switch (granularity) {
            case 'daily':
                timeGroups = window.DashboardCore.DateAggregator.aggregateByDay(projectDateData);
                break;
            case 'weekly':
                timeGroups = window.DashboardCore.DateAggregator.aggregateByWeek(projectDateData);
                break;
            case 'monthly':
                timeGroups = window.DashboardCore.DateAggregator.aggregateByMonth(projectDateData);
                break;
            case 'yearly':
                timeGroups = window.DashboardCore.DateAggregator.aggregateByYear(projectDateData);
                break;
            default:
                timeGroups = window.DashboardCore.DateAggregator.aggregateByMonth(projectDateData);
        }

        if (timeGroups.length === 0) {
            // 根据粒度调整第一列标题
            const firstColumnTitle = granularity === 'daily' ? '日期' :
                                   granularity === 'weekly' ? '周' :
                                   granularity === 'monthly' ? '月份' : '年份';
            return {
                headers: [
                    firstColumnTitle,
                    '投入金额',
                    '当前占比',
                    '健康程度',
                    '项目',
                    '状态',
                    '总收益',
                    '投资天数',
                    '日收益',
                    '当前APR',
                    '期望APR',
                    'APR Gap',
                    '当前APR总比',
                    '期望APR总比',
                    'APR Gap总比'
                ],
                data: []
            };
        }

        // 3. 构建表格数据
        const tableData = [];

        // 根据粒度调整第一列标题
        const firstColumnTitle = granularity === 'daily' ? '日期' :
                               granularity === 'weekly' ? '周' :
                               granularity === 'monthly' ? '月份' : '年份';

        const headers = [
            firstColumnTitle,
            '投入金额',
            '当前占比',
            '健康程度',
            '项目',
            '状态',
            '总收益',
            '投资天数',
            '日收益',
            '当前APR',
            '期望APR',
            'APR Gap',
            '当前APR总比',
            '期望APR总比',
            'APR Gap总比'
        ];

        // 处理每个时间组
        for (const timeGroup of timeGroups) {
            // 获取该时间段内的所有项目
            const projectsInTimeGroup = new Map();

            for (const item of timeGroup.projectData) {
                const projectKey = item.project.file.name;
                if (!projectsInTimeGroup.has(projectKey)) {
                    projectsInTimeGroup.set(projectKey, {
                        project: item.project,
                        dateEntries: []
                    });
                }
                projectsInTimeGroup.get(projectKey).dateEntries.push(item);
            }

            // 计算时间范围
            let timeRange = null;
            if (timeGroup.projectData.length > 0) {
                const dates = timeGroup.projectData.map(item => item.date).sort();

                // 根据粒度调整时间范围
                switch (granularity) {
                    case 'monthly':
                        // 月粒度：使用月份的第一天和最后一天
                        const monthStart = window.DashboardCore.Utils.Date.parseDate(dates[0]).startOf('month');
                        const monthEnd = window.DashboardCore.Utils.Date.parseDate(dates[0]).endOf('month');
                        timeRange = {
                            startDate: window.DashboardCore.Utils.Date.formatDate(monthStart, 'yyyy-MM-dd'),
                            endDate: window.DashboardCore.Utils.Date.formatDate(monthEnd, 'yyyy-MM-dd')
                        };
                        break;
                    case 'weekly':
                        // 周粒度：使用周的开始和结束
                        if (timeGroup.weekStart && timeGroup.weekEnd) {
                            timeRange = {
                                startDate: timeGroup.weekStart,
                                endDate: timeGroup.weekEnd
                            };
                        }
                        break;
                    case 'yearly':
                        // 年粒度：使用年份的第一天和最后一天
                        const yearStart = window.DashboardCore.Utils.Date.parseDate(dates[0]).startOf('year');
                        const yearEnd = window.DashboardCore.Utils.Date.parseDate(dates[0]).endOf('year');
                        timeRange = {
                            startDate: window.DashboardCore.Utils.Date.formatDate(yearStart, 'yyyy-MM-dd'),
                            endDate: window.DashboardCore.Utils.Date.formatDate(yearEnd, 'yyyy-MM-dd')
                        };
                        break;
                    case 'daily':
                    default:
                        // 日粒度：保持原有逻辑
                        timeRange = {
                            startDate: dates[0],
                            endDate: dates[dates.length - 1]
                        };
                        break;
                }
            }

            // 处理该时间段内的每个项目
            const protocolDetails = [];
            for (const [projectKey, projectInfo] of projectsInTimeGroup) {
                const project = projectInfo.project;

                // 计算该项目在该时间段的数据
                let historyData;
                if (timeRange) {
                    historyData = window.DashboardCore.BaseCalculator.calculateProjectHistoryInTimeRange(
                        project, timeRange.startDate, timeRange.endDate, priceMap
                    );
                } else {
                    historyData = window.DashboardCore.BaseCalculator.calculateProjectHistory(project, priceMap);
                }

                if (!historyData) continue;

                const unit = (project.Unit || 'USDT').toUpperCase();
                const riskLevel = project.Risk || 0;
                const expectApr = (riskInfo[riskLevel] && riskInfo[riskLevel].expectApr) ? riskInfo[riskLevel].expectApr : 0;
                const investDays = window.DashboardCore.BaseCalculator.calculateInvestDaysForDateView(project, timeRange?.endDate || new Date().toISOString().split('T')[0]);
                const usdtAmounts = window.DashboardCore.USDTCalculator.calculateUSDTAmount(historyData, unit, priceMap);

                // 计算时间范围收益
                let timeRangeEarnings = 0;
                if (timeRange) {
                    const earningsResult = window.DashboardCore.USDTCalculator.calculateTimeRangeEarnings(
                        project, priceMap, timeRange.startDate, timeRange.endDate, unit
                    );
                    timeRangeEarnings = earningsResult.timeRangeEarnings;
                }

                const projectApr = window.DashboardCore.DerivedCalculator.calculateUSDTBasedAPR(usdtAmounts, investDays);
                const earnings = window.DashboardCore.DerivedCalculator.calculateEarnings(usdtAmounts, investDays);

                protocolDetails.push({
                    project: project,
                    protocol: project.Protocol || "-",
                    riskLevel: riskLevel,
                    level: project.Level || 0,
                    unit: unit,
                    status: project.Status || "Doing",
                    expectApr: expectApr,
                    amountInUsdt: usdtAmounts.investAmount > 0 ? usdtAmounts.investAmount : 0,
                    usdtTotalEarned: timeRange ? timeRangeEarnings : earnings.totalEarned,
                    usdtDailyEarned: timeRange ? (timeRangeEarnings / Math.max(investDays, 1)) : earnings.dailyEarned,
                    investDays: investDays,
                    projectApr: projectApr,
                    priceFound: usdtAmounts.priceFound
                });
            }

            if (protocolDetails.length === 0) continue;

            // 计算该时间段的汇总数据
            const totalAmountInUsdt = protocolDetails.reduce((sum, p) => sum + p.amountInUsdt, 0);
            const avgAPR = protocolDetails.length > 0 ?
                protocolDetails.reduce((sum, p) => sum + p.projectApr, 0) / protocolDetails.length : 0;

            // 添加时间汇总行
            const summaryRow = window.DashboardCore.TableBuilder.buildTimeSummaryRow(
                timeGroup,
                protocolDetails,
                totalAmountInUsdt,
                avgAPR
            );
            // 调试：检查汇总行的列数
            if (summaryRow.length !== 15) {
                console.warn(`汇总行列数不匹配: 期望15列，实际${summaryRow.length}列`, summaryRow);
            }
            tableData.push(summaryRow);

            // 添加项目详情行
            protocolDetails.sort((a, b) => a.protocol.localeCompare(b.protocol));
            for (const project of protocolDetails) {
                const projectRow = window.DashboardCore.TableBuilder.buildDateProjectRow(
                    project,
                    totalAmountInUsdt
                );
                // 调试：检查项目行的列数
                if (projectRow.length !== 15) {
                    console.warn(`项目行列数不匹配: 期望15列，实际${projectRow.length}列`, projectRow);
                }
                tableData.push(projectRow);
            }
        }

        // 添加总计行
        if (tableData.length > 0) {
            const totalRow = window.DashboardCore.TableBuilder.buildDateTotalRow(timeGroups, headers, riskInfo, priceMap);
            tableData.push(totalRow);
        }

        return {
            headers: headers,
            data: tableData
        };
    }, { headers: [], data: [] }, '构建日期维度表格');
};

/**
 * 构建时间汇总行
 */
window.DashboardCore.TableBuilder.buildTimeSummaryRow = function(timeGroup, protocolDetails, totalAmountInUsdt, avgAPR) {
    return window.DashboardCore.Utils.Error.safeExecute(() => {
        const totalEarned = protocolDetails.reduce((sum, p) => sum + (p.usdtTotalEarned > 0 ? p.usdtTotalEarned : 0), 0);
        const totalDailyEarned = protocolDetails.reduce((sum, p) => sum + (p.usdtDailyEarned > 0 ? p.usdtDailyEarned : 0), 0);
        const maxInvestDays = Math.max(...protocolDetails.map(p => p.investDays));

        // 计算期望APR平均值
        const validExpectAprProjects = protocolDetails.filter(p => p.amountInUsdt > 0 && p.expectApr > 0);
        const avgExpectApr = validExpectAprProjects.length > 0 ?
            validExpectAprProjects.reduce((sum, p) => sum + (p.expectApr * 100), 0) / validExpectAprProjects.length : 0;

        // 计算APR Gap平均值
        const validAprGaps = protocolDetails.filter(p => p.amountInUsdt > 0 && p.expectApr > 0)
            .map(p => window.DashboardCore.DerivedCalculator.calculateAPRGap(p.projectApr, p.expectApr, true));
        const avgAprGap = validAprGaps.length > 0 ?
            validAprGaps.reduce((sum, gap) => sum + gap, 0) / validAprGaps.length : 0;

        // 计算当前APR总比平均值
        const validCurrentAprTotals = protocolDetails.filter(p => p.amountInUsdt > 0)
            .map(p => {
                const projectPercent = totalAmountInUsdt > 0 ? (p.amountInUsdt / totalAmountInUsdt) : 0;
                return window.DashboardCore.DerivedCalculator.calculatePortfolioAPR(projectPercent, p.projectApr);
            });
        const avgCurrentAprTotal = validCurrentAprTotals.length > 0 ?
            validCurrentAprTotals.reduce((sum, apr) => sum + apr, 0) / validCurrentAprTotals.length : 0;

        // 计算期望APR总比平均值
        const validExpectAprTotals = protocolDetails.filter(p => p.amountInUsdt > 0 && p.expectApr > 0)
            .map(p => {
                const projectPercent = totalAmountInUsdt > 0 ? (p.amountInUsdt / totalAmountInUsdt) : 0;
                return window.DashboardCore.DerivedCalculator.calculatePortfolioAPR(projectPercent, p.expectApr * 100);
            });
        const avgExpectAprTotal = validExpectAprTotals.length > 0 ?
            validExpectAprTotals.reduce((sum, apr) => sum + apr, 0) / validExpectAprTotals.length : 0;

        // 计算APR Gap总比平均值
        const avgAprGapTotal = avgCurrentAprTotal - avgExpectAprTotal;

        return [
            `**${timeGroup.displayText}**`, // 时间
            totalAmountInUsdt > 0 ?
                `**${window.DashboardCore.Utils.Number.formatNumber(totalAmountInUsdt)}**` : "**0**", // 投入金额
            totalAmountInUsdt > 0 ? "**100%**" : "**0%**", // 当前占比（汇总行总是100%）
            `**📊 ${protocolDetails.length}个项目**`, // 健康程度显示项目数量
            "", // 项目列为空
            "**-**", // 状态列为空（汇总行）
            totalEarned !== 0 ?
                (totalEarned > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalEarned)}</span>` :
                    `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalEarned)}</span>`) :
                "**0**", // 总收益（带颜色）
            maxInvestDays > 0 ? `**${maxInvestDays.toFixed(2)}**` : "**0**", // 投资天数（最大值）
            totalDailyEarned !== 0 ?
                (totalDailyEarned > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalDailyEarned)}</span>` :
                    `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalDailyEarned)}</span>`) :
                "**0**", // 日收益（带颜色）
            avgAPR !== 0 ?
                (avgAPR > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(avgAPR)}</span>` :
                    `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(avgAPR)}</span>`) :
                "**0%**", // 当前APR（带颜色）
            avgExpectApr > 0 ? `**${window.DashboardCore.Utils.Number.formatPercentage(avgExpectApr, 0)}**` : "**0%**", // 期望APR平均值
            validAprGaps.length > 0 ? window.DashboardCore.Utils.Color.getAPRGapColoredText(avgAprGap).replace('<span style="color:', '<span style="color:').replace(';">', '; font-weight: bold;">') : "**0%**", // APR Gap平均值
            avgCurrentAprTotal !== 0 ?
                (avgCurrentAprTotal > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(avgCurrentAprTotal)}</span>` :
                    `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(avgCurrentAprTotal)}</span>`) :
                "**0%**", // 当前APR总比平均值（带颜色）
            avgExpectAprTotal > 0 ? `**${window.DashboardCore.Utils.Number.formatPercentage(avgExpectAprTotal)}**` : "**0%**", // 期望APR总比平均值
            validAprGaps.length > 0 ? window.DashboardCore.Utils.Color.getAPRGapColoredText(avgAprGapTotal).replace('<span style="color:', '<span style="color:').replace(';">', '; font-weight: bold;">') : "**0%**" // APR Gap总比平均值
        ];
    }, [], '构建时间汇总行');
};

/**
 * 构建日期项目详情行
 */
window.DashboardCore.TableBuilder.buildDateProjectRow = function(project, totalAmountInUsdt) {
    return window.DashboardCore.Utils.Error.safeExecute(() => {
        const statusColor = project.status === "Doing" ? "#28a745" : "#6c757d";
        const statusIcon = project.status === "Doing" ? "🟢" : "⚪";

        // 计算APR相关指标
        const projectPercent = totalAmountInUsdt > 0 ? (project.amountInUsdt / totalAmountInUsdt) : 0;
        const projectExpectApr = project.expectApr * 100;
        const projectAprGap = window.DashboardCore.DerivedCalculator.calculateAPRGap(project.projectApr, project.expectApr, project.amountInUsdt > 0);
        const projectPortfolioCurrentApr = window.DashboardCore.DerivedCalculator.calculatePortfolioAPR(projectPercent, project.projectApr);
        const projectPortfolioExpectApr = window.DashboardCore.DerivedCalculator.calculatePortfolioAPR(projectPercent, projectExpectApr);
        const projectPortfolioAprGap = projectPortfolioCurrentApr - projectPortfolioExpectApr;

        return [
            "", // 时间列为空（项目行不显示时间）
            project.amountInUsdt > 0 ?
                window.DashboardCore.Utils.Number.formatNumber(project.amountInUsdt) : "0", // 投入金额
            window.DashboardCore.Utils.Number.formatPercentage(projectPercent * 100), // 当前占比
            "-", // 健康程度（项目行显示横杠）
            project.project.file.link, // 项目
            `<span style="color: ${statusColor};">${statusIcon} ${project.status}</span>`, // 状态列：使用带颜色标记的状态显示
            project.usdtTotalEarned === -1 ? "价格数据未找到" :
                (project.usdtTotalEarned !== 0 ? window.DashboardCore.Utils.Color.formatColoredNumber(project.usdtTotalEarned) : "0"), // 总收益（带颜色）
            project.investDays > 0 ? `${project.investDays.toFixed(2)}` : "0", // 投资天数
            project.usdtDailyEarned === -1 ? "价格数据未找到" :
                (project.usdtDailyEarned !== 0 ? window.DashboardCore.Utils.Color.formatColoredNumber(project.usdtDailyEarned) : "0"), // 日收益（带颜色）
            window.DashboardCore.Utils.Color.formatColoredPercentage(project.projectApr), // 当前APR（带颜色）
            project.amountInUsdt > 0 && projectExpectApr > 0 ? window.DashboardCore.Utils.Number.formatPercentage(projectExpectApr, 0) : "0%", // 期望APR
            project.amountInUsdt > 0 && projectExpectApr > 0 ? window.DashboardCore.Utils.Color.getAPRGapColoredText(projectAprGap) : "0%", // APR Gap
            window.DashboardCore.Utils.Color.formatColoredPercentage(projectPortfolioCurrentApr), // 当前APR总比（带颜色）
            window.DashboardCore.Utils.Number.formatPercentage(projectPortfolioExpectApr), // 期望APR总比
            project.amountInUsdt > 0 && projectExpectApr > 0 ? window.DashboardCore.Utils.Color.getAPRGapColoredText(projectPortfolioAprGap) : "0%" // APR Gap总比
        ];
    }, [], '构建日期项目详情行');
};

/**
 * 构建日期视图总计行
 * @param {Array} timeGroups - 时间组数据
 * @param {Array} headers - 表头数组
 * @param {Object} riskInfo - 风险信息
 * @param {Object} priceMap - 价格映射
 * @returns {Array} 表格行数据
 */
window.DashboardCore.TableBuilder.buildDateTotalRow = function(timeGroups, headers, riskInfo, priceMap) {
    return window.DashboardCore.Utils.Error.safeExecute(() => {
        // 收集所有项目数据
        let allProjects = [];
        let totalAmountInUsdt = 0;
        let totalTotalEarned = 0;
        let totalDailyEarned = 0;
        let maxInvestDays = 0;
        let totalProjectCount = 0;

        // 遍历所有时间组，收集项目数据
        for (const timeGroup of timeGroups) {
            for (const item of timeGroup.projectData) {
                const project = item.project;

                // 避免重复计算同一个项目
                const projectKey = project.file.name;
                if (!allProjects.find(p => p.file.name === projectKey)) {
                    allProjects.push(project);
                    totalProjectCount++;

                    // 这里需要重新计算项目数据，因为我们需要全局的数据而不是时间段的数据
                    // 使用项目的完整历史数据
                    const historyData = window.DashboardCore.BaseCalculator.calculateProjectHistory(project, priceMap || {});
                    if (historyData) {
                        const unit = (project.Unit || 'USDT').toUpperCase();
                        const investDays = window.DashboardCore.BaseCalculator.calculateInvestDays(project);
                        const usdtAmounts = window.DashboardCore.USDTCalculator.calculateUSDTAmount(historyData, unit, priceMap || {});
                        const earnings = window.DashboardCore.DerivedCalculator.calculateEarnings(usdtAmounts, investDays);

                        totalAmountInUsdt += usdtAmounts.investAmount > 0 ? usdtAmounts.investAmount : 0;
                        totalTotalEarned += earnings.totalEarned;
                        totalDailyEarned += earnings.dailyEarned;
                        maxInvestDays = Math.max(maxInvestDays, investDays);
                    }
                }
            }
        }

        // 计算平均APR
        const totalCurrentApr = allProjects.length > 0 ?
            allProjects.reduce((sum, project) => {
                const historyData = window.DashboardCore.BaseCalculator.calculateProjectHistory(project, priceMap || {});
                if (historyData) {
                    const unit = (project.Unit || 'USDT').toUpperCase();
                    const investDays = window.DashboardCore.BaseCalculator.calculateInvestDays(project);
                    const usdtAmounts = window.DashboardCore.USDTCalculator.calculateUSDTAmount(historyData, unit, priceMap || {});
                    const projectApr = window.DashboardCore.DerivedCalculator.calculateUSDTBasedAPR(usdtAmounts, investDays);
                    return sum + projectApr;
                }
                return sum;
            }, 0) / allProjects.length : 0;

        // 计算期望APR（基于风险等级）
        const totalExpectApr = allProjects.length > 0 ?
            allProjects.reduce((sum, project) => {
                const riskLevel = project.Risk || 0;
                const expectApr = (riskInfo[riskLevel] && riskInfo[riskLevel].expectApr) ? riskInfo[riskLevel].expectApr * 100 : 0;
                return sum + expectApr;
            }, 0) / allProjects.length : 0;

        const totalAprGap = totalCurrentApr - totalExpectApr;

        // 组合APR总比就是当前APR和期望APR（因为总占比是100%）
        const totalPortfolioCurrentApr = totalCurrentApr;
        const totalPortfolioExpectApr = totalExpectApr;
        const totalPortfolioAprGap = totalAprGap;

        // 构建总计行，确保列数与表头匹配
        return [
            `**总计**`, // 第一列：时间
            `**${window.DashboardCore.Utils.Number.formatNumber(totalAmountInUsdt)}**`, // 投入金额（加粗）
            `**100%**`, // 当前占比（加粗）
            `**📊 ${totalProjectCount}个项目**`, // 健康程度显示项目总数（加粗）
            "", // 项目列为空
            "**-**", // 状态列为空（总计行）
            // 总收益（带颜色和粗体）
            totalTotalEarned !== 0 ?
                (totalTotalEarned > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalTotalEarned)}</span>` :
                    `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalTotalEarned)}</span>`) :
                "**0**",
            maxInvestDays > 0 ? `**${maxInvestDays.toFixed(2)}**` : "**0**", // 投资天数（加粗）
            // 日收益（带颜色和粗体）
            totalDailyEarned !== 0 ?
                (totalDailyEarned > 0 ?
                    `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalDailyEarned)}</span>` :
                    `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatNumber(totalDailyEarned)}</span>`) :
                "**0**",
            // 当前APR（带颜色和粗体）
            totalCurrentApr > 0 ?
                `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(totalCurrentApr)}</span>` :
                totalCurrentApr < 0 ?
                    `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(totalCurrentApr)}</span>` :
                    `**${window.DashboardCore.Utils.Number.formatPercentage(totalCurrentApr)}**`,
            `**${window.DashboardCore.Utils.Number.formatPercentage(totalExpectApr, 0)}**`, // 期望APR（加粗）
            window.DashboardCore.Utils.Color.getAPRGapColoredText(totalAprGap).replace('<span style="color:', '<span style="color:').replace(';">', '; font-weight: bold;">'), // APR Gap（加粗）
            // 当前APR总比（带颜色和粗体）
            totalPortfolioCurrentApr > 0 ?
                `<span style="color: #28a745; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(totalPortfolioCurrentApr)}</span>` :
                totalPortfolioCurrentApr < 0 ?
                    `<span style="color: #dc3545; font-weight: bold;">${window.DashboardCore.Utils.Number.formatPercentage(totalPortfolioCurrentApr)}</span>` :
                    `**${window.DashboardCore.Utils.Number.formatPercentage(totalPortfolioCurrentApr)}**`,
            `**${window.DashboardCore.Utils.Number.formatPercentage(totalPortfolioExpectApr)}**`, // 期望APR总比（加粗）
            window.DashboardCore.Utils.Color.getAPRGapColoredText(totalPortfolioAprGap).replace('<span style="color:', '<span style="color:').replace(';">', '; font-weight: bold;">') // APR Gap总比（加粗）
        ];
    }, [], '构建日期视图总计行');
};

console.log('Dashboard TableBuilder (Complete) module loaded');

# Web3 Farm Dashboard 开发者文档

## 📋 概述

这是一个基于 Obsidian DataviewJS 的 Web3 农场投资项目仪表板系统，采用**模块化架构**设计，支持风险等级维度和日期维度的数据展示，提供项目投资分析、收益计算、APR 统计等功能。

### 🎯 系统特点

- **模块化架构**：代码分离为独立的 JavaScript 库模块，易于维护和扩展
- **双维度视图**：支持风险等级和日期维度的数据分析
- **智能缓存**：自动缓存计算结果，提升性能
- **完整的错误处理**：详细的错误信息和调试支持
- **响应式设计**：适配不同屏幕尺寸

## 📁 文件结构

```
web3/farm/
├── dashboard-new.md          # 🚀 新版主仪表板文件 (推荐使用)
├── dashboard.md              # 📜 原版仪表板文件 (兼容保留)
├── README_dashboard.md       # 📖 本文档
├── lib/                      # 📚 模块化库文件
│   ├── dashboard-config.js       # ⚙️ 配置管理
│   ├── dashboard-utils.js        # 🛠️ 工具函数
│   ├── dashboard-data-loader.js  # 📊 数据加载
│   ├── dashboard-calculator.js   # 🧮 计算引擎
│   ├── dashboard-table-builder.js # 📋 表格构建
│   ├── dashboard-renderer.js     # 🎨 渲染引擎
│   ├── dashboard-ui.js           # 🖱️ 用户界面
│   └── dashboard-core.js         # 🎯 核心API
├── data/                     # 📂 配置和价格数据
│   ├── risk_percentage.md        # 🎲 风险等级配置
│   ├── invest_level_limit.md     # 📊 投资等级配置
│   └── token_prices.md           # 💰 代币价格数据
└── history/                  # 📈 项目历史数据
    ├── doing/                    # 🟢 进行中项目
    └── done/                     # ✅ 已完成项目
```

## 🚀 重大更新 (2024年12月)

### ✅ 模块化重构完成

本次更新将原有的单文件 Dashboard 系统重构为**模块化架构**，带来以下重大改进：

#### **1. 架构优化**
- **从单文件 → 模块化**：将 1000+ 行代码拆分为 8 个独立模块
- **从双代码块 → 单代码块**：优化执行流程，提升可靠性
- **从 script 标签 → dv.view()**：使用更稳定的模块加载方式

#### **2. 功能完整性**
- ✅ **100% 功能保持**：所有原版功能完全保留
- ✅ **全局缓存系统**：实现性能优化的缓存机制
- ✅ **向后兼容**：保持与原版的完全兼容

#### **3. 开发体验提升**
- 🔧 **模块化开发**：每个功能模块独立，易于维护
- 🐛 **增强的错误处理**：更详细的错误信息和调试支持
- 📚 **完整的文档**：每个模块都有详细的代码注释

### 🎯 使用建议

**推荐使用 `dashboard-new.md`**，它提供：
- 更好的性能和稳定性
- 更易维护的代码结构
- 更完善的错误处理
- 与原版完全相同的功能

## 🏗️ 模块化架构

### 核心模块说明

#### 📚 **lib/dashboard-config.js** - 配置管理
- 系统配置和默认值管理
- 路径配置、性能参数、调试选项
- 配置获取和验证功能

#### 🛠️ **lib/dashboard-utils.js** - 工具函数库
- 日期处理：`parseDate()`, `formatDate()`, `calculateDaysDiff()`
- 数字格式化：`formatNumber()`, `formatPercentage()`
- 颜色处理：`getColorByRisk()`, `getAPRGapColoredText()`
- 错误处理：`safeExecute()`, `logPerformance()`

#### 📊 **lib/dashboard-data-loader.js** - 数据加载器
- 配置文件解析：`parseRiskFile()`, `parseLevelFile()`
- 价格数据处理：`parsePriceData()`
- 项目数据加载：`loadProjects()`, `validateProjects()`
- 缓存管理：`Cache.get()`, `Cache.set()`, `Cache.clear()`

#### 🧮 **lib/dashboard-calculator.js** - 计算引擎
- **BaseCalculator**: 基础计算逻辑
  - `calculateProjectHistory()`: 项目历史数据计算
  - `calculateAPR()`: 年化收益率计算
  - `calculateInvestDays()`: 投资天数计算
- **USDTCalculator**: USDT 价值计算
  - `calculateUSDTAmount()`: 代币转 USDT 价值
  - `calculateTimeRangeEarnings()`: 时间范围收益计算
- **DerivedCalculator**: 衍生指标计算
  - `calculateEarnings()`: 收益统计
  - `calculateUSDTBasedAPR()`: 基于 USDT 的 APR

#### 📋 **lib/dashboard-table-builder.js** - 表格构建器
- **风险维度表格**: `buildRiskTable()`
  - 风险等级分组和汇总
  - 项目详情行构建
  - 总计行计算
- **日期维度表格**: `buildDateTable()`
  - 时间数据聚合：`DateAggregator`
  - 支持日/周/月/年粒度
  - 时间范围计算和项目统计

#### 🎨 **lib/dashboard-renderer.js** - 渲染引擎
- 固定表头表格渲染：`renderFixedTable()`
- HTML 生成和样式应用
- 响应式设计支持

#### 🖱️ **lib/dashboard-ui.js** - 用户界面
- 视图切换器：`renderViewSwitcher()`
- 事件绑定：`bindEvents()`
- 交互式按钮和控件

#### 🎯 **lib/dashboard-core.js** - 核心 API
- 统一的 API 接口：`window.DashboardCore.API`
- 系统初始化：`initialize()`
- 视图管理：`switchMainView()`, `switchDateGranularity()`
- 全局缓存管理：`setupGlobalCache()`

## 🎨 核心功能

### 1. 双维度视图系统
- **风险等级维度**：按风险等级分组显示项目，计算风险配置比例
- **日期维度**：按时间粒度（日/周/月/年）展示项目表现
- **智能切换**：一键切换不同维度和时间粒度

### 2. 智能数据计算
- **APR 计算**：基于 USDT 价值的年化收益率
- **收益统计**：总收益、日收益、投资天数
- **仓位分析**：投入金额、仓位状态、操作建议
- **时间范围处理**：日期维度严格按时间范围计算

### 3. 性能优化
- **智能缓存**：自动缓存计算结果，避免重复计算
- **按需计算**：日期维度数据按粒度缓存
- **批处理**：大数据集的批量处理优化

### 4. 视觉优化
- **颜色编码**：正负数自动颜色显示（绿色=正数，红色=负数）
- **风险等级**：不同风险等级使用不同颜色标识
- **APR Gap**：根据差距大小显示不同颜色警示
- **状态指示**：仓位状态使用图标和颜色组合显示
- **固定表头**：大表格支持固定表头和第一列

## 🔧 开发指南

### 快速开始

1. **使用新版 Dashboard**：
   ```markdown
   直接使用 dashboard-new.md 文件即可
   ```

2. **修改配置**：
   ```javascript
   // 编辑 lib/dashboard-config.js
   HISTORY_PATH: '"web3/farm/history"',
   RISK_LIMIT_PATH: 'web3/farm/data/risk_percentage'
   ```

3. **添加新功能**：
   ```javascript
   // 在对应的模块文件中添加新方法
   // 例如在 dashboard-calculator.js 中添加新的计算逻辑
   ```

### 常见修改场景

#### 🔄 修改表格列顺序
编辑 `lib/dashboard-table-builder.js` 中的表头定义：
```javascript
const headers = [
    '风险等级', '协议', '投入金额', // 调整顺序
    // ... 其他列
];
```

#### 📊 修改 APR 计算逻辑
编辑 `lib/dashboard-calculator.js` 中的相关方法：
```javascript
window.DashboardCore.BaseCalculator.calculateAPR = function(/* 参数 */) {
    // 修改计算逻辑
};
```

#### 🎨 修改样式和颜色
编辑 `lib/dashboard-utils.js` 中的颜色函数：
```javascript
getColorByRisk(riskLevel) {
    // 修改颜色映射
}
```

### 调试和故障排除

#### 🐛 常见问题

1. **模块加载失败**
   - 检查 `lib/` 目录下的文件是否完整
   - 查看浏览器控制台的错误信息

2. **数据显示异常**
   - 检查数据文件格式是否正确
   - 验证项目文件的 YAML 头部字段

3. **性能问题**
   - 清空缓存：`window.DashboardCore.DataLoader.Cache.clear()`
   - 检查项目数量是否超过配置限制

#### 🔍 调试技巧

1. **启用调试模式**：
   ```javascript
   // 在 lib/dashboard-config.js 中设置
   DEBUG: {
       ENABLE_CONSOLE_LOG: true,
       ENABLE_PERFORMANCE_LOG: true
   }
   ```

2. **查看缓存状态**：
   ```javascript
   console.log('风险表格数据:', window.DashboardTableData);
   console.log('日期基础数据:', window.DashboardDateBaseData);
   console.log('日期表格缓存:', window.DashboardDateTableData);
   ```

3. **性能分析**：
   ```javascript
   // 查看模块加载时间和计算性能
   // 控制台会显示详细的性能日志
   ```

## ⚠️ 重要注意事项

### 🚨 高风险修改警告

#### 绝对不要修改的部分
1. **全局缓存变量名**：`DashboardTableData`, `DashboardDateBaseData`, `DashboardDateTableData`
2. **核心计算逻辑**：APR 计算、时间范围处理、价格转换
3. **模块加载顺序**：必须按依赖关系加载模块

#### 需要谨慎修改的部分
1. **配置文件路径**：修改后需确保新路径下有对应文件
2. **数据结构**：项目文件的 YAML 字段、配置文件格式
3. **全局变量**：修改 `window.DashboardCore` 下的对象名称

### 📋 最佳实践

1. **修改前备份**：
   ```bash
   cp dashboard-new.md dashboard-new_backup_$(date +%Y%m%d).md
   ```

2. **模块化开发**：
   - 新功能添加到对应的模块文件中
   - 保持模块间的低耦合
   - 使用统一的错误处理机制

3. **测试验证**：
   - 修改后测试所有视图切换功能
   - 验证数据计算的准确性
   - 检查缓存机制是否正常

## 📈 版本历史

### v3.0.0 (2024年12月) - 模块化重构
- ✅ 完全模块化架构重构
- ✅ 单代码块优化
- ✅ 全局缓存系统
- ✅ 增强的错误处理
- ✅ 100% 功能兼容

### v2.6.x (之前版本)
- 日期维度视图支持
- 时间范围计算优化
- 性能改进和缓存机制

---

## 🎯 总结

新版 `dashboard-new.md` 提供了：
- **更好的架构**：模块化设计，易于维护和扩展
- **更高的性能**：智能缓存和优化的计算逻辑
- **更强的稳定性**：完善的错误处理和调试支持
- **完全的兼容性**：保持与原版 100% 功能一致

**推荐立即切换到新版本使用！** 🚀

---

*最后更新：2024年12月 | 版本：v3.0.0*
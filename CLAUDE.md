# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This is an Obsidian vault focused on Web3 DeFi (Decentralized Finance) investment tracking and analysis. The repository contains a sophisticated dashboard system for managing cryptocurrency farming positions, analyzing returns, and tracking investment performance across different risk levels and time periods.

## Key Components

### 1. Web3 Farm Dashboard System
- **Primary File**: `web3/farm/dashboard.md` - Main DataviewJS-powered investment dashboard
- **Documentation**: `web3/farm/README_dashboard.md` - Comprehensive technical documentation
- **Data Sources**:
  - `web3/farm/data/risk_percentage.md` - Risk level configurations
  - `web3/farm/data/invest_level_limit.md` - Investment level limits  
  - `web3/farm/data/token_prices.md` - Historical token pricing data

### 2. Investment Tracking Structure
- **Active Projects**: `web3/farm/history/doing/` - Projects currently being tracked
- **Completed Projects**: `web3/farm/history/done/` - Historical completed investments
- **Data Format**: Each project file contains YAML frontmatter with metadata and timestamped balance entries

### 3. Price Data Management
- **Script**: `web3/farm/update_prices.py` - Python script for fetching historical prices
- **Sources**: Integrates with Binance and CoinGecko APIs
- **Caching**: Local JSON cache for price data optimization

## Architecture & Code Structure

### Dashboard System (DataviewJS)
The dashboard uses a modular JavaScript architecture with the following key modules:

1. **Data Loading Module** - Parses configuration files and project data
2. **Time Aggregation Module** - Handles date-based grouping and filtering
3. **Base Calculator Module** - Core financial calculations (APR, returns, etc.)
4. **Derived Calculator Module** - Advanced metrics and portfolio analysis
5. **Table Builder Module** - Constructs data tables for different views
6. **UI Components Module** - Handles view switching and user interaction

### Key Calculations
- **APR Calculation**: Based on USDT value with proper handling of additional investments
- **Time Range Processing**: Strict filtering by date ranges for accurate period analysis
- **Multi-currency Support**: Automatic conversion to USDT using historical price data
- **Risk Assessment**: Portfolio allocation analysis against target percentages

## Working with the Codebase

### Price Data Updates
```bash
# Run price update script
cd web3/farm
python update_prices.py
```

### Project File Format
```yaml
---
Chain: BSC
Fee: 0.5
Level: 1
Protocol: BitEqual
Risk: 4
Status: Doing
Type: Mint
Unit: USDC
Wallet: bn.web3
---
- [date:: 20250608 22:00:00] [balance:: 248.6] [remark:: ""]
- [date:: 20250630 10:00:00] [balance:: 250] [add:: 10] [remark:: ""]
```

### Dashboard Modifications
**IMPORTANT**: Before modifying `dashboard.md`, always read `README_dashboard.md` first. It contains:
- Complete function documentation with line numbers
- Modification impact analysis 
- High-risk change warnings
- Debugging and troubleshooting guides

Common modification areas:
- Table column ordering (low risk)
- Color schemes and styling (low risk)  
- APR calculation logic (high risk)
- Time range processing (high risk)

### Risk Levels
- **0**: No risk (stablecoins, guaranteed returns)
- **1**: Low risk (established protocols)
- **2**: Medium risk (newer protocols, higher yields)
- **3**: High risk (experimental protocols)
- **4**: Very high risk (speculative investments)

## Data Dependencies

The dashboard requires:
1. Project files in correct YAML format
2. Complete price data for all tracked tokens
3. Valid risk and level configuration files
4. Proper date formatting (YYYYMMDD format)

## Development Notes

- The system uses Obsidian's DataviewJS for dynamic data processing
- All calculations are designed to handle missing data gracefully
- Price conversion supports multiple cryptocurrencies with fallback to API sources
- Time zone handling assumes consistent local time across all entries
- The modular architecture allows for safe modification of individual components

## File Organization Patterns

- Investment files use naming: `YYYYMMDD-Protocol.Type.Unit.md`
- Backup data stored in `backup/` directory
- Templates available in `templates/` for new project creation
- Daily notes follow `YYYY-MM-DD.md` format in `daily/` directory

## Testing and Validation

When making changes:
1. Test with small data sets first
2. Verify calculations manually for known projects
3. Check both risk-based and time-based views
4. Validate price data integration
5. Ensure UI responsiveness across different screen sizes

The dashboard includes comprehensive error handling and will display warnings for missing data or calculation failures.